//document.write(" <script language=javascript src='https://cdn.jsdelivr.net/npm/dsbridge/dist/dsbridge.js'></script>");
import  { dsBridge }  from './dsBridge';
import  { parseResult }  from './parseResult'
const gsmdp = {
  getSystemInfo(obj:any) {
    dsBridge.call("getSystemInfo", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  startNewWebview(url:any){
    dsBridge.call("privateapi.startNewWebview",{"url":url},null);
  },
  getServerUrl(obj: any) {
    dsBridge.call('privateapi.getServerUrl',null, function (ret:any) {
      parseResult(ret, obj)
    })
  },
  getLocationInfo(obj:any){
    dsBridge.call("privateapi.getLocationInfo",obj.data,function (ret:any) {
      parseResult(ret, obj)
    });
  },
  downloadFilePost(obj:any){
    dsBridge.call("privateapi.downloadFilePost",obj,function (ret:any) {
      parseResult(ret, obj)
    });
  },
  finish(obj: any) {
    dsBridge.call("system.finish", obj, function (ret) {
        parseResult(ret, obj);
    });
  },
  goBack(obj: any) {
    dsBridge.call('privateapi.goBack',obj, function (ret:any) {
      parseResult(ret, obj)
    })
  },
  goBackold(obj: any) {
    dsBridge.call('goBack',obj, function (ret:any) {
      parseResult(ret, obj)
    })
  },
  setPageState(){
    dsBridge.call("privateapi.setPageState",null, null);
  },
  initGoback(callback:any){
    dsBridge.register('goBack',callback,null)
  },
  makePhoneCall(obj:any) {
      dsBridge.call("device.makePhoneCall", obj.phoneNumber, function (ret:any) {
        parseResult(ret, obj)
      });
  },
  makePhoneCallSync(obj: any) {
    return dsBridge.call("device.makePhoneCall_sync", obj.phoneNumber, null);
  },
  showToast(obj:any) {
    dsBridge.call("ui.showToast", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  // 显示消息提示框的同步方法
  showToastSync(obj:any) {
    return dsBridge.call("ui.showToast_sync", obj, null);
  },
  showModal(obj:any) {
    dsBridge.call("ui.showModal", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  showLoading(obj:any) {
    dsBridge.call("ui.showLoading", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  hideLoading(obj:any) {
    dsBridge.call("ui.hideLoading", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  getNetworkType(obj: any) {
    dsBridge.call("device.getNetworkType", obj, function (ret: any) {
          parseResult(ret, obj)
     });
  },
  getNetworkTypeSync() {
    const res = dsBridge.call("device.getNetworkType_sync", null, null);
    return res;
  },
  onNetworkStatusChange(obj:any) {
    dsBridge.call("device.onNetworkStatusChange", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  sendMessage(obj:any) {
    dsBridge.call("device.sendMessage", obj.data, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  smspermissions(obj: any){
    dsBridge.call('device.smspermissions', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  // 联系人
  addPhoneContact(obj:any) {
    dsBridge.call("device.addPhoneContacter", obj.key, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  addPhoneContacterSync(obj:any) {
    return dsBridge.call("device.addPhoneContactSync", obj.key, null);
  },
  findPhoneContact(obj: any) {
    dsBridge.call("device.findPhoneContacter",obj.key, function (ret: any) {
      parseResult(ret,obj)
    });
  },
  removePhoneContact(obj: any) {
    dsBridge.call("device.removePhoneContacter",obj.key, function (ret: any) {
      parseResult(ret,obj)
    });
  },
  updatePhoneContact(obj: any) {
    dsBridge.call("device.updatePhoneContacter",obj.key, function (ret: any) {
      parseResult(ret,obj)
    });
  },

  getLocation(obj:any) {
    dsBridge.call("device.getLocation", obj.data, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  httpRequest(obj:any) {
    dsBridge.call("netWork.request", obj.data, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  abort(obj:any) {
    dsBridge.call("netWork.abort", obj.data, function (ret:any) {
      parseResult(ret, obj)
    });
  },

  /*图片*/
  chooseImage(obj:any) {
    dsBridge.call("media.chooseImage", obj.key, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  getLocalImgData(obj:any) {
    dsBridge.call("media.getLocalImgData", obj.localId, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  /*预览视频*/
  previewVideo(obj:any) {
    dsBridge.call("media.previewVideo", obj.videoPath, function (ret:any) {
      parseResult(ret, obj)
    });
  },
   /*预览音频*/
   previewAudio(obj:any) {
    dsBridge.call("media.previewAudio", obj.filePath, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  /*数据缓存相关的API*/
  setStorage(obj:any) {
    dsBridge.call("storage.setStorage", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  // 缓存的同步方法
  setStorageSync(obj:any) {
    return dsBridge.call("storage.setStorage_sync", obj, null);
  },
  // 得到缓存
  getStorage(obj:any) {
    dsBridge.call("storage.getStorage", obj.key, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  // 得到缓存的同步方法
  getStorageSync(obj:any) {
    return dsBridge.call("storage.getStorage_sync", obj.key, null);
  },
  removeStorage(obj:any) {
    dsBridge.call("storage.removeStorage", obj.key, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  // 从缓存中移除同步方法
  removeStorageSync(obj:any) {
    return dsBridge.call("storage.removeStorage_sync", obj.key, null);
  },
  clearStorage(obj:any) {
    dsBridge.call("storage.clearStorage", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  clearStorageSync(obj:any) {
    return dsBridge.call("storage.clearStorage_sync", obj, null);
  },
  /*数据缓存相关的API*/

  /* 文件相关的API */
  saveFile(obj:any) {
    dsBridge.call("file.saveFile", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  saveFileSync(obj:any) {
    return dsBridge.call("file.saveFile_sync", obj, null);
  },
  copyFile(obj:any) {
    dsBridge.call("file.copyFile", obj, function (ret:any) {
    parseResult(ret, obj)
    });
  },
  copyFileSync(obj:any) {
    return dsBridge.call("file.copyFile_sync", obj, null);
  },
  removeSavedFile(obj:any) {
    dsBridge.call("file.removeSavedFile", obj.filePath, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  removeSavedFileSync(obj:any) {
    return dsBridge.call("file.removeSavedFile_sync", obj.filePath, null);
  },
  openFile(obj:any) {
    dsBridge.call("file.openDocument", obj, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  openFileSync(obj:any) {
    return dsBridge.call("file.openDocument_sync", obj, null);
  },
  getSavedFileInfo(obj:any) {
    dsBridge.call("file.getSavedFileInfo", obj.filePath, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  getSavedFileInfoSync(obj:any) {
    return dsBridge.call("file.getSavedFileInfo_sync", obj.filePath, null);
  },
  chooseFile(obj:any) {
    dsBridge.call("file.chooseFile", obj, function (ret:any) {
    parseResult(ret, obj)
    });
  },
  getAssetDirectory(obj: any) {
    dsBridge.call('directory.getAssetDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getAssetDirectorySync() {
    return dsBridge.call('directory.getAssetDirectory_sync', null, null)
  },
  getDataDirectory(obj: any) {
    dsBridge.call('directory.getDataDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getDataDirectorySync() {
    return dsBridge.call('directory.getDataDirectory_sync', null, null)
  },
  getFileDirectory(obj: any) {
    dsBridge.call('directory.getFileDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getFileDirectorySync() {
    return dsBridge.call('directory.getFileDirectory_sync', null, null)
  },
  getCacheDirectory(obj: any) {
    dsBridge.call('directory.getCacheDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getCacheDirectorySync() {
    return dsBridge.call('directory.getCacheDirectory_sync', null, null)
  },
  getExternalDataDirectory(obj: any) {
    dsBridge.call('directory.getExternalDataDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getExternalDataDirectorySync() {
    return dsBridge.call('directory.getExternalDataDirectory_sync', null, null)
  },
  getExternalCacheDirectory(obj: any) {
    dsBridge.call('directory.getExternalCacheDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getExternalCacheDirectorySync(obj: any) {
    return dsBridge.call('directory.getExternalCacheDirectory_sync', null, null)
  },
  getExternalRootDirectory(obj: any) {
    dsBridge.call('directory.getExternalRootDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getExternalRootDirectorySync() {
    return dsBridge.call('directory.getExternalRootDirectory_sync', null, null)
  },
  getExternalApplicationStorageDirectory(obj: any) {
    dsBridge.call('directory.getExternalApplicationStorageDirectory', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  getExternalApplicationStorageDirectorySync() {
    return dsBridge.call('directory.getExternalApplicationStorageDirectory_sync', null, null)
  },
  /* 文件相关的API */

  previewImage(obj:any) {
      dsBridge.call("media.previewImage", obj.key,  function (ret:any) {
        parseResult(ret, obj)
      });
    },
  /* 视频*/
  chooseVideo(obj:any) {
      dsBridge.call("media.chooseVideo", obj.key,  function (ret:any) {
        parseResult(ret, obj)
      });
  },
   /* 音频*/
   chooseAudio(obj:any) {
      dsBridge.call("media.chooseAudio", obj.key,  function (ret:any) {
        parseResult(ret, obj)
      });
  },
   /*打开语音转文字*/
   openVoice(obj:any) {
    dsBridge.call("voiceInput.openVoice", obj.key,  function (ret:any) {
      parseResult(ret, obj)
    });
  },
  getStatusHeightSync() {
    return dsBridge.call("getStatusHeightSync", {}, null);
  },
  /* 文件传输 */
  downloadFile(obj:any) {
      dsBridge.call("fileTransfer.voiceInput", obj,  function (ret:any) {
        parseResult(ret, obj)
      });
  },
  downloadIOFile(obj:any) {
    dsBridge.call("fileTransfer.downloadIOFile", obj,  function (ret:any) {
      parseResult(ret, obj)
    });
  },
  uploadFile(obj: any) {
    dsBridge.call("fileTransfer.uploadFile", obj.key,  function (ret: any) {
      parseResult(ret, obj)
    });
   },

  /* 音频 */
  startRecord(obj:any) {
      dsBridge.call("media.startRecord", obj, function (ret:any) {
        parseResult(ret,obj)
      });
    },
 stopRecord(obj:any) {
      dsBridge.call("media.stopRecord", obj, function (ret:any) {
        parseResult(ret,obj)
      });
    },
 playVoice(obj:any) {
      dsBridge.call("media.playVoice",  obj.localId, function (ret:any) {
        parseResult(ret,obj)
      });
    },
 pauseVoice(obj:any) {
       dsBridge.call("media.pauseVoice",  obj.localId, function (ret:any) {
         parseResult(ret,obj)
       });
     },
 stopVoice(obj:any) {
     dsBridge.call("media.stopVoice",  obj.localId, function (ret:any) {
       parseResult(ret,obj)
     });
   },
 onVoicePlayEnd(obj: any) {
    dsBridge.call('media.onVoicePlayEnd', obj, function(ret: any) {
      parseResult(ret, obj)
    })
  },
 onVoiceRecordEnd(obj: any) {
    dsBridge.call('media.onVoiceRecordEnd', obj, function(ret: any) {
      parseResult(ret, obj)
    })
  },

   /* 进程通讯*/
   processCommunication(obj: any) {
    dsBridge.call("process.processCommunication", obj.key, function (ret: any) {
      parseResult(ret,obj)
    });
  },

  /* 扫码*/
  scanCode(obj: any) {
    dsBridge.call('nfc.scanCode', obj.onlyFromCamera, function(ret: any) {
      parseResult(ret, obj)
    })
  },

  /* NFC */
  getNFCStatus(obj: any) {
    dsBridge.call('nfc.getNFCStatus', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  openNFCSetting(obj: any) {
    dsBridge.call('nfc.openNFCSetting', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },

  /* 安装包 */
  installApk(obj: any) {
    dsBridge.call("device.installApk", obj.key, function (ret: any) {
      parseResult(ret,obj)
    });
  },
  installApkSync(obj: any) {
    return dsBridge.call("device.installApk_sync", obj.key, null);
  },
  /* 热更新 */
  hotUpdate(obj: any) {
    dsBridge.call('device.hotUpdate', obj.path, function (ret: any) {
      parseResult(ret, obj)
    })
  },

  /* 通知相关的API START */
  showNotification(obj: any) {
    dsBridge.call('notification.showNotification', obj, function (ret: any) {
      parseResult(ret, obj)
    });
  },
  showNotificationSync(obj:any) {
    return dsBridge.call('notification.showNotification_sync', obj, null);
  },

  /* 阿里云推送相关api*/
  getDeviceId(obj: any) {
    dsBridge.call('push.getDeviceId',obj, function (ret: any) {
      parseResult(ret, obj)
    });
  },

  turnOnPushChannel(obj: any) {
    dsBridge.call('push.turnOnPushChannel',obj,function (ret: any) {
      parseResult(ret, obj)
    })
  },
  turnOffPushChannel(obj: any) {
    dsBridge.call('push.turnOffPushChannel',obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  checkPushChannelStatus(obj: any) {
    dsBridge.call('push.checkPushChannelStatus',obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  bindAccount(obj: any) {
    dsBridge.call('push.bindAccount', obj.account, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  unbindAccount(obj: any) {
    dsBridge.call('push.unbindAccount',obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  bindTag(obj: any) {
    dsBridge.call('push.bindTag', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  unbindTag(obj: any) {
    dsBridge.call('push.unbindTag', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  listTags(obj: any) {
    dsBridge.call('push.listTags', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },

  addAlias(obj: any) {
    dsBridge.call('push.addAlias', obj.alias, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  removeAlias(obj: any) {
    dsBridge.call('push.removeAlias', obj.alias, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  setNotificationSound(obj: any) {
    dsBridge.call('push.setNotificationSound', obj.path, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  setNotificationLargeIcon(obj: any) {
    dsBridge.call('push.setNotificationLargeIcon', obj.path, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  setDoNotDisturb(obj: any) {
    dsBridge.call('push.setDoNotDisturb', obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  closeDoNotDisturbMode(obj: any) {
    dsBridge.call('push.closeDoNotDisturbMode',obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  clearNotifications(obj: any) {
    dsBridge.call('push.clearNotifications',obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  bindPhoneNumber(obj: any) {
    dsBridge.call('push.bindPhoneNumber', obj.phoneNumber, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  unbindPhoneNumber(obj: any) {
    dsBridge.call('push.unbindPhoneNumber',obj, function (ret: any) {
      parseResult(ret, obj)
    })
  },
  registerOnMessage(obj: any) {
    dsBridge.register('onMessage', obj,function (msg: any) {
      console.log(msg)
      obj.onMessage(msg)
    })
  },
  registerOnNotificationOpened(obj: any) {
    dsBridge.register('onNotificationOpened',obj, function (
      title: any,
      summary: any,
      extraMap: any
    ) {
      obj.onNotificationOpened(title, summary, extraMap)
    })
  },
   /* 阿里云推送相关api -- end --*/
   // google推送获取token
   getGooglePushToken(obj: any) {
    dsBridge.call('googlepush.getGooglePushToken', obj,function (ret: any) {
      parseResult(ret, obj)
    })
  },
  // 判断是否有权限
  hasPermissions(obj: any) {
    dsBridge.call("permission.hasPermissions", obj.data, function (ret:any) {
      parseResult(ret, obj)
    });
  },
  // 申请权限
  permissions(obj: any) {
    dsBridge.call("permission.permissions", obj.data, function (ret:any) {
      parseResult(ret, obj)
    });
  }
};
window['gsmdp']=gsmdp;

export { gsmdp };
