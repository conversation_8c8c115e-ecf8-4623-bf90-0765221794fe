const dataApi = {
  /**
   *
   * @param res 对象
   * @param type 0为list 1为详情
   */
  whetherEmpty(res, type) {
    try {
      if (type == 0) {
        if (res.data.data && res.data.data.list && res.data.data.list.length > 0) {
          return true;
        } else {
          return false;
        }
      } else {
        if (res.data.data) {
          return true;
        } else {
          return false;
        }
      }
    } catch (error) {
      return false;
    }
  },
  emergencyList(res) {
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.planId, //ID
          title: _item.planName, //预案名称
          planTypeCode: _item.planTypeCode, // 类型
          planTypeCodeName: _item.planTypeCodeName, //类型名称
          planLevel: _item.planLevel, //级别
          planLevelName: _item.planLevelName, //级别名称
          publishTime: _item.publishTime, //发布时间
          establishOrgName: _item.establishOrgName, //执行机构
          state: _item.checkStatusName //状态
        };
        resultList.push(obj);
      });
      return resultList;
    } else {
      return resultList;
    }
  },
  emergencyInfo(res) {
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.id, //ID
        title: _item.title, //预案名称
        type: _item.type, // 类型
        typeName: _item.typeName, //类型名称
        level: _item.level, //级别
        levelName: _item.levelName, //级别名称
        endTime: _item.endTime, 
        startTime: _item.startTime, 
        warningInfo: _item.warningInfo, 
        warningGuide: _item.warningGuide, //状态
        districts: _item.districts,
        issueOrg: _item.issueOrg,
        issuePerson: _item.issuePerson,
      };
    }
    return obj;
  },
};
export default dataApi;
