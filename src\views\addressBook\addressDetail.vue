<template>
  <div class="addressDetail">
    <Header :title="$route.query.title || '通讯录'"></Header>
    <div class="addressDetail-container" v-if="personList && personList.length > 0">
      <div class="addressDetail-content" v-for="item in personList" :key="item.userId">
        <div class="item" @click="goToPage(item)">
          <span class="label el-icon-user-solid"></span>
          <div class="cont">
            <p>{{item.personName}}</p>
            <p>{{item.personJob == null || item.personJob =='null'?'':item.personJob }}</p>
          </div>
        </div>
        <!-- <div class="item">
          <span class="label">联系方式</span>
          <span class="cont">{{item.telNumber}}</span>
          <span style="marginLeft: 10px"><van-icon name="phone-circle" color="#13b2a1"/></span>
        </div>
        <div class="item">
          <span class="label">单位名称</span>
          <span class="cont">{{item.orgFullName}}</span>
        </div> -->
      </div>
    </div>
    <van-empty description="暂无人员数据" v-else/>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header,
  }
})
export default class addressDetail extends Vue {
  private personList:any = [];

  private getSubPersonFn() {
    let params = {
      nowPage: 1,
      pageSize: 999,
      orgCode: this.$route.params.id
    }
    apiServer.getSubPersons(params, (res) => {
      if (res.status === 200) {
        this.personList = res.data.data.list;
      }
    })
  }
  mounted() {
    this.getSubPersonFn()
  }
  goToPage(item) {
    
    this.$store.commit('ADDRESSDETAIL', item);
    this.$router.push({path: '/addressBook/addressInfo'})
  }
}
</script>

<style lang="less" scoped>
.addressDetail {
  display: flex;
  flex-direction: column;
  background: #f5f6f6;
  &-container {
    flex: 1;
    overflow: auto;
  }
  &-content {
    margin: 10px;
    padding: 5px 10px;
    background: #fff;
    border-radius: 4px;
  .item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 16px;
    span {
      display: inline-block;
    }
    .label {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 34px;
      width: 50px;
      margin-right: 20px;
      color: #666;
      text-align-last: justify;
      background-color: #99C0E7;
      border-radius: 5px;
      color: #fff;
      height: 50px;
    }
    .cont {
      color: #1c1d1d;
      p {
        margin: 5px 0;
      }
      >p:last-child {
        color: #9EA2A1;
        font-size: 15px;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  }
}
</style>