<template>
  <div class="applySuccess">
    <Header title="报名成功"></Header>
    <div class="applySuccess-content">
      <van-icon name="success" size="60" color="#fff" />
      <span class="result">报名成功</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header,
  }
})
export default class applySuccess extends Vue {
}
</script>

<style lang="less" scoped>
.applySuccess {
  background-color: #f5f6f6;
  &-content {
    margin: 12px 10px;
    padding: 20px 10px;
    padding-top: 50px;
    background: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #1c1d1d;
    /deep/.van-icon {
      padding: 45px;
      background-color: #3772fe;
      border-radius: 50%;
    }
    .result {
      margin-top: 20px;
    }
  }
}
</style>