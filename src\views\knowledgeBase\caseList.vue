<template>
  <div class="div_big">
    <van-nav-bar title="事故案例" left-text="返回"   left-arrow  @click-left="GoBack" >
      <template #right>
        <!-- <van-icon name="ellipsis" size="25" /> -->
      </template>
    </van-nav-bar>
   <!-- invoke common List -->
   <commonList :loadObj="{name:'caseItem'}" :tabArr="tabData" :searchObj="{whetherHave:true,placeholder:'请输入搜索关键词'}"></commonList>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import commonList from '../common/commonList.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    commonList
  }
})
export default class integratedQueryhList extends Vue {
  tabData = [];
  selected: any = 0;
  refreshing: any = false; 
  loading: any = false;
  showDetail: any = false;
  pageIndex: any = 1;
  pageSize: any = 10;
  keyword:any ="";
  open:any = false;
  position:any = 'left';
  queryMap: any ={};
  
  onClickLeft(){
    console.log("返回点击")

  }
  clickMore(){
    console.log("打开菜单")
    this.open=true;
  }
  handleObj(item){
    let _this=this;
    console.log(item)
    if(!item.uploadMore){
      _this.refreshing=false;
      _this.loading=false;
    }
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.pageIndex=1;
    let pageObj={pageIndex:1,pageSize:_this.pageSize}
    _this.$refs.itemRef['queryRequest'](pageObj)
  }
  load() {
    const _this = this;
    _this.pageIndex++;
    _this.loading = true;
    let pageObj={pageIndex:_this.pageIndex,pageSize:_this.pageSize}
    _this.$refs.itemRef['queryRequest'](pageObj)
  }
  GoBack(){
    let _this=this;
    try {
      if(this.$route.query.menu === 'navigatePage') {
        this.$router.push({path: '/navigatePage' });
      } else {
        this.$router.push({path: '/home' });
      }
    } catch (error) {
        console.log("close error")
    }
  }
  closeDetail(){
      this.showDetail=false;
  };
  initTab(){
    let _this=this;
    let arr= this.$store.state.knowledgeRule;
    arr.forEach((item,index)=>{
      if(item.menu_key=='case_list'){
         _this.tabData=item.children;
      }
    })
  }
  created() {
    let _this=this;
    _this.initTab();
    _this.$enJsBack();
  }
  mounted(){
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      _this.GoBack()
    })
    //  this.$gsmdp.initGoback(function(){
    //   _this.GoBack()
    // })
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div{
    width: 100%;
  }
}
.content_list{
  height: calc( 100% - 46px );
   overflow: auto;
    background: #f1efef;
}
.showMenu{
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
      color: white;
      
}
</style>
