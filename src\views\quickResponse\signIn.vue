<template>
  <div class="signIn">
    <Header title="抵达签到"></Header>
    <div class="signIn-content">
      <div class="signIn-content-user">
        <div class="name"><i class="ico ico-user"></i>何全福</div>
        <div class="org"><i class="ico ico-org"></i>湖北省应急厅</div>
      </div>
      <div class="signIn-content-sign">
        <div class="result">
          <i class="ico ico-right"></i>9:02已签到
        </div>
        <div class="sign-cont">
          <div class="sign-img">
            <span class="txt">打卡签到</span>
            <span class="time">10:56:58</span>
          </div>
          <div class="sign-range">
            <i class="ico ico-right-green"></i>已进入打卡范围
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header,
  }
})
export default class signIn extends Vue {
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images';
.signIn {
  background: #f5f6f6;
  &-content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 50px);
    padding: 14px 10px;
    &-user {
      padding: 18px 11px 12px 11px;
      margin-bottom: 10px;
      background: url("@{url}/quickResponse/bg-sign.png") center no-repeat;
      background-size: cover;
      color: #1c1d1d;
      .name {
        margin-bottom: 13px;
        font-size: 17px;
      }
      .org {
        font-size: 18px;
        font-weight: bold;
      }
      .ico {
        &.ico-user {
          background: url("@{url}/quickResponse/ico-user.png") center no-repeat;
        }
        &.ico-org {
          background: url("@{url}/quickResponse/ico-org.png") center no-repeat;
        }
      }
    }
    &-sign {
      flex: 1;
      padding: 10px 11px;
      background-color: #fff;
      border-radius: 4px;
      .result {
        padding: 12px 15px;
        background-color: #f5f6f6;
        color: #333;
        font-size: 15px;
        .ico-right {
          background: url("@{url}/quickResponse/quickResponse02.png") center no-repeat;
        }
      }
      .sign-cont {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 56px;
        .sign-img {
          // position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 165px;
          height: 165px;
          color: #fff;
          background: url("@{url}/quickResponse/img-signIn.png") center no-repeat;
          background-size: cover;
          .txt {
            font-size: 20px;
            font-weight: bold;
          }
          .time {
            font-size: 11px;
            color: #cee0e5;
          }
        }
        .sign-range {
          height: 30px;
          line-height: 30px;
          margin-top: 10px;
          color: #666;
        }
        .ico-right-green {
          background: url("@{url}/quickResponse/quickResponse15.png") center no-repeat;
        }
      }
    }
    .name, .org, .result, .sign-range {
      display: flex;
      align-items: center;
    }
    .ico {
      display: inline-block;
      margin-right: 7px;
      width: 17px;
      height: 17px;
      background-size: contain!important;
    }
  }
}
</style>