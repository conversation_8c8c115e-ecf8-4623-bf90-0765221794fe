<template>
    <div class="info_div">
      <Header title="个人信息"></Header>
      <div class="info_content">
        <ul>
          <li>
            <p>{{detailInfo.title}}</p>
          </li>
          <li class="info_cell">
            <span>姓名：</span>
            <span>{{detailInfo.personName}}</span>
          </li>
          <li class="info_cell">
            <span>性别：</span>
            <span>{{detailInfo.personSexName}}</span>
          </li>
          <li class="info_cell">
            <span>职务：</span>
            <span>{{detailInfo.personJob}}</span>
          </li>
          <li class="info_cell">
            <span>电话：</span>
            <div class="number-info">
                <span>{{detailInfo.telNumber}}</span>
                <span v-if="detailInfo.telNumber" style="marginLeft: 10px"><van-icon name="phone-circle" color="#13b2a1" @click="goToTelPage(detailInfo.telNumber)"/></span>
            </div>
          </li>
          <li class="info_cell">
            <span>办公室电话：</span>
            <div class="number-info">
            <span>{{detailInfo.dutyNumber}}</span>
            <span v-if="detailInfo.dutyNumber" style="marginLeft: 10px"><van-icon name="phone-circle" color="#13b2a1" @click="goToTelPage(detailInfo.telNumber)"/></span>
            </div>
          </li>
          <li class="info_cell">
            <span>部门：</span>
            <span>{{detailInfo.orgFullName}}</span>
          </li> 
        </ul>
      </div> 
    </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header
  },
})
export default class addressInfo extends Vue {
  @Prop(String) private requestId :string;
  @Prop(Object) private requestObj :any;
  detailInfo: any = {};
  

  mounted(){
    this.detailInfo = this.$store.state.app.addressDetail
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`
  }
    

}
</script>
<style scoped lang="scss">
.info_div{
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #f4f7f8;
  .info_content{
    width: 100%;
    height: calc( 100% - 46px );
    overflow-y: auto;
    ul{
      width: 100%;
      background: #fff;
    }
    li{
      width: 100%;
      padding: 0px 3%;
      p{
        margin: 0;
        line-height: 3rem;
        font-size: 17px;
        font-weight: bold;
      }
    }
    .info_cell{
      line-height: 2.5rem;
      color: #333333;
      overflow: hidden;
      span{
        display: inline-block;
      }
      span:nth-of-type(1){
        width: 5rem;
         float: left;
      }
      span:nth-of-type(2){
        width: calc( 100% - 5rem );
        
      }
    }
    .text_area_div{
      width: 100%;
      .text_area_title{
        width: 100%;
        height: 2.5rem;
        line-height: 2.5rem;
        padding: 0px 3%;
        color: c0c0c0;
      }
      .text_area_centent{
        width: 100%;
        background: #fff;
        line-height: 2.5rem;
        padding: 0px 3%;
      }
      
    }
    .info_div_attachment{
      width: 100%;
      background: #fff;
      padding: 0px 3%;
      span{
        display: inline-block;
        float: left;
        line-height: 2.5rem;
        img{
          width: 1.5rem;
          vertical-align: middle;
          float: left;
          margin-top: .3rem;
        }
      }
      span:nth-of-type(1){
        width: 3rem;
      }
      span:nth-of-type(2){
        width: calc( 100% - 3rem );
      }
    }
  }
  .number-info {
    display: flex;
  }
}
</style>