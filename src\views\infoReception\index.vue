<template>
  <div class="list">
    <Header title="信息接报" backPath="/"></Header>
    <div class="list-main">
      <div class="list-tab">
        <van-tabs v-model:active="curInfoType" @change="onClickTab">
          <van-tab
            :title="item.name"
            :name="item.type"
            v-for="item in infoType"
            :key="item.type"
          ></van-tab>
        </van-tabs>
      </div>
      <div class="list-main-search">
        <van-search v-model="keyword" placeholder="关键字搜索" @search="onSearch" />
      </div>
      <div class="list-main-cont" style="max-height: 725px">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            :offset="20"
            @load="getList"
          >
            <van-cell
              v-for="item in list"
              :key="item.id"
              class="van-ellipsis"
              @click="handleToDetail(item)"
            >
              <template #icon>
                <van-icon
                  name="todo-list"
                  size="24"
                  color="#01a9e8"
                  style="marginright: 10px"
                />
              </template>
              <template #title>
                <div class="list-content">
                  <span class="content">{{ item.title }}</span>
                       <span class="time">{{
                    item.reportOrg 
                  }}</span>
                  <span  class="time">{{
                    item.receiveTime || formateDate(item.createTime)
                  }}</span>
                 
                </div>
              </template>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </div>
      <div class="list-main-add" @click="handleAddInfo">{{ btnTitle }}信息</div>
    </div>
    <van-action-sheet
      v-model:show="showInfoType"
      :actions="infoType"
      cancel-text="取消"
      close-on-click-action
      @select="onSelect"
      @cancel="onCancelReception"
    />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import Header from "@/views/common/header.vue";
import { Notify } from "vant";
@Component({
  components: {
    Header,
  },
})
export default class index extends Vue {
  keyword: any = "";
  list: any = [];
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;
  showInfoType: boolean = false;
  addType: any = {}; // 新增信息接报类型
  infoType: any = [
    { name: "突发事件", type: "event" },
    { name: "普通信息", type: "normal" },
  ];
  private paging: any = {
    total: null,
    nowPage: 1,
    pageSize: 15,
  };
  curInfoType: any = "event"; // 当前选中tab
  btnTitle = "";
  mounted() {
    const role = JSON.parse(window.localStorage.getItem("role"));
    this.btnTitle = role.tenantId.split(".").length === 2 ? "录入" : "上报";
  }

  getList(first = false, isSearch = false) {
    let params = {
      // districtCode: JSON.parse(window.),
      districtCode:JSON.parse(window.localStorage.getItem("role")).districtCode,
      // orgCode: JSON.parse(window.localStorage.getItem("role")).orgCode,
      qType: this.curInfoType === "event" ? "1" : "2",
      keyword: this.keyword,
      nowPage: this.paging.nowPage,
      pageSize: this.paging.pageSize,
    };
    if (this.list.length > 0 && this.list.length === this.paging.total && !first) {
      return;
    }
    setTimeout(() => {
      this.loading = true;
      this["$api"].InfoRequest.getEventList(params, (res) => {
        let data = JSON.parse(res.data.data);
        this.paging.total = data.total;
        if (res.data.status === 200) {
          if (this.paging.nowPage === 1) {
            this.list = [];
          }
          if (isSearch) {
            this.list = [...JSON.parse(res.data.data).list];
          } else {
            this.list = [...this.list, ...JSON.parse(res.data.data).list];
          }
          console.log("list---------------->", this.list);
          this.paging.total = data.total;
          this.loading = false;
          this.refreshing = false;
          this.finished = false;
          if (this.list.length >= data.total) {
            this.finished = true;
          }
          this.paging.nowPage++;
        } else {
          Notify({ type: "danger", message: res.data.msg || "请求失败" });
        }
      });
    }, 500);
  }
  formateDate(time) {
    // 兼容之前的时间格式
    if (time instanceof Object) {
      return this["$moment"](time.time).format("YYYY-MM-DD HH:mm:ss");
    } else {
      return this["$moment"](time).format("YYYY-MM-DD HH:mm:ss");
    }
  }
  onClickTab(val) {
    console.log("+++++++++--------->", val);
    this.curInfoType = val;
    this.paging.nowPage = 1;
    this.keyword = "";
    this.getList(true);
  }
  // 点击添加上报信息
  handleAddInfo() {
    this.showInfoType = true;
  }
  onCancelReception() {
    this.showInfoType = false;
  }
  // 点击新增信息
  onSelect(item) {
    console.log("onSelect------>", item);
    this.addType = item;
    const path =
      item.type === "event" ? `/infoReception/addForm` : "/infoReception/ordinaryAddForm";
    this.$router.push({
      path: path,
      query: {
        type: "add",
        infotype: item.type,
      },
    });
  }
  onSearch(value) {
    this.paging.nowPage = 1;
    this.getList(true, true);
  }
  // 跳转详情页面
  handleToDetail(item) {
    const path =
      this.curInfoType === "event" ? `/infoReception/add` : "/infoReception/ordinaryAdd";
    this.$router.push({
      path: path,
      query: {
        type: "detail",
        infotype: this.curInfoType,
        id: item.id || item.ordinaryId || item.receiveid,
      },
    });
  }
  private timer: any = null;
  onRefresh() {
    // 清空列表数据
    this.finished = false;
    this.list = [];
    // 重新加载数据 将 loading 设置为 true，表示处于加载状态
    this.loading = true;
    this.refreshing = true;
    this.paging.nowPage = 1;
    this.getList();
  }
}
</script>

<style lang="less" scoped>
@url: "~@/assets/images/";
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);

      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  /deep/.van-tabs__line {
    background-color: #1967f2;
  }
  .list-content {
    display: flex;
    flex-direction: column;
    .time {
      display: inline-block;
      color: #9f9f9f;
      // span{
      //     &:nth-child(1){
      //       margin-right: 20px;
      //       display: inline-block;
      //     }
      // }
    }
  }
}
</style>
