export const Reservoir = {
  name: '水库大坝',
  layer: 'ALL_reservoir',
  idField: 'sjzj',
  keywordField: 'skmc',
  districtField: 'xzqhdm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'sjzj',
    name: 'skmc',
    address: 'skszwz'
  }
};
export const PumpStation = {
  name: '泵站',
  layer: 'ALL_pump_station',
  idField: 'bzdm',
  keywordField: 'bzmc',
  districtField: 'xzqhdm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'bzdm',
    name: 'bzmc',
    address: 'gcszdz'
  }
};
export const Anjian_chemicalpark = {
  name: '化工园区',
  layer: 'ALL_chemicalpark',
  idField: 'chemparkid',
  keywordField: 'chemparkname',
  districtField: 'districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'chemparkid',
    name: 'chemparkname',
    address: 'address'
  }
};

export const Anjian_chemicalpark_3dtiles = {
  name: '倾斜摄影',
  layer: 'ALL_chemicalpark_3dtiles',
  idField: 'chemparkid',
  keywordField: 'chemparkname',
  districtField: 'districtcode',
  geomField: 'shape',
  filter: {
    where: 'url NOTNULL'
  },
  fieldMap: {
    id: 'chemparkid',
    name: 'chemparkname',
    address: 'address',
    url: 'url'
  }
};
export const dyke = {
  name: '堤防（段）',
  layer: 'ALL_section',
  idField: 'objectid',
  keywordField: 'name',
  // districtField: "pac",
  districtField: 'pac||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    team_flood: 'team_flood'
  }
};
export const ANJIAN_DANGER = {
  name: '重大危险源',
  layer: 'ALL_22A00',
  idField: 'dangerid',
  keywordField: ['dangername', 'firmname'],
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'dangerid',
    name: 'dangername',
    address: 'address',
    hazardlevelcode: 'hazardlevelcode', //危险等级
    contactper: 'contactper',
    contactmtel: 'contactmtel',
    reportdept: 'reportdept',
    categorycode: 'categorycode',
    zdmj: 'zdmj',
    dagpropertycode: 'dagpropertycode', //危险源性质
    danmatnum: 'danmatnum', //危险物数量
    location: 'location', //所在位置
    dstype: 'dstype', //类型
    measureunit: 'measureunit', //计量单位
    danmatname: 'danmatname',
    zdwxyflmc: 'zdwxyflmc', //危险物分类名称
    zdjggymc: 'zdjggymc' //重点监管工艺名称
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    majorDanger: {
      name: '重大危险源',
      layer: 'ALL_22A00',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    majorDanger01: {
      name: '一级危险源',
      layer: 'majorDanger01',
      filter: {
        where: '1=1'
      }
    },
    majorDanger02: {
      name: '二级危险源',
      layer: 'majorDanger02',
      filter: {
        where: '1=1'
      }
    },
    majorDanger03: {
      name: '三级危险源',
      layer: 'majorDanger03',
      filter: {
        where: '1=1'
      }
    },
    majorDanger04: {
      name: '四级危险源',
      layer: 'majorDanger04',
      filter: {
        where: '1=1'
      }
    }
  }
};
export const hazardous = {
  name: '危化品企业',
  layer: 'ALL_anjian_dagchement',
  idField: 'dagchementid',
  keywordField: ['dagchementname', 'hgyqmc'], //hgyqmc:化工园名称
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'dagchementid',
    name: 'dagchementname',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    ANJIAN_hazardous_3dtiles: {
      name: '倾斜摄影',
      layer: 'ALL_DAG0',
      idField: 'dagchementid',
      keywordField: 'dagchementname',
      districtField: 'towncode||county',
      geomField: 'shape',
      filter: {
        where: '1=1'
      },
      fieldMap: {
        id: 'dagchementid',
        name: 'dagchementname',
        address: 'address',
        url: 'url'
      }
    },
    ANJIAN_DAGCHEMENT01: {
      name: '生产企业',
      layer: 'ALL_DAG1',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_DAGCHEMENT02: {
      name: '经营企业',
      layer: 'ALL_DAG2',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_DAGCHEMENT03: {
      name: '使用企业（使用许可）',
      layer: 'ALL_DAG3',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_DAGCHEMENT04: {
      name: '化工企业（不发使用许可）',
      layer: 'ALL_DAG4',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_DAGCHEMENT05: {
      name: '医药企业',
      layer: 'ALL_DAG5',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_DAGCHEMENT06: {
      name: '其他',
      layer: 'ALL_DAG6',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const coalMine = {
  name: '煤矿企业',
  layer: 'ALL_anjian_coal',
  idField: 'coalid',
  idKey: '_id',
  keywordField: 'coalname',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'coalid',
    name: 'coalname',
    address: 'address'
  }
};

// 救援保障企业
export const RescueCompany = {
  name: '救援保障企业',
  layer: 'ALL_jc_materialfirm',
  idField: 'firmid',
  idKey: '_id',
  keywordField: ['firmname', 'contactper', 'contactmtel'],
  geomField: 'shape',
  // districtField: 'towncode||rescounty',
  districtField: 'districtcode',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'firmid',
    name: 'firmname',
    address: 'address',
    respper: 'respper',
    respmtel: 'respmtel',
    contactper: 'contactper',
    contactmtel: 'contactmtel'
  }
};
// 运输保障企业
export const TransportCompany = {
  name: '运输保障企业',
  layer: 'ALL_trs_transport_p',
  idField: 'id',
  idKey: 'id',
  keywordField: ['name', 'address'],
  geomField: 'shape',
  districtField: 'pac',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    districtcode: 'pac',
    empnum: 'empnum',
    notes: 'notes',
    respper: 'respper',
    respmtel: 'respmtel',
    contactper: 'contactper',
    contactmtel: 'contactmtel'
  }
};
// 通信保障企业
export const CommunicationCompany = {
  name: '通信保障企业',
  layer: 'ALL_com_communication_p',
  idField: 'id',
  idKey: 'id',
  keywordField: ['name', 'address'],
  geomField: 'shape',
  districtField: 'pac',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    districtcode: 'pac',
    area: 'area',
    personnum: 'personnum',
    notes: 'notes',
    respper: 'respper',
    respmtel: 'respmtel',
    contactper: 'contactper',
    contactmtel: 'contactmtel'
  }
};
export const fireworkent = {
  name: '烟花爆竹企业',
  layer: 'ALL_anjian_fireworkent',
  idField: 'fireworkentid',
  keywordField: 'fireworkentname',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'fireworkentid',
    name: 'fireworkentname',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    explosive: {
      name: '烟花爆竹企业左侧',
      layer: 'ALL_anjian_fireworkent',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const mine = {
  name: '尾矿库',
  layer: 'ALL_anjian_tailingpond',
  idField: 'wkkid',
  idKey: '_id',
  keywordField: 'wkkmc',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'wkkid',
    name: 'wkkmc',
    address: 'wkkdzmc'
  }
};
export const metalnonmetal = {
  name: '非煤矿山',
  layer: 'ALL_anjian_metalnonmetal',
  idField: 'metalnonmetalid',
  keywordField: 'ksmc',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'metalnonmetalid',
    name: 'ksmc',
    address: 'address'
  }
};
export const ALL_META02 = {
  name: '地下矿山',
  layer: 'ALL_META02',
  idField: 'metalnonmetalid',
  keywordField: 'ksmc',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'metalnonmetalid',
    name: 'ksmc',
    address: 'ksdzmc'
  }
};
export const ALL_META01 = {
  name: '露天矿山',
  layer: 'ALL_META01',
  idField: 'metalnonmetalid',
  keywordField: 'ksmc',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'metalnonmetalid',
    name: 'ksmc',
    address: 'ksdzmc'
  }
};

export const rescueTeam = {
  name: '救援队伍',
  layer: 'ALL_120301010000',
  idField: 'rescueid',
  keywordField: 'rescuename',
  districtField: 'towncode||rescounty',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'rescueid',
    name: 'rescuename',
    address: 'address'
  },
  children: {
    rescueTeam01: {
      name: '地震救援队伍',
      layer: 'ALL_120301020000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam02: {
      name: '森林消防救援队伍',
      layer: 'ALL_120301030000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam03: {
      name: '矿山救援队伍',
      layer: 'ALL_120301040000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam04: {
      name: '危化行业应急救援队伍',
      layer: 'ALL_120301050000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam05: {
      name: '油气行业应急救援队伍',
      layer: 'ALL_120301060000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam06: {
      name: '隧道行业应急救援队伍',
      layer: 'ALL_120301080000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam07: {
      name: '防汛抗旱应急救援队伍',
      layer: 'ALL_120301130000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam08: {
      name: '航空护林队',
      layer: 'ALL_120301150000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam09: {
      name: '海事救援队伍',
      layer: 'ALL_120301090000',
      filter: {
        where: '1=1'
      }
    },
    rescueTeam10: {
      name: '专职消防救援队伍',
      layer: 'ALL_120301580000',
      filter: {
        where: '1=1'
      }
    }
  }
};
export const RescueTeam = {
  name: '消防救援队伍',
  layer: 'ALL_120301010000',
  idField: 'rescueid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: 'rescuename',
  '//districtField': '区划字段，可多字段混合匹配，通过 &&(与) ||(或) 定义匹配规则',
  districtField: 'towncode||rescounty',
  '//geomField': '几何字段，默认为shape',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  '//fieldMap': '字段key:value映射，key为映射后结果字段，value为原始字段',
  fieldMap: {
    id: 'rescueid',
    _id: 'rescueid',
    rescueid: 'rescueid',
    name: 'rescuename',
    rescuename: 'rescuename',
    DISTRICT: 'rescounty',
    ISHAVAIRPORT: 'ishavairport',
    DUTYTEL: 'dutytel',
    LEADERTEL: 'leadertel',
    FORESTEAMTYPE: 'foresteamtype',
    phone: 'leadermtel',
    address: 'address',
    CHARGECONPER: 'chargeconper',
    CHARGECONTEL: 'chargecontel',
    RESCUEGRADE: 'rescuegrade',
    TIMEQUALITY: 'timequality',
    captain: 'leader',
    CHARGEDEPT: 'chargedept',
    rescuetypecode: 'rescuetypecode',
    totalpernum: 'totalpernum',
    districtname: 'rescounty',
    type: 'rescuetypecode',
    timeduty: 'timeduty'
  },
  tableConfig: [
    {
      label: '名称',
      prop: 'rescuename'
    },
    {
      label: '地址',
      prop: 'address'
    }
  ],
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    RescueTeam01: {
      name: '防汛抗旱队',
      layer: 'ALL_120301130000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam02: {
      name: '消防救援队',
      layer: 'ALL_120301010000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam04: {
      name: '森林消防救援队',
      layer: 'ALL_120301030000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam05: {
      name: '危化品救援队',
      layer: 'ALL_120301050000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam06: {
      name: '煤矿救援队',
      layer: 'ALL_120301040000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam07: {
      name: '非煤矿山救援队',
      layer: 'ALL_120301450000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam08: {
      name: '商贸流通救援队',
      layer: 'ALL_120301460000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam09: {
      name: '交通运输救援队',
      layer: 'ALL_120301140000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam10: {
      name: '应急供电救援队',
      layer: 'ALL_120301180000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam11: {
      name: '通信保障队',
      layer: 'ALL_120301170000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam12: {
      name: '燃气救援队',
      layer: 'ALL_120301290000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam13: {
      name: '环境救援队',
      layer: 'ALL_120301200000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam14: {
      name: '抢险打捞队',
      layer: 'ALL_120301230000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam15: {
      name: '海上救援队',
      layer: 'ALL_120301090000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam16: {
      name: '船舶溢油救援队',
      layer: 'ALL_120301470000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam17: {
      name: '医疗卫生队',
      layer: 'ALL_120301210000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam18: {
      name: '港口码头抢险队',
      layer: 'ALL_120301480000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam19: {
      name: '港口客运场站应急队',
      layer: 'ALL_120301490000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam20: {
      name: '港口施工安全队',
      layer: 'ALL_120301500000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam21: {
      name: '建筑应急救援队',
      layer: 'ALL_120301120000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam22: {
      name: '客运应急救援队',
      layer: 'ALL_120301510000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam23: {
      name: '应急运力队',
      layer: 'ALL_120301520000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam24: {
      name: '清雪队伍',
      layer: 'ALL_120301530000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam25: {
      name: '机械设备社会力量',
      layer: 'ALL_120301540000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    RescueTeam26: {
      name: '民间救援队',
      layer: 'ALL_120301420000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const rescueTeamALL = {
  name: '全部救援队伍',
  layer: 'ALL_120301000000',
  idField: 'rescueid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: ['rescuename', 'chargedept', 'leader', 'leadermtel', 'teamspecial'],
  '//districtField': '区划字段，可多字段混合匹配，通过 &&(与) ||(或) 定义匹配规则',
  districtField: 'towncode||rescounty',
  '//geomField': '几何字段，默认为shape',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'rescueid',
    name: 'rescuename',
    phone: 'leadermtel',
    address: 'address',
    leader: 'leader',
    totalpernum: 'totalpernum',
    type: 'rescuetypecode',
    peoplenum: 'totalpernum',
    rescueorgname: 'rescueorgname',
    chargedept: 'chargedept',
    dutytel: 'dutytel',
    rescounty: 'rescounty',
    rescuetype: 'rescuetype',
    rescuetypename: 'rescuetypecode',
    rescuegrade: 'rescuegrade',
    timequality: 'timequality',
    chargeconper: 'chargeconper',
    chargecontel: 'chargecontel',
    sourcedept: 'sourcedept',
    updatetime: 'updatetime',
    xfdwlx: 'xfdwlx'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    rescueTeamALL_01: {
      name: '综合队伍',
      layer: 'rescueTeamALL_01',
      filter: {
        '//where': '在父级基础上追加筛选'
        // "where": "rescuetype = '01'"
      }
    },
    rescueTeamALL_02: {
      name: '专业队伍',
      layer: 'rescueTeamALL_02',
      filter: {
        '//where': '在父级基础上追加筛选'
        // "where": "rescuetype = '02'"
      }
    },
    rescueTeamALL_03: {
      name: '社会队伍',
      layer: 'rescueTeamALL_03',
      filter: {
        '//where': '在父级基础上追加筛选'
        // "where": "rescuetype = '03'"
      }
    },
    rescueTeamALL_04: {
      name: '企业队伍',
      layer: 'rescueTeamALL_04',
      filter: {
        '//where': '在父级基础上追加筛选'
        // "where": "rescuetype = '04'"
      }
    }
  }
};
export const Shelter = {
  name: '避难场所',
  layer: 'ALL_47A00',
  idField: 'shelterid',
  keywordField: 'sheltername',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'shelterid',
    name: 'sheltername',
    respper: 'respper',
    respmtel: 'respmtel',
    address: 'address'
  },
  tableConfig: [
    {
      label: '名称',
      prop: 'name'
    },
    {
      label: '地址',
      prop: 'address'
    }
  ],
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    shelter: {
      name: '避难场所左侧',
      layer: 'ALL_47A00',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const AllExpert = {
  name: '专家',
  layer: 'ALL_JC_EXPERT',
  idField: 'expertid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: ['expertname', 'currentmajor', 'deptname'],
  districtField: 'towncode||district',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    name: 'expertname',
    address: 'address',
    id: 'expertid'
  },
  children: {
    expert: {
      name: '所有专家',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where:
          "(expertgroup in ('3c9282c76d719afb016d71c90e580000','3c9282c76d719afb016d71c90e580001','3c9282c76d719afb016d71c90e580002','3c9282c76d719afb016d71c90e580003','3c9282c76d719afb016d71c90e580004','3c9282c76d719afb016d71c90e580005','3c9282c76d719afb016d71c90e580006','3c9282c76d719afb016d71c90e580007','3c9282c76d719afb016d71c90e580008','3c9282c76d719afb016d71c90e580009') or expertgroup is null)"
      }
    }
  }
};
export const ALL_JC_EXPERT = {
  name: '专家',
  layer: 'ALL_JC_EXPERT',
  idField: 'expertid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: ['expertname', 'currentmajor', 'deptname'],
  districtField: 'towncode||district',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    name: 'expertname',
    address: 'address',
    id: 'expertid'
  },
  children: {
    expert: {
      name: '所有专家',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where:
          "(expertgroup in ('3c9282c76d719afb016d71c90e580000','3c9282c76d719afb016d71c90e580001','3c9282c76d719afb016d71c90e580002','3c9282c76d719afb016d71c90e580003','3c9282c76d719afb016d71c90e580004','3c9282c76d719afb016d71c90e580005','3c9282c76d719afb016d71c90e580006','3c9282c76d719afb016d71c90e580007','3c9282c76d719afb016d71c90e580008','3c9282c76d719afb016d71c90e580009') or expertgroup is null)"
      }
    }
  }
};
export const Expert = {
  name: '专家',
  layer: 'ALL_JC_EXPERT',
  idField: 'expertid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: ['expertname', 'expertitems', 'deptname'],
  districtField: 'towncode||district',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'expertid',
    name: 'expertname',
    district: 'district',
    SEXCODE: 'sexcode',
    DEPTNAME: 'deptname',
    phone: 'tel',
    GROUPNAME: 'expertgroup',
    DUTIES: 'duties',
    PROTITLE: 'protitle',
    HARVEST: 'harvest',
    ACHIHONOR: 'achihonor',
    CURRENTMAJOR: 'currentmajor',
    goodatind: 'goodatind',
    deptname: 'deptname',
    expertitems: 'expertitems',
    sourcedept: 'sourcedept',
    updatetime: 'updatetime'
  },
  tableConfig: [
    {
      label: '专家姓名',
      prop: 'name'
    },
    {
      label: '职称',
      prop: 'PROTITLE'
    },
    {
      label: '联系电话',
      prop: 'TEL'
    }
  ],
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    Expert02: {
      name: '非煤矿山',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580000'"
      }
    },
    Expert03: {
      name: '危险化学品',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580001'"
      }
    },
    Expert04: {
      name: '工商贸',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580002'"
      }
    },
    Expert05: {
      name: '应急救援',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580003'"
      }
    },
    Expert06: {
      name: '火灾防治',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580004'"
      }
    },
    Expert07: {
      name: '防汛抗旱',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580005'"
      }
    },
    Expert08: {
      name: '地震地质灾害',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580006'"
      }
    },
    Expert09: {
      name: '烟花爆竹',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580007'"
      }
    },
    Expert10: {
      name: '政策法规',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580008'"
      }
    },
    Expert11: {
      name: '宣教与信息化',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "expertgroup='3c9282c76d719afb016d71c90e580009'"
      }
    },
    Expertother: {
      name: '其他专家',
      layer: 'ALL_JC_EXPERT',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: 'expertgroup is null'
      }
    }
  }
};
export const disinfoper = {
  name: '灾情信息员',
  layer: 'ALL_disinfoper',
  idField: 'disinfoperid',
  keywordField: 'disinfopername',
  districtField: 'towncode||districtcode',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'disinfoperid',
    name: 'disinfopername',
    personName: 'disinfopername',
    telNumber: 'mobphone',
    duty: 'post',
    personJob: 'post',
    phone: 'mobphone',
    districtname: 'districtname',
    districtcode: 'districtcode',
    personId: 'disinfoperid',
    sourcedept: 'sourcedept',
    updatetime: 'updatetime'
  }
};
// 测绘保障力量
export const ALL_CS_DHL_0001 = {
  name: '测绘影像库',
  layer: 'ALL_CS_DHL_0001',
  idField: 'Mapping',
  filter: {
    where: '1=1'
  },
  keywordField: 'name',
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    longitude: 'longitude',
    latitude: 'latitude',
    resp_per: 'resp_per',
    resp_per_tel: 'resp_per_tel',
    equip: 'equip',
    capacity: 'capacity',
    create_by: 'create_by',
    create_time: 'create_time',
    type: 'type',
    experience: 'experience'
  }
};

// 危险化学品
export const anjian_chemical = {
  name: '危险化学品',
  layer: 'anjian_chemical',
  idField: 'hazarchemId',
  filter: {
    where: '1=1'
  },
  keywordField: ['tyshxydm', 'zdwxybh'],
  fieldMap: {
    id: 'hazarchemId',
    name: 'tyshxydm',
    zwm: 'zwm', //化学品属性名称
    zwbm: 'zwbm', //中文别名
    cash: 'cash', //CAS号
    hxpsxmc: 'hxpsxmc', //化学品属性
    wgyxz: 'wgyxz', //外观与形状
    nsyl: 'nsyl', //年使用量（标方）
    sjscnl: 'sjscnl', //设计生产能力
    sjzdcl: 'sjzdcl', //设计最大储量
    zdwxybh: 'zdwxybh', //重大危险源编号
    ccfsmc: 'ccfsmc' //存储方式名称
  }
};
export const ANJIAN_REPERTORY = {
  name: '物资储备库',
  layer: 'ALL_jc_repertory',
  idField: 'repertoryid',
  idKey: '_id',
  districtField: 'towncode||districtcode',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: ['repertoryname'],
  '//geomField': '几何字段，默认为shape',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  '//fieldMap': '字段key:value映射，key为映射后结果字段，value为原始字段',
  fieldMap: {
    id: 'repertoryid',
    _id: 'repertoryid',
    repertoryid: 'repertoryid',
    name: 'repertoryname',
    repertoryname: 'repertoryname',
    address: 'address',
    REPERTORYTYPENAME: 'repertorytypecode',
    CHARGEDEPT: 'chargedept',
    LEVELNAME: 'levelcode',
    leader: 'concateper',
    phone: 'concatemobtel',
    REPERTORYTYPECODE: 'repertorytypecode',
    sourcedept: 'sourcedept',
    updatetime: 'updatetime'
  },
  tableConfig: [
    {
      label: '名称',
      prop: 'name'
    },
    {
      label: '地址',
      prop: 'address'
    }
  ],
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    ANJIAN_REPERTORYAll: {
      name: '通用防护物资库',
      layer: 'ALL_jc_repertory',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: ''
      }
    },
    ANJIAN_REPERTORY02: {
      name: '通用防护物资库',
      layer: 'ALL_JC1',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY03: {
      name: '防汛抗旱物资库',
      layer: 'ALL_JC2',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY04: {
      name: '城市防汛物资库',
      layer: 'ALL_JC3',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY05: {
      name: '消防设施物资库',
      layer: 'ALL_JC4',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    FOR_ANJIAN_REPERTORY06: {
      name: '防火物资库',
      layer: 'ALL_JC5',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY06: {
      name: '防火物资库',
      layer: 'ALL_JC5',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY07: {
      name: '电力设施物资库',
      layer: 'ALL_JC6',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY08: {
      name: '通讯物资库',
      layer: 'ALL_JC7',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY09: {
      name: '生物防疫物资库',
      layer: 'ALL_JC8',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY10: {
      name: '机场消防设施库',
      layer: 'ALL_JC9',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY11: {
      name: '溢油防治物资库',
      layer: 'ALL_JC10',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY13: {
      name: '港口救援物资库',
      layer: 'ALL_JC11',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_REPERTORY12: {
      name: '防震物资库',
      layer: 'ALL_JC12',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};

export const v_equipment = {
  name: '救援装备',
  layer: 'ALL_EQUIP',
  idField: 'othid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: 'equipname',
  geomField: {
    '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
    source: 'relation',
    '//tag': '关联表标识',
    tag: 'jyxx_tea_rescue',
    '//field': '业务表字段',
    field: 'shape'
  },
  districtField: {
    '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
    source: 'relation',
    '//tag': '关联表标识',
    tag: 'jyxx_tea_rescue',
    '//field': '业务表字段',
    field: 'rescounty'
  },
  filter: {
    where: '1=1'
  },
  '//fieldMap': '字段key:value映射，key为映射后结果字段，value为原始字段',
  fieldMap: {
    id: 'othid',
    rescueid: 'rescueid',
    unit: 'unitcode',
    EQUIPMENTID: 'othid',
    name: 'equipname',
    equipname: 'equipname',
    SPMODEL: 'spmodel',
    EQUIPTYPENAME: 'equiptypecode',
    equiptypecode: 'equiptypecode',
    RESCUENAME: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'relation',
      '//tag': '关联表标识',
      tag: 'jyxx_tea_rescue',
      '//field': '业务表字段',
      field: 'rescuename'
    },
    address: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'relation',
      '//tag': '关联表标识',
      tag: 'jyxx_tea_rescue',
      '//field': '业务表字段',
      field: 'address'
    },
    ADDRESS: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'relation',
      '//tag': '关联表标识',
      tag: 'jyxx_tea_rescue',
      '//field': '业务表字段',
      field: 'address'
    },
    CONTACTPER: 'leader',
    DUTYTEL: 'leadermtel',
    value: 'equipnum',
    SOURCEDEPT: 'sourcedept',
    PARAMETERNAME: 'parametername'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    v_equipment03: {
      name: '直升机',
      layer: 'ALL_1001001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment04: {
      name: '无人机',
      layer: 'ALL_1001002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment06: {
      name: '挖掘机机械',
      layer: 'ALL_1002001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment07: {
      name: '推土机',
      layer: 'ALL_1002002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment08: {
      name: '吊装设备',
      layer: 'ALL_1002003',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment09: {
      name: '装载机',
      layer: 'ALL_1002004',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment10: {
      name: '大流量排水设备',
      layer: 'ALL_1002005',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment11: {
      name: '发电设备',
      layer: 'ALL_1002006',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment12: {
      name: '机动船只',
      layer: 'ALL_1002007',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment13: {
      name: '橡皮艇',
      layer: 'ALL_1002008',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment14: {
      name: '泵车',
      layer: 'ALL_1002009',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment15: {
      name: '运输车辆',
      layer: 'ALL_1002010',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment24: {
      name: '指挥车',
      layer: 'ALL_1004001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment25: {
      name: '举高喷射消防车',
      layer: 'ALL_1004002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment26: {
      name: '泡沫消防车',
      layer: 'ALL_1004003',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment27: {
      name: '水罐消防车',
      layer: 'ALL_1004004',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment28: {
      name: '干粉消防车',
      layer: 'ALL_1004005',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment29: {
      name: '压缩空气消防车',
      layer: 'ALL_1004006',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment30: {
      name: '排烟消防车',
      layer: 'ALL_1004007',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment31: {
      name: '抢险救援车',
      layer: 'ALL_1004008',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment32: {
      name: '后援保障车',
      layer: 'ALL_1004009',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment33: {
      name: '登高平台',
      layer: 'ALL_1004010',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment34: {
      name: '云梯',
      layer: 'ALL_1004011',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment36: {
      name: '救护车',
      layer: 'ALL_1005001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment37: {
      name: '矿山救护车',
      layer: 'ALL_1005002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment38: {
      name: '救护装备车',
      layer: 'ALL_1005003',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment40: {
      name: '应急通信车',
      layer: 'ALL_1006001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment41: {
      name: '应急发电车',
      layer: 'ALL_1006002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment42: {
      name: '配电抢修车',
      layer: 'ALL_1006003',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment43: {
      name: '燃气抢修车',
      layer: 'ALL_1006004',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment45: {
      name: '公交车',
      layer: 'ALL_1007001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment46: {
      name: '客车',
      layer: 'ALL_1007002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment48: {
      name: '洒水车',
      layer: 'ALL_1008001',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    v_equipment49: {
      name: '吸污车',
      layer: 'ALL_1008002',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};

export const ANJIAN_MATERIALINFO = {
  name: '装备信息表',
  layer: 'ALL_MATERIALINFO',
  idField: 'materialid',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: 'materialname',
  filter: {
    where: '1=1'
  },
  '//fieldMap': '字段key:value映射，key为映射后结果字段，value为原始字段',
  fieldMap: {
    MATERIALNAME: 'materialname',
    MATERIALNUM: 'materialnum',
    MATERIALTYPE: 'materialtype',
    materialid: 'materialid',
    repertoryid: 'repertoryid',
    MEASUREUNIT: 'measureunit'
  }
};
// 承载体
export const Portwharf = {
  name: '港口码头',
  layer: 'ALL_32D02',
  idField: 'portwharfid',
  keywordField: 'portwharfname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'portwharfid',
    name: 'portwharfname'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    portwharf: {
      name: '港口码头左侧',
      layer: 'ALL_32D02',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const Airport = {
  name: '机场',
  layer: 'ALL_32E0A',
  idField: 'airportid',
  keywordField: 'airportname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'airportid',
    name: 'airportname',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    airport: {
      name: '飞机左侧',
      layer: 'ALL_32E0A',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const Station = {
  name: '火车站',
  layer: 'ALL_32C05',
  idField: 'stationid',
  keywordField: 'stationname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'stationid',
    name: 'stationname',
    address: 'address'
  },
  children: {
    railwaystation: {
      name: '火车站左侧',
      layer: 'ALL_32C05',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const Coachstation = {
  name: '汽车站',
  layer: 'ALL_32B04',
  idField: 'guid',
  keywordField: 'name',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'guid',
    name: 'name',
    address: 'address',
    RESPPER: 'respper',
    RESPMTEL: 'respmtel',
    districtname: 'districtcode'
  }
};
export const School = {
  name: '学校',
  layer: 'ALL_SCHOOL',
  idField: 'schoolid',
  keywordField: 'schoolname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'schoolid',
    name: 'schoolname',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    school: {
      name: '学校左侧',
      layer: 'ALL_SCHOOL',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const resident = {
  name: '居民区',
  layer: 'ALL_resident',
  idField: 'defobjid',
  keywordField: 'defobjname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'defobjid',
    name: 'defobjname',
    address: 'address'
  }
};
export const Hospital = {
  name: '医院',
  layer: 'ALL_HEL',
  idField: 'orgid',
  keywordField: 'orgname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'orgid',
    name: 'orgname',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    hospital: {
      name: '医院左侧',
      layer: 'ALL_HEL',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};
export const Culturalrelicunit = {
  name: '文物保护单位',
  layer: 'ALL_31H03',
  idField: 'relicid',
  keywordField: 'relicname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'relicid',
    name: 'relicname',
    address: 'address',
    CONTACTPER: 'contactper',
    CONTACTMTEL: 'contactmtel',
    DISTRICTCODE: 'districtcode',
    RELICTYPECODE: 'relictypecode',
    AREA: 'area',
    RELICAGE: 'relicage',
    RELICDESCRIP: 'relicdescrip'
  }
};
export const government = {
  name: '党政机关',
  layer: 'ALL_31A00',
  idField: 'partygovtid',
  keywordField: 'partygovtname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'partygovtid',
    name: 'partygovtname',
    address: 'address',
    CONTACTPER: 'respper',
    CONTACTMTEL: 'respmtel',
    districtname: 'districtcode'
  }
};
export const Archives = {
  name: '档案馆',
  layer: 'ALL_31H11',
  idField: 'partygovtid',
  keywordField: 'partygovtname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'partygovtid',
    name: 'partygovtname',
    address: 'address',
    CONTACTPER: 'respper',
    CONTACTMTEL: 'respmtel',
    districtname: 'districtcode'
  }
};
export const Newscast = {
  name: '电视台',
  layer: 'ALL_31D01',
  idField: 'stationid',
  keywordField: 'stationname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'stationid',
    name: 'stationname',
    address: 'address',
    CONTACTOTEL: 'respper',
    CONTACTMTEL: 'respmtel',
    DISTRICTCODE: 'districtcode'
  }
};
export const Newscast2 = {
  name: '广播电台',
  layer: 'ALL_31D02',
  idField: 'stationid',
  keywordField: 'stationname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'stationid',
    name: 'stationname',
    address: 'address',
    CONTACTOTEL: 'respper',
    CONTACTMTEL: 'respmtel',
    DISTRICTCODE: 'districtcode'
  }
};
export const Resins = {
  name: '科研机构',
  layer: 'ALL_31C00',
  idField: 'resinsid',
  keywordField: 'resinsname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'resinsid',
    name: 'resinsname',
    address: 'address',
    CONTACTPER: 'contactper',
    CONTACTMTEL: 'contactmtel',
    DISTRICTCODE: 'districtcode'
  }
};
export const Financialins = {
  name: '金融机构',
  layer: 'ALL_31G00',
  idField: 'financialid',
  keywordField: 'financialname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'financialid',
    name: 'financialname',
    address: 'address',
    CONTACTPER: 'respper',
    CONTACTMTEL: 'respmtel',
    DISTRICTCODE: 'districtcode'
  }
};
export const Hotel = {
  name: '宾馆饭店',
  layer: 'ALL_31F00',
  idField: 'hotelid',
  keywordField: 'hotelname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'hotelid',
    name: 'hotelname',
    address: 'address'
  }
};
export const Market = {
  name: '大型商贸',
  layer: 'ALL_31F02',
  idField: 'marketid',
  keywordField: 'marketname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'marketid',
    name: 'marketname',
    address: 'address'
  }
};
export const Bazaar = {
  name: '集贸市场',
  layer: 'ALL_31F003',
  idField: 'bazaarid',
  keywordField: 'bazaarname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'bazaarid',
    name: 'bazaarname',
    assress: 'address',
    CONTACTPER: 'respper',
    CONTACTMTEL: 'respmtel',
    DEFOBJTYPECODE: 'defobjtypecode',
    BUSINESSAREA: 'businessarea',
    MATERIAL: 'material',
    EXITDESC: 'exitdesc',
    DISTRICTCODE: 'districtcode'
  }
};
export const Gymnasium = {
  name: '大型文化体育场所',
  layer: 'ALL_31F01',
  idField: 'gymid',
  keywordField: 'gymname',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'gymid',
    name: 'gymname',
    address: 'address'
  }
};

export const zjhdcs = {
  name: '宗教活动场所',
  layer: 'zjwhcs',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    },
    EXITDESC: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: 'exitdesc'
    }
  }
};
export const shfwjg = {
  name: '社会服务机构',
  layer: 'shfwjg',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    },
    EXITDESC: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: 'exitdesc'
    }
  }
};
export const Tourist = {
  name: '旅游景区',
  layer: 'ALL_110214000000',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    },
    EXITDESC: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: 'exitdesc'
    }
  }
};
export const Tourist2 = {
  name: '文化场所',
  layer: 'ALL_110209060000',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    }
  }
};
export const Powerfacilities = {
  name: '供电设施',
  layer: 'ALL_110107000000',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'relation',
      '//tag': '关联表标识',
      tag: 'rel_person_business',
      '//field': '关联表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'info_person',
        '//field': '关联表字段',
        field: '*'
      }
    },
    DISTRICTCODE: 'pac'
  }
};
export const Supwatfacil = {
  name: '供水设施',
  layer: 'ALL_111008000000、110109030000',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    },
    DISTRICTCODE: 'pac'
  }
};
export const Gasfacil = {
  name: '燃气供应设施',
  layer: 'ALL_110108070000',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    },
    DISTRICTCODE: 'pac'
  }
};
export const Gasfacil2 = {
  name: '大型能源动力设施',
  layer: 'ALL_120409000000',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address',
    PERSONINFO: {
      '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
      source: 'business',
      '//field': '业务表字段',
      field: {
        '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
        source: 'relation',
        '//tag': '关联表标识',
        tag: 'rel_person_business',
        '//field': '关联表字段',
        field: {
          '//source': '数据来源，目前两种：relation(关联表)、business(业务表)',
          source: 'relation',
          '//tag': '关联表标识',
          tag: 'info_person',
          '//field': '关联表字段',
          field: '*'
        }
      }
    },
    DISTRICTCODE: 'pac'
  }
};
export const Gasstation = {
  name: '加油站',
  layer: 'ALL_GAS',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    NAME: 'name',
    address: 'address'
  }
};
export const ALL_010300 = {
  name: '加气站',
  layer: 'ALL_010300',
  idField: 'id',
  keywordField: 'name',
  districtField: 'towncode||districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    NAME: 'name',
    address: 'address'
  }
};
export const Tunnel = {
  name: '隧道',
  layer: 'ALL_tunnel',
  idField: 'tunnelid',
  keywordField: 'tunnelname',
  districtField: 'districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'tunnelid',
    name: 'tunnelname',
    address: 'address',
    length: 'tunnellength',
    width: 'tunnelwidth',
    height: 'tunnelheight',
    road: 'road',
    dutytel: 'dutytel',
    respper: 'respper',
    respomtel: 'respomtel',
    contactper: 'contactper',
    contactmtel: 'contactmtel'
  }
};
export const Bridge = {
  name: '桥梁',
  layer: 'ALL_bridge',
  idField: 'bridgeid',
  keywordField: 'bridgename',
  districtField: 'districtcode',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'bridgeid',
    name: 'bridgename',
    address: 'address',
    length: 'bridgelength',
    width: 'bridgewidth',
    limitload: 'limitload',
    road: 'road',
    dutytel: 'dutytel',
    respper: 'respper',
    respomtel: 'respomtel',
    contactper: 'contactper',
    contactmtel: 'contactmtel'
  }
};
export const RoadWater = {
  name: '公路水毁风险点',
  layer: 'ALL_ROAD001',
  idField: 'id',
  keywordField: 'name',
  districtField: 'pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name'
  }
};
export const RoadLandslide = {
  name: '公路滑坡风险点',
  layer: 'ALL_ROAD002',
  idField: 'id',
  keywordField: 'name',
  districtField: 'pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name'
  }
};
export const RoadCollapse = {
  name: '公路沉陷与塌陷风险点',
  layer: 'ALL_ROAD003',
  idField: 'id',
  keywordField: 'name',
  districtField: 'pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name'
  }
};
export const NAH_GEO_GEOLOGICHAZ_P = {
  name: '全部地质灾害隐患点',
  layer: 'ALL_nah_geo_geologichaz_p',
  idField: 'id',
  '//keywordField': '关键字字段，可为字符串或数组；为数组时可对多个字段匹配，或操作，满足其一即可',
  keywordField: 'name',
  '//districtField': '区划字段，可多字段混合匹配，通过 &&(与) ||(或) 定义匹配规则',
  districtField: 'towncode||pac',
  '//geomField': '几何字段，默认为shape',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  '//fieldMap': '字段key:value映射，key为映射后结果字段，value为原始字段',
  fieldMap: {
    id: 'id',
    name: 'name',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    NAH_GEO_GEOLOGICHAZ_P01: {
      name: '地质灾害隐患点',
      layer: 'ALL_nah_geo_geologichaz_p',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: "featurecode in ('100103030000','100103010000','100103020000','100103050000','100103090000')"
      }
    },
    mountaincollapse: {
      name: '崩塌',
      layer: 'ALL_100103030000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    landslide: {
      name: '滑坡',
      layer: 'ALL_100103010000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    debrisflow: {
      name: '泥石流',
      layer: 'ALL_100103020000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    bottomcollapse: {
      name: '地面塌陷',
      layer: 'ALL_100103050000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    emptysubside: {
      name: '采空塌陷',
      layer: 'ALL_100103090000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    groundfissure: {
      name: '地裂缝',
      layer: 'ALL_100103060000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    landsubsidence: {
      name: '地面沉降',
      layer: 'ALL_100103070000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    unstableslopes: {
      name: '不稳定斜坡',
      layer: 'ALL_100103040000',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};

export const trade = {
  name: '工贸企业',
  layer: 'ALL_anjian_ent_whsmyhbz',
  idField: 'whsmyhbzid',
  idKey: '_id',
  keywordField: 'whsmyhbzname',
  districtField: 'towncode||county',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'whsmyhbzid',
    name: 'whsmyhbzname',
    address: 'address'
  },
  '//children': '继承父级所有配置，并覆盖重写（where条件除外）；目前只支持一层children',
  children: {
    ANJIAN_ENT_WHSMYHBZ02: {
      name: '冶金行业',
      layer: 'ALL_ENT01',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ03: {
      name: '有色行业',
      layer: 'ALL_ENT02',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ04: {
      name: '机械铸造',
      layer: 'ALL_ENT03',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ05: {
      name: '粉尘涉爆',
      layer: 'ALL_ENT04',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ06: {
      name: '涉氨制冷',
      layer: 'ALL_ENT05',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ07: {
      name: '商贸行业',
      layer: 'ALL_ENT07',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ08: {
      name: '轻工行业',
      layer: 'ALL_ENT08',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ09: {
      name: '建材行业',
      layer: 'ALL_ENT09',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ10: {
      name: '烟草行业',
      layer: 'ALL_ENT10',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ11: {
      name: '纺织行业',
      layer: 'ALL_ENT11',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    },
    ANJIAN_ENT_WHSMYHBZ12: {
      name: '重点工贸企业',
      layer: 'ALL_ENT12',
      filter: {
        '//where': '在父级基础上追加筛选',
        where: '1=1'
      }
    }
  }
};

export const jznl01 = {
  name: '大型救援装备生产企业数据管理',
  layer: 'jznl01',
  idField: 'id',
  keywordField: 'dwmc',
  districtField: 'rescounty',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'dwmc',
    address: 'address'
  }
};

export const jznl02 = {
  name: '社会组织减灾能力数据管理',
  layer: 'jznl02',
  idField: 'id',
  keywordField: 'dwmc',
  districtField: 'rescounty',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'dwmc',
    address: 'address'
  }
};

export const jznl03 = {
  name: '乡镇街道减灾能力数据管理',
  layer: 'jznl03',
  idField: 'id',
  keywordField: 'dzxiang',
  districtField: 'rescounty',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'dzxiang',
    address: 'address',
    nmczrksl: 'nmczrksl',
    zhs: 'zhs',
    dwfzr: 'dwfzr',
    lxdh: 'lxdh'
  }
};

export const jznl04 = {
  name: '社区行政村减灾能力数据管理',
  layer: 'jznl04',
  idField: 'id',
  keywordField: 'dwmc',
  districtField: 'rescounty',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'dwmc',
    address: 'address',
    nmczrksl: 'nmczrksl',
    zhs: 'zhs',
    dwfzr: 'dwfzr',
    lxdh: 'lxdh'
  }
};

export const ALL_fire_road = {
  name: '防火道路数据管理',
  layer: 'ALL_fire_road',
  idField: 'objectid',
  keywordField: 'fhdlmc',
  districtField: 'xzbm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'fxpc_dataid_sjgl',
    bh: 'bh',
    name: 'fhdlmc',
    dcdw: 'dcdw',
    dcqy: 'dcqy',
    dcrxm: 'dcrxm',
    dh: 'dh',
    length: 'length',
    kd: 'kd',
    jssj: 'jssj',
    sourcedept: 'sourcedept',
    updatetime: 'updatetime',
    xzbm: 'xzbm'
  }
};

export const ALL_forest_barrier = {
  name: '森林防火阻隔带数据管理',
  layer: 'ALL_forest_barrier',
  idField: 'objectid',
  keywordField: 'mc',
  districtField: 'xzbm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'fxpc_dataid_sjgl',
    bh: 'bh',
    name: 'mc',
    dcdw: 'dcdw',
    dcqy: 'dcqy',
    dcrxm: 'dcrxm',
    dh: 'dh',
    cd: 'cd',
    kd: 'kd',
    jssj: 'jssj',
    sourcedept: 'sourcedept',
    updatetime: 'updatetime',
    xzbm: 'xzbm'
  }
};

export const ALL_StationInfo = {
  name: '监测站数据管理',
  layer: 'ALL_StationInfo',
  idField: 'objectid',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'czdm'
  }
};

export const ALL_ReservoirInfo = {
  name: '监测站数据管理',
  layer: 'ALL_StationInfo',
  idField: 'objectid',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'czdm'
  }
};
export const EmergencyStation = {
  name: '社区应急服务站',
  layer: 'ALL_em_station',
  idField: 'fwzdm',
  keywordField: 'fwzmc',
  districtField: 'xzqhdm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'fwzdm',
    name: 'fwzmc',
    address: 'yjfwzxxdz'
  }
};
export const StationEquip = {
  name: '社区应急服务站装备',
  layer: 'ALL_em_station_equip',
  idField: 'zbdm',
  keywordField: 'zbmc',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'zbdm',
    name: 'zbmc',
    value: 'zbsl'
  }
};
export const StationShelter = {
  name: '社区应急服务站紧急疏散场所',
  layer: 'ALL_em_station_shelter',
  idField: 'yjbncsdm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'yjbncsdm',
    name: 'yjbncsmc',
    capacity: 'krnrs',
    area: 'zdzmj',
    address: 'xxdz'
  }
};

export const Culvertgate = {
  name: '涵闸',
  layer: 'ALL_culvertgate',
  idField: 'id',
  keywordField: 'name',
  districtField: 'pac',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'id',
    name: 'name'
  }
};

export const WeatherStationLive = {
  name: '气象站点实况',
  layer: 'ALL_Weather_Station',
  idField: 'zdbh',
  keywordField: 'zdmc',
  districtField: 'xzqhdm',
  geomField: 'shape',
  filter: {
    where: '1=1'
  },
  fieldMap: {
    id: 'zdbh',
    name: 'zdmc',
    distName: 'xzqhmc',
    jd: 'jd',
    wd: 'wd'
  }
};
