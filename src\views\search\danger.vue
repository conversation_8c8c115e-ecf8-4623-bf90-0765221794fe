<template>
  <div class="info_div">
    <van-nav-bar :title="layerInfo.label" left-text="返回" left-arrow @click-left="GoBack"> </van-nav-bar>
    <MeetBack @documentBack="documentBack"></MeetBack>
    <div class="search_div">
      <van-search v-model="keyword" @search="onSearch" input-align="center" show-action placeholder="请输入搜索关键词" :maxlength="20">
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    <div class="info_content">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <van-cell v-for="(item, index) in list" :key="index" style="margin-top: 16px;">
            <template #title>
                <div class="list_content">
                <div> {{ item.name }}</div>
              </div>     
            </template>
            <template #label>
              <div class="list_content">
                <div>{{ item.address }}</div>
              </div>
              <div class="list_content">
                <div>联系人：{{ item.address }}</div>
              </div>
            </template>
          </van-cell>
        </van-list>
      </van-pull-refresh>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import MeetBack from '@/components/meet-back/meet-back';
@Component({
  components: {
    MeetBack
  }
})
export default class HazardInfo extends Vue {
  @Prop(String) private layerName: string;
  @Prop(Object) private layerInfo?: object;
  params: any = {
    nowPage: 1,
    pageSize: 20,
    eventTypeCode: '',
    planTypeCode: '',
    orgname: ''
  };
  keyword: string = '';
  list: any = [
    { name: '武汉东湖新技术开发区佛祖岭社区卫生服务中心', address: '湖北省武汉市江夏经济开发区藏龙岛栗庙路21号' },
    { name: '武汉东湖新技术开发区佛祖岭社区卫生服务中心', address: '湖北省武汉市江夏经济开发区藏龙岛栗庙路21号' }
  ];
  loading: any = false;
  finished: any = false;
  refreshing: any = false;
  onSearch() {
    this.params.orgname = this.keyword;
    this.params.nowPage = 1;
    // this.queryRequest(this.params);
  }
  created() {
    console.log('hazardInfo created');
    console.log(this.layerName);
  }
  documentBack() {
    this.GoBack();
  }
  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
  onLoad() {
    this.refreshing = false;
    this.loading = false;
    return;
    setTimeout(() => {
      if (this.refreshing) {
        this.list = [];
        this.refreshing = false;
      }
      for (let i = 0; i < 10; i++) {
        this.list.push(this.list.length + 1);
      }
      this.loading = false;
      if (this.list.length >= 40) {
        this.finished = true;
      }
    }, 1000);
  }
  onRefresh() {
    // 清空列表数据
    this.finished = false;

    // 重新加载数据
    // 将 loading 设置为 true，表示处于加载状态
    this.loading = true;
    this.onLoad();
  }
}
</script>
<style lang="less" scoped>
.info_div {
  width: 100%;
  height: 100%;
 
  .info_content {
    height: calc(100% - 110px);
    background: #f1efef;
    .list_content {
      font-size: 14px;
      width: 100%;
      line-height: 24px;
    }
  }
}
</style>
