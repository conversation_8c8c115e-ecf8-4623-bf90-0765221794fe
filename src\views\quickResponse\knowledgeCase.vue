<template>
  <div class="knowledgeCase">
    <Header title="知识案例"></Header>
     <div class="list-search">
      <!-- <van-search v-model="searchObj.keyword"
                  placeholder="搜索" />      -->
      <!-- <div class="search-btn">查询</div> -->
    </div>
    <div class="knowledgeCase-container">
      <!-- <div class="caseTable">
        <span @click="clicCaseTable(1)" :class="[caseTableActive==1?'active':'']"><van-icon name="label" />相关知识</span>
        <span @click="clicCaseTable(2)" :class="[caseTableActive==2?'active':'']"><van-icon name="column" />案例推荐</span>
      </div>
      <div class="caseTableCon" v-if="caseTableActive==1">
        <h4>堤防防汛抢险</h4>
        <p>（一）“四必须”<br/>
        （1）必须坚持统一领导、分段负责；<br/>
        （2）必须坚持拉网式巡查不遗漏，相邻对组越界巡查应当相隔至少20米；<br/>
        （3）坚持做到24小时巡查不间断；<br/>
        （4）必须清理堤身、堤脚影响巡查的杂草、灌木等，密切关注堤后水塘。<br/>
        </p>
      
        <p>（二）“六注意”
         （1）注意黎明时；<br/>
         （2）注意吃饭时；<br/>
         （3）注意换班时；<br/>
         （4）注意黑夜时；<br/>
         （5）注意狂风暴雨时；<br/>
         （6）注意退水时。<br/>
        </p>
      
        <p>（三）“五部位”
          （1）背水坡；<br/>
          （2）险工险段；<br/>
          （3）砂基堤段；<br/>
          （4）穿堤建筑物；<br/>
          （5）堤后洼地、水塘。<br/>
        </p>
       
        <p>（四）“五到”<br/>
          （1）眼到。密切观察堤顶、堤坡、堤脚有无裂缝、塌陷、崩垮、浪坎、脱坡、潮湿、渗水、漏洞、翻沙冒水，以及近堤水面有无小漩窝、流势变化。
            （2）手到。用手探摸检查。尤其是堤坡有杂草或障碍物的，要拨开查看。<br/>
            （3）耳到。听水声有无异常，判断是否堤身有漏洞，滩坡有崩坍。<br/>
            （4）脚到。用脚探查。看脚踩土层是否松软，水温是否凉，特别是水下部分更要赤脚探查。<br/>
            （5）工具到。巡堤查险应随身携带铁锹、木棍、探水杆。<br/>

        </p>
      
      </div> -->
      <div class="caseTableCon">
        <h4>{{detailObj.caseName}}</h4>
        <!-- <img style="width:100%;height:100%;padding:0 20px" src="../../assets/images/case01.png" /> -->
        <p>事发时间：{{detailObj.startTime}}</p>
        <p>事发地点：{{detailObj.address}}</p>
        
        详情描述：{{detailObj.conResume}}
       
      </div>
      <p>附件：</p>
      <file-preview direction="left" :fileList="detailObj.attachmentList"></file-preview>
    </div>
    <!-- <van-empty description="暂无知识案例数据"> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
import FilePreview from '@/components/filePreview.vue';
@Component({
  components: {
    Header,
    FilePreview
  }
})
export default class knowledgeCase extends Vue {
  private showObject: any = {};
  private caseTableActive:any=1;
  private searchObj:any={
    keyword:''
  }

  private detailObj = {}
  
  mounted() {  
    const caseId = this.$route.query.caseId;
    this.getDetail(caseId)
  }
  getDetail(caseId) {
    taskRequest.getCaseById({caseId}, (res) => {
      if (res.data.status === 200) {
        this.detailObj = res.data.data;
        this.showObject.showText = true;
        this.showObject.attachmentList = this.detailObj['attachmentList'];
      } else {
        this.detailObj = {}
      }
    });
  }
  private clicCaseTable(n){
    this.caseTableActive=n
  }
}
</script>

<style lang="less" scoped>
.knowledgeCase{
  .list-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px 0 15px;    
    /deep/.van-search {
      width: 100%;
      padding: 0;      
      height: 30px;
      .van-search__content {
        height: 100%;
      }
      .van-cell {
        color: #b5b5b5;
        background: #f4f7f8;
      }
    }
    /deep/.el-select {
      flex: 1;
      background: #f4f7f8;
      .el-input__inner {
        height: 30px;
        border: none;
        color: #b5b5b5;
        outline: none;
      }
    }
    .search-btn {
      border: 1px solid #c9ccd7;
      border-radius: 2px;
      padding: 0 5px;
      // margin-left: 10px;
      height: 30px;
      line-height: 30px;
      color: #333;
    }
  }
  .knowledgeCase-container{
    background: #fff;
    margin-top:10px;
    border-radius: 5px;
    padding:0 15px 15px 15px;
    height: calc(100% - 90px);
    overflow: auto;
    .caseTable{
      span{
        width:50%;
        display: inline-block;
        text-align: center;
        border-bottom:1px solid rgb(215, 215, 215);
        line-height: 40px;
        height: 40px;
        margin-bottom:5px;
        color:#ccc;
        i{
          display: inline-block;
          margin:0 20px 0 0;
          font-size: 20px;
        }
      }
      span.active{
        color:rgb(25, 103, 242);
      }
    }
  }
  
}

</style>