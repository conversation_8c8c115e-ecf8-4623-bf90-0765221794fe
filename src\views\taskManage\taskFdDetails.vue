<template>
  <div class="info_div">
    <van-nav-bar title="任务反馈详情" left-text="返回" left-arrow @click-left="GoBack">
      <template #right>
        <!-- <van-icon name="ellipsis" size="25" /> -->
      </template>
    </van-nav-bar>
    <MeetBack @documentBack="documentBack"></MeetBack>
    <div class="info_content">
      <div class="taskDetails_content">
        <div class="taskDetails_content_lists clearfix">
          <div>反馈标题</div>
          <div>
            {{ taskFkDetails.feedbackTitle }}
          </div>
        </div>
        <div class="taskDetails_content_lists clearfix">
          <div>反馈时间</div>
          <div>
            {{ taskFkDetails.feedbackTime }}
          </div>
        </div>

        <div class="taskDetails_content_lists clearfix">
          <div>反馈状态</div>
          <div v-if="taskFkDetails.status">
            {{ taskFkDetails.stateName }}
          </div>
        </div>
        <div class="taskDetails_content_lists clearfix">
          <div>反馈内容</div>
          <div>
            {{ taskFkDetails.feedbackContent }}
          </div>
        </div>
        <!-- <div class="taskDetails_content_lists clearfix">
              <div>
                  反馈机构
              </div>
              <div>
                  {{taskFkDetails.feedbackOrgName}}
              </div>
          </div> -->
        <!-- <div class="taskDetails_content_lists clearfix">
              <div>
                  反馈地址
              </div>
              <div>
                  {{taskFkDetails.position}}
              </div>
          </div> -->

        <!-- <div class="taskDetails_content_lists clearfix">
              <div>
                  执行机构
              </div>
              <div>
                  {{taskFkDetails.receiveOrgName}}
              </div>
          </div> -->
        <!-- <div class="clearfix">
              <div class="imgsd">
                  <div class="image-choose clearfix">
                      <template v-for="(item,index) in picturelist">
                          <img :key="index" :src="item.src" style="float: left" @click="show(index)">
                      </template>
                      <previewer @on-close="ifClose" :list="picturelist" ref="preview"></previewer>
                  </div>
                  <span v-for="(item,index) in fujianlist" :key="index" @click="openFileZi(item)">
                      <img src="../../assets/images/taskList/ppt.png" alt="">
                      <i>
                          {{item.name}}
                      </i>
                  </span>
              </div>
          </div> -->
        <p>附件：</p>
      <file-preview direction="left" :fileList="taskFkDetails.attachmentList"></file-preview>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import FilePreview from '@/components/filePreview.vue';
import MeetBack from '@/components/meet-back/meet-back';
@Component({
  components: {
    FilePreview,
    MeetBack
  }
})
export default class integratedItem extends Vue {
  @Prop(String) private requestId: string;
  @Prop(Object) private requestObj: any;
  taskFkDetails: any = {};
  showObject: any = {};
  infoRequest() {
    let _this = this;
    let param = {
      id: _this.requestId
    };
    apiServer.findTaskFkInfo(param, function (res) {
      console.log(res);
      if (res.data.data) {
        _this.taskFkDetails = res.data.data;
        _this.taskFkDetails.stateName = _this.taskFkDetails.status && _this.taskFkDetails.status == '1' ? '进行中' : '已完成';
      }
    });
  }
  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
  documentBack() {
    this.GoBack();
  }
  created() {
    let _this = this;
    //_this['$apiServer'].setToken();
    console.log('init run');
    _this.infoRequest();
    // setTimeout(function() {

    // }, 500);
  }
  mounted() {
    let _this = this;
    _this['$gsmdp'].initGoback(function () {
      console.log('调用返回');
      _this.GoBack();
    });
  }
}
</script>
<style scoped lang="scss">
.info_div {
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #f4f7f8;

  .info_content {
    width: 100%;
    height: calc(100% - 46px);
    overflow-y: auto;
    .taskDetails_content {
      width: 100%;
      height: 100%;
      background-color: #fff;
      overflow: auto;
      .taskDetails_content_lists {
        border-top: 1px solid #c7c7c7;
        div:nth-child(1) {
          float: left;
          margin-left: 20px;
          height: 50px;
          font-size: 16px;
          color: #333;
          display: flex;
          align-items: center;
          margin-right: 30px;
        }
        div:nth-child(2) {
          overflow: hidden;
          color: #333;
          line-height: 40px;
          padding-top: 5px;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
      .taskDetails_content_lists:nth-child(1) {
        border-top: none;
      }
    }
  }
}
</style>
