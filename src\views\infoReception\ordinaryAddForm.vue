<template>
  <div class="list">
    <Header :title="type === 'add' ? '新增普通信息' : details.title" backPath="/infoReception"></Header>
    <div class="list-main">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field v-model="details.title" name="title" label="信息标题" placeholder="请输入信息标题" maxlength="50" :rules="rules.title">
            <template #left-icon>
              <van-icon name="coupon-o" color="#c8e3ee" />
            </template>
          </van-field>
          <van-field v-model="details.note" name="note" label="备注" type="textarea" placeholder="请输入备注">
            <template #left-icon>
              <van-icon name="orders-o" color="#eed49b" />
            </template>
          </van-field>
          <van-field v-model="details.issuername" name="issuername" label="签发人" placeholder="请输入签发人" :rules="rules.issuername">
            <template #left-icon>
              <van-icon name="user-o" color="#e4bfa0" />
            </template>
          </van-field>
          <van-field v-model="details.editerName" name="editerName" label="编辑人" :readonly="true" placeholder="请输入编辑人">
            <template #left-icon>
              <van-icon name="user-o" color="#e4bfa0" />
            </template>
          </van-field>
          <van-field name="uploader" label="附件">
            <template #left-icon>
              <van-icon name="photo-o" color="#e4bfa0" />
            </template>
            <template #input>
              <van-uploader v-model="details.attachment" multiple :max-count="5" />
            </template>
          </van-field>
        </van-cell-group>
        <div class="submit-btn">
          <van-button block type="primary" native-type="submit"> 确认{{btnTitle}} </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class ordinaryAddForm extends Vue {
  public type: any = ''; // 页面类型，add-新增，detail-详情
  public infoType: any = ''; // 页面信息类型
  public details: any = {}; // 接报信息字段

  public rules: any = {
    title: [
      { required: true, message: '请输入信息标题' },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5\d-#:：\(\)\）\（]{1,50}$/, message: '请输入50字以内标题' }
      // { pattern: /^\d{6}$/, message: '请输入6位数字' }
    ],
    issuername: [
      { required: true, message: '请输入姓名' },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: '请输入正确的姓名' }
    ]
  };
  private btnTitle = '';
  private role: any;
  async mounted() {
    console.log('信息接报----》', this.$route.query);
    this.type = this.$route.query.type;
    this.infoType = this.$route.query.infotype;
    this.role = JSON.parse(localStorage.getItem('role'));
    this.btnTitle = this.role.tenantId.split('.').length === 2 ? '录入' : '上报'
    this.details.tenantId = this.role.tenantId;
    this.details.orgcode = this.role.orgCode; // 所属机构编码
    this.details.reporter = this.role.personId; // 报送人用户ID
    this.details.editorid = this.role.personId; // 报送人用户ID
    this.details.districtCode = this.role.districtCode; // 行政区划代码
    this.$set(this.details, 'editerName', this.role.name);
  }

  // 新增/保存信息接报详情
  saveInfo() {
    let params = Object.assign({}, this.details);
    // 格式化日期传参
    params.reportDistCode = this.role.districtCode;
    params.dealStateCode = this.role.tenantId.split('.').length === 2 ? '04' : '14'; // 处置状态,保存时传04，上报时传14
    this['$api'].InfoRequest.getOrdinaryEventReport(params, (res) => {
      if (res.data.status == 200) {
        Notify({ type: 'success', message: res.data.msg || '上报成功' });
        this.$router.push('/infoReception');
        this.afterRead(res.data.data)
      } else {
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    });
  }

  // 确认上报按钮
  onSubmit() {
    console.log('确认上报按钮+++++++++', this.details);
    this.saveInfo();
  }

  //上传
  async afterRead(receiveId) {
    let _this = this;
    const formData = new FormData();
    this.details.attachment.map(ele => {
      formData.append('files',ele.file );
    })
    formData.append('receiveId ', receiveId);
    this['$api'].InfoRequest.getAttach(formData, function (res) {
      console.log('getAttach-上传完成--->', res);
    });
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: auto;
    /deep/.van-form {
      display: flex;
      flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
      }
    }
  }
}
</style>