<template>
  <div>
    <div class="item_content_list" v-for="(item, index) in requestList" :key="index">
      <ul>
        <li>
          <div class="list_content">
            <div @click="comeInDetail(item)">{{ item.teamName }}</div>
          </div>
          <div class="list_content">
            <file-preview direction="left" :fileList="attachmentList"></file-preview>
          </div>
          <div class="list_top">
            <div class="btnPart">
              {{ item.routeDate }}
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div v-if="eventList && eventList.length == 0 && requestStatus == 'end'" style="width: 100%; text-align: center; padding-top: 10%">
      <van-empty description="暂无数据" />
    </div>
    <van-popup v-model="detailHandle.showInfo" v-if="detailHandle.showInfo" position="right" :style="{ height: '100%', width: '100%' }">
      <!-- <Infodetail :requestId="requestId" :requestObj="requestObj" @close="closeInfo" /> -->
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { Notify, Dialog } from 'vant';
import apiServer from '../../api/request-service';
import FilePreview from '@/components/filePreview.vue';
@Component({
  components: {
    FilePreview
    //   Infodetail
  }
})
export default class integratedItem extends Vue {
  @Prop(String) private requestFlag: string;
  @Prop(Object) private requestObj: any;
  @Prop(Array) private requestList: any;
  eventList: any = [];
  keywords: any = '';
  requestStatus: any = '';
  requestId: any = '';
  isInit: any = true;
  detailHandle: any = { showInfo: false };
  attachmentList = [
    {
      $type: 'AttachmentOutDTO,http://www.dv.com',
      name: '深岑高速公路（江门段） “4·22”较大碰撞事故调查报告.docx',
      url: '/gapi/gemp-user/api/attachment/download/v1?fileId=hdf0907006',
      msg: null,
      status: null,
      attachId: 'hdf0907006',
      urlOuterNet: '/upload/uploadFile/duty/hdf0907006.docx'
    }
  ];
  closeInfo() {
    let _this = this;
    _this.detailHandle.showInfo = false;
    _this['$gsmdp'].initGoback(function () {
      // console.log("调用返回")
      try {
        _this['$gsmdp'].goBack(function () {});
      } catch (error) {
        // console.log("close error")
      }
    });
  }
  searchSet(_keyword) {
    this.keywords = _keyword;
  }
  comeInDetail(_item) {
    let _this = this;
    _this.requestId = _item.id;
    _this.requestObj = _item;
    // console.log("come in detail "+_this.requestId)
    _this.detailHandle.showInfo = true; //显示详情
  }
  // 追加
  handleAddTask(_item, type) {
    let _this = this;
    _this.requestId = _item.id;
    _this.requestObj = _item;
    _this.detailHandle.showInfo = true; //显示详情
  }
  handleRelease(_item) {
    Dialog.confirm({
      message: '确定解除预警吗？'
    }).then(() => {
      apiServer.liftWarning({ id: _item.id }, (res) => {
        if (res.data.status === 200) {
          // this.groupMenberList.splice(index, 1);
          this.eventList = [];
          this.queryRequest(null);
        } else {
          Notify(res.data.msg);
        }
      });
    });
  }
  queryRequest(list) {
    console.log('queryRequest', list);
    let _this = this;
    _this.eventList = list;
    _this.requestStatus = 'end';
  }

  created() {
    let _this = this;
    //_this['$apiServer'].setToken();
    console.log('init run');
    _this.eventList = _this.requestList;
    //   _this.queryRequest(null);
    // setTimeout(function() {

    // }, 500);
  }
}
</script>
<style scoped lang="scss">
.item_content_list {
  width: 100%;
  height: 100%;
  background-color: #fff;
  li {
    width: 100%;
    background: white;
    margin-top: 15px;
    padding-top: 10px;
    .list_top {
      width: 100%;
      height: 50%;
      position: relative;
      text-align: right;
      .btnPart {
        padding: 0px 4vw 4vw 3%;
        .action {
          background: transparent;
          color: #e45050;
          border: 1px solid #e45050;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          margin-left: 5px;
          max-width: 30%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .total {
          background: transparent;
          color: #00a0e9;
          border: 1px solid #00a0e9;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          max-width: 30%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .time_show {
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 0px;
          float: right;
        }
      }
    }
    .list_content {
      width: 100%;
      // line-height: 35px;
      padding-left: 3%;
      font-size: 15px;
      div {
        // float: left;
        line-height: 35px;
      }
      div:nth-of-type(1) {
        width: 90%;
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      div:nth-of-type(2) {
        width: 10%;
        height: 100%;
        img {
          width: 15px;
          vertical-align: middle;
          margin-top: -5px;
        }
      }
    }
  }
}
</style>
