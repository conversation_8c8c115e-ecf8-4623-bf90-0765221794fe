<template>
  <div class="addressBookCheck">
    <!-- <div class="common-header">
      <van-nav-bar title="通讯录" />
    </div> -->
    <Header title="通讯录" backPath="/reportaddform"></Header>
    <!-- <div class="addressBookCheck-card">
      <div class="cardImg"></div>
      <div class="cardText">{{ currenInfo.label }}</div>
    </div> -->
    <!-- <div class="addressBookCheck-cardInfo">
      <van-cell-group inset>
        <van-cell inset title="部门电话" :value="currenInfo.dutyTel">
          <template #right-icon v-if="currenInfo.dutyTel">
            <van-icon name="phone-circle" class="phone-icon" color="#13b2a1" @click="goToTelPage(currenInfo.dutyTel)" />
          </template>
        </van-cell>
        <van-cell inset title="部门传真号" :value="currenInfo.fax"></van-cell>
        <van-cell inset title="上级部门" :value="currenInfo.orgName"></van-cell>
      </van-cell-group>
    </div> -->
    <div class="addressBookCheck-content">
      <div class="addressBookCheck-content-search">
        <!-- <van-search v-model="keyword" placeholder="可按名称查询" @search="onSearch" /> -->
        <van-search @change="onSearch2" v-model="keyword2" placeholder="可按姓名关键字查询" @search="onSearch2" />
      </div>
      <div>
        <!-- <el-autocomplete popper-class="my-autocomplete" v-model="queryParams"
						:fetch-suggestions="querySearch" placeholder="可输入姓名关键字查询" 
						@select="handleSelect">
						<template slot-scope="{ item }">
							<div class="name">{{ item.name }}</div>
						</template>
					</el-autocomplete> -->
      </div>
      <template v-if="keyword2">
        <!-- <van-loading color="#0094ff" v-if="loadover" vertical>加载中...</van-loading> -->
        <div class="addressDetail-container" v-if="personList && personList.length > 0">
          <div class="addressDetail-content" v-for="item in personList" :key="item.userId">
            <div class="item">
              <span class="label el-icon-user-solid"></span>
              <div class="cont">
                <p>{{ item.personName }}</p>
                <p>{{ item.personJob }}</p>
              </div>
              <div class="sperCheck">
                <van-checkbox shape="square" checked-color="#07c160" v-model="item.checked" @click="changeCheckBox(item)"></van-checkbox>
              </div>
            </div>
          </div>
        </div>
        <!-- aa -->
        <van-empty description="暂无查询到人员数据" v-else></van-empty>
      </template>

      <template v-else>
        <div class="addressBookCheck-content-list" v-for="item in addressBooks" :key="item.id">
          <!-- <span class="title">{{item.label}}</span> -->
          <!-- <span class="title">下级部门</span> -->
          <ul>
            <li v-for="list in item.children" :key="list.id" @click="handleAddress(list)">
              <span>{{ list.label }}</span>
              <van-icon name="arrow" />
            </li>
          </ul>
        </div>
      </template>      
    </div>
    <div v-if="keyword2" class="list-main-add" @click="handleAddInfo">确认勾选</div>
  </div>
</template>

<script lang="ts">
import { Component, Inject, Vue, Watch } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class addressBookCheck extends Vue {
  private keyword: any = '';
  private loadover: any = false;
  private addressBooks: any = [];
  private addressAllBooks: any = [];
  private queryParams = '';
  private keyword2 = '';
  private personList = [];
  private handleSelect(item) {}
  private querySearch(queryString, cb) {
    this.getSeachData(queryString || '', cb);
  }

  private currenInfo: any = {};

  private handleAddInfo() {
    debugger;
    this.$store.commit('PERSONDETAIL', []);
    let newAry = this.personList.filter((el) => el.checked);
    if(newAry.length > 1 || newAry.length == 0){
       Notify({ type: "danger", message: "请勾选一个审批人" });
       return
    }
    console.log(newAry, '选中的数据');
    this.$store.commit('PERSONDETAIL', newAry);
    this.$router.push({ path: '/reportAddForm' });
  }

  private handleAddress(item) {
    debugger;
    if (item.leaf) {
      this.$router.push({ path: `/addressBookCheck/detail/${item.id}`, query: { title: item.label } });
    } else {
      // 有子节点
      this.currenInfo = item;
      this.$router.push({
        path: `/addressBookCheck/list/${item.id}`,
        query: {
          title: item.label,
          parentLabel: item.parentLabel
        }
      });
    }
  }

  private getUserTree() {
    let datas = [];
    apiServer.getCurrentUserTreeList({ orgCode: this.$store.state.userInfo.orgcode, keyword: this.keyword }, (res) => {
      if (res.status === 200) {
        this.addressBooks = res.data.data;
        this.addressAllBooks = res.data.data[0]?.children || [];
        this.getUserInfo(res.data.data[0].id);
      }
    });
  }

  private getUserInfo(orgCode) {
    apiServer.getUserInfo({ orgCode }, (res) => {
      if (res.status === 200) {
        this.currenInfo = res.data.data;
      }
    });
  }

  private getSeachData(text, cb) {
    apiServer.baseuserUser({ nowPage: 1, pageSize: 200, keyWord: this.keyword2 }, (res) => {
      if (res.status == 200) {
        if (res.data.data.list.length > 0) {
          cb(res.data.data.list);
        } else {
          cb([]);
        }
      }
    });
  }

  private onSearch(value) {
    this.keyword = value;
    this.addressBooks[0].children = this.addressAllBooks.filter((ele) => ele.label.includes(value));
    // this.getUserTree();
  }
  @Watch('keyword2')
  private onChangeValue() {
    this.onSearch2();
  }
  private onSearch2() {
    this.loadover = true;
    apiServer.baseuserUser({ nowPage: 1, pageSize: 200, keyWord: this.keyword2 }, (res) => {
      if (res.status == 200) {
        this.loadover = false;
        if (res.data.data.list.length > 0) {
          this.personList = res.data.data.list;
        } else {
          this.personList = [];
        }
      } else {
        this.loadover = false;
      }
    });
  }

  mounted() {
    this.getUserTree();
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`;
  }
}
</script>

<style lang="less" scoped>
.list-main-add {
  // position: absolute;
  // bottom: 0;
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #fff;
  color: #1967f2;
  letter-spacing: 3px;
  font-weight: bold;
  position: absolute;
  bottom:0;
}
.addressBookCheck {
  background: #f5f6f6;
  position: relative;
  span {
    display: inline-block;
  }
  &-card {
    height: 200px;
    width: 100%;
    background: #3577e9;
    display: flex;
    .cardImg {
      width: 80px;
      height: 80px;
      background: url('../../assets/images/home/<USER>');
      background-size: 100% 100%;
      margin: 30px 20px 0 50px;
    }
    .cardText {
      color: #fff;
      font-size: 18px;
      margin-top: 60px;
    }
  }
  &-cardInfo {
    width: calc(100vw - 20px);
    margin: 10px;
    background: #fff;
    position: absolute;
    top: 160px;
    border-radius: 10px;
  }
  &-content {
    margin: 0 10px;
    width: calc(100vw - 20px);
    position: absolute;
    top: 50px;
    height: calc(100% - 60px);
    // overflow: auto;
    &-search {
      margin-bottom: 5px;
    }
    &-list {
      overflow: auto;
      height: calc(100% - 58px);
      .title {
        padding: 5px 10px;
        color: #b2b4b5;
      }
      ul {
        padding-left: 10px;
        background: #fff;
        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 36px;
          padding-right: 10px;
          border-bottom: 1px solid #eae8eb;
          color: #1c1d1d;
          /deep/.van-icon {
            color: #dddde0;
          }
        }
      }
    }
  }
}
.addressDetail {
  display: flex;
  flex-direction: column;
  background: #f5f6f6;
  &-container {
    flex: 1;
    overflow: auto;
    height: calc( 100% - 80px );
  }
  &-content {
    margin: 10px;
    padding: 5px 10px;
    background: #fff;
    border-radius: 4px;
    .lefItem {
      display: flex;
      align-items: center;
      width: calc(100% - 50px);
    }
    .sperCheck {
      width: 50px;
      display: flex;
      justify-content: flex-end;
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 16px;
      justify-content: space-between;
      align-items: center;
      span {
        display: inline-block;
      }
      .label {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 34px;
        width: 50px;
        margin-right: 20px;
        color: #666;
        text-align-last: justify;
        background-color: #99c0e7;
        border-radius: 5px;
        color: #fff;
        height: 50px;
      }
      .cont {
        color: #1c1d1d;
        width: calc(100% - 50px);
        p {
          margin: 5px 0;
        }
        > p:last-child {
          color: #9ea2a1;
          font-size: 15px;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>