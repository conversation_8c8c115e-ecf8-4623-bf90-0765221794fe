<template>
  <div class="div_big">
    <!-- <van-nav-bar title="每日签到" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
    <van-icon name="ellipsis" size="25" />
  </template>
   </van-nav-bar> -->
    <!-- 主体 -->
  <div class="main_box">
    <span class="Go_back" @click="GoBack">
      <van-icon size="18" style="vertical-align: middle;margin-top: -3px;padding-right: 3px;" name="arrow-left" />返回
    </span>
    <div class="time_show_div" @click="monthRange()">
      <p>{{dataStr}}</p>
      <span>
        <img src="../../assets/images/icons/calendar.png" alt="" srcset="">
      </span>
    </div>
    <!-- 日历 -->
    <div class="datePicker" @touchstart="moveClick" @touchmove="moveEv" @touchend="moveEnd">
      <Calendar
        :now="false"
        :responsive="false"
        lunar
        clean
        @select="selected"
        @selectYear="selectYeared"
        @selectMonth="selectMonthed"
        @prev="preved"
        @next="nexted"
        :multi="false"
        :tileContent="tileContent"
        ref="calendar"
      />
    </div>
    <!-- 值班内容 -->
    <div class="duty_box">
      <div class="element_div">
        <div class="child_div" @click="refreshLocation()">
          <span v-if="!signFlag">未签到</span>
          <span v-else>
            <img src="../../assets/images/icons/signIn.png" alt="" srcset="">
          </span>
        </div>
      </div>
      <div class="every_sign_text">
        每日签到
      </div>
     
    </div>
  </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Calendar from '../mpvueCalendar/mpvue-calendar.vue';
import '../mpvueCalendar/browser-style.css';
import apiServer from '../../api/request-service';
@Component({
  name: 'dynamic',
  components: { 
    Calendar
  }
})
export default class dynamic extends Vue {
  private value = '';
  //DOM切换
  private dataList = '';
  private openSimple = false;
  private signFlag = false;
  private org_code = this.$store.state.userInfo.orgCode; //组织机构
  private listArr = [];
  private isCreate =true;
  private allDuty = [];//有值班信息的集合
  private dataStr ='';
  private orgObj={};//组织机构信息
  private tileContent = [
      // {date: '2020-6-22', className: 'workday', content: ' '},
      // {date: '2020-6-10', className: 'workday', content: ' '},
      // {date: '2020-6-24', className: 'workday', content: ' '}
  ];
  //时间选择器
  private year = '';
  private mounth = '';
  private year_mounth = '';
  private curr_year_mounth='';
  private currDay=false;//是否为今天
  private selected_date="";
  private selected_date_other="";
  //组织选择期
  private orgList = [];
  private pageXI = 0;
	private pageYI = 0;
  private swcount = 0;
  private lontatStr='';
  private lonStr="";
  private latStr="";
  private address="";
  private defaultProps = {
    children: 'children',
    label: 'org_name'
  };
  private baseurl = '';
  signIn(){
    let _this=this;
    console.log("点击签到")
    let signDate=_this.$formatTimeStr(_this.selected_date);
    let param = {
      signDate:signDate,
      userId:_this.$store.state.userInfo.userid,
      signPlace:_this.address
    }
    console.log("signIn param==>"+JSON.stringify(param))
   
    apiServer.signIn(param,function name(res) {
      _this.$toast.clear();
      console.log(res)
      if(res.status==200){
        _this.$showBottomToast(res.data.data)
        _this.getNowYearAndMounth();//签到成功刷新数据
        _this.signFlag=true;
      }else{
        _this.$showBottomToast("签到失败"+res.data)
      }
      
    })
    
  }
  locationSet(lontatStr){
    let _this=this;
    console.log("locationSet===>"+lontatStr)
    _this.lontatStr=lontatStr;
    _this.lonStr=lontatStr.split("_")[0];
    _this.latStr=lontatStr.split("_")[1];
    _this.address=lontatStr.split("_")[2];
    _this.signIn()
  }
  refreshLocation(){
    let _this=this;
    if(!_this.currDay){
       _this.$showBottomToast("您不能签到其它日期！")
       return;
    }
    if(_this.signFlag){
       _this.$showBottomToast("您已经签到了不用再次签到！")
       return;
    }
     _this.$showBottomToast("正在加载...",1)
    if(this.$isAndroid()){
      console.log("invoking android!")
      // window.android.getCurrentLocation();
      _this['$gsmdp'].getLocation({
      data: {
        type: 'gcj02',
        altitude: 'true'
      },
      success(result) {
        //alert('getLocation' + JSON.stringify(result));
        //alert(result.longitude+"==>"+result.latitude)
        _this['$gsmdp'].getLocationInfo({
              data:{
                "longitude":result.longitude,
                "latitude":result.latitude,
                "address":"",
              },
              success(res){
                if(res.longitude!=""&&res.latitude!=""&&res.address!=""){
                  let addressStr=res.longitude+"_"+res.latitude+"_"+res.address;
                  _this.locationSet(addressStr)
                }
              }
              
            })
          },
          fail(err) {
            console.log(err);
            _this.$toast("获取位置信息失败请重新点击签到")
          }
        });
    }else{
      setTimeout(() => {
         _this.locationSet("116.237649_40.075885_北京市海淀区丰秀中路3号院1号楼")
      }, 2000);
     
    }
    }
  setDateTitle(datastr){
    let _this=this;
    let dateArr=datastr.split("-");
    if(dateArr.length==2){
      _this.dataStr=dateArr[0]+"年"+dateArr[1]+"月"
    }else{
      _this.dataStr=dateArr[0]+"年"+dateArr[1]+"月"+dateArr[1]+"日"
    }
    
  }
  //点击日期
  private selected(val, val2) {
    console.log(val2);
    let mounth = val[1];
    if(mounth < 9){
      mounth = '0' + mounth
    }
    let day = val[2];
    if(day <9){
      day = '0' + day
    }
    // this.value = val2.date;
    let  value = val[0] + '-' + mounth + '-' + day;
    //点击页面日历显示详情
    this.showInfo(value);
  }
  showInfo(value){
    let _this=this;
    console.log("显示详情==》"+value)
    let _date=new Date(value);
    console.log(_date)
    let weekindex=_date.getDay();
    this.selected_date=value;
    this.selected_date_other="星期"+this.$refs.calendar['weeks'][weekindex]
    console.log(_this.$formatTimeToZero(_this.curr_year_mounth,0)+"===>"+value)
    if(_this.$formatTimeToZero(_this.curr_year_mounth,0)==value){
      _this.currDay=true;
    }else{
      _this.currDay=false;
    }
    //交互
    // this.dynamic_list();
    this.listArr = [];
    try {
      let flag=false;
      this.allDuty.forEach(element => {
        if(value == element.day){
          _this.signFlag=true;
          flag=true;
          throw new Error("break")
        }
      });
      if(!flag){
        _this.signFlag=false;
      }
    } catch (error) {
      console.log(error)
    }
  }
  private monthRange(){
    let _this=this;
    //debugger
    _this.$refs.calendar['changeYear']()
  }
  //点击年份
  private selectYeared(y) {
    this.year_mounth = y + '-' + this.mounth;
    console.log( this.year_mounth,' this.year_mounth')
    this.mounthnamic_list();
  }
  //点击月份
  private selectMonthed(m, y) {
    if(m.toString().length==1){
      m="0"+m;
    }
   this.mounth=m;
    this.year_mounth = y + '-' + m;
    this.mounthnamic_list();
  }
  //上一月
  private preved(y, m, W) {
    if(m.toString().length==1){
      m="0"+m;
    }
    this.year_mounth = y + '-' + m;
    this.mounthnamic_list();
  }
  //下一月
  private nexted(y, m, W) {
    if(m.toString().length==1){
      m="0"+m;
    }
    this.year_mounth = y + '-' + m;
    this.mounthnamic_list();
  }
  private openOrg() {
    this.openSimple = true;
  }
  private handleNodeClick(data) {
    //组织机构
    this.openSimple = false;
    console.log(data);
    this.org_code = data.emorgid;
    //交互
  }
  private getNowYearAndMounth() {
    let _this=this;
    var myDate = new Date();
    var tYear = myDate.getFullYear();
    var tMonth = myDate.getMonth();
    var tdata = myDate.getUTCDate();
    var m = JSON.stringify(tMonth + 1);
    if (m.toString().length == 1) {
        m = "0" + m;
    }
    this.mounth=m;
    this.year_mounth =  tYear + '-' + m;
    this.curr_year_mounth = this.year_mounth+"-"+tdata;
    console.log("当前年月==>"+this.curr_year_mounth)
    //this.setDateTitle(this.year_mounth)
    //console.log("fff"+this.year_mounth)
    _this.mounthnamic_list();
  }
  private dataHandlesign(_dataArr){
    let _this=this;
    _this.allDuty =[];//清空当月值班信息
    //处理后台接口返回
    if(_dataArr&&_dataArr.length>0){
      _dataArr.forEach((item,index) => {
        if(item.signIn=="1"){
          _this.allDuty.push(item);
          let dateStr= this.$formatTimeToZero(item.day,1)
          let obj={date: dateStr, className: 'workday', content: ' '}
          _this.tileContent.push(obj)
        }
      });
      if(_this.isCreate){
        let myDate = new Date();
        let _days= myDate.getDate().toString();
        if (_days.length == 1) {
            _days = "0" + _days;
        }
        _this.showInfo(_this.year_mounth+"-"+_days)
        _this.isCreate=false;
      }
    }
  }
  //获取组织机构
  private getAllOrg() {
    let that = this;
    let parma = {
      orgCode: '',
      updateTime: '',
      userId: ''
    };
    
  }
  theAndroidH(_type,_phoneNum){
    let _this=this;
    if(_type==0){
       _this['$gsmdp'].makePhoneCall({
        phoneNumber:_phoneNum
      })
    }else if(_type==1){
      _this['$gsmdp'].sendMessage({
        data: {
          phoneNumber: _phoneNum,
          message: '',  //仅为示例，并非真实的短信内容
          sms: true
        }
      })
    }
  }
  GoBack(){
    this.$router.go(-1);
    // let _this=this;
    // try {
    //     _this['$gsmdp'].goBack(function(){
    //       console.log("Goback success")
    //     })
    // } catch (error) {
    //     console.log("close error")
    // }
  }
  private filterArray(data, id) {
    //组织机构的递归
    var fa = function(parentid) {
      var _array = [];
      for (var i = 0; i < data.length; i++) {
        var n = data[i];
        if (n.parentcode === parentid) {
          n.children = fa(n.emorgid);
          _array.push(n);
        }
      }
      return _array;
    };
    return fa(id);
  }
  private clearTree(obj) {
    this.openSimple = false;
    this.org_code = '';
    //交互
  }
  private mounthnamic_list() {
    let _this = this;
    let dateStr=_this.$formatTimeStr(_this.year_mounth);
    let param={
      signDate:dateStr,
      userId:_this.$store.state.userInfo.userid,
    }
    apiServer.findEverydayInfoByMonth(param,function(res){
      let dataArr=res.data.data;
      _this.dataList=dataArr;
      _this.dataHandlesign(dataArr)
    })
   this.setDateTitle(this.year_mounth)
  }
  //取月份数据遍历方法
  private sort(data) {}
  moveEv(e){
    this.swcount++;
    if(this.swcount>5){
      let pageY=e.changedTouches[0].pageY;
      //console.log("yvalue=>"+(pageY-this.pageYI)*4)
      let pageX=e.changedTouches[0].pageX;
    }
  }
  moveEnd(e){
    let _this=this;
    let pageX=e.changedTouches[0].pageX;
    console.log(pageX+"===>"+this.pageXI)
    /*左右滑*/
    if(pageX+100<this.pageXI){
        console.log("进入右滑")
        //this.NextMonth();
        _this.$refs.calendar['next']()
        this.swcount=0;
    }else if(pageX>this.pageXI+100){
        console.log("进入左滑")
         _this.$refs.calendar['prev']()
        //this.PreMonth();
        this.swcount=0;
    }
  }
  moveClick(e){
    this.swcount=0;
    console.log("moveClick==>"+e.changedTouches[0].pageX)
    this.pageXI=e.changedTouches[0].pageX;
    this.pageYI=e.changedTouches[0].pageY;
  }
  requestOrg(){
    let _this=this;
    let params={
      orgId:_this.$store.state.userInfo.orgcode,
    }
    //orgObj
    apiServer.findOrgObByCode(params,function(res){
      console.log("org info==>",res)
      _this.orgObj=res.data.data;
      console.log(_this.orgObj)
    })
  }
  private created() {
    let _this = this;
    _this.$enJsBack();
    // this.getAllOrg();
    //_this.requestOrg();
    _this.getNowYearAndMounth();
    // setTimeout(function() {
    //   _this.mounthnamic_list();
    // }, 100);
  }
  mounted(){
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      _this.GoBack()
    })

  }
}
</script>
<style scoped lang="scss">
.div_big {
  // height: 667px;
  [class*=van-hairline]::after{
    border: none;
  }
  .main_box {
    height: calc( 100% );;
    overflow: auto;
    margin: 0;
    background: white;
    .time_show_div{
      width: 100%;
      height: 8vh;
      background: #1541e2;
      color: white;
      position: relative;
      p{
        margin: 0;
        font-size: 20px;
        height: 100%;
        line-height: 8vh;
        text-align: center;
      }
      span{
        width: 8vw;
        height: 8vw;
        position: absolute;
        top: 2.66667vw;
        right: 15px;
        img{
          width: 30px;
          height: 30px;
        }
      }
    }
  }
  .duty_box {
    width: 100%;
    .element_div{
      width: 40vw;
      height: 40vw;
      background-color: #f59a23;
      border-radius: 50%;
      margin: 10vw 0px 0px 30vw;
    }
    .child_div{
      width: 34vw;
      height: 34vw;
      border-radius: 50%;
      background-color: white;
      position: relative;
      top: 3vw;
      left: 3vw;
      //字体
      line-height: 34vw;
      text-align: center;
      color: red;
      font-size: 18px;
      font-weight: bold;
      font-family: '微软雅黑';
      img{
        margin-top: calc( 48px - 2vw ) ;
      }
    }
    .every_sign_text{
      width: 100%;
      font-size: 28px;
      font-weight: bold;
      padding-top: 10px;
      text-align: center; 
    }
  }
}
</style>
