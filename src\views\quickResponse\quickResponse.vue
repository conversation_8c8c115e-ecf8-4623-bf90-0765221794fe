<template>
  <div class="div_big">
    <van-nav-bar title="快速响应"
        left-arrow
        @click-left="GoBack">
      <!-- <template #right>
        <van-icon name="bars" size="25" @click="handleChangeEvent" />
      </template> -->
    </van-nav-bar>
    <div class="content_list">
      <div class="content_item">
        <div class="quickResponse_top">
          <h2>{{$route.query.title || '东西湖区化工企业天然气爆炸事故'}}</h2>
          <div class="top_tip">
            <span>重大</span>
            <span>危化品事故</span>
            <span class="sign_in">
              <i></i>9:02已签到</span>
          </div>
          <p>武汉市东西湖区化工企业天然气运输管道发生泄漏,并与周边灌区发生反应,引发爆燃爆炸</p>
          <div class="quickResponse_time">
            <i></i>2022-03-02 14:32</div>
          <div class="quickResponse_adress">
            <i></i>武汉市东西湖区二七小路42号</div>
          <div class="icon_list">
            <ul>
              <li v-for="item in menuList"
                  :key="item.name" @click="handleToMenu(item)">
                  <i class="liangma" v-if="item.name == '通行码'"></i>
                <van-image width="40px"
                           height="40px"
                           fit="contain"
                           :src="item.icon" />
                <span class="name">{{item.name}}</span>
              </li>
            </ul>

          </div>
        </div>
        <div class="quickResponse_title">
          <h2>
            <i></i>我的任务
            <div class="title_tab">
              已完成
              <span class="blue">2</span>进行中
              <span class="org">2</span>
            </div>
          </h2>
        </div>
        <div class="quickResponse_list">
          <div class="list-title">组织参与抢险救援行动
            <span>进行中</span>
          </div>
          <p :class="currentActive ? 'active':''" @click="currentActive = !currentActive">严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记
            <span style="color:#3284ff">收起</span>
          </p>
          <div class="issue_time">
            <span>下 发 时 间:</span> 2022-03-12 19:32</div>
          <div class="finish_time">
            <span>拟完成时间:</span> 2022-03-13 19:32</div>
          <div class="quickResponse_btn">
            <div @click="taskFeedback">任务反馈</div>
            <div @click="applyCoordinate">申请协调</div>
          </div>
        </div>
        <div class="quickResponse_list">
          <div class="list-title">组织参与抢险救援行动
            <span>进行中</span>
          </div>
          <p>严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记
          </p>
          <div class="issue_time">
            <span>下 发 时 间:</span> 2022-03-12 19:32</div>
          <div class="finish_time">
            <span>拟完成时间:</span> 2022-03-13 19:32</div>
          <div class="quickResponse_btn">
            <div @click="taskFeedback">任务反馈</div>
            <div @click="applyCoordinate">申请协调</div>
          </div>
        </div>
        <div class="quickResponse_list">
          <div class="list-title-finished">组织参与抢险救援行动
            <span class="finished">已完成</span>
          </div>
          <p>严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记严格落实外围管控区的出入等级管理,并将核心救援区救出的人员情况以及伤亡情况及时记
          </p>
          <div class="issue_time">
            <span>下 发 时 间:</span> 2022-03-12 19:32</div>
          <div class="finish_time">
            <span>拟完成时间:</span> 2022-03-13 19:32</div>
          <div class="quickResponse_btn_finished">
            <div>查看反馈</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
@Component({
  components: {}
})
export default class quickResponse extends Vue {
  private menuList: any = [
    { name: '通行码', icon: require(`@/assets/images/quickResponse/quickResponse08.png`), path: '/passCode' },
    { name: '救援登记', icon: require(`@/assets/images/quickResponse/quickResponse05.png`), path: '/RescueRegistration' },
    { name: '现场公告', icon: require(`@/assets/images/quickResponse/quickResponse06.png`), path: '/Affiche' },
    { name: '资源申请', icon: require(`@/assets/images/quickResponse/quickResponse07.png`),path: '/resourceApplication' },
    { name: '灾情上报', icon: require(`@/assets/images/quickResponse/quickResponse10.png`),path: '/disasterReporting' },
    // { name: '协同会商', icon: require(`@/assets/images/quickResponse/quickResponse11.png`) },
    { name: '进入群聊', icon: require(`@/assets/images/quickResponse/quickResponse12.png`) },
    { name: '抵达签到', icon: require(`@/assets/images/quickResponse/quickResponse09.png`), path: '/signIn' }
  ];
  private currentActive:any = false;
  handleChangeEvent() {
    this.$router.push('/changeEvent');
  }
  handleToMenu(item) {
    this.$router.push(item.path)
  }
  GoBack(){
    this.$router.push('/list');
  }
  taskFeedback(){
    this.$router.push({path: '/taskFeedback' });
  };
  applyCoordinate() {
    this.$router.push('/applyCoordinate')
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div {
    width: 100%;
  }
}
.content_list {
  height: 100%;
  background: linear-gradient(to bottom, #1967f2 30%, transparent 60%, #f5f6f6 100%);
  // background: url('../../assets/images/quickResponse/quickResponse01.png') center no-repeat;
  overflow: auto;
  background-size: contain;
  padding-bottom: 60px;
  .content_item {
    padding: 0 10px;
    height: 100%;
    .quickResponse_top {
      padding: 3px 10px 16px 10px;
      background: #fff;
      border-radius: 5px;
      h2 {
        font-size: 18px;
        color: #1c1d1d;
        font-weight: bolder;
      }
      .top_tip {
        span {
          color: #ca0118;
          font-size: 13px;
          display: inline-block;
          padding: 5px 7px;
          background: #fae5e7;
          margin-right: 7px;
          border-radius: 3px;
        }
        .sign_in {
          color: #333;
          background: #e9f2ff;
          i {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('../../assets/images/quickResponse/quickResponse02.png') center no-repeat;
            background-size: contain;
            margin-right: 5px;
            position: relative;
            top: 2px;
          }
        }
      }
      p {
        font-size: 16px;
        color: #1c1d1d;
      }
      .quickResponse_time {
        font-size: 14px;
        color: #1c1d1d;
        margin-bottom: 10px;
        i {
          display: inline-block;
          width: 14px;
          height: 14px;
          background: url('../../assets/images/quickResponse/quickResponse03.png') center no-repeat;
          background-size: contain;
          margin-right: 7px;
          position: relative;
          top: 2px;
        }
      }
      .quickResponse_adress {
        font-size: 14px;
        color: #1c1d1d;
        padding-bottom: 20px;
        i {
          display: inline-block;
          width: 13px;
          height: 15px;
          background: url('../../assets/images/quickResponse/quickResponse04.png') center no-repeat;
          background-size: contain;
          margin-right: 7px;
          position: relative;
          top: 2px;
        }
      }
      .icon_list {
        border-top: 1px solid #c9c9c9;
        padding-top: 12px;
        ul {
          display: flex;
          justify-content: flex-start;
          flex-wrap: wrap;
          align-items: center;
          li {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 25%;
            text-align: center;
            margin-top: 5px;
            .liangma{
              z-index:999;
              display: inline-block;
              width: 32px;
              height: 14px;
              background: url('../../assets/images/quickResponse/quickResponse14.png') center no-repeat;
              background-size: contain;
              margin-right: 7px;
              position: relative;
              top: 8px;
              right:-23px;
            }
          }
        }
      }
    }
    .quickResponse_title {
      h2 {
        font-size: 18px;
        color: #333;
        i {
          display: inline-block;
          width: 20px;
          height: 20px;
          background: url('../../assets/images/quickResponse/quickResponse13.png') center no-repeat;
          background-size: contain;
          margin-right: 5px;
          position: relative;
          top: 4px;
        }
        .title_tab {
          float: right;
          font-size: 14px;
          margin-top: 3px;
          .org {
            color: #ed6a0c;
            font-size: 15px;
          }
          .blue {
            color: #3284ff;
            font-size: 15px;
            border-right: 1px solid #b3b3b3;
            padding-right: 10px;
            margin-right: 10px;
          }
        }
      }
    }
    .quickResponse_list {
      border-radius: 5px;
      background: #fff;
      margin-bottom: 15px;
      .list-title-finished {
        line-height: 33px;
        padding: 5px 7px;
        font-size: 16px;
        color: #333;
        border-radius: 5px;
        background: linear-gradient(to right, #ecf2ff 60%, transparent 80%, #f5f6f6 100%);
        span {
          float: right;
          color: #ed6a0c;
          font-size: 14px;
        }
        .finished {
          color: #3284ff;
        }
      }
      .list-title {
        line-height: 33px;
        padding: 5px 7px;
        font-size: 16px;
        color: #333;
        border-radius: 5px;
        background: linear-gradient(to right, #fff8eb 60%, transparent 80%, #f5f6f6 100%);
        span {
          float: right;
          color: #ed6a0c;
          font-size: 14px;
        }
        .finished {
          color: #3284ff;
        }
      }
      p {
        font-size: 14px;
        padding: 0 10px;
        margin: 5px 0;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        transition: 0.3s;
        -webkit-line-clamp: 2;
      }
      p.active{
        font-size: 14px;
        padding: 0 10px;
        margin: 5px 0;
        line-height: 22px;
        overflow: visible;
        display:block;
      }
      .issue_time {
        color: #1c1d1d;
        font-size: 14px;
        padding: 0 10px 5px 10px;
        span {
          color: #646566;
        }
      }
      .finish_time {
        color: #1c1d1d;
        font-size: 14px;
        padding: 0 10px 10px 10px;
        span {
          color: #646566;
        }
      }
      .quickResponse_btn {
        border-top: 1px solid #c9c9c9;
        height: 40px;
        line-height: 40px;
        div {
          width: 50%;
          float: left;
          font-size: 16px;
          color: #3284ff;
          text-align: center;
          border-right: 1px solid #c9c9c9;
        }
        div:last-child {
          border-right: none;
        }
      }
      .quickResponse_btn_finished {
        border-top: 1px solid #c9c9c9;
        height: 40px;
        line-height: 40px;
        div {
          width: 100%;
          float: left;
          font-size: 16px;
          color: #3284ff;
          text-align: center;
        }
      }
    }
  }
}
.showMenu {
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
  color: white;
}
[class*='van-hairline']::after {
  border: none;
}
</style>