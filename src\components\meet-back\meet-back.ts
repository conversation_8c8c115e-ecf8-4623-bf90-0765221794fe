import { Component, Vue } from 'vue-property-decorator';

@Component({
  name: 'meet-back',
  components: {}
})
export default class MeetBack extends Vue {
  callBack() {
    this.$nextTick(() => {
      // window.history.pushState(null, '', document.URL);
      this.$emit('documentBack');
    });
    return;
  }
  mounted() {
    if (window.history && window.history.pushState) {
      window.history.pushState(null, '', document.URL);
      window.addEventListener('popstate', this.callBack);
    }
  }
  beforeDestroy() {
    window.removeEventListener('popstate', this.callBack, false);
  }
}
