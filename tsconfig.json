{
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": false,
    "jsx": "preserve",
    "importHelpers": true,
    "noImplicitAny": false,
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "strictFunctionTypes": false,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "sourceMap": true,
    "baseUrl": ".",
     "typeRoots": [
      "node_modules/@types"
    ],
    "paths": {
      "@/*": [
        "src/*"
      ],
      // "element-ui/lib/button": ["node_modules/element-ui/types/button"]
    },
    "lib": [
      "esnext",
      "dom",
      "dom.iterable",
      "scripthost",
      "es2015"
    ]
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "tests/**/*.ts",
    "tests/**/*.tsx", "src/assets/server/enterprise_index.js"
  ],
  "exclude": [
    "node_modules"
  ]
}
