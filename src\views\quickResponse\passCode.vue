<template>
  <div class="passCode">
    <Header title="通行二维码"></Header>
    <div class="passCode-top">
      <div class="passCode-top-title">
        武汉市东西湖区化工企业天然气爆炸事故
        <br/><b>核心救援区</b>通行码
      </div>
      <div class="passCode-top-qrcode">
        <vue-qr
         :text="qrInfo">
        </vue-qr>
        <!-- 可以加logo图片， logoSrc设置logo图片， logoScale设置logo尺寸-->
      </div>
    </div>
    <div class="passCode-bottom">
      <div class="passCode-bottom-title">
        <van-image
          width="18px"
          height="18px"
          fit="contain"
          :src="require(`@/assets/images/quickResponse/ico-passrecord.png`)"
        />
        <span>通行信息</span>
      </div>
      <div class="passCode-bottom-cont">
        <div class="item">
          <span class="label">负责人</span>
          <span class="cont">李爱国</span>
        </div>
        <div class="item">
          <span class="label">联系方式</span>
          <span class="cont">13148765456</span>
        </div>
        <div class="item">
          <span class="label">单位名称</span>
          <span class="cont">湖北省危化品事故救援队</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import VueQr from 'vue-qr';
@Component({
  components: {
    Header,
    VueQr
  }
})
export default class PassCode extends Vue {
  private qrInfo:any = '武汉市东西湖区化工企业天然气爆炸事故核心救援区通行码';
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.passCode {
  background: linear-gradient(to bottom, #1967f2 30%, transparent 60%, #f5f6f6 100%);
  &-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 346px;
    margin: 10px;
    padding: 0 28px;
    padding-top: 37px;
    background: #fff;
    border-radius: 7px;
    &-title {
      text-align: center;
      font-size: 15px;
      b {
        padding: 0 7px;
        color: #ca0118;
      }
    }
    &-qrcode {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 194px;
      height: 194px;
      // line-height: 194px;
      margin-top: 28px;
      background: url("@{url}/quickResponse/code-border.png") center no-repeat;
      background-size: cover;
      text-align: center;
      /deep/img {
        display: inline-block;
        width: 100%;
        height: 100%;
        padding: 2px;
        // margin-top: 2px;
      }
    }
  }
  &-bottom {
    height: 162px;
    margin: 10px;
    padding: 0 11px;
    border-radius: 4px;
    background: url("@{url}/quickResponse/bg-passrecord.png") center no-repeat;
    background-size: cover;
    &-title {
      display: flex;
      align-items: center;
      height: 45px;
      color: #1c1d1d;
      font-size: 18px;
      /deep/.van-image {
        margin-right: 8px;
      }
    }
    &-cont {
      padding: 12px 10px;
      background: #fff9fa;
      border-radius: 4px;
      .item {
        margin-bottom: 12px;
        font-size: 13px;
        span {
          display: inline-block;
        }
        .label {
          width: 56px;
          margin-right: 20px;
          color: #666;
          text-align-last: justify;
        }
        .cont {
          color: #1c1d1d;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>