<template>
  <div>
    <div class="item_content_list" v-for="(item, index) in eventList" :key="index">
      <ul>
        <li>
          <div class="list_content">
            <div @click="comeInDetail(item)">[{{ item.title }}]{{ item.warningInfo }}</div>
          </div>
          <div class="list_top" v-if="item.status == 1">
            <div class="btnPart">
              <button class="total text_ellipsis" @click="handleAddTask(item, 'addTask')">追加</button>
              <button class="action text_ellipsis" @click="handleRelease(item)">解除预警</button>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <div v-if="eventList && eventList.length == 0 && requestStatus == 'end'" style="width: 100%; text-align: center; padding-top: 10%">
      <van-empty description="暂无数据" />
    </div>
    <van-popup v-model="detailHandle.showInfo" v-if="detailHandle.showInfo" position="right" :style="{ height: '100%', width: '100%' }">
      <Infodetail :requestId="requestId" :requestObj="queryObj" @close="closeInfo" />
    </van-popup>
    <!-- 追加预警信息 -->
    <popAppend v-if="showAppendPop" :showAppendPop="showAppendPop" :requestType="type" @closeFeedback="closeAppend" @handleSureFeedback="handleSure" />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { Notify, Dialog } from 'vant';
import apiServer from '../../api/request-service';
import Infodetail from './emergencyInfo.vue';
import popAppend from './components/popAppend.vue';

@Component({
  components: {
    Infodetail,
    popAppend
  }
})
export default class integratedItem extends Vue {
  @Prop(String) private requestFlag: string;
  @Prop(Object) private requestObj: any;
  eventList: any = [];
  keywords: any = '';
  requestStatus: any = '';
  requestId: any = '';
  isInit: any = true;
  detailHandle: any = { showInfo: false };
  queryObj: any = {};
  type: any = 'add';
  closeInfo() {
    let _this = this;
    _this.detailHandle.showInfo = false;
    _this['$gsmdp'].initGoback(function () {
      // console.log("调用返回")
      try {
        _this['$gsmdp'].goBack(function () {});
      } catch (error) {
        // console.log("close error")
      }
    });
  }
  searchSet(_keyword) {
    this.keywords = _keyword;
  }
  comeInDetail(_item) {
    let _this = this;
    _this.requestId = _item.id;
    _this.queryObj = _item;
    _this.type = null;
    // console.log("come in detail "+_this.requestId)
    _this.detailHandle.showInfo = true; //显示详情
  }
  showAppendPop: boolean = false;

  // 追加
  handleAddTask(_item, type) {
    let _this = this;
    _this.requestId = _item.id;
    // _this.showAppendPop = true;
    _this.queryObj = _item;
    _this.detailHandle.showInfo = true; //显示详情
    _this.type = type;
    // this.$router.push({
    //   path: '/emergency/add',
    //   query: {
    //     id: _this.requestId
    //   }
    // });
  }
  closeAppend(){
    this.showAppendPop = false;
  }
  handleSure(){
    this.showAppendPop = false;
  }
  handleRelease(_item) {
    Dialog.confirm({
      message: '确定解除预警吗？'
    }).then(() => {
      apiServer.liftWarning({ id: _item.id }, (res) => {
        if (res.data.status === 200) {
          // this.groupMenberList.splice(index, 1);
          this.eventList = [];
          this.queryRequest(null);
        } else {
          Notify(res.data.msg);
        }
      });
    });
  }
  queryRequest(pageObj) {
    let _this = this;
    let _objPage = { pageIndex: 1, pageSize: 10 };
    if (pageObj) {
      _objPage.pageIndex = pageObj.pageIndex;
    }
    let param = {
      title: _this.keywords,
      nowPage: _objPage.pageIndex,
      pageSize: _objPage.pageSize,
      way: _this.requestObj.way,
      status: _this.requestObj.status,
      type: ''
    };
    apiServer.findWarningPage(
      param,
      function (res) {
        _this.isInit = false;
        let dataHandleList = res.data.data.list; //dataHandle.caseList(res);
        _this.requestStatus = 'end';
        _this.$emit('handleObj', { uploadMore: false });
        if (dataHandleList.length > 0) {
          if (_objPage.pageIndex > 1) {
            _this.eventList = [..._this.eventList, ...dataHandleList];
          } else {
            _this.eventList = dataHandleList;
          }
        } else {
          if (_objPage.pageIndex <= 1) {
            _this.eventList = [];
          }
        }
      },

    );
  }

  created() {
    let _this = this;
    //_this['$apiServer'].setToken();
    _this.queryRequest(null);
    // setTimeout(function() {

    // }, 500);
  }
}
</script>
<style scoped lang="scss">
.item_content_list {
  width: 100%;
  height: 100%;
  background-color: #fff;
  li {
    width: 100%;
    background: white;
    margin-top: 15px;
    padding-top: 10px;
    .list_top {
      width: 100%;
      height: 50%;
      position: relative;
      text-align: right;
      .btnPart {
        padding: 0px 4vw 4vw 3%;
        .action {
          background: transparent;
          color: #e45050;
          border: 1px solid #e45050;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          margin-left: 5px;
          max-width: 30%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .total {
          background: transparent;
          color: #00a0e9;
          border: 1px solid #00a0e9;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          max-width: 30%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .time_show {
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 0px;
          float: right;
        }
      }
    }
    .list_content {
      width: 100%;
      // line-height: 35px;
      padding-left: 3%;
      font-size: 15px;
      div {
        // float: left;
        line-height: 35px;
      }
      div:nth-of-type(1) {
        width: 90%;
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      div:nth-of-type(2) {
        width: 10%;
        height: 100%;
        img {
          width: 15px;
          vertical-align: middle;
          margin-top: -5px;
        }
      }
    }
  }
}
</style>
