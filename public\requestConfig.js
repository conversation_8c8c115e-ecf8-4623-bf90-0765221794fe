window.g = {
  // IP:'http://*************:18202/gemp/gemp-app/',//配置服务器地址
  // BASEIP:"http://*************:18202/gemp-app/",
  //IP:'http://**************:8000/gapi',
  //IP:'http://***************:39090/gapi',
  //IP:'http://**************:19290/gapi/',//烟台
  //IP:'http://**************:19309/gapi/',//烟台
  //  IP:'http://**************:8990/gapi/',//烟台
  // IP:'http://***********:8990/gapi/',//烟台
  // IP:'http://************:8990/gapi/',// 湖北省厅
  // IP: 'http://**************:19355',// 湖北省厅外网
  // IP: '/gapi/gemp-app',// 湖北省厅
  //IP:'',//天翼云
  IP: '/gapi',
  APPIP: '/gemp-app',
  BASEIP: "", //图片
}
function RequestConfig(url) {
  // console.log("url  is " + url)
  return new Promise((resolve, reject) => {
    if (config_data) {
      resolve(config_data)
      // let request=new XMLHttpRequest();
      // request.open("get",url)
      // request.send(null)
      // request.onload = () =>{
      //   if(request.status == 200){
      //     let jsonObj=JSON.parse(request.responseText)
      //     resolve(jsonObj)
      //   }else{
      //     reject(request)
      //   }
      // }
    } else {
      reject(config_data)
    }
  })
};
async function GitConfig() {
  //debugger
  let myconfig = await RequestConfig("config.json");
  // console.log("config", myconfig)
  //window.myconfig = myconfig;
  return myconfig;
}
window.GitConfig = GitConfig();

