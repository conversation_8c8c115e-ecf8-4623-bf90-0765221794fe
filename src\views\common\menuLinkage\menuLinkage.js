const M_TYPY={
    CASCADER:"cascader",//级联模式
    SINGLE:"single",//单选模式
    MULTIPLE:"multiple"//多选模式
}
export const menuLinkage = {
    data() {
        return {
            openSimple: false,
            tabs: [{ "code": "0", "name": "请选择", "parentId": "0", "active": false }],
            defaultObj: { "code": "0", "name": "请选择", "parentId": "0", "active": true },
            listArr: [],
            selectedList: [],
            checked: true,
            dataTree: [],
            tempList: [],
            initType: 0,//0为全量初始化 1为层级初始化
            allIndexList: [],//总体缓存数据list 根据下标存储
            isMultiple:false//是否为多选
        };
    },
    props: ["options","title", "type"],
    methods: {
        changeCheckBox(_item) {
            let _this = this;
            if (!_this.isMultiple) {
                _this.listArr.forEach((item, index) => {
                    if (_item.id == item.id) {
                        item.checked = true;
                    } else {
                        item.checked = false;
                    }
                })
            }
            console.log(_item)
        },
        handleBtn(_type) {
            let _this = this;
            if (_type == 0) {
                console.log("取消")
                _this.$emit("setSelectObj", [])
            } else if (_type == 1) {
                console.log("确定")
                console.log(_this.dataTree)
                console.log(_this.allIndexList)
                console.log(_this.listArr)
                _this.setTreeData();
                console.log("selectedList", _this.selectedList)
                let resultList = [];
                if (_this.isMultiple) {
                    let parentArr = [];
                    _this.selectedList.forEach((item, index) => {
                        //找出所有选中的最父级
                        if (item.checked) {
                            let flag = false;
                            for (const iterator of _this.selectedList) {
                                if (iterator.checked) {
                                    if (item.parentId && item.parentId == iterator.id) {
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                            if (!flag) {
                                parentArr.push(item);
                            }
                        }
                    })
                    //迭代所有最父级的所有子级
                    console.log("parentArr==>", parentArr)
                    if (parentArr.length > 0) {
                        parentArr.forEach((item, index) => {
                            if (item.children) {
                                resultList.push({ "code": item.id, "name": item.label })
                                let resultArr = [];
                                //console.log("filterArray==>",_this.findResultArr(item.children,resultArr))
                                _this.findResultArr(item.children, resultArr)
                                resultList = resultList.concat(resultArr)
                                //console.log(resultArr)
                            } else {
                                resultList.push({ "code": item.id, "name": item.label })
                            }
                        })
                    }
                } else {
                    _this.selectedList.forEach((item, index) => {
                        //找出所有选中的最父级
                        if (item.checked) {
                            resultList.push({ "code": item.id, "name": item.label })
                        }
                    })
                    let newTab = _this.tabs[_this.tabs.length - 1]
                    let flag = false;
                    for (const iterator of resultList) {
                        if (iterator.code == newTab.code) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        resultList.push(newTab)
                    }
                }
                console.log("resultList==>", resultList)
                _this.$emit("setSelectObj", resultList)
            }
        },
        initData(treeData, initType) {
            let _this = this;
            //console.log(treeData)
            _this.dataTree = treeData;
            _this.allIndexList = [];
            _this.initType = initType;
            if (treeData) {
                _this.setListArr(_this.dataTree)
            }
        },
        clickTab(_index) {
            //console.log(_index)
            //console.log(this.tabs.length)
            let templist = this.allIndexList[_index];
            //找到当前点击tab对象
            let tabObj = this.tabs[_index]
            //循环上一页面数组找到tab对象对应的数据数据赋值checked为tab对象值
            templist.forEach((item, index) => {
                //console.log(tabObj,item)
                if (tabObj.code == item.id) {
                    item.checked = tabObj.checked
                }
            })
            this.setTreeData(tabObj)
            this.allIndexList.splice(_index, this.tabs.length - _index);
            this.tabs.splice(_index, this.tabs.length - _index);
            this.tabs.push(this.defaultObj);
            this.setListArr(templist)

            //console.log(this.allIndexList[_index])
            //arr.splice(1,1)
        },
        setListArr(_list, isChecked) {
            console.log("_list==>", _list)
            let _this = this;
            _this.listArr = [];
            _this.tempList = [];
            _list.forEach((item, index) => {
                //console.log(typeof(item.checked))
                if (_this.isMultiple) {
                    if (typeof (item.checked) == "undefined") {
                        item.checked = !isChecked ? false : true;
                    } else if (isChecked) {
                        item.checked = true;
                    }
                }
                for (const iterator of _this.selectedList) {
                    if (iterator.id == item.id) {
                        item.checked = iterator.checked;
                        break;
                    }
                }
                let obj = { "id": item.id, "label": item.label, "parentId": item.parentId, "checked": item.checked, "children": item.children, "leaf": item.leaf }
                _this.listArr.push(obj);
            })
            _this.allIndexList.push(_list);
            _this.$refs.divContent.scrollTop = 0;//保证每次滚动条都在最顶部
            _this.tempList = _list;
        },
        handleData(_item, _type) {
            let _this = this;
            //console.log(_item)
            if(_type==0){
                if (_this.type==M_TYPY.SINGLE) {
                    let obj = { "code": _item.id, "name": _item.label, "parentId": _item.parentId, "active": false, "checked": _item.checked }
                    _this.tabs.pop();//删除最后一列数据
                    console.log("tabs", obj)
                    _this.tabs.push(obj);//添加点击的OBJ
                    _this.handleBtn(1)
                } else {
                    _this.queryHandle(_item);
                }
            }else{
                _this.queryHandle(_item);
            }
        },
        /**
         * 给初始树结构赋值
         */
        setTreeData(_item) {
            let _this = this;
            console.log("===================>set init tree data!")
            //定义当前页面未选中集合
            let unselectArr = new Array();
            //当前页面显示所有选中集合
            let itemSelectArr = new Array();
            _this.listArr.forEach((item, index) => {
                if (item.checked) {
                    itemSelectArr.push(item)
                } else {
                    unselectArr.push(index)
                }
            })
            if (_this.isMultiple) {
                //对比页面显示是否全部选中 
                if (itemSelectArr.length == _this.listArr.length) {
                    //判断是否有父级
                    if (_this.listArr[0].parentId) {
                        //父级是否包含在已选择列表中
                        if (_this.selectedList.length > 0) {
                            let parentindex = 0;
                            let flag = false;
                            _this.selectedList.forEach((item, index) => {
                                //如果当前页面全部选中且有上一级则把上一级选中置为true
                                if (_this.listArr[0].parentId == item.id) {
                                    item.checked=true;
                                    flag = true;
                                    parentindex = index;
                                }
                            })
                            if (!flag) {
                                (_this.allIndexList[_this.allIndexList.length - 2]?_this.allIndexList[_this.allIndexList.length - 2]:[]).forEach((item, index) => {
                                    if (_this.listArr[0].parentId == item.id) {
                                        _this.setSelectedList(item, 0)
                                    }
                                })
                            }
                        } else {
                            //console.log(_this.allIndexList[_this.allIndexList.length-2])
                            if(_this.allIndexList[_this.allIndexList.length - 2]){
                                _this.allIndexList[_this.allIndexList.length - 2].forEach((item, index) => {
                                    if (_this.listArr[0].parentId == item.id) {
                                        item.checked = true;
                                        _this.setSelectedList(item, 0)
                                    }
                                })
                            }
                        }
                    }
                } else {
                    if (_this.listArr[0].parentId) {
                        let parentindex = 0;
                        let flag = false;
                        _this.selectedList.forEach((item, index) => {
                            if (item.id == _this.listArr[0].parentId) {
                                // flag=true;
                                // parentindex=index;
                                item.checked = false;
                                if (unselectArr.length > 0&&item.children&&item.children.length>0) {
                                    //获取取消选中的数据把取消选中的子级选中状态都改为false
                                    unselectArr.forEach((itemch, indexch) => {
                                        item.children[itemch].checked = false;
                                    })
                                }
                            }
                        })
                    }
                }
                console.log("_this.selectedList==>", _this.selectedList)
                //全选则其父级一起选中 不全选其取消父级选中

            } else {
                //_this.selectedList=;
                _this.selectedList = [];
            }

            if(_this.selectedList.length < _this.listArr.length){
                _this.listArr.forEach((item, index) => {
                    if (item.checked) {
                        _this.allIndexList[_this.allIndexList.length - 1].forEach((itemch, indexch) => {
                            if (item.id == itemch.id) {
                                itemch.checked = true;
                                _this.setSelectedList(itemch, 1)
                            }
                        })
                    }
                })
            }
            console.log("selectedList==>", _this.selectedList)
            //selectedList

        },
        setSelectedList(_item, _type) {
            let _this = this;
            if (_this.selectedList.length > 0) {
                let flag = false;
                for (const iterator of _this.selectedList) {
                    if (_item.id == iterator.id) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    if (_type && _type == 0) {
                        _this.selectedList.unshift(_item)
                    } else {
                        _this.selectedList.push(_item);
                    }
                }
            } else {
                if (_type && _type == 0) {
                    _this.selectedList.unshift(_item)
                } else {
                    _this.selectedList.push(_item);
                }
            }
        },
        queryHandle(_item) {
            let _this = this;
            _this.setTreeData(_item)
            _this.tempList.forEach((item, index) => {
                if (item.id == _item.id) {
                    //console.log(item.children)
                    let childrens = item.children;
                    if (childrens && childrens.length > 0) {
                        childrens.forEach((itemch, indexch) => {
                            itemch.parentId = _item.id;
                            //itemch.checked=false;
                        })
                    }
                    console.log(_item)
                    if (_this.initType == 1) {
                        //  queryCityData({"parentCode":item.id},function(res){
                        //       //console.log("res= queryCityData= parentCode==>"+item.id,res)
                        //       if(typeof(res.data)!="undefined"){
                        //           let obj={"code":_item.id,"name":_item.label,"parentId":_item.parentId,"active":false}
                        //           _this.tabs.pop();//删除最后一列数据
                        //           _this.tabs.push(obj);//添加点击的OBJ
                        //           childrens=res.data;
                        //           _this.handleList(childrens)
                        //       }
                        //   })
                    } else {
                        let obj = { "code": _item.id, "name": _item.label, "parentId": _item.parentId, "active": false, "checked": _item.checked }
                        _this.tabs.pop();//删除最后一列数据
                        console.log("tabs", obj)
                        _this.tabs.push(obj);//添加点击的OBJ
                        //判断如果没有下一级机构去请求下一级机构
                        if (childrens && childrens.length > 0) {
                            _this.handleList(childrens, _item.checked)
                        } else {
                            _this.$emit("queryChild", { id: _item.id, checked: _item.checked })
                            //console.log("objres---->",objres)
                        }
                    }


                }
            });
        },
        handleList(childrens, isChecked) {
            let _this = this;
            if (childrens && childrens.length > 0) {
                _this.setListArr(childrens, isChecked);
                _this.tabs.push(_this.defaultObj);//添加默认请选择tabs
            } else {
                console.log("no more anthing else data")
                _this.setTreeData()
                let resultList = [];
                _this.selectedList.forEach((item, index) => {
                    //找出所有选中的最父级
                    if (item.checked) {
                        resultList.push({ "code": item.id, "name": item.label })
                    }
                })
                if(_this.type==M_TYPY.CASCADER){
                    resultList=_this.tabs
                }else{
                    let newTab = _this.tabs[_this.tabs.length - 1]
                    let flag = false;
                    for (const iterator of resultList) {
                        if (iterator.code == newTab.code) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        resultList.push(newTab)
                    }
                }
                ///console.log(_this.tabs)
                _this.$emit("setSelectObj", resultList)
                //_this.$emit("setSelectObj",_this.tabs)
            }
        },
        setDefault(_array, setType) {
            let _this = this;
            if (_array && _array.length > 0) {
                _array.forEach((item, index) => {
                    if (index > 0) {
                        setTimeout(() => {
                            _this.tempList.forEach((itemch, indexch) => {
                                if (setType == 1) {
                                    if (item == itemch.id) {
                                        _this.handleData(itemch);
                                    }
                                } else {
                                    if (item == itemch.label) {
                                        _this.handleData(itemch);
                                    }
                                }
                            })
                        }, parseInt(index * 2 + "00"))
                    } else {
                        _this.tempList.forEach((itemch, indexch) => {
                            if (setType == 1) {
                                if (item == itemch.id) {
                                    _this.handleData(itemch);
                                }
                            } else {
                                if (item == itemch.label) {
                                    _this.handleData(itemch);
                                }
                            }
                        })
                    }

                })
            }
        },
        findResultArr(datas, _resultArr) { //遍历树  获取id数组
            for (var i in datas) {
                _resultArr.push({ "code": datas[i].id, "name": datas[i].label })
                if (datas[i].children) {
                    this.findResultArr(datas[i].children, _resultArr);
                }
            }
        },
        filterArray(data, id) {//组织机构的递归
            var fa = function (parentid) {
                var _array = [];
                for (var i = 0; i < data.length; i++) {
                    var n = data[i];
                    if (n.parentcode === parentid) {
                        n.children = fa(n.emorgid);
                        _array.push(n);
                    }
                }
                return _array;
            }
            return fa(id);
        },
    }, mounted: function () {
        let _this = this;
        console.log("type===="+_this.type)
        _this.type=_this.type?_this.type.toLowerCase():M_TYPY.SINGLE;
        _this.isMultiple=_this.type==M_TYPY.MULTIPLE?true:false;//判断如果为多选isMultiple为true
        if (_this.options && _this.options.length > 0) {
            _this.initData(_this.options)
        }
    }


}