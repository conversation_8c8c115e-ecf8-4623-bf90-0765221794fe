<template>
  <div class="div_big">
    <van-nav-bar title="任务详情" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
    <!-- <van-icon name="ellipsis" size="25" /> -->
  </template>
   </van-nav-bar>
   <!-- invoke common List -->
  <div class="info_big">
    <p>
      <span>
        <img src="../../assets/images/icons/3.png" alt="" srcset=""> 
      </span>  
      <span>{{detailInfo.taskLevelName}}</span>
    </p>
    <div class="info_head">
        <p>{{detailInfo.taskname}}</p>
        <!-- <li>
          <span>所属行政区划：</span>
          <span>{{detailInfo.districtName}}</span>
        </li>
        <li>
          <span>队伍位置：</span>
          <span>{{detailInfo.address}}</span>
        </li> -->
    </div>
    <div class="info_content">
      <li>
        <span>发布时间：</span>
        <span>{{detailInfo.sendTimeStr}}</span>
      </li>
      <p>
       {{detailInfo.notes==null||detailInfo.notes==''?"无描述":detailInfo.notes}}
      </p>
      <!-- <p>
        <img v-for="(item,index) in imgList" :src="item.urlOuterNet" alt="" :key="index">
      </p>

      <p>{{detailInfo.sendOrgName}}</p> -->
    </div>
    <p>附件：</p>
    <file-preview direction="left" :fileList="detailInfo.attachmentList"></file-preview>
    <!-- <div class="attachmentList">
      <li v-for="(item,index) in fileList" :key="index" @click="openFile(item)">
         <span><img src="../../assets/images/ppt.png" alt="" srcset=""></span>
         <span>{{item.name}}</span>
      </li>
    </div> -->
  </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import FilePreview from '@/components/filePreview.vue';
@Component({
  components: {
    FilePreview
  }
})
export default class rescueTeamDetail extends Vue {
  @Prop(String) private requestId :string;
  position:any = 'left';
  queryMap: any ={};
  detailInfo: any = {};
  imgList: any =[];
  fileList:any =[];
  showObject: any ={};
  onClickLeft(){
    console.log("返回点击")
  }
  requestItem(){
    let _this=this;
    let param={"taskId":_this.requestId,"dispatchType":""}
    console.log(JSON.stringify(param))
    apiServer.findDetailQuery(param,function(res){
      console.log(res)
      if(res.status==200){
        _this.detailInfo=res.data.data;
        _this.showObject.orgName=_this.detailInfo.sendOrgName;
      }
    },"3")
  }
  openfile_old(_item){
    let _this=this;
    console.log(_item)
    alert(JSON.stringify(_item))
    const toast = _this.$toast.loading({
          duration: 0, // 持续展示 toast
          forbidClick: true,
          message: '正在下载',
        });
    
    let timeout= setTimeout(() => {
      let displaynone = document.querySelector(".van-toast")['style'].display;
      if(displaynone!='none'){
         _this.$toast.clear();
        _this.$toast("请求超时")
      }
    }, 30000);
    let token=localStorage.getItem("token")
    console.log(_item.urlOuterNe)
    let dowloadUrl= window['g'].IP+"/gemp-app/api/gemp/app/basement/attachment/download/v1"
    let paramStr="?fileId="+_item.attachId+"&token="+token;
    _this['$gsmdp'].downloadFilePost({
      url: dowloadUrl,
      //url:"https://outexp-beta.cdn.qq.com/outbeta/2020/06/18/comgsafetyfoshan_3.1.0_eec3c153-f056-58bd-b88e-3b71b4c37f5c.apk",
      header:{
        token:token,
        fileId:_item.attachId
      },
      request:"post",
      filePath: '/storage/emulated/0/com.gsafety.main/file/'+_item.name,
      success: function(res) {
        const filePath = res.tempFilePath
        _this['$gsmdp'].openFile({
          filePath: filePath,
          success: function(res) {
            alert(JSON.stringify(res))
            console.log('打开文档成功')
          }
        })
      },
      taskRunning(result){
        //_this.$toast("start==>"+taskres)
        console.log(JSON.stringify(result))
        toast.message="正在下载"+result.progress+"%"
        console.log(JSON.stringify(result))
      }
      ,taskComplete(result) {
        _this.$toast.clear();
        clearTimeout(timeout)
        alert('Download taskComplete: ' + JSON.stringify(result));
         const filePath = result.tempFilePath
        _this['$gsmdp'].openFile({
          filePath: filePath,
          success: function(res) {
            alert(JSON.stringify(res))
            console.log('打开文档成功')
          }
        })
      }
    })
  }
  openFile(_item){
    let _this=this;
    console.log(_item)
    alert(JSON.stringify(_item))
    const toast = _this.$toast.loading({
          duration: 0, // 持续展示 toast
          forbidClick: true,
          message: '正在下载',
        });
    let timeout= setTimeout(() => {
      let displaynone = document.querySelector(".van-toast")['style'].display;
      if(displaynone!='none'){
         _this.$toast.clear();
        _this.$toast("请求超时")
      }
    }, 30000);
    let token=localStorage.getItem("token")
    console.log(_item.urlOuterNe)
    let dowloadUrl= window['g'].IP+"/gemp-app/api/gemp/app/basement/attachment/download/v1"
    let paramStr="?fileId="+_item.attachId+"&token="+token;

    _this['$gsmdp'].downloadFilePost({
      url: dowloadUrl,
      fileName:_item.name,
      params:{
        token:token,
        fileId:_item.attachId
      }
      ,taskComplete(result) {
        _this.$toast.clear();
        clearTimeout(timeout)
         const filePath = result.filePath
        _this['$gsmdp'].openFile({
          filePath: filePath,
          success: function(res) {
             console.log('打开文档成功')
          }
        })
      }
    })

    
  }
  GoBack(){
    this.$emit("close")
  }
  created() {
    let _this=this;
    
  }
  mounted(){
    this.requestItem();
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      _this.GoBack()
    })
  }
}
</script>
<style scoped lang="scss">
.div_big{
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  .info_big{
    width: 90%;
    height: calc( 100% - 46px - 6% );
    margin: 5%;
    background: white;
    border-radius: 15px;
    font-size: 15px;
    >p{
      padding-top: 10px;
      line-height: 25px;
      img{
          height: 25px;
          vertical-align: middle;
          margin-right: 1.33333vw;
          margin-top: -5px;
      }
      span:nth-of-type(1){
        display: inline-block;
        height: 25px;;
      }
      span:nth-of-type(2){
        display: inline-block;
        font-weight: bold;
        color: gray;
        padding-top: 5px;
        line-height: 25px;
      }
    }
    .info_head{
      width: 100%;
      padding: 0% 5% 5% 5%;
      text-align: center;
      >li{
        width: 100%;
        text-align: left;
      }
    }
    .info_content{
      width: 100%;
      padding: 0px 5%;
      border-top: 1px solid #f2f2f2;
      li{
        padding-top: 10px;
        font-size: 13px;
        text-align: center;
        color: #4277bf;
      }
      p:nth-of-type(2){
        text-align: center;
        img{
          width: 80%;
        }
      }
      p:nth-of-type(3){
        float: right;
        font-size: 15px;
        line-height: 20px;
      }
    }
    .attachmentList{
      li{
        width: 100%;
        height: 30px;
        padding-left: 5%;
        color: #4277bf;
        span{
          display: inline-block;
          height: 100%;
        }
        span:nth-of-type(1){
          width: 10%;
          float: left;
        }
        span:nth-of-type(2){
          width: 90%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        img{
          height: 25px;
          vertical-align: middle;
          margin-right: 6px;
          margin-top: -3px;
        }
      }
    }
  }
}

</style>
