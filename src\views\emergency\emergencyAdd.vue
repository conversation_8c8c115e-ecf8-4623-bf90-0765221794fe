<template>
  <div class="list">
    <Header title="预警信息" backPath="/emergencyResponse"></Header>
    <div class="list-main">
      <van-form @submit="onSubmit">
        <van-cell-group>
          <van-field
            v-model="formObj.title"
            name="title"
            label="预警标题"
            input-align="right"
            placeholder="请输入预警标题"
            :rules="[{ required: true,  }]"
          />
          <van-field
            v-model="formObj.typeName"
            is-link
            name="picker"
            label="预警类型"
            placeholder="请选择预警类型"
            :rules="[{ required: true, message: '请选择预警类型' }]"
            @click="showType = true"
            @focus="focus"
          />
          <van-field
            v-model="formObj.levelName"
            is-link
            name="picker"
            label="预警级别"
            placeholder="请选择预警级别"
            @click="showLevel = true"
          />
          <van-cell title="预警时间" :value="formObj.startTime" @click="showCalendar = true" is-link />
          <van-field is-link name="picker" label="预警范围" placeholder="请选择预警范围" @click="showOrg = true">
            <template #input>
              <span v-for="item in formObj.districts" :key="item.code">{{ item.text }}</span>
            </template>
          </van-field>
          <van-field
            v-model="formObj.warningInfo"
            name="warningInfo"
            label="预警描述"
            type="textarea"
            placeholder="请输入预警描述"
            :rules="[{ required: true, message: '请输入预警描述' }]"
          />
          <van-field v-model="formObj.warningGuide" type="textarea" name="warningGuide" label="防御指南" placeholder="请输入防御指南" />
          <van-field
            v-model="formObj.issueOrg"
            is-link
            name="picker"
            label="发布单位"
            placeholder="请选择发布单位"
            @click="showType = true"
          /><van-field
            v-model="formObj.issuePerson"
            is-link
            name="picker"
            label="发布人员"
            placeholder="请选择发布人员"
            @click="showType = true"
          />
        </van-cell-group>
        <div class="submit-btn">
          <van-button class="btn"  round block type="primary" @click="onSubmit"> 保存 </van-button>
          <van-button  
          type="info"
          plain 
          class="btn"
           block   @click="handleBack" > 取消 </van-button>
        </div>
      </van-form>
    </div>
    <van-calendar v-model:show="showCalendar" color="#1967f2" @confirm="onConfirmDate" />
    <!-- 选择预警级别 -->
    <van-popup v-model:show="showLevel" position="bottom"  round :lock-scroll="false">
      <van-picker :columns="levelList" @change="onConfirmLevel" @cancel="showLevel = false" />
    </van-popup>
    <!-- 选择类别 -->
    <van-popup v-model:show="showType" position="bottom"  round lock-scroll>
      <van-picker :columns="typeList" @change="onConfirmType" @cancel="showType = false" />
    </van-popup>
    <van-popup v-model:show="showOrg" position="bottom"  round :style="{ height: '50%' }">
      <van-checkbox-group v-model="formObj.districts">
        <van-cell-group>
          <van-cell v-for="(item, index) in orgList" clickable :key="item.code" :title="`${item.text}`" @click="toggle(index)">
            <template #right-icon>
              <van-checkbox :name="item" ref="checkboxes" />
            </template>
          </van-cell>
        </van-cell-group>
      </van-checkbox-group>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header
  }
})
export default class add extends Vue {
  formObj: any = {};
  showCalendar: boolean = false; // 展示日历
  showLevel: boolean = false; // 展示预警类型选择器
  levelList: any = [];
  showType: boolean = false;
  typeList: any = [];
  showOrg: boolean = false;
  orgList: any = [];
  requestId: any;
  created() {
    this.getLevelData();
    this.getTypeData();
    this.getOrgData();
    const requestId = this.$route.query.id;
    if (requestId) {
      this.requestId = requestId;
      this.getINfo();

    }
  }
  onSubmit() {}
  handleBack(){
    this.$router.go(-1);
  }
  formatDate(date) {
    return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
  }
  focus() {
    // document.activeElement.blur();
  }
  // 选择日期
  onConfirmDate(val) {
    this.showCalendar = false;
    this.formObj.startTime = this.formatDate(val);
  }
  toggle(index) {
    this.$refs.checkboxes[index].toggle();
  }
  
  getINfo() {
    let _this = this;
    const params = {
      id: _this.requestId
    };
    apiServer.findWarningById(params, function (res) {
      let dataHandleInfo = res.data.data; //dataHandle.caseInfo(res);
      _this.formObj = dataHandleInfo;
    });
  }
  getOrgData() {
    let _this = this;
    const params = {
      parentCode: JSON.parse(window.localStorage.getItem('role')).districtCode
    };
    apiServer.getOrgList(params, function (res) {
      _this.orgList = res.data.data.map((item) => {
        return { text: item.districtName, code: item.districtCode };
      });
    });
  }
  // 获取预警级别数据
  getLevelData() {
    let _this = this;
    const params = {
      parentCode: JSON.parse(window.localStorage.getItem('role')).districtCode
    };
    apiServer.warningLevel(params, function (res) {
      _this.levelList = res.data.data.map((item) => {
        return { text: item.name, code: item.level };
      });
    });
  }
  // 选择预警级别
  onConfirmLevel(picker, value, index) {
    this.showLevel = false;
    this.formObj.level = value.code;
    this.formObj.levelName = value.text;
  }
  // 获取预警类型数据
  getTypeData() {
    let _this = this;
    const params = {
      parentCode: JSON.parse(window.localStorage.getItem('role')).districtCode
    };
    apiServer.warningType(params, function (res) {
      _this.typeList = res.data.data.map((item) => {
        return { text: item.typeName, code: item.typeCode };
      });
    });
  }
  onConfirmType(picker, value, index) {
    this.showType = false;
    this.formObj.type = value.code;
    this.formObj.typeName = value.text;
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    /deep/.van-form {
      display: flex;
      flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
          border-bottom: 1px solid #cecece;
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
        display: flex;
        justify-content: space-around;
        .btn{
          width: 45%;
        }
      }
    }
  }
  /deep/.van-field__error-message {
    text-align: right;
  }
}
</style>
