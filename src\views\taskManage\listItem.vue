<template>
  <div class="list">
    <div class="list-main">
      <van-tabs v-model="curInfoType" @change="onClickTab">
        <van-tab
          color="#1a66f2"
          title-active-color="#1a66f2"
          :title="item.name"
          :name="item.teamTaskStatusCd"
          v-for="item in infoType"
          :key="item.teamTaskStatusCd"
        ></van-tab>
      </van-tabs>
      <div class="list-main-cont">
            <div v-for="(item, index) in eventList" :key="index" class="van-ellipsis" @click="handleToDetail(item)">
                <div class="list_item">
                  <div class="taskTitle">
                    <p class="text_ellipsis">
                         <span v-if='item.taskPhaseName'>【{{item.taskPhaseName}}】</span>
                      {{item.taskTitle}}</p>
                    <!-- 进行中-10 已完成-50 -->
                    <van-tag :type="item.teamTaskStatusCd==10? 'priamry':item.teamTaskStatusCd=='50'?'primary':'warning'">{{item.teamTaskStatusCd==10? '进行中':item.teamTaskStatusCd=='50'?'已完成':'未签收'}}</van-tag>
                  </div>
                  <p class="taskContent">
                   
                    {{item.taskText}}</p>
                  <p class="taskFooter">
                    <span>{{item.orgName}}<van-icon name="phone" /></span>
                    <a>{{item.taskDate}}</a>
                  </p>
                </div>
            </div>
             <div v-if="eventList&&eventList.length==0&&isData" style="width:100%;text-align:center;padding-top: 10%;">
            <van-empty description="暂无数据" />
          <!-- <img src="../../assets/images/notdata.png" />
           <div style="font-size:1rem;color:#969799;margin-top:-1rem">暂无数据</div> -->
        </div>
      </div>
        <van-popup v-model="showDetail" v-if="showDetail"  position="right" :style="{ height: '100%' , width: '100%' }" >
            <taskDetailsNew :requestId="requestId" @close="closeInfo"
              :taskId='taskId'
              :eventId='eventId'
              :teamId='teamId'
              :teamTaskId='teamTaskId'
              :locationInfo='locationInfoData'
              />
        </van-popup>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch,Prop } from 'vue-property-decorator';
import taskRequest from '@/api/webcross/task';
import taskDetailsNew from './taskDetailsNew.vue'
@Component({
  components: {
    taskDetailsNew,
  }
})
export default class listItem extends Vue {

  @Prop(Object) private locationInfo?:object;
  private locationInfoData:any={};
   @Watch('locationInfo',{
    deep:true
  })
  private handleData(val){
    this.locationInfoData = val;
  }
  private teamTaskId;
  private eventId;
  private teamId;
  private taskId;

  eventList: any = [];
  isData :any = false;
  isInit :any = true;
  isUpUser :any =""
  queryMap: any ={};
  requestId:any = "";
  private keywords= '';
  showObj:any = {
      feedbackModel:false,
      taskDetailModel:false,
      taskFkDetailModel:false
  };
  showDetail = false;
     private curInfoType: any = ''; // 当前tabs
   private infoType =[
    {
          name: '全部',
          type:'all',
          teamTaskStatusCd:'',
    },
     {
          name: '进行中',
          type:'doing',
          teamTaskStatusCd:'10',
    },
     {
          name: '已完成',
          type:'finished',
          teamTaskStatusCd:'50',
    }
   ]
   closeInfo(){
    let _this=this;
   this.showDetail = false;
     _this['$gsmdp'].initGoback(function(){
        try {
             _this['$gsmdp'].goBack({
            success(res) {
                alert(JSON.stringify(res))
            }
            })
        } catch (error) {
            console.log("close error")
        }
    })
     
  }
   searchSet(_keyword){
    this.keywords=_keyword;
  }
  handleToDetail(item){
    this.teamTaskId = item.teamTaskId;
    this.teamId = item.teamId;
    this.eventId = item.eventId;
    this.taskId = item.taskId
    this.showDetail = true;
    this.requestId = item.taskId;
  }
  private onClickTab(data) {
     this.curInfoType = data;
     console.log(data,'dddd')
     this.eventList = [];
    this.queryRequest(
                { pageIndex: 1, pageSize:10 }
    )
  }
  androidPath: any = 'file:///android_asset'
    queryRequest(pageObj){
      let _this=this;
      let _objPage={pageIndex:1,pageSize:10}
      if(pageObj){
        console.log(pageObj.pageIndex)
        _objPage.pageIndex=pageObj.pageIndex;
      }
        let param = {
            "nowPage": _objPage.pageIndex,
            "pageSize": _objPage.pageSize,
              eventId: this.$route.query.eventId,
             teamId: this.$route.query.teamId,
            teamTaskStatusCd:this.curInfoType,
            keywords:this.keywords
        };
      console.log(JSON.stringify(param))
      //apiServer.findDispatchList(param,function(res){
      taskRequest.findTaskListNew(param,function(res){
          //alert(JSON.stringify(res))
         console.log(res)
         _this.isInit=false;
         _this.$emit("handleObj",{uploadMore:false})
         _this.isData=true;
         _this.$toast.clear();
         let resultsObj=res['data'].data.list;
          if(resultsObj){
            console.log("赋值成功");
            resultsObj['forEach']((item,index)=>{
              //console.log(item)
              item.feedbackFlag=false;
            })
            if(_objPage.pageIndex>1){
               _this.eventList = [..._this.eventList, ...resultsObj];
            }else{
              _this.eventList=resultsObj;
            }
            
          }
      })
    }
    created(){
        this.queryRequest(null)
    }
}
</script>
<style scoped lang="scss">
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  .van-tabs {
    margin-bottom: 10px;
  }

  .van-ellipsis {
    margin-bottom: 10px;
  }
  .list_item {
    background: white;
    padding: 15px 20px 15px 20px;
    .text_ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    p{
        margin: 4px 0 !important;
    }
    .taskTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      p {
        font-size: 16px;
        font-weight: bold;
        width: 280px;
        padding: 0;
      }
      span {
        text-align: right;
      }
    }
    .taskContent {
      font-size: 14px;
    }
    .taskFooter {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      span i {
        color: rgb(67, 89, 223);
      }
      a{
        color: #333;
      }
    }
  }
}
  /deep/ .van-tabs__line {
    background-color: #1967f2 !important;
  }
</style>
