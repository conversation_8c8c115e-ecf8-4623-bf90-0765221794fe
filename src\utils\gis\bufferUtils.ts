/**
 * 缓冲区工具类
 */

// 定义GeoJSON类型
namespace GeoJSON {
  export interface Geometry {
    type: string;
    coordinates: any[];
  }

  export interface Feature {
    type: string;
    properties: any;
    geometry: Geometry;
  }

  export interface FeatureCollection {
    type: string;
    features: Feature[];
  }
}

/**
 * 创建圆形缓冲区的GeoJSON几何对象
 * @param center 中心点坐标 [longitude, latitude]
 * @param radius 半径，单位：公里
 * @returns GeoJSON几何对象（经过抽稀处理，控制点数在约50个左右）
 */
export function createCircleBuffer(center: [number, number], radius: number): GeoJSON.Geometry {
  const [longitude, latitude] = center;

  // 引入egis库
  const egis = require('egis-2d');

  // 创建圆心点
  const centerPoint = new egis.sfs.Point({
    x: longitude,
    y: latitude,
    spatialReference: egis.sfs.EnumSpatialReference.EPSG4490
  });

  // 将公里转换为经纬度单位（度）
  // 地球起算的平均半径为 6371 公里
  // 1 度经度在赤道上约为 111 公里
  // 经度随纬度增加而减小，需要考虑纬度因素
  const latRadians = (latitude * Math.PI) / 180; // 将纬度转换为弧度
  const radiusInDegrees = radius / (111.32 * Math.cos(latRadians)); // 考虑纬度因素

  // 创建圆
  const circle = new egis.sfs.Circle({
    spatialReference: egis.sfs.EnumSpatialReference.EPSG4490, // 坐标系
    center: centerPoint, // 圆心
    radius: radiusInDegrees // 半径（经纬度单位）
  });

  // 转换为GeoJSON
  const geoJson = circle.asGeoJson(6);

  // 手动对GeoJSON坐标进行抽稀处理
  // 原始GeoJSON生成的圆约有360个点，这里通过抽样减少到约50个点
  if (geoJson && geoJson.coordinates && geoJson.coordinates.length > 0) {
    // 对多边形的外环进行抽稀
    const outerRing = geoJson.coordinates[0];
    if (outerRing && outerRing.length > 50) {
      console.log(`原始点数: ${outerRing.length}，进行抽稀处理...`);
      // 圆环上的点太多，需要抽稀处理
      const thinned = [outerRing[0]]; // 保留第一个点
      // 计算抽稀步长，控制保留点数在约45-50个
      const step = Math.max(2, Math.floor(outerRing.length / 45));

      for (let i = step; i < outerRing.length - 1; i += step) {
        thinned.push(outerRing[i]);
      }

      // 确保最后一个点被保留，并闭合多边形
      const lastPoint = outerRing[outerRing.length - 1];
      // 检查是否已经添加了最后一个点
      if (JSON.stringify(thinned[thinned.length - 1]) !== JSON.stringify(lastPoint)) {
        thinned.push(lastPoint);
      }

      // 确保多边形闭合（首尾点相同）
      if (JSON.stringify(thinned[0]) !== JSON.stringify(thinned[thinned.length - 1])) {
        thinned.push(thinned[0]);
      }

      // 更新坐标数组
      geoJson.coordinates[0] = thinned;
      console.log(`抽稀后点数: ${thinned.length}`);
    }
  }

  return geoJson;
}

/**
 * 创建指定半径的缓冲区GeoJSON
 * @param center 中心点坐标 [longitude, latitude]
 * @param radius 半径，单位：公里
 * @returns GeoJSON对象
 */
export function createBufferGeoJSON(center: [number, number], radius: number): GeoJSON.FeatureCollection {
  const geometry = createCircleBuffer(center, radius);

  return {
    type: 'FeatureCollection',
    features: [
      {
        type: 'Feature',
        properties: {
          radius: radius
        },
        geometry: geometry
      }
    ]
  };
}

/**
 * 创建圆对象
 * @param center 中心点坐标 [longitude, latitude]
 * @param radius 半径，单位：公里
 * @returns egis.sfs.Circle对象
 */
export function createCircle(center: [number, number], radius: number): any {
  const [longitude, latitude] = center;
  const egis = require('egis-2d');

  // 创建圆心点
  const centerPoint = new egis.sfs.Point({
    x: longitude,
    y: latitude,
    spatialReference: egis.sfs.EnumSpatialReference.EPSG4490
  });

  // 将公里转换为经纬度单位（度）
  const latRadians = (latitude * Math.PI) / 180; // 将纬度转换为弧度
  const radiusInDegrees = radius / (111.32 * Math.cos(latRadians)); // 考虑纬度因素

  // 创建圆
  return new egis.sfs.Circle({
    spatialReference: egis.sfs.EnumSpatialReference.EPSG4490, // 坐标系
    center: centerPoint, // 圆心
    radius: radiusInDegrees // 半径（经纬度单位）
  });
}

export default {
  createCircleBuffer,
  createBufferGeoJSON,
  createCircle
};
