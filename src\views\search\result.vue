<template>
  <div class="div_big">
    <!-- <Header
      :title="detailInfo.eventTitle">
  </Header> -->
    <van-nav-bar :title="detailInfo.eventTitle || ''" left-text="返回" left-arrow @click-left="GoBack">
      <template #right>
        <van-icon :name="showPanel ? 'arrow-down' : 'arrow-up'" size="25" @click="togglePanel" />
      </template>
    </van-nav-bar>

    <!-- 周边分析范围选择器，移至顶部 -->
    <div class="buffer-radius-selector" v-show="this.showPanel">
      <van-button
        v-for="(radius, index) in bufferRadiusOptions"
        :key="index"
        size="small"
        :type="selectedBufferRadius === radius ? 'primary' : 'default'"
        class="buffer-option"
        @click="selectBufferRadius(radius)"
      >
        {{ radius }}km
      </van-button>
    </div>

    <!-- 地图 start -->
    <div class="map">
      <baseMap :mapId="`map_${new Date().getTime()}`"></baseMap>
    </div>
    <!-- 地图 end -->
    <!-- <div class="layer-icon" @click="switchLayer">
      <van-icon name="points" size="24" />
      <div>图层</div>
    </div> -->
    <!-- 详情信息 -->
    <div class="footer-content">
      <!-- 事件详情：当路由参数type为event时显示 -->
      <div v-if="!!$route.query.type && Object.keys(detailInfo).length > 0">
        <searchInfo :result-info="detailInfo" :type="$route.query.type"></searchInfo>
      </div>

      <!-- 应急资源详情：当路由参数中有layerKey时显示 -->
      <div v-if="$route.query.layerKey">
        <resourceInfo :resourceInfo="detailInfo" :layerKey="$route.query.layerKey" />
      </div>

      <!-- 点位详情信息：当用户点击地图上的点位后显示 -->
      <van-popup
        v-model="showPointDetail"
        position="bottom"
        round
        :style="{ maxHeight: '30%', zIndex: 102, overflow: 'unset' }"
        get-container=".div_big"
        @closed="closePointDetail"
        :overlay="false"
        :close-on-click-overlay="false"
        class="van-popup--point-detail"
      >
        <div class="point-detail">
          <div class="detail-header">
            <div class="back-icon" @click="closePointDetail">
              <van-icon name="arrow-left" />
            </div>
            <div class="title">{{ selectedPoint ? selectedPoint.title || '详情信息' : '详情信息' }}</div>
            <div class="placeholder"></div>
          </div>
          <div class="point-detail-content">
            <!-- 显示配置的字段信息 -->
            <div class="field-list">
              <div v-for="(field, index) in pointFields" :key="index" class="field-item">
                <div class="field-label">{{ field.label }}</div>
                <div class="field-value">
                  <template
                    v-if="field.type === 'Tel' && selectedPoint && selectedPoint.originalData && selectedPoint.originalData[field.name]"
                  >
                    {{ selectedPoint.originalData[field.name] }}
                    <van-icon
                      name="phone-circle"
                      class="phone-icon"
                      color="#13b2a1"
                      @click.stop="goToTelPage(selectedPoint.originalData[field.name])"
                    />
                  </template>
                  <template v-else>
                    {{
                      selectedPoint && selectedPoint.originalData && selectedPoint.originalData[field.name]
                        ? selectedPoint.originalData[field.name]
                        : '暂无'
                    }}
                    <span v-if="field.unit">{{ field.unit }}</span>
                  </template>
                </div>
              </div>
              <div v-if="!pointFields || pointFields.length === 0" class="no-field">
                <van-empty description="暂无详情数据" />
              </div>
            </div>
          </div>
        </div>
      </van-popup>
    </div>
    <!-- 图层面板（始终显示） -->
    <div class="layer-panel" v-show="this.showPanel">
      <!-- 顶部标签页 -->
      <div class="layer-tabs">
        <div
          v-for="(item, index) in tabArr"
          :key="index"
          class="tab-item"
          :class="{ active: activeTabIndex === index }"
          @click="handleTabClick(index)"
        >
          {{ item.label }}
        </div>
      </div>

      <!-- 子标签页 -->
      <div class="sub-tabs" v-if="activeTabIndex >= 0 && activeTab && activeTab.children && activeTab.children.length > 0">
        <div
          v-for="(subTab, subIndex) in subTabs"
          :key="subIndex"
          class="sub-tab-item"
          :class="{ active: activeSubTabIndex === subIndex }"
          @click="showLocationList(subIndex)"
        >
          {{ subTab.label }} <span class="count">({{ subTab.count }})</span>
        </div>
      </div>
    </div>

    <!-- 列表内容（点击子标签后显示） -->
    <van-popup
      v-model="showList"
      position="bottom"
      :style="{ height: '30%', zIndex: 101 }"
      round
      @closed="onPopupClosed"
      get-container=".div_big"
      :overlay="false"
      :close-on-click-overlay="false"
    >
      <div class="location-list">
        <div class="location-header">
          <span>{{ activeSubTab ? activeSubTab.label : '' }}</span>
          <van-icon name="cross" class="close-icon" @click="closeList()" />
        </div>
        <van-list class="location-items-container" v-if="locationList.length > 0">
          <van-cell
            v-for="(item, index) in locationList"
            :key="index"
            :title="item.title"
            :label="item.address || ''"
            @click="selectLocation(item)"
            class="location-item"
            is-link
          >
            <template #icon>
              <div class="item-index">{{ index + 1 }}</div>
            </template>
          </van-cell>
        </van-list>
        <div class="no-data" v-if="locationList.length === 0">
          <van-empty description="暂无数据" />
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import baseMap from '@/components/baseMap/baseMap.vue';
import Header from '@/views/common/header.vue';
import { Notify, Toast } from 'vant';
// import { customBrowserVersion, guide } from '@/utils/getLocation';
import infoRequest from '@/api/webcross/infoRequest';
import searchInfo from './searchInfo.vue';
import simplified from './simplified.vue';
import layerUtils from '@/utils/gis/layerUtils';
import egisDataServer from '@/api/service/EgisDataServer';
import bufferUtils from '@/utils/gis/bufferUtils';
import resourceInfo from './resourceInfo.vue';

import danger from './danger.vue';
import { EnvironmentContext } from '@/utils/gis/environmentContext';
@Component({
  components: {
    baseMap,
    Header,
    searchInfo,
    simplified,
    danger,
    resourceInfo
  }
})
export default class Result extends Vue {
  locationInfo: any = {
    getLocSuccess: true, // 获取当前位置是否成功
    locInfo: {
      latitude: 30.6402,
      longitude: 114.3889,
      address: '当前位置'
    } as any // 当前位置
  };
  detailInfo: any = {}; // 详情信息
  showPanel: boolean = true; // 控制顶部面板显示/隐藏状态

  eventId: any = '';
  tabArr: any = [];
  // 列表显示控制
  showList: boolean = false;
  activeTabIndex: number = -1; // 初始不选中任何Tab
  activeSubTabIndex: number = -1; // 初始不选中任何子Tab
  locationList: any[] = [];

  // 添加应急资源图层数组
  resourceLayers: any[] = [];

  // 周边分析相关参数
  bufferRadiusOptions: number[] = [1, 3, 5, 10]; // 缓冲区半径选项（公里）
  selectedBufferRadius: number = 5; // 默认选中 5 公里的缓冲区半径
  bufferGeometry: any = null; // 缓冲区几何对象

  // 新增数据属性
  selectedPoint: any = null; // 当前选中的点位
  showPointDetail: boolean = false; // 是否显示点位详情
  pointFields: any[] = []; // 点位详情字段配置

  get activeTab() {
    return this.tabArr[this.activeTabIndex] || null;
  }

  get activeSubTab() {
    return this.subTabs[this.activeSubTabIndex] || null;
  }

  get subTabs() {
    // 如果当前标签有子标签，直接返回子标签数组
    if (this.activeTab && this.activeTab.children && this.activeTab.children.length > 0) {
      return this.activeTab.children;
    }
    return [];
  }

  // 点击主标签时的处理逻辑
  handleTabClick(index: number) {
    // 如果点击已选中的标签，则取消选中并关闭列表
    if (this.activeTabIndex === index) {
      this.activeTabIndex = -1; // 设置为-1表示无选中状态
      this.activeSubTabIndex = -1;
      this.showList = false;

      // 如果显示的是点位详情，则继续保持显示
      if (this.showPointDetail) {
        // 不做任何改变，保持点位详情显示
      }
    } else {
      // 选中新标签
      this.activeTabIndex = index;
      this.activeSubTabIndex = -1; // 默认不选中子标签
      this.showList = false;
    }
  }

  // 点击子标签显示列表并上图
  async showLocationList(subIndex: number) {
    // 如果点击已选中的子标签，则取消选中并关闭列表
    if (this.activeSubTabIndex === subIndex && this.showList) {
      this.activeSubTabIndex = -1;
      this.showList = false;
      layerUtils.clearLayer();
      return;
    }

    // 选中新子标签
    this.activeSubTabIndex = subIndex;

    // 显示加载提示
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
      duration: 0
    });

    // 获取当前选中的子标签
    const activeSubTab = this.subTabs[subIndex];
    if (activeSubTab) {
      try {
        // 确保缓冲区已创建
        if (!this.bufferGeometry && this.locationInfo.getLocSuccess) {
          const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
          this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);

          // 显示缓冲区
          this.showBufferOnMap();
        }

        // 获取数据并上图
        await this.getLocationListData(activeSubTab.key);
        await this.getLayerData(activeSubTab.key);

        // 如果查询到数据才显示列表
        if (this.locationList && this.locationList.length > 0) {
          this.showList = true;
        } else {
          this.showList = false;
          Notify({ type: 'warning', message: '未查询到相关数据' });
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        Notify({ type: 'danger', message: '获取数据失败，请稍后重试' });
        this.showList = false;
      } finally {
        Toast.clear();
      }
    } else {
      Toast.clear();
      this.showList = false;
    }
  }

  // 图层按钮点击事件
  switchLayer() {
    // 重置为未选中状态
    if (this.activeTabIndex === -1) {
      // 如果当前未选中任何Tab，则选中第一个
      this.activeTabIndex = 0;
    } else {
      // 如果当前已选中某个Tab，则取消选中
      this.activeTabIndex = -1;
      // 关闭列表
      this.showList = false;
    }

    // 重置子标签选中状态
    this.activeSubTabIndex = -1;

    // 重新获取图层统计数据
    this.getLayerStatistics();
  }

  selectLocation(item: any) {
    console.log('选择位置:', item);

    // 显示加载提示
    Toast.loading({
      message: '加载详情中...',
      forbidClick: true,
      duration: 0
    });

    // 获取当前选中的子标签key
    const layerKey = this.activeSubTab ? this.activeSubTab.key : '';

    // 使用egisDataServer.getDetailById获取详情信息
    egisDataServer
      .getDetailById({
        code: layerKey || item.key,
        id: item.id
      })
      .then((detailData: any) => {
        console.log('获取详情成功:', detailData);

        // 保存详情数据到selectedPoint
        this.selectedPoint = {
          ...item,
          originalData: detailData || item // 使用返回的详情数据，如果没有则使用原item
        };

        // 先显示点位详情，但不隐藏列表
        this.showPointDetail = true;

        // 获取字段配置
        this.loadFieldConfig(layerKey || item.key);

        // 高亮显示点位
        if (item.id) {
          layerUtils.locationHighlight(item.id);
        }

        // 关闭加载提示
        Toast.clear();
      })
      .catch((error: any) => {
        console.error('获取详情失败:', error);

        // 使用基本数据显示
        this.selectedPoint = {
          ...item,
          originalData: item
        };

        // 显示点位详情
        this.showPointDetail = true;

        // 获取字段配置
        this.loadFieldConfig(layerKey || item.key);

        // 关闭加载提示
        Toast.clear();

        // 显示错误提示
        Notify({ type: 'warning', message: '获取详情失败，显示基本信息' });
      });
  }

  GoBack() {
    history.back();
  }

  mounted() {
    const layerData = require(`./allLayers.json`);
    // 从路由参数中获取数据
    if (this.$route.query.eventId) {
      this.eventId = this.$route.query.eventId;
    }
    const that = this;

    // 根据路由参数类型获取不同的详情
    if (this.$route.query.type && this.$route.query.type == 'event') {
      // 获取事件信息
      this.getInfo(this.eventId, () => {
        // 创建默认缓冲区
        const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
        this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);
        this.showBufferOnMap();
        this.tabArr = layerData.allLayers;
        this.getLayerStatistics(this.bufferGeometry);
      });
    } else if (this.$route.query.layerKey && this.$route.query.id) {
      // 获取应急资源详情
      egisDataServer
        .getDetailById({
          code: this.$route.query.layerKey,
          id: this.$route.query.id
        })
        .then((res: any) => {
          // // 设置位置信息
          that.locationInfo.getLocSuccess = true;
          that.locationInfo.locInfo = {
            latitude: res.latitude,
            longitude: res.longitude,
            address: res.address
          };
          const center: [number, number] = [that.locationInfo.locInfo.longitude, that.locationInfo.locInfo.latitude];
          that.bufferGeometry = bufferUtils.createCircleBuffer(center, that.selectedBufferRadius);
          that.showBufferOnMap();
          setTimeout(() => {
            that.tabArr = layerData.allLayers;
            that.getLayerStatistics(that.bufferGeometry);
            that.detailInfo = res;
          }, 500);
        });
    } else if (this.$route.query.longitude && this.$route.query.latitude) {
      // 处理地址搜索结果
      this.locationInfo.getLocSuccess = true;
      this.locationInfo.locInfo = {
        latitude: Number(this.$route.query.latitude),
        longitude: Number(this.$route.query.longitude),
        address: this.$route.query.address || '搜索位置'
      };

      // 显示位置点和缓冲区
      this.$nextTick(() => {
        const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
        this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);
        this.showBufferOnMap();
        setTimeout(() => {
          that.tabArr = layerData.allLayers;
          that.getLayerStatistics(that.bufferGeometry);
          that.detailInfo = {
            eventTitle: that.$route.query.name || '搜索结果',
            infoAddress: that.$route.query.address,
            latitude: Number(this.$route.query.latitude),
            longitude: Number(this.$route.query.longitude)
          };
        }, 500);
      });
    }
  }

  // 获取位置列表数据
  async getLocationListData(layerKey: string) {
    try {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      });

      // 构建查询参数
      const params: any = {
        code: layerKey,
        filter: {
          districtCodes: '420100' // 默认使用武汉市的区划编码
        }
      };

      // 默认添加空间过滤，使用 5 公里缓冲区
      if (this.locationInfo.getLocSuccess) {
        // 如果缓冲区几何对象不存在，创建一个新的
        if (!this.bufferGeometry) {
          const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
          this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);
        }

        // 添加空间过滤
        params.filter.geometry = this.bufferGeometry;
      }

      // 调用接口获取数据
      const result = await egisDataServer.getLayerList(params);

      // 处理返回的数据
      if (Array.isArray(result) && result.length > 0) {
        // 格式化数据为列表需要的格式
        this.locationList = result.map((item: any, index: number) => {
          return {
            id: item.id || index,
            title: item.name || '未命名',
            address: item.address || item.location || '',
            longitude: item.longitude || item.lon || item.jd,
            latitude: item.latitude || item.lat || item.wd,
            // 保存原始数据，用于后续处理
            originalData: item
          };
        });
      } else {
        this.locationList = [];
        Notify({
          type: 'warning',
          message: `在${this.selectedBufferRadius}km范围内未找到相关数据`
        });
      }

      Toast.clear();
    } catch (error) {
      console.error('获取位置列表数据失败:', error);
      Notify({ type: 'danger', message: '获取数据失败，请稍后重试' });
      this.locationList = [];
      Toast.clear();
    }
  }

  // 获取图层统计数据
  async getLayerStatistics(bufferGeometry?: any) {
    try {
      // 获取所有图层的key
      const layerKeys: string[] = [];

      // 收集所有子标签的key
      this.tabArr.forEach((tab: any) => {
        if (tab.children && tab.children.length > 0) {
          tab.children.forEach((child: any) => {
            if (child.key) {
              layerKeys.push(child.key);
            }
          });
        } else if (tab.key) {
          layerKeys.push(tab.key);
        }
      });

      // 构建查询参数
      const params: any = {
        codes: layerKeys,
        filter: {
          districtCodes: '420100' // 默认使用武汉市的区划编码
        }
      };

      // 如果提供了缓冲区几何对象，添加到查询参数中
      if (bufferGeometry) {
        params.filter.geometry = bufferGeometry;
      }

      // 调用接口获取统计数据
      const result = await egisDataServer.getStatistics(params);

      // 更新子标签的统计数据
      if (result) {
        console.log('统计数据结果:', result);
        // 更新子标签的统计数据
        this.updateSubTabsCount(result);
      }
    } catch (error) {
      console.error('获取图层统计数据失败:', error);
    }
  }

  // 更新子标签的统计数据
  updateSubTabsCount(statistics: any) {
    // 更新每个标签的统计数据
    this.tabArr.forEach((tab: any) => {
      if (tab.children && tab.children.length > 0) {
        tab.children.forEach((child: any) => {
          if (child.key && statistics[child.key] !== undefined) {
            // 使用Vue.set确保响应式更新
            this.$set(child, 'count', statistics[child.key]);
          }
        });
      } else if (tab.key && statistics[tab.key] !== undefined) {
        this.$set(tab, 'count', statistics[tab.key]);
      }
    });

    // 强制更新视图
    this.$forceUpdate();
    console.log('更新后的tabArr:', this.tabArr);
  }

  // 获取图层数据并上图
  async getLayerData(layerKey: string) {
    try {
      // 构建查询参数
      const params: any = {
        code: layerKey,
        filter: {
          districtCodes: '420100' // 默认使用武汉市的区划编码
        }
      };

      // 默认添加空间过滤，使用 5 公里缓冲区
      if (this.locationInfo.getLocSuccess) {
        // 如果缓冲区几何对象不存在，创建一个新的
        if (!this.bufferGeometry) {
          const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
          this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);
        }

        // 添加空间过滤
        params.filter.geometry = this.bufferGeometry;
      }

      // 调用接口获取数据
      const result = await egisDataServer.getLayerList(params);

      // 处理返回的数据
      if (Array.isArray(result) && result.length > 0) {
        // 获取图标路径
        const activeSubTab = this.activeSubTab;
        const iconName = activeSubTab ? activeSubTab.key : 'default';
        const imageUrl = `img/marker/${iconName}.png`;

        // 在地图上显示点位
        layerUtils.showPointList(
          result,
          {
            useImage: true,
            imageUrl: imageUrl,
            width: 32,
            height: 32,
            key: activeSubTab.key
          },
          this.selectLocation
        );

        Notify({
          type: 'success',
          message: `在${this.selectedBufferRadius}km范围内找到 ${result.length} 个点位`
        });
      } else {
        Notify({
          type: 'warning',
          message: `在${this.selectedBufferRadius}km范围内未找到相关数据`
        });
      }

      Toast.clear();
    } catch (error) {
      console.error('获取图层数据失败:', error);
      Notify({ type: 'danger', message: '获取数据失败，请稍后重试' });
      Toast.clear();
    }
  }

  // 在地图上显示缓冲区
  showBufferOnMap() {
    // 检查地图是否已经初始化
    if (!EnvironmentContext.egisMap) {
      console.warn('地图尚未初始化，稍后将自动重试显示缓冲区');
      // 如果地图未初始化，则等待一段时间后重试
      setTimeout(() => {
        this.showBufferOnMap();
      }, 500);
      return;
    }

    // 创建圆对象的中心点坐标
    const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
    layerUtils.addPointToMap(center);
    // layerUtils.addEventToMap(center);

    // 使用layerUtils显示缓冲区
    layerUtils.showBufferOnMap(center, this.selectedBufferRadius, {
      fillColor: { r: 255, g: 0, b: 0, a: 0 }, // 半透明橙色
      borderColor: { r: 255, g: 0, b: 0, a: 255 },
      borderThickness: 4
    });
  }

  // 获取详情信息
  getInfo(id: string | number, callback?: Function) {
    infoRequest.getEventById({ id: id }, (res: any) => {
      if (res.status === 200) {
        let data = res.data.data;
        this.locationInfo.locInfo.address = data.infoAddress;
        this.locationInfo.locInfo.latitude = data.latitude;
        this.locationInfo.locInfo.longitude = data.longitude;
        this.locationInfo.getLocSuccess = true;
        this.detailInfo = data;

        // 如果有回调函数，执行回调
        if (typeof callback === 'function') {
          callback();
        } else {
          // 没有回调函数，自动初始化空间查询
          this.$nextTick(() => {
            // 创建默认缓冲区
            const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
            this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);

            // 显示缓冲区
            this.showBufferOnMap();

            // 获取统计数据，必须带空间过滤
            this.getLayerStatistics(this.bufferGeometry);
          });
        }
      }
    });
  }

  // 加载字段配置
  loadFieldConfig(layerKey: string) {
    try {
      // 导入字段配置
      const fieldConfig = require('@/utils/fieldConfig.ts').default;

      // 获取对应的字段配置，如果没有则使用通用配置
      this.pointFields = fieldConfig[layerKey] || [];
    } catch (error) {
      console.error('加载字段配置失败:', error);
      this.pointFields = []; // 出错时使用空配置
    }
  }
  goToTelPage(tel) {
    window.location.href = `tel://${tel}`;
  }
  // 关闭点位详情
  closePointDetail() {
    this.showPointDetail = false;
    layerUtils.setBufferCenter(this.selectBufferRadius);
    // 使用nextTick确保UI更新完成后再执行后续操作
    this.$nextTick(() => {
      // 如果列表已经打开，高亮列表中的项目
      if (this.showList && this.selectedPoint && this.selectedPoint.id) {
        layerUtils.locationHighlight(this.selectedPoint.id);
      }
      // 否则清除选中状态
      else {
        layerUtils.clearHighlight();
      }

      // 清空选中的点位数据
      this.selectedPoint = null;
    });
  }
  // 关闭列表事件处理
  closeList() {
    this.showList = false;
    layerUtils.clearLayer();
    // 重置子标签选中状态但保持主标签选中
    this.activeSubTabIndex = -1;
  }

  // 选择缓冲区半径
  selectBufferRadius(radius: number) {
    this.selectedBufferRadius = radius;

    // 更新缓冲区几何对象
    const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
    this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);
    this.showBufferOnMap();
    // 获取统计数据，带空间过滤
    this.getLayerStatistics(this.bufferGeometry);
    if (this.activeSubTab) {
      this.performBufferAnalysis();
    }
  }

  // 执行周边分析
  async performBufferAnalysis() {
    if (!this.locationInfo.locInfo.longitude || !this.locationInfo.locInfo.latitude) {
      Notify({ type: 'warning', message: '无法获取当前位置信息' });
      return;
    }

    // 显示加载提示
    Toast.loading({
      message: '分析中...',
      forbidClick: true,
      duration: 0
    });

    try {
      // 如果缓冲区几何对象不存在，创建一个新的
      if (!this.bufferGeometry) {
        const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
        this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedBufferRadius);
      }

      // 显示缓冲区
      this.showBufferOnMap();

      // 获取当前选中的子标签
      if (this.activeSubTab) {
        // 构建查询参数
        const params = {
          code: this.activeSubTab.key,
          filter: {
            districtCodes: '420100', // 默认使用武汉市的区划编码
            geometry: this.bufferGeometry // 添加空间查询条件
          }
        };

        // 调用接口获取数据
        const result = await egisDataServer.getLayerList(params);

        // 处理返回的数据
        if (Array.isArray(result) && result.length > 0) {
          // 格式化数据为列表需要的格式
          this.locationList = result.map((item: any, index: number) => {
            return {
              id: item.id || index,
              title: item.name || '未命名',
              address: item.address || item.location || '',
              longitude: item.longitude || item.lon || item.jd,
              latitude: item.latitude || item.lat || item.wd,
              // 保存原始数据，用于后续处理
              originalData: item
            };
          });

          // 显示列表
          this.showList = true;

          // 在地图上显示点位
          const activeSubTab = this.activeSubTab;
          const iconName = activeSubTab ? activeSubTab.key : 'default';
          const imageUrl = `img/marker/${iconName}.png`;

          layerUtils.showPointList(
            result,
            {
              useImage: true,
              imageUrl: imageUrl,
              width: 32,
              height: 32,
              key: activeSubTab.key
            },
            this.selectLocation
          );

          Notify({ type: 'success', message: `在${this.selectedBufferRadius}km范围内找到 ${result.length} 个点位` });
        } else {
          this.locationList = [];
          this.showList = true;
          layerUtils.clearLayer();
          Notify({ type: 'warning', message: `在${this.selectedBufferRadius}km范围内未找到相关数据` });
        }
      } else {
        Notify({ type: 'warning', message: '请先选择一个图层类型' });
      }

      Toast.clear();
    } catch (error) {
      console.error('周边分析失败:', error);
      Notify({ type: 'danger', message: '分析失败，请稍后重试' });
      Toast.clear();
    }
  }

  onPopupClosed() {
    // 处理列表关闭后的逻辑
    this.activeSubTabIndex = -1;
    this.showList = false;
    layerUtils.clearLayer();
  }

  // 切换顶部面板显示/隐藏
  togglePanel() {
    this.showPanel = !this.showPanel;
  }
}
</script>
<style scoped lang="less">
.div_big {
  width: 100%;
  height: 100%;
  .map {
    width: 100%;
    height: 70%;
  }
  .layer-icon {
    position: fixed;
    right: 20px;
    top: 25%;
    width: 40px;
    padding: 5px 5px;
    text-align: center;
    border-radius: 10px;
    z-index: 999;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  .tabs-box {
    position: fixed;
    top: 20px;
    left: 0;
    background: #fff;
    z-index: 999;
    padding: 10px;
  }
}

// 图层面板样式
.layer-panel {
  position: fixed;
  top: 88px; // 导航栏(46px) + buffer选择器高度(约42px)
  left: 0;
  right: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

// 顶部标签页样式
.layer-tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  overflow-x: auto;

  .tab-item {
    flex: 1;
    min-width: 70px;
    text-align: center;
    padding: 10px 5px;
    font-size: 14px;
    position: relative;
    white-space: nowrap;

    &.active {
      color: #1989fa;
      font-weight: bold;

      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 40%;
        height: 2px;
        background-color: #1989fa;
      }
    }
  }
}

// 子标签页样式
.sub-tabs {
  display: flex;
  background-color: #fff;
  padding: 8px 0;
  overflow-x: auto;

  .sub-tab-item {
    padding: 5px 10px;
    margin: 0 4px;
    font-size: 13px;
    border-radius: 4px;
    white-space: nowrap;

    &.active {
      background-color: #f0f9ff;
      color: #1989fa;
    }

    .count {
      font-size: 12px;
      color: #999;
      margin-left: 2px;
    }
  }
}

// 关闭按钮
.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  z-index: 101;
}

// 列表面板样式
.location-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 33%;
  z-index: 99;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  transition: transform 0.3s ease;
}

// 列表头部
.location-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 15px;
  border-bottom: 1px solid #f5f5f5;
  font-weight: bold;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 2;

  .close-icon {
    padding: 5px;
    font-size: 18px;
  }
}

.location-list {
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.location-items-container {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.location-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f5f5f5;
  background-color: #fff;
  transition: background-color 0.2s;

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }

  .item-index {
    width: 24px;
    height: 24px;
    background-color: #f0f9ff;
    color: #1989fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .item-content {
    flex: 1;
    min-width: 0;

    .item-title {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .item-address {
      font-size: 12px;
      color: #999;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .item-arrow {
    margin-left: 8px;
    color: #999;
  }
}

.no-data {
  padding: 30px 0;
  text-align: center;
}

.slider_div {
  padding: 0 10px;
  h4 {
    font-size: 14px;
    img {
      height: 25px;
      width: 25px;
      vertical-align: middle;
      margin-right: 10px;
    }
  }

  .planType_div {
    p {
      color: #808080;
      height: 30px;
      line-height: 30px;
      padding-left: 20px;
    }
  }
  .eventType_div {
    p {
      height: 30px;
      line-height: 30px;
      padding-left: 20px;
    }
  }
}

.planBg {
  background: #e8f1f5;
  color: #3e5dd2 !important;
}

// 点位详情样式
.point-detail {
  background-color: #fff;
  width: 100%;
  display: flex;
  flex-direction: column;
  max-height: 80vh;

  .detail-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    height: 46px;
    background-color: #fff;

    .back-icon {
      padding: 8px;
      margin-left: -8px;

      .van-icon {
        font-size: 16px;
        color: #323233;
      }
    }

    .title {
      flex: 1;
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      margin: 0 16px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .placeholder {
      width: 24px;
    }
  }

  .point-detail-content {
    overflow-y: auto;
    padding: 0 16px 16px;
    padding-bottom: env(safe-area-inset-bottom, 16px);

    .field-list {
      .field-item {
        display: flex;
        padding: 12px 0;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
        }

        .field-label {
          width: 90px;
          color: #646566;
          font-size: 14px;
          flex-shrink: 0;
        }

        .field-value {
          flex: 1;
          font-size: 14px;
          color: #323233;
          word-break: break-all;

          .phone-icon {
            margin-left: 8px;
            vertical-align: -3px;
          }
        }
      }

      .no-field {
        padding: 32px 0;
      }
    }
  }
}

.footer-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  max-height: 33%;
  overflow-y: auto;
  z-index: 99;
}

// 添加动画效果
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all 0.3s ease;
}

.slide-down-enter-from,
.slide-down-leave-to {
  transform: translateY(-100%);
  opacity: 0;
}

// 添加周边分析范围选择器样式
.buffer-radius-selector {
  position: fixed;
  top: 46px; // 导航栏高度
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
  background-color: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid #eee;
  z-index: 101; // 高于图层面板的z-index(100)

  .buffer-option {
    margin: 0 5px;
    min-width: 60px;
    font-size: 12px;
    border-radius: 16px;
  }
}

// 添加弹窗样式增强
:deep(.van-popup) {
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.15);

  &.van-popup--bottom {
    overflow: visible;

    &::before {
      content: '';
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 4px;
      background-color: #ddd;
      border-radius: 2px;
    }
  }
}

// 调整点位详情弹窗样式，使其在列表面板上方
:deep(.van-popup--point-detail) {
  bottom: calc(40% + 10px) !important;
  max-height: calc(50% - 10px) !important;
}

// 增强点位详情和列表样式，使其在无遮罩时更加突出
.point-detail,
.location-list {
  background-color: #fff;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}
</style>
