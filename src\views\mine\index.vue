<template>
  <div class="mine">
    <Header title="个人中心"></Header>
    <div class="mine-container">
      <van-form @submit="onSubmit">
        <div class="title">登录信息</div>
        <van-cell-group inset>
          <van-field disabled v-model="role.loginName" label="用户登录名：" />
        </van-cell-group>
        <div class="title">个人信息</div>
        <van-cell-group inset>
          <van-field disabled v-model="role.orgName" label="单位名称：" />
          <van-field disabled v-model="role.orgName" label="处所科室：" />
          <van-field disabled v-model="role.name" label="用户姓名" />
          <van-field disabled v-model="role.roleNames" label="用户职务：" />
          <van-field
            disabled
            v-model="role.cellphone"
            label="手机号码："
            @click-right-icon="goToTelPage(role.cellphone)"
            :right-icon="role.cellphone ? 'phone-circle' : ''"
          />
          <van-field
            disabled
            v-model="role.telephone"
            label="办公电话："
            @click-right-icon="goToTelPage(role.telephone)"
            :right-icon="role.telephone ? 'phone-circle' : ''"
          />
          <van-field disabled value="v1.0.0" label="版本：" />
        </van-cell-group>
        <div style="margin: 16px">
          <van-button round block type="primary" native-type="submit"> 退出系统 </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
import { Notify, ImagePreview, Toast } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class mine extends Vue {
  public role: any = {};
  // 退出系统
  public onSubmit() {
    let params = {
      username: this.role.loginName,
      password: ''
    };
    this.$router.push('/home');
    localStorage.clear();
    localStorage.setItem('isLogout', '1'); // 标志主动退出登录
    sessionStorage.clear();
    apiServer.loginOut(params, (res) => {
      if (res.data.status === 200) {
        console.log('退出成功');
      }
    });
  }
  mounted() {
    this.$nextTick(() => {
      this.role = JSON.parse(localStorage.getItem('role')) || {};
      this.role.roleNames = this.role.roleRelationDTO && this.role.roleRelationDTO.roleNames ? this.role.roleRelationDTO.roleNames : '';
    });
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`;
  }
}
</script>

<style lang="less" scoped>
.mine {
  background: #f5f6f6;
  span {
    display: inline-block;
  }
  .title {
    padding: 10px 20px;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 15px;
      margin-right: 8px;
      vertical-align: middle;
      border-radius: 2px;
      background: #1967f2;
    }
  }
}
</style>
<style lang="less">
.mine {
  .van-field__right-icon {
    color: #13b2a1;
  }
}
</style>
