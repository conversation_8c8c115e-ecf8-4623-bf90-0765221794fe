<template>
  <div class="mine">
    <Header title="个人中心"></Header>
    <div class="mine-container">
      <van-form @submit="onSubmit">
        <div class="title">登录信息</div>
        <van-cell-group inset>
          <van-field disabled v-model="role.loginName" label="用户登录名：" />
        </van-cell-group>
        <div class="title">个人信息</div>
        <van-cell-group inset>
          <van-field disabled v-model="role.orgName" label="单位名称：" />
          <van-field disabled v-model="role.orgName" label="处所科室：" />
          <van-field disabled v-model="role.name" label="用户姓名" />
          <van-field disabled v-model="role.roleNames" label="用户职务：" />
          <van-field
            disabled
            v-model="role.cellphone"
            label="手机号码："
            @click-right-icon="goToTelPage(role.cellphone)"
            :right-icon="role.cellphone ? 'phone-circle' : ''"
          />
          <van-field
            disabled
            v-model="role.telephone"
            label="办公电话："
            @click-right-icon="goToTelPage(role.telephone)"
            :right-icon="role.telephone ? 'phone-circle' : ''"
          />
          <van-field disabled value="v1.0.0" label="版本：" />
          <van-field name="coordinate" label="坐标开关：">
            <template #input>
              <van-switch v-model="coordinateEnabled" size="20" />
            </template>
          </van-field>
          <div class="feedback-container">
            <van-field
              v-model="feedbackContent"
              name="feedback"
              label="问题反馈"
              type="textarea"
              placeholder="请输入您的问题或建议"
              rows="3"
              :autosize="{ maxHeight: 100 }"
              class="feedback-field"
            />
            <div size="small" type="primary" @click="submitFeedback" class="feedback-button">发送</div>
          </div>
        </van-cell-group>
        <div style="margin: 16px">
          <van-button round block type="primary" native-type="submit"> 退出系统 </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
import { Toast } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class mine extends Vue {
  public role: any = {};
  public coordinateEnabled: boolean = false;
  public feedbackContent: string = '';

  // 退出系统
  public onSubmit() {
    let params = {
      username: this.role.loginName,
      password: ''
    };
    this.$router.push('/home');
    localStorage.clear();
    localStorage.setItem('isLogout', '1'); // 标志主动退出登录
    sessionStorage.clear();
    apiServer.loginOut(params, (res: any) => {
      if (res.data.status === 200) {
        console.log('退出成功');
      }
    });
  }

  mounted() {
    this.$nextTick(() => {
      this.role = JSON.parse(localStorage.getItem('role') || '{}');
      this.role.roleNames = this.role.roleRelationDTO && this.role.roleRelationDTO.roleNames ? this.role.roleRelationDTO.roleNames : '';

      // 获取坐标开关状态
      const savedCoordinateState = localStorage.getItem('coordinateEnabled');
      this.coordinateEnabled = savedCoordinateState ? savedCoordinateState === 'true' : false;
    });
  }

  // 监听坐标开关状态变化
  @Watch('coordinateEnabled')
  onCoordinateEnabledChanged(val: boolean) {
    // 保存坐标开关状态
    localStorage.setItem('coordinateEnabled', val.toString());
    // 这里可以添加实际开启/关闭坐标的功能
    console.log('坐标状态已更改为:', val);
  }

  goToTelPage(tel: string) {
    window.location.href = `tel://${tel}`;
  }

  submitFeedback() {
    if (!this.feedbackContent.trim()) {
      Toast('请输入反馈内容');
      return;
    }

    // 这里可以添加提交反馈的实际逻辑
    Toast('反馈提交成功');
    console.log('提交的反馈内容:', this.feedbackContent);

    // 清空输入框
    this.feedbackContent = '';
  }
}
</script>

<style lang="less" scoped>
.mine {
  background: #f5f6f6;
  span {
    display: inline-block;
  }
  .title {
    padding: 10px 20px;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 15px;
      margin-right: 8px;
      vertical-align: middle;
      border-radius: 2px;
      background: #1967f2;
    }
  }

  .feedback-container {
    position: relative;
    margin-bottom: 16px;

    .feedback-field {
      max-height: 120px;
      overflow-y: auto;
    }

    .feedback-button {
      position: absolute;
      right: 16px;
      bottom: 8px;
      z-index: 1;
      height: 30px;
      
    }
  }
}
</style>
<style lang="less">
.mine {
  .van-field__right-icon {
    color: #13b2a1;
  }
}
</style>
