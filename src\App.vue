<template>
  <div id="app">
    <!-- keep-alive 页面缓存组件 -->
    <!--<keep-alive :include="['Sticky', 'Home']">-->
    <router-view></router-view>
    <!--</keep-alive>-->
    <div class="footer" v-if="$route.meta.navShow">
      <ul>
        <li v-for="item in footerList" :key="item.icon" :class="curFooter.icon === item.icon ? 'active' : ''" @click="handleFooter(item)">
          <van-image
            width="30px"
            height="30px"
            fit="contain"
            :src="
              curFooter.icon === item.icon
                ? require(`@/assets/images/home/<USER>
                : require(`@/assets/images/home/<USER>
            "
          />
          <span class="name">{{ item.name }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
// import Home from '@/view/cubedemo/home.vue';
// import gisAbility from '@/utils/gis/gisAbility';
import taskRequest from '@/api/webcross/task';
import zwwxSDK from '@/utils/zwwx';

@Component({
  components: {}
})
export default class App extends Vue {
  @Watch('$route', { immediate: true, deep: true })
  async onRouteChange(to, from) {
    // 保留原有功能
    this.curFooter = this.footerList.filter((v) => v.path === to.fullPath)[0];
    // 初始化政务微信SDK
    this.initZwwxSDK();
  }

  beforeCreate() {}

  created() {
    let _this = this;
    console.log('come in to app');
    // gisAbility.locationServer();

    // 初始化政务微信SDK
    this.initZwwxSDK();
  }

  /**
   * 初始化政务微信SDK
   */
  async initZwwxSDK() {
    if (!zwwxSDK.isWeiXin()) return;
    try {
      // 检查缓存是否有效
      const cacheValid = zwwxSDK.checkCache();
      console.log(`缓存状态: ${cacheValid ? '有效' : '无效或过期'}`);
      // 如果缓存无效，尝试刷新凭证
      if (!cacheValid) {
        try {
          await zwwxSDK.refreshCredentials();
        } catch (error) {
          console.warn('自动刷新凭证失败，将在使用时重新获取');
        }
      }

      if (zwwxSDK.isWeiXin()) {
        // 设置当前页面不包含hash的URL
        const url = location.href.split('#')[0];
        console.log(`当前页面URL: ${url}`);

        const apiList = ['getLocation', 'openLocation', 'scanQRCode', 'chooseImage', 'previewImage', 'uploadImage'];

        // 配置SDK
        await zwwxSDK.configWx(apiList);
      } else {
        console.log('非政务微信环境，跳过SDK初始化');
      }
    } catch (error) {
      console.error('政务微信SDK初始化失败:', error);
    }
  }

  public footerList: any = [
    {
      name: '首页',
      path: '/home',
      icon: 'ico-home',
      iconUrl: require(`@/assets/images/home/<USER>
    },
    {
      name: '待办事项',
      path: '/backlog',
      icon: 'ico-backlog',
      iconUrl: require(`@/assets/images/home/<USER>
    },
    {
      name: '通讯录',
      path: '/addressBook',
      icon: 'ico-address',
      iconUrl: require(`@/assets/images/home/<USER>
    },
    {
      name: '我的',
      path: '/mine',
      icon: 'ico-user',
      iconUrl: require(`@/assets/images/home/<USER>
    }
  ];
  public curFooter: any = {};
  private setUserObj(resultObj) {
    if (resultObj.IP) {
      window['g'].IP = resultObj.IP;
      console.log('IP setObj==>' + window['g'].IP);
    }
    if (resultObj.BASEIP) {
      window['g'].BASEIP = resultObj.BASEIP;
      console.log('IP setObj==>' + window['g'].BASEIP);
    }
  }
  public handleFooter(item) {
    this.curFooter = item;
    this.$router.push(item.path);
    window.sessionStorage.setItem('curFooterTab', JSON.stringify(item));
  }
  private getTeamNews() {
    let role = JSON.parse(localStorage.getItem('role')) || {};
    if (Object.keys(role).length > 0) {
      taskRequest.getTeamId({ userId: role.userId }, (res) => {
        if (res.data.status === 200) {
          localStorage.setItem('teamId', res.data.data);
        }
      });
    }
  }
  mounted() {
    if (JSON.parse(window.sessionStorage.getItem('curFooterTab'))) {
      this.curFooter = JSON.parse(window.sessionStorage.getItem('curFooterTab'));
    } else {
      [this.curFooter] = this.footerList;
    }
    window['eventBus'].$on('onCurFooter', (flag) => {
      if (flag) {
        [this.curFooter] = this.footerList;
      }
    });
    this.getTeamNews();
  }
}
</script>

<style lang="scss">
@import './assets/styles/index.scss'; // 全局自定义的css样式
#app {
  position: relative;
  .common-header {
    background: #1967f2;
    .van-nav-bar {
      background: unset;
      .van-icon,
      .van-nav-bar__title {
        background: unset;
        color: #fff;
        font-size: 16px;
      }
    }
    [class*='van-hairline']::after {
      border: none;
    }
  }
  .footer {
    position: fixed;
    bottom: 0;
    z-index: 100;
    width: 100%;
    height: 80px;
    border-top: 1px solid #e4e4e4;
    background: #fff;
    ul {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      height: 100%;
      li {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        text-align: center;
        span {
          margin-top: 5px;
          font-size: 12px;
          color: #666;
        }
        &.active span {
          color: #3284ff;
        }
      }
    }
  }
}
</style>
