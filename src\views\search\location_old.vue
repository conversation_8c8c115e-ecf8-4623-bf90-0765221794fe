<template>
  <div class="div_big">
    <van-nav-bar :title="detailInfo.eventTitle" left-text="返回" left-arrow @click-left="GoBack" @click-right="switchLayer">
      <template #right>
        <van-icon name="ellipsis" size="25" />
      </template>
    </van-nav-bar>
    <!-- 地图 start -->
    <div class="map" @click="guideLoc('gd')" v-if="locationInfo.getLocSuccess">
      <baseMap
        ref="baseMapRef"
        :mapId="`map_${new Date().getTime()}`"
        :longitude="locationInfo.locInfo.longitude"
        :latitude="locationInfo.locInfo.latitude"
      ></baseMap>
    </div>
    <!-- 地图 end -->
    <div class="layer-icon" @click="switchLayer">
      <van-icon name="points" size="24" />
      <div>图层</div>
    </div>
    <!-- 详情信息 -->
    <div class="footer-content">
      <searchInfo :result-info="detailInfo" type="event" @surround="toSurround" v-if="surroundVisible"></searchInfo>
      <simplified
        :result-info="detailInfo"
        type="event"
        v-if="!surroundVisible"
        @setType="handleType"
        @setLocation="resetLocation"
      ></simplified>
    </div>
    <!-- 图层 -->
    <van-popup v-model="layerShow" v-if="layerShow" position="top">
      <div class="slider_div">
        <van-tabs type="card" v-model="current" :ellipsis="false" color="#1f4ae3" @change="changeTab">
          <van-tab v-for="(item, index) in tabArr" :key="index" :title="item.label"></van-tab>
        </van-tabs>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import baseMap from '@/components/baseMap/baseMap.vue';
import Header from '@/views/common/header.vue';
import { Notify, ImagePreview, Toast } from 'vant';
import { customBrowserVersion, guide } from '@/utils/getLocation';
import infoRequest from '@/api/webcross/infoRequest';
import searchInfo from './searchInfo.vue';
import simplified from './simplified.vue';

import danger from './danger.vue';
@Component({
  components: {
    baseMap,
    Header,
    searchInfo,
    simplified,
    danger
  }
})
export default class teamSignIn extends Vue {
  locationInfo: any = {
    getLocSuccess: false, // 获取当前位置是否成功
    locInfo: {
      latitude: 30.6402,
      longitude: 114.3889,
      address: '当前位置'
    } as any // 当前位置
  };
  detailInfo: any = {}; // 详情信息
  type: any = 'event';
  tabArr: any = [];
  // 图层切换
  layerShow: any = false;
  switchLayer() {
    this.layerShow = !this.layerShow;
  }
  GoBack() {
    history.back();
  }
  current: any = null; //图层
  changeTab(item) {
    debugger;
    this.layerShow = false;
  }
  // 操作类型
  active: any = null;
  handleType(type) {
    this.active = type;
    if (type == 'event') {
      this.layerShow = true;
    }
  }
  changeLayer(val, item) {
    this.layerShow = false;
    this.$router.push({
      path: `/layerList/${this.$route.query.eventId}`,
      query: {
        title: val.label,
        layer: item.key,
        type: val.key
      }
    });
  }
  created() {
    const eventId = this.$route.query.eventId;
    this.type = this.$route.query.type;
    this.getInfo(eventId);
    const data = require(`./allLayers.json`);
    this.tabArr = data.allLayers;
  }
  surroundVisible: any = false;
  // 跳转周边
  toSurround() {
    this.surroundVisible = true;
  }
  closeInfo() {
    let _this = this;
    return;
    _this['$gsmdp'].initGoback(function () {
      // console.log("调用返回")
      try {
        _this['$gsmdp'].goBack(function () {});
      } catch (error) {
        // console.log("close error")
      }
    });
  }
  // 获取当前位置
  private guideLoc(signMap, loc) {
    let self = this;
    if (this.locationInfo.locInfo.latitude && this.locationInfo.locInfo.longitude) {
      //景点位置partnerAddress 景点经纬度lng lat
      var lng = this.locationInfo.locInfo.longitude;
      var lat = this.locationInfo.locInfo.latitude;
      let address = this.locationInfo.locInfo.address;
      let locInfo = {
        lng,
        lat,
        address
      };
      // guide('bd', locInfo);
    } else {
      Notify({ type: 'danger', message: '请先获取当前位置' });
    }
  }
  resetLocation(val) {
    this.locationInfo.locInfo.latitude = val.latitude;
    this.locationInfo.locInfo.longitude = val.longitude;
  }
  // 获取详情信息
  getInfo(id) {
    infoRequest.getEventById({ id: id }, (res) => {
      if (res.status === 200) {
        let data = res.data.data;
        this.locationInfo.locInfo.address = data.infoAddress;
        this.locationInfo.locInfo.latitude = data.latitude;
        this.locationInfo.locInfo.longitude = data.longitude;
        this.locationInfo.getLocSuccess = true;
        this.detailInfo = data;
      }
    });
  }
}
</script>
<style scoped lang="less">
.div_big {
  width: 100%;
  height: 100%;
  .map {
    width: 100%;
    height: 100%;
  }
  .layer-icon {
    position: fixed;
    right: 20px;
    top: 25%;
    width: 40px;
    padding: 5px 5px;
    text-align: center;
    border-radius: 10px;
    z-index: 999;
    background: #fff;
  }
  .tabs-box {
    position: fixed;
    top: 20px;
    left: 0;
    background: #fff;
    z-index: 999;
    padding: 10px;
  }
}
.slider_div {
  padding: 20px 10px;
}
.planBg {
  background: #e8f1f5;
  color: #3e5dd2 !important;
}
</style>
