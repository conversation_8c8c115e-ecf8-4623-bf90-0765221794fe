<template>
  <div class="testcorpper">
    <div class="div_file_model">
      <div class="file_title">
        <img src="../../assets/images/icons/fileicon.png" alt="" srcset="" />
        <span>附件</span>
        <span>{{`添加附件（单个文件大小${limitmax}M以内）`}}</span>
      </div>
    </div>
    <!-- <group :title="title==null?'附件':title"> -->
    <div style="padding: 0 3vw">
      <div class="image-choose">
        <div v-for="(item, index) in picslist" :key="index" style="float: left; position: relative">
          <img :src="item" @click="showImgPreview(index)" />
          <span class="close_picture" @click="removeImg('0', index, item)">
            <img src="../../assets/images/icons/closePicture.png" style="width: 12px; height: 12px" alt="" />
          </span>
          <!-- <icon type="cancel" style="position: absolute;right: -8px;top: -6px;"  @click.native="removeImg('0',index,item.fileUrl)" ></icon> -->
        </div>
        <div v-for="(vitem, vindex) in videolist" :key="vindex" style="float: left; position: relative">
          <img :src="vitem.msrc" @click="openMedia(1, vitem)" />
          <span class="close_picture" @click="removeImg('1', vindex, vitem.fileUrl)">
            <img src="../../assets/images/icons/closePicture.png" style="width: 12px; height: 12px" alt="" />
          </span>
          <span class="show_vedio" @click="removeImg('1', vindex, vitem.fileUrl)">
            <img src="../../assets/images/icons/picture_icon_video.png" style="width: 25px; height: 19px" alt="" />
          </span>
          <!-- <icon type="cancel" style="position: absolute;right: -8px;top: -6px;"  @click.native="removeImg('1',vindex,vitem.fileUrl)" ></icon> -->
        </div>
        <div v-for="(aitem, aindex) in audiolist" :key="aindex" style="float: left; position: relative">
          <img :src="aitem.msrc" @click="openMedia(2, aitem)" />
          <!-- <icon type="cancel" style="position: absolute;right: -8px;top: -6px;"  @click.native="removeImg('2',aindex,aitem.fileUrl)" ></icon> -->
          <span class="close_picture" @click="removeImg('2', aindex, aitem.fileUrl)">
            <img src="../../assets/images/icons/closePicture.png" style="width: 12px; height: 12px" alt="" />
          </span>
        </div>
        <div v-for="(ditem, dindex) in documentlist" :key="dindex" style="float: left; position: relative">
          <img :src="ditem.msrc" @click="openMedia(3, ditem)" />
          <!-- <icon type="cancel" style="position: absolute;right: -8px;top: -6px;"  @click.native="removeImg('2',dindex,ditem.fileUrl)" ></icon> -->
          <span class="close_picture" @click="removeImg('3', dindex, ditem.fileUrl)">
            <img src="../../assets/images/icons/closePicture.png" style="width: 12px; height: 12px" alt="" />
          </span>
        </div>

        <div
          v-if="videolist.length + audiolist.length + picslist.length + documentlist.length <= limitSize"
          class="add-image"
          @click="changeFile"
        >
          <!-- <input id="file_input"
                            ref="takepicture"
                            style="display: none"
                            @change="getFile"
                            type="file"
                            accept="image/*"
                            capture="camera"
                            multiple="multiple"
                        > -->
          附件
          <!-- <i class="icon ion-plus-round "></i> -->
        </div>
      </div>
      <div class="file_select">
        <span @click="clickMenu('menu1')">
          <img src="../../assets/images/icons/widget_upload_pic.png" alt="" srcset="" />
          <font>上传图片</font>
        </span>
        <span @click="clickMenu('menu2')">
          <img src="../../assets/images/icons/widget_upload_video.png" alt="" srcset="" />
          <font>上传视频</font>
        </span>
        <span @click="clickMenu('menu3')">
          <img src="../../assets/images/icons/widget_upload_audio.png" alt="" srcset="" />
          <font>上传语音</font>
        </span>
        <span @click="clickMenu('menu4')">
          <img src="../../assets/images/icons/widget_upload_file.png" alt="" srcset="" />
          <font>上传文件</font>
        </span>
      </div>
    </div>
    <!-- </group> -->
    <!-- <previewer :list="picslist"  :options="options"></previewer> -->
    <van-image-preview v-model="showPreview" ref="preview" :images="picslist" @change="onChangeImgView">
      <!-- <template v-slot:index>第{{ index }}页</template> -->
    </van-image-preview>
    <!-- <actionsheet  v-model="showSelect" :menus="menuSelect" theme="android" @on-click-menu="clickMenu" @on-after-hide="afterHide" @on-after-show="afterShow">
    </actionsheet> -->
  </div>
</template>
<script>
import { Loading } from 'vant';
//  import { Icon, Previewer } from "vux";
let formData = new FormData();
export default {
  props: ['title', 'typeflag', 'upNumber'],
  components: {},
  data() {
    return {
      fileList: [], //原始附件数组
      picslist: [], //放大附件数组
      index: 0,
      videolist: [],
      audiolist: [],
      documentlist: [],
      uplist: [],
      upvideolist: [],
      showPreview: false,
      showSelect: false,
      limitSize: 5,
      limitmax:10,
      menuSelect: {
        menu1: '图片',
        menu2: '视频',
        menu3: '音频'
      },
      options: {
        isClickableElement: function (el) {
          return /previewer-delete-icon/.test(el.className);
        }
      }
    };
  },
  methods: {
    getStorageSync() {
      let isAndroid = this.$isAndroid();
      if (isAndroid) {
        let res = this.$gsmdp.getStorageSync({ key: 'extra_max_size' });
       this.limitmax=JSON.parse(res).result.data
       console.log(JSON.parse(res).result.data);
      }
    },
    onChangeImgView(index) {
      this.index = index;
    },
    removeImg(type, index, fileUrl) {
      let vm = this;
      /* let file_input= document.getElementById("file_input");
			console.log(file_input.currentTarget)
			console.log(file_input.outerHTML) */
      console.log(type + '====>' + index + '===>' + fileUrl);
      let contentStr = '';
      if (type == '0') {
        contentStr = '确认删除该图片？';
      } else if (type == '1') {
        contentStr = '确认删除该视频？';
      } else if (type == '2') {
        contentStr = '确认删除该录音？';
      } else if (type == '3') {
        contentStr = '确认删除该文件？';
      }
      console.log('contentStr===>' + contentStr);
      vm.$dialog
        .confirm({
          title: '',
          message: contentStr
        })
        .then(() => {
          console.log(JSON.stringify(vm.picslist));
          console.log(JSON.stringify(vm.fileList));
          if (type == '0') {
            console.log('del picslist fileList');
            vm.picslist.splice(index, 1);
            vm.fileList.splice(index, 1);
            console.log('del picslist fileList success');
          } else if (type == '1') {
            vm.videolist.splice(index, 1);
          } else if (type == '2') {
            vm.audiolist.splice(index, 1);
          } else if (type == '3') {
            vm.documentlist.splice(index, 1);
          }
          try {
            console.log('param==>delete img success ' + fileUrl);
            window.android.removePhoto(fileUrl);
          } catch (e) {
            console.log('param==> delete img error');
            //TODO handle the exception
          }
        })
        .catch(() => {
          // on cancel
        });
    },
    openMedia(type, item) {
      let _this = this;
      console.log('type==>' + type + '==>');
      console.log(item);
      try {
        //window.android.openlocalFile(item.fileUrl);
        if (type == 1) {
          this.$gsmdp.previewVideo({
            videoPath: item.fileUrl,
            success(result) {
              console.log('success' + result);
            },
            fail(err) {
              console.log(err);
            }
          });
        } else if (type == 2) {
          // this.$gsmdp.previewVideo({
          // 	videoPath:item.fileUrl,
          // 	success(result) {
          // 		console.log('success' + result)
          // 	},
          // 	fail(err) {
          // 		console.log(err)
          // 	}
          // })
          console.log('打开audio==>' + item.fileUrl);
          this.$gsmdp.previewAudio({
            filePath: item.fileUrl,
            success: function (res) {
              console.log('打开音频成功');
            }
          });
        } else if (type == 3) {
          console.log('打开document==>' + item.fileUrl);
          _this['$gsmdp'].openFile({
            filePath: item.fileUrl,
            success: function (res) {
              //alert(JSON.stringify(res))
              console.log('打开文档成功');
            }
          });
        }
      } catch (e) {
        console.log('param==> open Gallery error');
        //TODO handle the exception
      }
    },
    clickMenu(item) {
      console.log('param==> item is ==>' + item);
      let _this = this;
      try {
        console.log('close menu...');
        this.showSelect = false;
        if (item && item == 'menu1') {
          //打开android相册功能
          this.$gsmdp.chooseImage({
            key: {
              count: _this.limitSize,
              sizeType: ['original', 'compressed'],
              sourceType: ['album', 'camera']
            },
            success(res) {
              const tempFiles = res.tempFiles;
              let obj = { result: tempFiles };
              _this.getMediasSdk('1', obj);
            }
          });
          //window.android.openGallery(1,this.limitSize);
        } else if (item && item == 'menu2') {
          //打开android视频功能
          // window.android.openGallery(2,this.limitSize);
          this.$gsmdp.chooseVideo({
            key: {
              sourceType: ['camera', 'album'],
              compressed: true
            },
            success(res) {
              const tempFiles = res.tempFiles;
              let obj = { result: tempFiles };
              _this.getMediasSdk('2', obj);
            }
          });
        } else if (item && item == 'menu3') {
          //打开android录音功能
          // window.android.openGallery(3,this.limitSize);
          this.$gsmdp.chooseAudio({
            key: {
              count: _this.limitSize,
              maxDuration: 30
            },
            success(res) {
              console.log(res);
              const tempFiles = res.tempFiles;

              let obj = { result: tempFiles };
              _this.getMediasSdk('3', obj);
            }
          });
          // this.$gsmdp.chooseFile({
          // 	suffix: 'amr,mp3',
          // 	maxCount: 1,
          // 	type: 0,
          // 	success: (res) => {
          // 		let obj ={"result":res}
          // 		_this.getMediasSdk("3",obj)
          // 	},
          // 	fail(err) {
          // 		alert('err' + err);
          // 	}
          // });
        } else if (item && item == 'menu4') {
          //打开android文件功能
          this.$gsmdp.chooseFile({
            suffix: 'doc,docx,xlsx,xls',
            maxCount: _this.limitSize,
            type: 0,
            success: (res) => {
              let obj = { result: res };
              _this.getMediasSdk('4', obj);
            },
            fail(err) {
              alert('err' + err);
            }
          });
        } else {
          console.log('param==> item is error ==>' + item);
        }
      } catch (e) {
        console.log('param==> open Gallery error');
        //TODO handle the exception
      }
    },
    afterHide() {
      console.log('afterHide');
    },
    afterShow() {
      console.log('afterShow');
    },
    changeFile: function () {
      //项目需求关闭组件弹出
      return;
      //this.$refs.takepicture.dispatchEvent(new MouseEvent("click"));
      let _this = this;
      let listsize = _this.uplist.length + _this.upvideolist.length;
      _this.uplist = [];
      _this.upvideolist = [];
      if (listsize >= _this.limitSize) {
        this.$vux.toast.show({
          text: '附件个数不能大于' + _this.limitSize + '个',
          type: 'cancel'
        });
      } else {
        try {
          //打开android相册功能
          //window.android.openGallery(1,9);
          if (this.typeflag) {
            this.showSelect = true;
          } else {
            window.android.openGallery(1, this.limitSize);
          }
        } catch (e) {
          console.log('param==> open Gallery error');
          //TODO handle the exception
        }
      }
    },
    getMediasSdk(type, medias) {
      let _this = this;
      //console.log("param==>"+medias);
      console.log('param==>' + JSON.stringify(medias));
      //alert(JSON.stringify(medias))
      console.log('type==>' + type + '=typeflag==>' + _this.typeflag);
      //alert("type==>"+type+"=typeflag==>"+_this.typeflag)
      //如果有值需要传图片视频与录音文件并由android进行上传操作
      if (_this.typeflag) {
        if (type == '1') {
          // _this.picslist = [];
          let listsize = _this.videolist.length + _this.audiolist.length + _this.documentlist.length;
          if (medias && medias.result) {
            console.log('其它数量==>' + listsize + '==>android数据==>' + medias.result.length);
            if (listsize + medias.result.length > _this.limitSize) {
              _this.$showBottomToast('附件个数不能大于' + _this.limitSize + '个');
            } else {
              //alert(JSON.stringify(medias.result))
              for (var index in medias.result) {
                let item = medias.result[index];
                console.log('param==>file://' + item);
                let base64 = 'http://gsafety_img_storage' + item.path;
                base64 = encodeURI(base64);
                _this.picslist.push(base64);
              }
            }
          }
        } else if (type == '2') {
          for (var i = 0; i < _this.videolist.length; i++) {
            let item = _this.videolist[i];
            try {
              console.log('param==>delete img success ' + item.fileUrl);
              window.android.removePhoto(item.fileUrl);
            } catch (e) {
              console.log('param==> delete img error');
              //TODO handle the exception
            }
          }
          // _this.videolist = [];
          let listsize = _this.picslist.length + _this.audiolist.length + _this.documentlist.length;
          if (medias && medias.result) {
            //alert(JSON.stringify(medias.result))
            console.log('其它数量==>' + listsize + '==>android数据==>' + medias.result.length);
            if (listsize + medias.result.length > _this.limitSize) {
              _this.$showBottomToast('附件个数不能大于' + _this.limitSize + '个');
            } else {
              for (var index in medias.result) {
                let item = medias.result[index];
                _this.videolist.push({
                  msrc: 'http://gsafety_img_storage' + item.compressPath,
                  fileUrl: item.path
                });
              }
            }
          }
        } else if (type == '3') {
          // _this.audiolist = [];
          let listsize = _this.picslist.length + _this.videolist.length + _this.documentlist.length;
          if (medias && medias.result) {
            console.log('其它数量==>' + listsize + '==>android数据==>' + medias.result.length);
            if (listsize + medias.result.length > _this.limitSize) {
              _this.$showBottomToast('附件个数不能大于' + _this.limitSize + '个');
            } else {
              for (var index in medias.result) {
                let item = medias.result[index];
                _this.audiolist.push({
                  msrc: require('../../assets/images/icons/audioshow.png'),
                  fileUrl: item.path
                });
              }
            }
          }
        } else if (type == '4') {
          // _this.documentlist = [];
          let listsize = _this.picslist.length + _this.videolist.length + _this.documentlist.length;
          if (medias && medias.result) {
            console.log('其它数量==>' + listsize + '==>android数据==>' + medias.result.length);
            if (listsize + medias.result.length > _this.limitSize) {
              _this.$showBottomToast('附件个数不能大于' + _this.limitSize + '个');
            } else {
              for (var index in medias.result) {
                let item = medias.result[index];
                _this.documentlist.push({
                  msrc: require('../../assets/images/icons/filemessage.png'),
                  fileUrl: item
                });
              }
            }
          }
        }
        _this.postBack();
      } else {
        //执行原始上传操作
        // _this.picslist = [];
        // _this.fileList = [];
        if (medias && medias.result) {
          if (medias.result.length > _this.limitSize) {
            _this.$showBottomToast('附件个数不能大于' + _this.limitSize + '个');
          } else {
            for (var index in medias.result) {
              let item = medias.result[index];
              console.log('param==>file://' + item);
              let base64;
              try {
                base64 = window.android.file2Base64(item);
              } catch (e) {
                base64 = 'base64isnull';
              }
              if (base64 != 'base64isnull') {
                _this.picslist.push({
                  msrc: 'data:image/png;base64,' + base64,
                  src: 'data:image/png;base64,' + base64,
                  fileUrl: item
                });
                console.log('item name===>' + item.substr(item.length - 7, item.length));
                let fileobj = _this.dataURLtoFile('data:image/png;base64,' + base64, item.substr(item.length - 7, item.length));
                _this.fileList.push(fileobj);
              }
              // _this.picslist.push({
              // 	msrc: "file://"+item,
              // 	src: "file://"+item
              // })
            }
          }

          _this.postBack();
        }
      }
    },
    setFileObj(fileObj) {
      console.log('come in');
      console.log(fileObj);
      console.log('fileObj==>');
      //console.log(JSON.stringify(fileObj))
      this.fileList.push(fileObj);
    },
    showImgPreview(index) {
      //this.preview
      //点击图片放大
      //this.$refs.preview.show(index);
      this.showPreview = true;
    },
    getCurrentItem() {
      let itemIndex = this.$refs.preview.getCurrentIndex();
      console.log('currindex==>' + itemIndex);
      console.log(this.$refs.preview.photoswipe);
    },
    closePreview() {
      this.$refs.preview.close();
    },
    GetAriaHidden() {
      let ariaHidden;
      try {
        ariaHidden = document.querySelector('.vux-previewer').getAttribute('aria-hidden');
        if (ariaHidden && ariaHidden == 'false') {
          ariaHidden = false;
        } else {
          ariaHidden = true;
        }
      } catch (error) {
        console.warn(error);
        ariaHidden = true;
      }
      return ariaHidden;
    },
    getFile: function (e) {
      //附件数据处理
      console.log(e);
      let vm = this;
      try {
        let files = e.currentTarget.files;
        if (files.length > 0) {
          /* for (let i = 0; i < files.length; i++) {
                        if (vm.fileList.length < 5) {
                            vm.fileList.push(files[i]);
                            let img = window.URL.createObjectURL(files[i]);
                            vm.picslist.push({
                                msrc: img,
                                src: img
                            });
                        }
                    } */
          vm.imgList = [];
          let newfiles = [];
          for (let k = 0; k < files.length; k++) {
            lrz(files[k], {
              width: 500,
              quality: 1 //自定义使用压缩方式
            })
              .then(function (rst) {
                console.log(rst);
                vm.picslist.push({ msrc: rst.base64, src: rst.base64 });
                vm.imgList.push(rst.base64);
                newfiles.push(rst.file);
                let fileobj = vm.dataURLtoFile(rst.base64, 'fff');
                console.log(fileobj);
                //成功时执行
              })
              .catch(function (error) {
                //失败时执行
              })
              .always(function () {
                //不管成功或失败，都会执行
              });
          }
          vm.fileList = newfiles;

          vm.postBack();
        }
      } catch (e) {
        alert(e);
      }
    },
    //将base64转换为文件对象
    dataURLtoFile(dataurl, filename) {
      var arr = dataurl.split(',');
      console.log(arr[0]);
      var mime = arr[0].match(/:(.*?);/)[1];
      var bstr = atob(arr[1]);
      var n = bstr.length;
      console.log('文件大小==' + n);
      var u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      let file = new File([u8arr], filename, { type: mime });
      console.log('filename==>' + file.name);
      //转换成file对象
      return file;
      //转换成成blob对象
      //return new Blob([u8arr],{type:mime});
    },
    //将图片转换为Base64
    getImgToBase64(url, callback) {
      var canvas = document.createElement('canvas'),
        ctx = canvas.getContext('2d'),
        img = new Image();
      img.crossOrigin = 'Anonymous';
      img.onload = function () {
        canvas.height = img.height;
        canvas.width = img.width;
        ctx.drawImage(img, 0, 0);
        var dataURL = canvas.toDataURL('image/png');
        callback(dataURL);
        canvas = null;
      };
      img.src = url;
    },
    postBack() {
      let _this = this;
      _this.uplist = [];
      _this.upvideolist = [];
      //重新更新要上传的数组
      _this.picslist.forEach(function (item, index) {
        let newpath = item.replace('http://gsafety_img_storage', '');
        _this.uplist.push(newpath);
      });
      _this.videolist.forEach(function (item, index) {
        _this.upvideolist.push(item.fileUrl);
      });
      _this.audiolist.forEach(function (item, index) {
        _this.uplist.push(item.fileUrl);
      });
      _this.documentlist.forEach(function (item, index) {
        _this.uplist.push(item.fileUrl);
      });

      //数据回传
      this.$emit('fliws', this.fileList);
      console.log('更新字符串图片=' + JSON.stringify(this.uplist));
      console.log('更新字符串视频=' + JSON.stringify(this.upvideolist));
      let paramObj = { uplist: this.uplist, upvideolist: this.upvideolist };
      //paramObj={"uplist":["/storage/emulated/0/1582544844680431.jpg"],"upvideolist":[]}
      console.log('set up list ==>' + JSON.stringify(paramObj));
      //alert(JSON.stringify(paramObj))
      this.$emit('uplist', paramObj);
    },
    clearPhoto() {
      try {
        window.android.clearPhotos();
      } catch (e) {
        console.log('param==>clearphotos error');
        //TODO handle the exception
      }
    }
  },

  created: function () {
    console.log('我找到地方了吗');
    this.getStorageSync();
    if (this.upNumber) {
      this.limitSize = parseInt(this.upNumber);
    }
    //this.picslist=["http://i51.top/macau_public/yq/1.png","http://i51.top/macau_public/yq/1.png","http://i51.top/macau_public/yq/1.png","http://i51.top/macau_public/yq/1.png"]
    //this.videolist=[{"msrc":"http://i51.top/macau_public/yq/1.png"}]
  },
  mounted: function () {
    let _this = this;
    this.clearPhoto();
    //alert("typeflag=>"+_this.typeflag)
    window.getMedias = this.getMedias;
    window.setFileObj = this.setFileObj;
    // let intervalint= setInterval(() => {
    // 	console.log(_this.GetAriaHidden())
    // }, 3000);
    // setTimeout(() => {
    // 	clearInterval(intervalint)
    // }, 15000);
  }
};
</script>
<style  scoped lang="scss">
.testcorpper {
  width: 100%;
  background: #fff;
  .div_file_model {
    width: 95%;
    margin-left: 5%;
    border-top: #c7c7c7 solid 1px;
    .file_title {
      width: 100%;
      height: 50px;
      line-height: 50px;
      border-bottom: #c7c7c7 solid 1px;
      img {
        width: 20px;
        vertical-align: middle;
        margin-top: -5px;
      }
      > span:nth-of-type(1) {
        display: inline-block;
        width: 20%;
        font-size: 16px;
        line-height: 50px;
        padding-left: 10px;
      }
      > span:nth-of-type(2) {
        display: inline-block;
        width: 70%;
        font-size: 13px;
        line-height: 50px;
        padding-left: 10px;
        text-align: right;
        color: grey;
      }
    }
  }
  .image-choose {
    margin: 10px auto;
    overflow: hidden;
    .close_picture {
      width: 15px;
      height: 15px;
      display: block;
      position: absolute;
      top: 4px;
      right: 5px;
      opacity: 0.9;
      img {
        width: 100%;
      }
    }
    .show_vedio {
      width: 25px;
      display: block;
      position: absolute;
      top: 40px;
      left: 5px;
      opacity: 1;
      img {
        width: 100%;
      }
    }
    img {
      margin-top: 5px;
      margin-left: 0.7vw;
      width: 18vw;
      height: 16vw;
      vertical-align: top;
      border-radius: 6px;
    }
    .add-image {
      display: inline-block;
      margin-top: 5px;
      margin-left: 1vw;
      width: 18vw;
      height: 16vw;
      line-height: 16vw;
      color: #20a0e7;
      font-size: 16px;
      /* background: #eee; */
      border-radius: 6px;
      text-align: center;
      border: dashed 1px #1b9fe7;
      .iconfont {
        font-size: 36px;
        color: #ccc;
      }
    }
  }
  .file_select {
    height: 18vh;
    padding-top: 6%;
    margin-top: 4%;
    border-top: solid 1px #c5c5c5;
    span {
      width: 20%;
      height: 80%;
      margin-left: 4%;
      display: inline-block;
      font {
        width: 100%;
        display: block;
        text-align: center;
        margin-top: -4px;
      }
      img {
        width: 60%;
        margin-left: 20%;
      }
    }
  }
}
</style>
<style>
.testcorpper .weui-cells:before {
  border-top: none;
}
</style>

