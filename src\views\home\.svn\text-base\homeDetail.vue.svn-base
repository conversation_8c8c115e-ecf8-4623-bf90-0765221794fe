<template>
  <div class="detail_big">
    <x-header :left-options="{backText: '',preventGoBack: true}" @on-click-back='goBack' style="position: fixed; top: 0px;width: 100%; z-index: 10;background:#1FB1FE;">事件详情</x-header>
    <div class="detail_centent">
      <div class="detail_title">{{detailInfo.emergencyTitle}}</div>
      <div class="detail_attention">
        <span class="big_attention">
          <span class="attention_top">
					<mu-checkbox v-model="isLike" uncheck-icon="favorite_border" @change="checkChange" checked-icon="favorite" ></mu-checkbox>
					</span>
          <span class="attention_buttom">{{isLike==true?"已关注":"关注"}}</span>
        </span>
      </div>
      <div class="detail_charts">
        <div class="detail_child_title">
          <img src="../../assets/imgs/icon/dengpao.png" alt="">
          <font style="font-size:15px">&nbsp;&nbsp;事件博文数据趋势</font>
        </div>
        <div class="detail_charts_time"> <font>事件起始抓取时间：{{chartsOptionEvent.data[0].fileTime}}</font></div>
        <div class="charts_one">
         
          <v-chart :data="chartsOptionEvent.data" :showTooltip="true"   @on-render="renderVChart" :height="200" :width="350" >
            <v-scale x :tick-count="5" alias="时间"  :showTooltip="true"  field="fileTimeStr"  />
            <v-scale y :tick-count="0" alias="博文数量" :showTooltip="true"  :formatter="formatterFun" :min="0"  field="contentQuantity"  />
            <v-line shape="smooth" :colors="chartsOptionEvent.gradient" :showTooltip="true" />
            <v-guide type="tag" :showTooltip="true" :options="chartsOptionEvent.tag" />
            <v-tooltip :show-item-marker="false"  :showTooltip="true"   :onShow="onShow"/>
            <v-point :style="{ stroke: '#fff', lineWidth: 1 }" shape="line" />
            <v-area shape="smooth" :colors="chartsOptionEvent.gradient"/>
            <v-axis x :label="labelFormat"/>
          </v-chart>
        </div>
        <div class="detail_child_title">
           <img src="../../assets/imgs/icon/dengpao.png" alt="">
          <font style="font-size:15px">&nbsp;&nbsp;微博用户讨论趋势</font> </div>
        <div class="charts_one">
          <v-chart :data="chartsOptionUser.data" :height="200" :width="350" >
            <v-scale x :tick-count="5" :min="0"  field="fileTimeStr"  />
            <v-scale y alias="讨论数" field="discussQuantity"  />
            <v-line shape="smooth" :colors="chartsOptionUser.gradient"/>
            <v-guide type="tag" :options="chartsOptionUser.tag" />
             <v-tooltip :show-item-marker="false"  :onShow="onShow"/>
            <v-point :style="{ stroke: '#fff', lineWidth: 1 }" shape="smooth" />
            <v-area shape="smooth" :colors="chartsOptionUser.gradient"/>
            <v-axis x :label="labelFormat"/>
          </v-chart>
        </div>
      </div>
      <!-- 媒体最新报道 -->
      <div class="media_new_big">
        <div class="detail_child_title">
           <img src="../../assets/imgs/icon/erji.png" alt="">
          <font style="font-size:15px">&nbsp;&nbsp;媒体最新报告</font> 
          <a class="a_more" href="javascript:void(0)" @click="showMoreList(0)">更多报道</a>
        </div>
        <div class="media_content">
            <ul>
              <li v-for="(item,index) in detailInfo.vvoiceTopTwoInfo" :key="index">
                <div class="media_list_top">
                  <img src="../../assets/imgs/icon/wbdw.png" /><font>{{item.username}}</font> 
                  <mu-icon value="keyboard_arrow_down" class="arraw_show" ></mu-icon>
                </div>
                <div class="media_list_center">
                  <font>{{item.label}}</font>
                </div>
                <div class="media_list_buttom">
                  <font>{{item.content}}</font>
                </div>
              </li>
             
            </ul>
        </div>
      </div>
      <!-- 民众声音 -->
      <div class="media_new_big">
        <div class="detail_child_title">
           <img src="../../assets/imgs/icon/erji.png" alt="">
          <font style="font-size:15px">&nbsp;&nbsp;民众声音</font> 
          <a class="a_more" href="javascript:void(0)" @click="showMoreList(1)">更多报道</a>
        </div>
        <div class="media_content">
            <ul>
              <li v-for="(item,index) in detailInfo.peopleVoiceTopTwoInfo" :key="index">
                <div class="media_list_top">
                  <img src="../../assets/imgs/icon/ptmz.png" /> <font>{{item.username}}</font>
                  <mu-icon value="keyboard_arrow_down" class="arraw_show" ></mu-icon>
                </div>
                <div class="media_list_center">
                  <font>{{item.label}}</font>
                </div>
                <div class="media_list_buttom">
                  <font>{{item.content}}</font>
                </div>
              </li>
            </ul>
        </div>
      </div>
      <popup :hide-on-blur=false  v-model="showMore" width="100%" position="right">
        <moreList v-if="showMore"  :queryMore="queryMore" @close="closeMore"></moreList>
      </popup>
      </div>
    
    
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { XHeader,Popup,VChart, VGuide, VLine, VArea, VScale,VPoint,VAxis,VTooltip } from 'vux';
import moreList from './moreEvent.vue';

@Component({
  components: {
    XHeader,
    Popup,VChart, 
    VGuide,
    VLine,
    VArea,
    VScale,
    VPoint,
    VAxis,
    VTooltip,
    moreList
  },
  props:["queryMap"]
})
export default class Home extends Vue {
  tabData = [{"code":"0","name":"全部"},{"code":"0","name":"我的关注"}];
  result: any = [undefined, undefined];
	isLike:any =false;
  selected: any = 0;
  refreshing: any = false; 
  loading: any = false;
  detailInfo: any={};
  showMore: any = false;
  queryMore: any = {};
  onShow(ev,ch) {
    let now_item=[];
    ev.items.splice(0,1)
    var items = ev.items;
    items.forEach(function(item,index){
      console.log(item)
      item.name="事件抓取时间："+item.origin.fileTime+" ";
      item.value="";
    })
  }
   scaleFormat (text, index, total) {
     
      //return "<span>测试</span>"
   }
  labelFormat (text, index, total) {
					const cfg = {
						 //textAlign: 'start',
						//textBaseline: 'middle',
						//rotate: Math.PI / 5,
          };
					// 第一个点左对齐，最后一个点右对齐，其余居中，只有一个点时左对齐
					 if (index === 0) {
					 	cfg['textAlign'] = 'start';
					 }
					 if (index > 0 && index === total - 1) {
					 	cfg['textAlign'] = 'end';
					 }
					 //cfg.text = text + '%';  // cfg.text 支持文本格式化处理
					return cfg;
        }
    renderVChart ({ chart }) {
        // do what you want
      }
    formatterFun(ev){
      return ev;
    }
    chartsOptionEvent: any = {
      gradient: [
          [0, '#5885E9'],
          [0.5, '#A1BAF3'],
          [1, '#D2DEF2']
        ],
        tag: {
          position: [ 2017, 30.12 ],
          content: '30.12',
          direct: 'tl',
          offsetY: -5,
          background: {
            fill: '#8659AF'
          },
          pointStyle: {
            fill: '#8659AF'
          }
        },
        showTitle:true,
        data: [ { year: 2000, age: 27.2 }]
    }
    chartsOptionUser: any = {
      gradient: [
          [0, '#5885E9'],
          [0.5, '#A1BAF3'],
          [1, '#D2DEF2']
        ],
        tag: {
          position: [ 2017, 30.12 ],
          content: '30.12',
          direct: 'tl',
          offsetY: -5,
          background: {
            fill: '#8659AF'
          },
          pointStyle: {
            fill: '#8659AF'
          }
        },
        data: [ { year: 2000, age: 27.2 }]
    }
   
    goBack(){
      this.$emit("close");
    }
    showMoreList(_index){
      let _this=this;
      console.log("show more ...")
      _this.queryMore=_this['queryMap'];
      if(_index==0){
        _this.queryMore.type="1";
        console.log("媒体最新")
      }else if(_index==1){
        _this.queryMore.type="0";
        console.log("民众声音")
      }
      this.showMore=true;
    }
    closeMore(){
       this.showMore=false;
    }
		checkChange(){
			console.log("list state is ==>"+this.isLike)
      this.attentionRequest();
		}
    attentionRequest(){
      let _this=this;
      var p1 = new Promise(function (resolve, reject) {
        //_this.loading = true;
        try {
          console.log(_this['queryMap'].id)
          let eventId=_this['queryMap'].id;
          //eventId="525";
          let followed=1;
          if(_this.isLike){
            followed=1;
          }else{
            followed=0;
          }
          let param={"eventId":eventId,"followed":followed}
          let res = _this['$apiServer'].commonRequest('api/event/follow/v1',param);
          console.log(res)
          resolve(res)
        } catch (error) {
          reject("500");
        }
      });
      Promise.all([p1]).then(function (results) {
        let resultsObj=results[0];
        if(resultsObj==null||resultsObj['status']!=200){
          console.log("处理异常");
        }else{
          if(_this.isLike){
            console.log("关注成功")
          }else{
            console.log("取关成功")
          }
            
        }
      }).catch(function (status) {
        console.log("处理异常")
      });
    }
    queryRequest(){
      let _this=this;
      var p1 = new Promise(function (resolve, reject) {
        //_this.loading = true;
        try {
          console.log(_this['queryMap'].id)
          const params = new URLSearchParams();
          params.append('id', _this['queryMap'].id);
          //params.append('id', "525");
          let res = _this['$apiServer'].commonRequest('api/event/details/list/v1',params);
          console.log(res)
          resolve(res)
        } catch (error) {
          reject("500");
        }
      });
      Promise.all([p1]).then(function (results) {
        let resultsObj=results[0];
        if(resultsObj==null||resultsObj['status']!=200){
          console.log("处理异常");
        }else{
          console.log(resultsObj)
          _this.detailInfo=resultsObj['data'];
          if(_this.detailInfo.followed==1){
            _this.isLike=true;
          }
          let discussAmountPerHour=_this.detailInfo.discussAmountPerHour;
          if(discussAmountPerHour&&discussAmountPerHour.length>0){
            discussAmountPerHour.forEach(function(_item,_index){
              _item.discussQuantity=parseInt(_item.discussQuantity);
               _item.fileTimeStr=_this.getFormatTime(_item.fileTime);
            })
          }
          let textAmountPerHour=_this.detailInfo.textAmountPerHour;
          if(textAmountPerHour&&textAmountPerHour.length>0){
            textAmountPerHour.forEach(function(_item,_index){
              _item.contentQuantity=parseInt(_item.contentQuantity);
               _item.fileTimeStr=_this.getFormatTime(_item.fileTime);
            })
          }
          _this.chartsOptionUser.data=discussAmountPerHour;
          _this.chartsOptionEvent.data=textAmountPerHour;
          console.log(textAmountPerHour)
          console.log(JSON.stringify(textAmountPerHour))
          
        }
      }).catch(function (status) {
        console.log("处理异常")
      });
    }
    getFormatTime(val){
      let rTime="";
      if(val){
        let setTime=new Date(val);
        rTime=this.setZero(setTime.getDate())+"-"+this.setZero(setTime.getHours());
      }
      return rTime;
    }
    setZero(num){
      num=num+"";
      if(num&&num.length==1){
        return "0"+num;
      }else{
        return num;
      }
    }
    created(){
      let _this=this;
      console.log("come in detail")
      _this.queryRequest();
      
    }
    

}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%
}
.detail_centent{
  margin-top: 45px;
  height: calc( 100% - 44px );
  background: white;
}
.detail_title{
  font-size: 16px;
  font-weight: bold;
   color: #3b70e5;
   line-height: 30px;
   width: 90%;
   margin:10px 5%; 
   padding-top: 10px;
}
.detail_attention{
   line-height: 30px;
   width: 90%;
   margin:0px 5%; 
}
.big_attention{
  width: 50px;
  height: 20px;
  display: block;
  float: right;
  margin-top: -10px;
  color: gray;
}
.big_attention .attention_top{
  width: 50px;
  height: 17px;
  display: block;
  text-align: center;
}
.big_attention .attention_buttom{
  width: 50px;
  height: 20px;
  display: block;
  text-align: center;
}
.detail_charts{
  width: 100%;
  margin-top: 20px;
}
.detail_child_title{
  width: 90%;
  color: #3b70e5;
  padding:10px 5%;
  font-size: 14px;
  font-weight: bold;
  position: relative;
  text-indent: 1.3rem;
}
.detail_child_title > img{
    width: 21px;
    position: absolute;
    left: 1.4rem;
    top: .6rem;
}
.charts_one{
   margin-top: -20px;
   width: 96%;
   margin: 5px 2%;
   box-shadow: #c1c1c1 0vw 1vw 2vw 0vw;
}

.media_content{
   width: 96%;
   margin: 5px 2%;
   box-shadow: #c1c1c1 0vw 1vw 2vw 0vw;
}
.media_content li{
  padding-top: 10px
}

.media_list_top{
  width: 88%;
  height: 20px;
  margin: 5px 6%;
  border-radius: 20px;
  line-height: 20px;
  background: #dfdfdf;
  color: #ea6874;
  text-indent: 2em;//首行缩进
}
.media_list_center{
  width: 88%;
  margin: 5px 6%;
  font-size: 17px;
  color: #ea6874;
  text-indent: 1em;//首行缩进
  text-align:justify;
	clear: both;
}
.media_list_buttom{
  width: 88%;
  margin: 5px 6%;
  font-size: 13px;
  color:black;
  text-indent: 2em;//首行缩进
  padding-bottom: 10px;
  text-align:justify;
  word-break:break-all;
  word-wrap:break-word;
}
.media_list_top img{
    position: absolute;
    width: 35px;
    left: 9px;
    top: 6px;
    border-radius: 30px;
}
.media_content li{
  position: relative;
}
.a_more{
  float: right;
  text-decoration: underline;
  color: gray;
  margin-right: -10px;
}
.detail_charts_time{
  width: 90%;
  text-indent: 2rem;
  color: #3b70e5;
}
</style>