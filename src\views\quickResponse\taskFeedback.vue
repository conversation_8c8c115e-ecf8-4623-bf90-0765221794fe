<template>
  <div class="taskFeedback">
    <Header title="任务反馈"></Header>
    <div class="taskFeedback_content">
      <div class="quickResponse_list">
        <div class="list-title">组织参与抢险救援行动
          <span>进行中</span>
        </div>
        <p>严格落实外围管控区的出入登记管理，并将核心救援区救出的人员情况以及伤亡情况及时记录汇总，登记受灾区域转移群众的个人信息，后续进一步跟踪处理。
        </p>
        <div class="issue_time">
          <span>任 务 地 点</span> 湖北省</div>
        <div class="issue_time">
          <span>执 行 主 体</span> 湖北省救援基地、湖北省红十字会</div>
        <div class="issue_time">
          <span>下 发 时 间</span> 2022-03-12 19:32</div>
        <div class="finish_time">
          <span>拟完成时间</span> 2022-03-13 19:32</div>
      </div>
    </div>
    <div class="taskFeedback_form">
      <van-form @submit="onSubmit">
        <van-cell-group>
          <div class="text">任务反馈</div>
          <van-field
            v-model="goods"
            type="textarea"
            rows="3"
            name="goods"
            label="反馈内容"
            :placeholder="'请输入反馈内容'"
            :rules="[{ required: true, message: '请输入反馈内容' }]"
          />
          <div class="text">附件</div>
          <van-uploader v-model="fileList" multiple style="margin-top:16px;"/>
        </van-cell-group>
      <div style="background: #fff;padding: 16px;position: absolute;width: 100%;bottom: 0;">
        <van-button block type="primary" native-type="submit">
          结束任务
        </van-button>
      </div>
      </van-form>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
// import { ref } from 'vue';
// import { Uploader } from 'vant';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
    // [Uploader.name]: Uploader,
  }
})
export default class taskFeedback extends Vue {
  private goods:any = ''
  private pattern:any = /\d/;
  private onSubmit() {
    
  };
  private fileList :any = [
    { url: 'https://cdn.jsdelivr.net/npm/@vant/assets/leaf.jpeg' },
    { url: 'https://cloud-image', isImage: true },
  ];
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.taskFeedback {
  display: flex;
  flex-direction: column;
  background: #f5f6f6;
  .taskFeedback_content{
    padding:13px 10px;
    .quickResponse_list {
      border-radius: 5px;
      background: #fff;
      .list-title-finished {
        line-height: 40px;
        padding: 5px 7px;
        font-size: 19px;
        color: #333;
        border-radius: 5px;
        background: linear-gradient(to right, #ecf2ff 30%, transparent 60%, #f5f6f6 100%);
        span {
          float: right;
          color: #ed6a0c;
          font-size: 14px;
        }
        .finished {
          color: #3284ff;
        }
      }
      .list-title {
        line-height: 40px;
        padding: 5px 7px;
        font-size: 19px;
        color: #333;
        border-radius: 5px;
        background: linear-gradient(to right, #fff8eb 60%, transparent 80%, #f5f6f6 100%);
        span {
          float: right;
          color: #ed6a0c;
          font-size: 14px;
        }
        .finished {
          color: #3284ff;
        }
      }
      p {
        font-size: 14px;
        color:#1c1d1d;
        padding: 0 10px;
        margin: 5px 0;
        line-height: 22px;
      }
      .issue_time {
        color: #1c1d1d;
        font-size: 14px;
        padding: 0 10px 5px 10px;
        span {
          color: #646566;
          margin-right:12px;
        }
      }
      .finish_time {
        color: #1c1d1d;
        font-size: 14px;
        padding: 0 10px 15px 10px;
        span {
          color: #646566;
          margin-right:12px;
        }
      }
      .quickResponse_btn {
        border-top: 1px solid #c9c9c9;
        height: 40px;
        line-height: 40px;
        div {
          width: 50%;
          float: left;
          font-size: 16px;
          color: #3284ff;
          text-align: center;
          border-right: 1px solid #c9c9c9;
        }
        div:last-child {
          border-right: none;
        }
      }
      .quickResponse_btn_finished {
        border-top: 1px solid #c9c9c9;
        height: 40px;
        line-height: 40px;
        div {
          width: 100%;
          float: left;
          font-size: 16px;
          color: #3284ff;
          text-align: center;
        }
      }
    }
  }
  .taskFeedback_form{
    flex: 1;
    display: flex;
    flex-direction: column;
    // padding:0px 10px 13px 10px;
    // border-radius:5px;
    // h2{
    //   padding:0 10px;
    //   line-height:40px;
    //   font-size:16px;
    //   color:#1c1d1d;
    //   background:#fff;
    //   border-bottom:1px solid #cecece;
    //   margin:0 auto;
    // }
    // .taskFeedback_div{
    //   line-height:40px;
    //   padding:0 10px;
    //   border-bottom:1px solid #cecece;
    //   font-size:14px;
    //   color:#666;
    //   background:#fff;
    // }
    // /deep/.van-cell-group--inset {
    //   margin: 0 0;
    //   overflow: hidden;
    //   border-radius: 0
    // }
    /deep/.van-form{
      height: calc(100% - 85px);
      // padding: 0 10px;
      .van-cell-group {
        margin: 0 10px;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0 10px;
        border-radius:5px;
        .text {
          padding: 16px 0;
          border-bottom: 1px solid #cecece;
          font-size:17px;
          color:#1c1d1d;
        }
        .van-cell {
          padding: 12px 0;
          border-bottom: 1px solid #cecece;
          &:nth-child(1) {
            font-size:16px;
            color:#1c1d1d;
          }
          &:nth-child(2) {
            flex: 1;
            flex-direction: column;
            // height: 216px;
            // border: none;
            .van-cell__title {
              width: 100%;
              padding-bottom: 12px;
              border-bottom: 1px solid #cecece;
            }
            .van-cell__value {
              padding-top: 12px;
            }
          }
        }
      }
      textarea.van-field__control {
        height: 92px;
        min-height: 92px;
      }
    }
    .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
  }
}
</style>
