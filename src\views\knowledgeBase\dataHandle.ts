const dataApi = {
  /**
   *
   * @param res 对象
   * @param type 0为list 1为详情
   */
  whetherEmpty(res, type) {
    try {
      if (type == 0) {
        if (res.data.data && res.data.data.list && res.data.data.list.length > 0) {
          return true;
        } else {
          return false;
        }
      } else {
        if (res.data.data) {
          return true;
        } else {
          return false;
        }
      }
    } catch (error) {
      console.warn(error);
      return false;
    }
  },
  planList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.planId, //ID
          title: _item.planName, //预案名称
          planTypeCode: _item.planTypeCode, // 类型
          planTypeCodeName: _item.planTypeCodeName, //类型名称
          planLevel: _item.planLevel, //级别
          planLevelName: _item.planLevelName, //级别名称
          publishTime: _item.publishTime, //发布时间
          establishOrgName: _item.establishOrgName, //执行机构
          state: _item.checkStatusName //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  planInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.planId, //ID
        title: _item.planName, //预案名称
        planTypeCode: _item.planTypeCode, // 类型
        planTypeCodeName: _item.planTypeCodeName, //类型名称
        planLevel: _item.planLevel, //级别
        planLevelName: _item.planLevelName, //级别名称
        notes: _item.notes, //编制说明
        publishTime: _item.publishTime, //发布时间
        establishOrgName: _item.establishOrgName, //执行机构
        state: _item.checkStatusName, //状态
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },
  caseList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.caseId, //ID
          title: _item.caseName, //预案名称
          planTypeCode: '案例', // 类型
          planTypeCodeName: _item.typeName, //类型名称
          planLevel: _item.levelName, //级别
          planLevelName: _item.levelName == null || _item.levelName == '' ? '暂无级别' : _item.levelName, //级别名称
          publishTime: _item.startTime, //发布时间
          establishOrgName: _item.createOrgCode, //执行机构
          state: '无' //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  caseInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.caseId, //ID
        address:_item.address,
        title: _item.caseName, //预案名称
        type: _item.typeCode, // 类型
        typeCodeName: _item.typeName, //类型名称
        level: _item.levelCode, //级别
        levelName: _item.levelName == null || _item.levelName == '' ? '暂无级别' : _item.levelName, //级别名称
        publishTime: _item.startTime, //发布时间
        establishOrgName: _item.createOrgCode, //执行机构
        caseKeyword:_item.caseKeyword,//关键词
        precaution:_item.precaution,//预防措施
        state: '无', //状态
        notes: _item.conResume, //编制说明
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },
  lawList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.lawId, //ID
          title: _item.lawName, //预案名称
          type: _item.lawCategoryId, // 类型
          typeCodeName: _item.lawCategory, //类型名称
          planLevel: _item.levelName, //级别
          planLevelName: _item.levelName == null || _item.levelName == '' ? '暂无级别' : _item.levelName, //级别名称
          publishTime: _item.publishDate, //发布时间
          establishOrgName: _item.createOrgCode, //执行机构
          publishOrg: _item.publishOrg,
          publishOrgName: _item.publishOrgName,
          state: '无' //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  lawInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.lawId, //ID
        title: _item.lawName, //法规名称
        type: _item.lawCategoryId, // 类型
        typeCodeName: _item.lawCategory, //类型名称
        planLevel: _item.levelName, //级别
        planLevelName: _item.levelName == null || _item.levelName == '' ? '暂无级别' : _item.levelName, //级别名称
        publishTime: _item.publishDate, //发布时间
        establishOrgName: _item.createOrgCode, //执行机构
        publishOrg: _item.publishOrg,
        publishOrgName: _item.publishOrgName,
        notes: _item.content, //编制说明
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },
  knowledgeList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.knoId, //ID
          title: _item.knoTitle, //知识名称
          type: _item.eventTypeCode, // 类型
          typeCodeName: _item.eventTypeName, //类型名称
          planLevel: _item.levelName, //级别
          planLevelName: _item.knoKeyword == null || _item.knoKeyword == '' ? '暂无关键字' : _item.knoKeyword, //级别名称
          publishTime: _item.publishDate, //发布时间
          establishOrgName: _item.createOrgCode, //执行机构
          publishOrg: _item.sourceDeptCode,
          publishOrgName: _item.sourceDeptName,
          state: '无' //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  knowledgeInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.knoId, //ID
        title: _item.knoTitle, //知识名称
        type: _item.eventTypeCode, // 类型
        typeCodeName: _item.eventTypeName, //类型名称
        planLevel: _item.levelName, //级别
        knoSource: _item.knoSource,
        planLevelName: _item.knoKeyword == null || _item.knoKeyword == '' ? '暂无关键字' : _item.knoKeyword, //级别名称
        publishTime: _item.publishDate, //发布时间
        establishOrgName: _item.createOrgCode, //
        publishOrg: _item.sourceDeptCode,
        publishOrgName: _item.sourceDeptName,
        notes: _item.knoAbstract, //编制说明
        typeName:_item.typeName,
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },
  knowBaseLedgeList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.baseId, //ID
          title: _item.baseName, //知识名称
          type: _item.baseCategoryId, // 类型
          typeCodeName: _item.baseCategory, //类型名称
          planLevel: _item.levelName, //级别
          planLevelName: _item.knoKeyword == null || _item.knoKeyword == '' ? '暂无关键字' : _item.knoKeyword, //级别名称
          publishTime: _item.publishDate, //发布时间
          establishOrgName: _item.publishOrgName, //执行机构
          publishOrg: _item.sourceDeptCode,
          publishOrgName: _item.publishOrgName,
          state: '无' //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  knowBaseLedgeInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.baseId, //ID
        title: _item.baseName, //名称
        type: _item.baseCategoryId, // 类型
        typeCodeName: _item.baseCategory, //类型名称
        planLevel: _item.levelName, //级别
        knoSource: _item.knoSource,
        planLevelName: _item.knoKeyword == null || _item.knoKeyword == '' ? '暂无关键字' : _item.knoKeyword, //级别名称
        publishTime: _item.publishDate, //发布时间
        establishOrgName: _item.createOrgCode, //
        publishOrg: _item.sourceDeptCode,
        publishOrgName: _item.publishOrgName,
        notes: _item.content, //编制说明
        typeName:_item.typeName,
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },
  teachingVideoList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.videoId, //ID
          title: _item.videoName, //名称视频
          type: _item.eventTypeCode, // 类型
          typeCodeName: _item.videoCategory, //类型名称
          planLevel: _item.levelName, //级别
          planLevelName: _item.publishOrgName, //执行机构
          publishTime: _item.createTime, //发布时间
          establishOrgName: _item.publishOrgName, //
          publishOrg: _item.sourceDeptCode,
          publishOrgName: _item.sourceDeptName,
          state: '无' //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  teachingVideoInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.videoId, //ID
        title: _item.videoName, //名称视频
        type: _item.eventTypeCode, // 类型
        typeCodeName: _item.videoCategory, //类型名称
        planLevel: _item.levelName, //级别
        planLevelName: _item.publishOrgName, //执行机构
        publishTime: _item.updateTime, //发布时间
        establishOrgName: _item.publishOrgName, //
        publishOrg: _item.sourceDeptCode,
        publishOrgName: _item.sourceDeptName,
        content: _item.content, //编制说明
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },

  specificationsList(res) {
    console.log('data handle--------------');
    console.log(res);
    let resultList = [];
    if (dataApi.whetherEmpty(res, 0)) {
      let list = res.data.data.list;
      list.forEach((_item, _index) => {
        let obj = {
          id: _item.standId, //ID
          title: _item.standName, //名称
          type: _item.standTypeName, // 类型
          typeCodeName: _item.eventTypeName, //类型名称
          planLevel: _item.levelName, //级别
          planLevelName: _item.keyWord == null || _item.keyWord == '' ? '暂无关键字' : _item.keyWord, //级别名称
          publishTime: _item.pubDate, //发布时间
          establishOrgName: _item.createOrg, //执行机构
          publishOrg: _item.sourceDeptCode,
          publishOrgName: _item.sourceDeptName,
          standTypeName:_item.standTypeName,
          standLevelCodeName:_item.standLevelCodeName,
          effDate:_item.effDate,
          lawForceName:_item.lawForceName,
          standAbstract:_item.standAbstract,
          notes:_item.notes,
          state: '无' //状态
        };
        resultList.push(obj);
      });
      console.log(res.data.data);
      return resultList;
    } else {
      console.log('data handle 返回数据为空');
      return resultList;
    }
  },
  specificationsInfo(res) {
    console.log('data info handle--------------');
    console.log(res);
    let obj = {};
    if (dataApi.whetherEmpty(res, 1)) {
      let _item = res.data.data;
      obj = {
        id: _item.standId, //ID
        title: _item.standName, //名称
        type: _item.standTypeName, // 类型
        typeCodeName: _item.eventTypeName, //类型名称
        planLevel: _item.levelName, //级别
        planLevelName: _item.keyWord == null || _item.keyWord == '' ? '暂无关键字' : _item.keyWord, //级别名称
        publishTime: _item.pubDate, //发布时间
        establishOrgName: _item.createOrg, //执行机构
        publishOrg: _item.sourceDeptCode,
        publishOrgName: _item.sourceDeptName,
        standTypeName:_item.standTypeName,
        standLevelCodeName:_item.standLevelCodeName,
        effDate:_item.effDate,
        lawForceName:_item.lawForceName,
        standAbstract:_item.standAbstract,
        notes:_item.notes,
        attachmentList: _item.attachmentList //附件
      };
    }
    return obj;
  },

};
export default dataApi;
