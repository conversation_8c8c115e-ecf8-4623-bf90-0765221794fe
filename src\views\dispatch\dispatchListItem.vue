<template>
    <div>
     
        <div class="taskList_content_list" v-for="(item,index) in eventList" :key="index">
            <div class="taskList_content_list_task" @click="comeInDetail(item)">
                <div class="taskList_content_list_task_tit clearfix">
                    <div>
                        {{item.taskname}}
                    </div>
                    <div class="clearfix">
                        <span class="wei" v-if="item.sendstate=='0'">未下发</span>
                        <span class="wei" v-if="item.sendstate=='1'">已下发</span>
                        <span class="wei" v-if="item.sendstate=='2'">已签收</span>
                    </div>
                </div>
                <div class="taskList_content_list_task_content">
                    {{item.taskcontent}}
                </div>
                <div class="taskList_content_list_task_unitTime">
                    <span>
                        {{item.sendTimeStr}}
                    </span>
                </div>
                <div class="taskList_content_list_task_org">
                    <span>
                        {{item.sendOrgName}}
                    </span>
                </div>
            </div>
            <div v-if="item.feedbackFlag">
                <div class="taskList_content_list_feedback" @click="openFdInfo(item1)" v-for="(item1,index1) in item.feedback" :key="index1">
                    <div>
                        <span>
                            {{item1.tasktitle}}
                        </span>
                        <!-- <span>
                            <span class="status_span" >未下发</span>
                        </span> -->
                    </div>
                    <div>
                        {{item1.backinfo}}
                    </div>
                    <div class="feed_time">
                        {{$formatTimeStr(item1.exectime)}} 反馈
                    </div>
                </div>
            </div>
            <div class="taskList_content_list_botton">
                <img @click="feedbackFlagClick(item)" v-if="!item.feedbackFlag&&item.num!=0" src="../../assets/images/bot.png"/>
                <img @click="feedbackFlagClick(item)" v-if="item.feedbackFlag&&item.num!=0" src="../../assets/images/top.png"/>
                <img src="../../assets/images/write.png" @click="comeDispatch(item)" />
            </div>
        </div>
        <div v-if="eventList&&eventList.length==0&&isData" style="width:100%;text-align:center;padding-top: 10%;">
          
          <!-- <img src="../../assets/images/notdata.png" />
          <div style="font-size:1rem;color:#969799;margin-top:-1rem">暂无数据</div> -->
          <van-empty description="暂无数据" />
        </div>


    </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
@Component({
  components: {
  }
})
export default class Home extends Vue {
  eventList: any = [];
  isData :any = false;
  isInit :any = true;
  androidPath: any = 'file:///android_asset'
  comeDispatch(_item){
    console.log(_item)
    let _this=this;
    let dispatchStr="?refid="+_item.eventid+"&dispatchtaskid="+_item.taskid;
    console.log(dispatchStr)
    
    let jumpUrl="http://***************:8081/#/formElement"+dispatchStr;
    if(_this.$isAndroid()){
        console.log("come android jump")
        jumpUrl=_this.androidPath+"/dist/index.html#/formElement"+dispatchStr;
        //jumpUrl="http://***************:8081/#/formElement"+dispatchStr
        //jumpUrl="http://i51.top/dist/index.html#/formElement"+dispatchStr
        
       // alert("come android jump===>"+jumpUrl)
    
        _this['$gsmdp'].startNewWebview(jumpUrl);
        //alert("come android jump===>"+jumpUrl)
    }else{
        window.location.href=jumpUrl;
    }
  }
  comeInDetail(_item) {
      let _this=this;
      let dispatchStr="?eventid="+_item.eventid+"&dispatchtaskid="+_item.taskid;
      console.log(dispatchStr)
      
      let jumpUrl="";
      if(_this.$isAndroid()){
            console.log("come android indexDetail jump")
            jumpUrl=_this.androidPath+"/dist/index.html#/participateRescue"+dispatchStr 
            //jumpUrl="http://***************:8081/#/participateRescue"+dispatchStr 
            //jumpUrl="http://i51.top/distyantai/index.html#/participateRescue"+dispatchStr 
             
            //alert("come android jump===>"+jumpUrl)
             _this['$gsmdp'].startNewWebview(jumpUrl);
           // window['android'].getWEbviewUrl(jumpUrl,"file:///android_asset/dist/index.html#/dispatchTaskList");
        }else{
            jumpUrl="http://localhost:8081/#/participateRescue"+dispatchStr;
            window.location.href=jumpUrl;
        }
    }
    feedbackFlagClick(item){//点击下拉请求一键反馈
      var _this = this;
      console.log(item)
      if(!item.feedbackFlag){
        if(item.feedback){
            item.feedbackFlag=!item.feedbackFlag;
            console.log("close feedlist")
        }else{
            var param={
              "backState": "",
              "eventId": item.eventid,
              "nowPage": 1,
              "pageSize": 100,
              "taskId": item.taskid
            }
            apiServer.findFeekList(param,function(res) {
              console.log("return res==",res)
              let data=res.data.data;
              if(data){
                let list=data.list;
                item.feedback=list;
                console.log(list)
                item.feedbackFlag=!item.feedbackFlag;
              }else{

              }
            })
            // let feedList=require("./feedlist.json")
            // console.log(feedList)
            // console.log("open feedlist")
            // item.feedback=feedList;
        }
      }else{
          item.feedbackFlag=!item.feedbackFlag;
          console.log("close feedlist all")
      }
      _this.$forceUpdate();
        
    }
    
    queryRequest(pageObj){
      let _this=this;
      let _objPage={pageIndex:1,pageSize:5}
      if(pageObj){
        console.log(pageObj.pageIndex)
        _objPage.pageIndex=pageObj.pageIndex;
      }
      let param={
          "evenLevel": "",
          "eventId": "",
          "execPersonid": "",
          "execorgcode": "",
          "exeteam": "",
          "nowPage": _objPage.pageIndex,
          "orgcode": "",
          "pageSize": _objPage.pageSize,
          "personorg": "",
          "sendEndTime": "",
          "sendOrgcode": "",
          "sendStartTime": "",
          "sendState": "",
          "taskLevelCode": "",
          "taskName": ""
        };
      apiServer.findDispatchList(param,function(res){
         console.log(res)
         _this.isInit=false;
         _this.$emit("handleObj",{uploadMore:false})
         _this.isData=true;
         _this.$toast.clear();
         let resultsObj=res['data'].data.list;
          console.log(resultsObj)
          if(resultsObj){
            console.log("赋值成功");
            resultsObj['forEach']((item,index)=>{
              //console.log(item)
              item.feedbackFlag=false;
            })
            if(_objPage.pageIndex>1){
               _this.eventList = [..._this.eventList, ...resultsObj];
            }else{
              _this.eventList=resultsObj;
            }
            
          }
      },_this.isInit)
      
    }
    setZero(num){
      num=num+"";
      if(num&&num.length==1){
        return "0"+num;
      }else{
        return num;
      }
    }
    created() {
      let _this=this;
      _this['$apiServer'].setToken();
      console.log("init run")
      _this.queryRequest(null);
      // setTimeout(function() {
        
      // }, 500);
     
    }
    

}
</script>
<style scoped lang="scss">

  .taskList_content_list{
    width: 100%;
    background-color: #ffffff;
    box-shadow: 0px 0px 2px 0px 
        rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    margin: 15px 0 30px;
    position: relative;
    .taskList_content_list_task{
        padding: 10px;
        .taskList_content_list_task_tit{
            div:nth-child(1){
                font-size: 20px;
                // line-height: 20px;
                letter-spacing: 0px;
                color: #333333;
                float: left;
                width: 60%;
                overflow: hidden;
                text-overflow: ellipsis; 
                -o-text-overflow: ellipsis;
                white-space:nowrap;
            }
            div:nth-child(2){
                float: right;
                span{
                    padding: 2px 0;
                    border-radius: 23px;
                    letter-spacing: 0px;
                    color: #ffffff;
                    width: 55px;
                    float: left;
                    text-align: center;
                    background-color: #ff0000;
                }
                span:nth-child(1){
                    margin-right: 5px;
                    background-color: #ae5da1;
                }
                span:nth-child(2){
                    background-color: #e51818;
                }
                span.wei{
                    background-color: #eb6100;
                }
                span.yi{
                    background-color: #ae5da1;
                }
                span.jieshu{
                    background-color: #d2d2d2;
                }
            }
        }
        .taskList_content_list_task_feedNum{
            text-align: right;
            color: #222222;
            padding: 5px 0;
            span{
                color: #ff0000;
                font-size: 15px;
            }
        }
        .taskList_content_list_task_unitTime{
            margin:3px 0px;
            color: gray;
            span:nth-child(1){
                font-size: 12px;
                float: right;
                padding-right: 5%;
            }
            span:nth-child(2){
                float: right;
            }
        }
        .taskList_content_list_task_org{
            margin-bottom: 5px;
            border-top: 1px solid #c7c7c7;
            padding-top: 5px;
            span{
               color: #4dbaf6;
            }
        }
        .taskList_content_list_task_content{
            // text-indent: 2em;
            // line-height: 1.7;
            // height: 46px;
            overflow: hidden;
            word-wrap: break-word;
            word-break: break-all;
        }
    }
    .taskList_content_list_feedback{
        padding: 10px;
        border-top: 1px solid #c7c7c7;
        color: #4dbaf6;
        div:nth-child(1){
            margin-bottom: 10px;
            span:nth-child(1){
                // color: #4dbaf6;
                font-size: 15px;
            }
            span:nth-child(2){
                float: right;
            }
            .status_span{
              padding: 2px 0;
              border-radius: 23px;
              letter-spacing: 0px;
              color: #ffffff;
              font-size: 12px;
              width: 55px;
              float: left;
              text-align: center;
              background-color: #eb6100;
            }
        }
        div:nth-child(2){
            // text-indent: 2em;
            line-height: 1.7;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .feed_time{
          float: right;
          font-size: 12px;
          padding-bottom: 10px;
        }
    }
    .taskList_content_list_botton{
        position: absolute;
        bottom: -15px;
        right: 0;
        height: 30px;
        img{
            height: 100%;
            margin-right: 10px;
        }
    }
}
</style>
