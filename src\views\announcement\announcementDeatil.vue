<template>
  <div class="list">
    <Header title="通知公告"></Header>
    <div class="list-card">
      <van-cell-group inset>
        <van-cell
          v-for="ele in cellInfoList"
          :key="ele.prop"
          :value="ele.type !== 'label' ? ele.value : null"
          :label="ele.type === 'label' ? ele.value : null"
        >
          <template #title>
            <van-icon :name="ele.icon" :color="ele.color" style="margin-right: 10px" />
            <span>{{ ele.name }}</span>
          </template>
          <template #label v-if="ele.prop.includes('List') && ele.type">
            <div class="list-casualties" v-if="ele.prop.includes('List') && ele.label">
              <div
                class="attach_area"
                v-for="(item, index) in ele.label.attachmentList"
                :key="index"
                @click="downLoad(item, ele.type)"
              >
                <span><img src="../../assets/images/ppt.png" alt="" srcset="" /></span>
                <span>{{ item.journalName || item.name }}</span>
              </div>
            </div>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import Header from "@/views/common/header.vue";
import axios from "axios";
import { Notify } from "vant";
import { downloadReport } from "@/utils/validate";
@Component({
  components: {
    Header,
  },
})
export default class announcementDeatil extends Vue {
  private infoList: any = [
    {
      name: "标题",
      prop: "noticetitle",
      value: "",
      color: "#c8e3ee",
      icon: "notes-o",
      type: "label",
    },
    {
      name: "下发单位",
      prop: "downunit",
      value: "",
      color: "#b5dbe8",
      icon: "cluster-o",
    },
    {
      name: "下发时间",
      prop: "updateTimeStr",
      value: "",
      color: "#eed49b",
      icon: "clock-o",
    },
    {
      name: "编辑人",
      prop: "editpersonName",
      value: "",
      color: "#b5dbe8",
      icon: "user-o",
    },
    {
      name: "编辑单位",
      prop: "editunit",
      value: "",
      color: "#eed49b",
      icon: "cluster-o",
    },
    {
      name: "文电",
      prop: "attachList",
      value: "",
      color: "#b5dbe8",
      icon: "photo-o",
      type: "kwls",
    },
  ];

  private listData: any = [
    {
      name: "发布单位",
      prop: "createDept",
      value: "",
      color: "#eed49b",
      icon: "cluster-o",
    },
    {
      name: "标题",
      prop: "caption",
      value: "",
      color: "#c8e3ee",
      icon: "user-o",
      type: "label",
    },
    {
      name: "发文日期",
      prop: "publicDate",
      value: "",
      color: "#b5dbe8",
      icon: "clock-o",
    },
    {
      name: "有效期",
      prop: "effectiveTime",
      value: "",
      color: "#b5dbe8",
      icon: "clock-o",
    },
    {
      name: "接收日期",
      prop: "createDateStr",
      value: "",
      color: "#b5dbe8",
      icon: "clock-o",
    },
    {
      name: "附件",
      prop: "attachList",
      value: "",
      color: "#b5dbe8",
      icon: "photo-o",
      type: "exattach",
    },
    {
      name: "文档",
      prop: "receiveJournalList",
      value: "",
      color: "#b5dbe8",
      icon: "photo-o",
      type: "bjtz",
    },
  ];

  private cellInfoList = [];

  mounted() {
    const id = this.$route.query.id;
    let requestType = this.$route.query.infotype;
    if (requestType === "1") {
      this.cellInfoList = this.listData;
      this.getDepartmentDetail(id);
    } else {
      this.cellInfoList = this.infoList;
      this.getDetail(id);
    }
  }

  getDepartmentDetail(id) {
    this["$api"].InfoRequest.announcePartDetail({ id }, (res) => {
      if (res.status === 200) {
        let value = JSON.parse(res.data.data);
        value.receiveJournalList = [value.receiveJournal];
        this.getData(this.cellInfoList, value);
      }
    });
  }

  getDetail(id) {
    this["$api"].InfoRequest.announceDetail({ id }, (res) => {
      if (res.status === 200) {
        let value = JSON.parse(res.data.data);
        value.attachList = [value.receiveJournal];
        this.getData(this.cellInfoList, value);
      }
    });
  }

  getData(arr, data) {
    arr.forEach((item: any) => {
      item.value = data[item.prop];
      if (item.prop === "attachList" || item.prop === "receiveJournalList") {
        item.value = null;
        item.label = {
          attachmentList: data[item.prop],
          showText: false,
        };
      }
    });
    return arr;
  }

  //下载
  downLoad(item, fileType) {
    console.log("下载------》", item, fileType);
    if (fileType === "kwls") {
      // window.open(`/wpsFile/05/${item.journalId}.docx`);
      this["$api"].InfoRequest.getInfoFileUrl({ journalId: item.journalId }, (res) => {
        // downloadReport(res);
        // window.open(item.pathOuterNet || item.urlOuterNet);
        if (res.data.status === 200) {
          window.open(res.data.data);
        } else {
          Notify({ type: "danger", message: "获取链接地址失败" });
        }
      });
      return false;
    }
    const params: any = {
      fileId: item.journalId || item.id,
      fileName: item.journalName || item.name,
      fileType,
    };
    this["$api"].InfoRequest.downLoad(params, (res) => {
      downloadReport(res);
    });
  }
}
</script>

<style lang="less" scoped>
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-card {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    .van-cell-group {
      display: flex;
      flex-direction: column;
      margin: 12px 10px;
      padding: 0 10px;
      .van-cell {
        padding: 12px 0;
      }
    }
  }
  .attach_area {
    display: flex;
    align-items: center;
    margin: 5px 0;
  }
}
</style>
