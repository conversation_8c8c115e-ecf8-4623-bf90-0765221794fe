<template>
  <div class="list">
    <Header title="预警信息" backPath="/"></Header>
    <div class="diselect">
      <van-button
        v-for="item in disCodeList"
        :key="item.type" :color="curentDisCode === item.type ? '#ee0a24' : '#aaa'"
        plain
        size="small"
        style="margin: 0 10px"
        @click="onChangeDisCode(item.type)"
      >{{item.name}}</van-button>
    </div>
    <div class="selectTabs">
      <van-tabs v-model="curInfoType" @change="onClickTab">
        <van-tab :title="item.name" :name="item.type" v-for="item in selectType" :key="item.type"></van-tab>
      </van-tabs>
    </div>
    <div class="list-main">
      <!-- <div class="list-main-search">
        <van-search v-model="searchObj.keyword" placeholder="关键字搜索"  @search="onSearch"/>
      </div> -->
      <div class="list-main-cont">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            offset="100"
            @load="onLoad"
          >
            <!-- <van-cell v-for="(item, index) in list" :key="index" :title="item.vprodContent" class="van-ellipsis" @click="handleToDetail(item)" /> -->
            <van-cell v-for="(item, index) in list" :key="index" class="van-ellipsis" @click="handleToDetail(item)">
              <template #title>
                <div class="warInfoItem">
                  <div class="warInfoItemPic">
                    <img :src="getPic(item)" alt="" style="width: 100%; height: 100%">
                  </div>
                  <div class="warInfoItemCon">
                    <div class="warInfoItemTitle">{{item.headline}}</div>
                    <!-- <div>{{item.cissueUnit}}</div> -->
                    <div>{{item.sendtime}}</div>
                  </div>
                </div>
              </template>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Notify } from 'vant';
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
const dataJson = require('../infoReception/data.json');
@Component({
  components: {
    Header
  }
})
export default class warnInfo extends Vue {
  searchObj: any = {
    countycode: '',
    cwarLevelCode: '',
    cwarTypeCode: '',
    nowPage: 1,
    pageSize: 10,
    keyword: ''
  }
  list: any = [];
  total: any = []
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;

  private curentDisCode: any = '0';
  private disCodeList: any = [
    {
      name: '全部',
      type: '0',
    },
    {
      name: '市级',
      type: '1'
    },
    {
      name: '县级',
      type: '2'
    },
  ]

  private curInfoType: any = '0';
  private selectType: any = [
    {
      name: '全部',
      type: '0',
    },
    {
      name: '实时预警',
      type: '1'
    },
    {
      name: '已解除预警',
      type: '2'
    }
  ]

  private onClickTab(item) {
    this.curInfoType = item;
    this.searchObj.nowPage = 1;
    this.getList(true);
  }

  mounted() {
    // this.getList()
    const tenantId: string = JSON.parse(localStorage.getItem('role')).tenantId;
    const len = tenantId.split('.').length;
    if (len === 2) {
      this.disCodeList = [
        {
          name: '全部',
          type: '0',
        },
        {
          name: '市级',
          type: '1'
        },
        {
          name: '县级',
          type: '2'
        },
      ]
    } else if (len === 3) {
      this.disCodeList = [
        {
          name: '全部',
          type: '0',
        },
        {
          name: '县级',
          type: '2'
        },
      ]
    } else if (len === 4) {
      this.disCodeList = [
        {
          name: '全部',
          type: '0',
        },
      ]
    }
  }
  getList(first = false) {
    const that = this;
    if (this.list.length > 0 && this.list.length === this.total && !first) {
      return
    }
    let params: any = {
      districtCode: JSON.parse(localStorage.getItem('role')).districtCode,
      nowPage: this.searchObj.nowPage,
      pageSize: this.searchObj.pageSize,
      areaLevel: this.curentDisCode === '0' ? '' : this.curentDisCode,
      warnStatus: this.curInfoType === '0' ? '' : this.curInfoType,
    }
    
    // apiServer.monitoringAndEarlyWarning(this.searchObj, function(res){
    //   console.log(res,"res")
    //   if(res.status === 200) {
    //     const data = res.data.data
    //     if(that.searchObj.nowPage === 1) {
    //       that.list = data.list;
    //     } else {
    //       that.list = [...that.list, ...data.list]
    //     }
    //     that.total = data.total;
    //     that.loading = false;
    //     that.refreshing = false;
    //     if(that.list.length >= data.total && that.searchObj.nowPage === data.pages) {
    //       that.finished = true;
    //     } else {
    //       that.finished = false;
    //     }
    //     that.searchObj.nowPage++;
    //   } else {
    //     that.finished = true;
    //     Notify({ type: 'danger', message: res.data.msg || '请求失败' });
    //   }
    // })
    this['$api'].InfoRequest.monitoringAndEarlyWarningNew(params, res => {
      if (res.status === 200) {
        const data = JSON.parse(res.data.data);
        if(that.searchObj.nowPage === 1) {
          that.list = data.list;
        } else {
          that.list = [...that.list, ...data.list]
        }
        that.total = data.total;
        that.loading = false;
        that.refreshing = false;
        if(that.list.length >= data.total && that.searchObj.nowPage === data.nowPage) {
          that.finished = true;
        } else {
          that.finished = false;
        }
        that.searchObj.nowPage++;
      } else {
        that.finished = true;
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    })
  }
  onSearch() {
    this.searchObj.nowPage = 1;
    this.getList()
  }
  // 跳转详情页面
  handleToDetail(item) {
    this.$router.push({
      path: `/warnDetailInfo`,
      query: {
        alertInfoId: item.alertId
      }
    })
  }

  private getPic(item) {
    if (!item) return false;
    let num: any;
    if (item.severity === 'Red') {
      num = 1;
    } else if (item.severity === 'Orange') {
      num = 2;
    } else if (item.severity === 'Yellow') {
      num = 3;
    } else {
      num = 4;
    }
    return require(`@/assets/images/warnInfo/${item.eventtype ? item.eventtype : 'default'}_${num}.png`)
  }

  private onChangeDisCode(code) {
    this.curentDisCode = code;
    this.searchObj.nowPage = 1;
    this.getList(true);
  }
  // onLoad() {
  //   setTimeout(() => {
  //     if (this.refreshing) {
  //       this.list = [];
  //       this.refreshing = false;
  //     }
  //     // this.list = dataJson.listData;
  //     this.getList();
  //     this.loading = false;

  //     this.finished = true;
  //   }, 1000);
  // }
  // onRefresh() {
  //   // 清空列表数据
  //   this.finished = false;

  //   // 重新加载数据 将 loading 设置为 true，表示处于加载状态
  //   this.loading = true;
  //   this.onLoad();
  // }

   // 下拉刷新
  public onRefresh() {
    this.searchObj.nowPage = 1;
    this.list = [];
    // if (this.finished) return;
    this.getList();
  }
  // 上拉加载刷新
  public onLoad() {
    if (this.finished) return;
    this.getList();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    // position: absolute;
    // z-index: 1;
    // left: 0;
    // top: 46px;
    // height: calc(100vh - 46px);
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: auto;
    // overflow: auto;
    &-search {
      height: 56px;
    }
    &-cont {
      // flex: 1;
      // height: calc(100% - 96px);
      
    }
    &-add {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #03A9F4;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
}
.van-ellipsis {
  margin-bottom: 10px;
}
.diselect {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  align-items: center;
  &-btn {
    padding: 3px 12px;
    border: 1px solid #eaeaea;
    color: #727272;
    border-radius: 4px;
  }
}
.selectTabs {
  margin-bottom: 5px;
  // .van-tabs__nav {
  //   background: #f7f7f7;
  // }
}
.warInfoItem {
  display: flex;
  height: 80px;
  align-items: center;
  overflow: hidden;
  .warInfoItemPic {
    width: 70px;
    height: 70px;
    margin-right: 10px;
  }
  .warInfoItemCon {
    flex: 1;
    text-align: right;
    color: #9f9f9f;
    font-size: 14px;
  }
  .warInfoItemTitle {
    width: 260px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>

<style lang="less">
.selectTabs {
  .van-tabs__nav {
    background: #eee;
  }
  .van-tab--active {
    color: #ee0a24;
  }
}
</style>