<template>
    <div>
     
        <div class="taskList_content_list" v-for="(item,index) in eventList" :key="index">
            <div class="taskList_content_list_task" @click="comeInDetail(item)">
                <div class="taskList_content_list_task_tit clearfix">
                    <div>
                        {{item.taskTitle}}
                    </div>
                    <div class="clearfix">
                        
                        <!-- <span class="wei" v-if="item.taskStateCode=='20'">&nbsp;&nbsp;未下发&nbsp;&nbsp;</span>
                        <span class="yi" v-if="item.taskStateCode=='30'">&nbsp;&nbsp;已下发&nbsp;&nbsp;</span>  -->
                        <span  v-if="item.taskLevelName">&nbsp;&nbsp;{{item.taskLevelName}}&nbsp;&nbsp;</span>
                        <span :class="{'wancheng':item.taskStateCode!='20'}">{{item.taskStateCode=="20"?'进行中':'已完成'}}</span>
                    </div>
                </div>
                
                <div class="taskList_content_list_task_feedNum">
                    已反馈
                    <span>
                        {{item.taskFeedbackCount}}
                    </span>
                    次
                </div>
                <div class="taskList_content_list_task_org">
                    <span>
                        {{item.sendOrgName}}
                    </span>
                    
                </div>
                 <div class="taskList_content_list_task_unitTime">
                    <span>
                        {{item.sendTime}}发布
                    </span>
                </div>
                <div class="taskList_content_list_task_content">
                    {{item.taskContent}}
                </div>
               
                
            </div>
            <div v-if="item.feedbackFlag">
                <div class="taskList_content_list_feedback" @click="openFdInfo(item1)" v-for="(item1,index1) in item.feedback" :key="index1">
                    <div>
                        <span>
                            {{item1.feedbackTitle}}
                        </span>
                        <div class="feed_time">
                            {{$formatTimeStr(item1.feedbackTime)}} 反馈
                        </div>
                        <!-- <span>
                            <span class="status_span" >未下发</span>
                        </span> -->
                    </div>
                    <div>
                        {{item1.feedbackContent}}
                    </div>
                    
                </div>
            </div>
            <div class="taskList_content_list_botton">
                <img @click="feedbackFlagClick(item)" v-if="!item.feedbackFlag&&item.num!=0" src="../../assets/images/bot.png"/>
                <img @click="feedbackFlagClick(item)" v-if="item.feedbackFlag&&item.num!=0" src="../../assets/images/top.png"/>
                <img v-show="item.taskStateCode!='30'" src="../../assets/images/write.png" @click="comeFeedback(item)" />
            </div>
        </div>
        <div v-if="eventList&&eventList.length==0&&isData" style="width:100%;text-align:center;padding-top: 10%;">
            <van-empty description="暂无数据" />
          <!-- <img src="../../assets/images/notdata.png" />
           <div style="font-size:1rem;color:#969799;margin-top:-1rem">暂无数据</div> -->
        </div>
        <van-popup v-model="showObj.feedbackModel" v-if="showObj.feedbackModel"  position="right" :style="{ height: '100%' , width: '100%' }" >
            <feedback :queryMap="queryMap" @success="successQuery" @close="closeInfo" />
        </van-popup>
        <van-popup v-model="showObj.taskDetailModel" v-if="showObj.taskDetailModel"  position="right" :style="{ height: '100%' , width: '100%' }" >
            <taskDetails :requestId="requestId" @close="closeInfo" />
        </van-popup>
        <van-popup v-model="showObj.taskFkDetailModel" v-if="showObj.taskFkDetailModel"  position="right" :style="{ height: '100%' , width: '100%' }" >
            <taskFkDetails :requestId="requestId" @close="closeInfo" />
        </van-popup>


    </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import feedback from './feedback.vue';
import taskDetails from './taskDetails.vue' 
import taskFkDetails from './taskFdDetails.vue'
@Component({
  components: {
      feedback,
      taskDetails,
      taskFkDetails
  }
})
export default class Home extends Vue {
  eventList: any = [];
  isData :any = false;
  isInit :any = true;
  isUpUser :any =""
  queryMap: any ={};
  requestId:any = "";
  showObj:any = {
      feedbackModel:false,
      taskDetailModel:false,
      taskFkDetailModel:false
  };
  androidPath: any = 'file:///android_asset'
  closeInfo(){
    let _this=this;
    for(let key in this.showObj){
        this.showObj[key]=false;
    }
     _this['$gsmdp'].initGoback(function(){
        try {
             _this['$gsmdp'].goBack({
            success(res) {
                alert(JSON.stringify(res))
            }
            })
        } catch (error) {
            console.log("close error")
        }
    })
     
  }
  successQuery(){
      let _this=this;
      console.log("successQuery==>")
      //关闭所有详情添加页面
      _this.closeInfo();
      setTimeout(() => {
          _this.queryRequest(null);
      }, 200);
      
  }
  comeFeedback(_item){
    console.log(_item)
    let _this=this;
    _this.queryMap.eventId=_item.eventId;
    _this.queryMap.taskId=_item.taskId;
    _this.showObj.feedbackModel=true;
  }
  openFdInfo(_item){
      console.log(_item)
      this.requestId=_item.feedbackId;
      this.showObj.taskFkDetailModel=true;
  }
  comeInDetail(_item) {
    let _this=this;
    _this.requestId=_item.taskId;
    _this.showObj.taskDetailModel=true; 
  }
    feedbackFlagClick(item){//点击下拉请求一键反馈
        var _this = this;
        console.log(item)
        if(!item.feedbackFlag){
        if(item.feedback){
            item.feedbackFlag=!item.feedbackFlag;
            console.log("close feedlist")
        }else{
            var param={
                "feedbackTitle": "",
                "nowPage": 1,
                "pageSize": 100,
                "taskId": item.taskId
            }
            apiServer.findTaskFeekList(param,function(res) {
                console.log("return res==",res)
                let data=res.data;
                if(data){
                    let list=data.data;
                    item.feedback=list;
                    console.log(list)
                    item.feedbackFlag=!item.feedbackFlag;
                }else{

                }
            })
            // let feedList=require("./feedlist.json")
            // console.log(feedList)
            // console.log("open feedlist")
            // item.feedback=feedList;
        }
        }else{
            item.feedbackFlag=!item.feedbackFlag;
            console.log("close feedlist all")
        }
        _this.$forceUpdate();
        
    }
    
    queryRequest(pageObj){
      let _this=this;
      let _objPage={pageIndex:1,pageSize:10}
      if(pageObj){
        console.log(pageObj.pageIndex)
        _objPage.pageIndex=pageObj.pageIndex;
      }
      console.log("----------------"+_this.isUpUser)
      console.log("------>"+(_this.isUpUser=="1"?"":_this.$store.state.userInfo.orgcode))
      let param={
          "eventId": "",
        "orgCode": _this.isUpUser=="1"?"":_this.$store.state.userInfo.orgcode,
        //eventId: "ff8080817b32e6a2017b340f9c9d7392",
        "taskStateCode": "",
        "taskTitle": "",
        "nowPage": _objPage.pageIndex,
        "pageSize": _objPage.pageSize
      };
      console.log(JSON.stringify(param))
      //apiServer.findDispatchList(param,function(res){
      apiServer.findTaskList(param,function(res){
          //alert(JSON.stringify(res))
         console.log(res)
         _this.isInit=false;
         _this.$emit("handleObj",{uploadMore:false})
         _this.isData=true;
         _this.$toast.clear();
         let resultsObj=res['data'].data.list;
          console.log(resultsObj)
          if(resultsObj){
            console.log("赋值成功");
            resultsObj['forEach']((item,index)=>{
              //console.log(item)
              item.feedbackFlag=false;
            })
            if(_objPage.pageIndex>1){
               _this.eventList = [..._this.eventList, ...resultsObj];
            }else{
              _this.eventList=resultsObj;
            }
            
          }
      },_this.isInit)
      
    }
    setZero(num){
      num=num+"";
      if(num&&num.length==1){
        return "0"+num;
      }else{
        return num;
      }
    }
    created() {
      let _this=this;
      console.log("init run")
      apiServer.isUpOrDownUser({},(userLevel)=>{
          console.log("用户是否上级",userLevel.data.data)
          if(userLevel.data.data){
              _this.isUpUser="1";
          }else{
              _this.isUpUser="0";
          }
          _this.queryRequest(null);
      })
      //_this['$apiServer'].setToken();
      // setTimeout(function() {
        
      // }, 500);
     
    }
    

}
</script>
<style scoped lang="scss">

  .taskList_content_list{
    width: 94%;
    background-color: #ffffff;
    box-shadow: 0px 0px 2px 0px 
        rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    margin: 15px 3% 30px;
    position: relative;
    .taskList_content_list_task{
        padding: 10px;
        .taskList_content_list_task_tit{
            div:nth-child(1){
                font-size: 20px;
                // line-height: 20px;
                letter-spacing: 0px;
                color: #333333;
                float: left;
                width: 60%;
                overflow: hidden;
                text-overflow: ellipsis; 
                -o-text-overflow: ellipsis;
                white-space:nowrap;
            }
            div:nth-child(2){
                float: right;
                span{
                    padding: 2px 0;
                    border-radius: 23px;
                    letter-spacing: 0px;
                    color: #ffffff;
                    width: 55px;
                    font-size: 13px;
                    float: left;
                    text-align: center;
                    background-color: #ff0000;
                }
                span:nth-child(1){
                    margin-right: 5px;
                    background-color: #ae5da1;
                }
                span:nth-child(2){
                    background-color: #e51818;
                }
                span.wancheng{
                    background-color:#599047
                }
                span.wei{
                    background-color: #eb6100;
                }
                span.wei{
                    background-color: #eb6100;
                }
                span.yi{
                    background-color: #97ca48;
                }
                span.jieshu{
                    background-color: #d2d2d2;
                }
            }
        }
        .taskList_content_list_task_feedNum{
            text-align: right;
            color: #222222;
            //padding: 5px 0;
            span{
                color: #ff0000;
                font-size: 15px;
            }
        }
        .taskList_content_list_task_unitTime{
            margin:3px 0px;
            color: gray;
            span:nth-child(1){
                font-size: 12px;
                float: right;
            }
            span:nth-child(2){
                float: right;
            }
        }
        .taskList_content_list_task_org{
            margin-bottom: 1.33333vw;
            color: #222222;
            width: 50%;
            float: left;
        }
        .taskList_content_list_task_content{
            // text-indent: 2em;
            // line-height: 1.7;
            height: 46px;
            // overflow: hidden;
            word-wrap: break-word;
            word-break: break-all;
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
        }
    }
    .taskList_content_list_feedback{
        padding: 10px;
        border-top: 1px solid #c7c7c7;
        // color: #1b45e3;
        div:nth-child(1){
            margin-bottom: 5px;
            span:nth-child(1){
                color: #1b45e3;
                font-size: 15px;
            }
            span:nth-child(2){
                float: right;
            }
            .status_span{
              padding: 2px 0;
              border-radius: 23px;
              letter-spacing: 0px;
              color: #ffffff;
              font-size: 12px;
              width: 55px;
              float: left;
              text-align: center;
              background-color: #eb6100;
            }
        }
        div:nth-child(2){
            text-indent: 2em;
            // line-height: 1.7;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .feed_time{
          float: right;
          color: #1b45e3;
          font-size: 12px;
          padding-bottom: 10px;
        }
    }
    .taskList_content_list_botton{
        position: absolute;
        bottom: -.8rem;
        right: 0;
        height: 30px;
        img{
            height: 100%;
            margin-right: 10px;
        }
    }
}
</style>
