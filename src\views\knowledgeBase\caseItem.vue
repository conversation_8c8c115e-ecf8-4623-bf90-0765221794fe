<template>
    <div>
        <div class="item_content_list" v-for="(item,index) in eventList" :key="index">
            <ul>
                <li >
                    <div class="list_content">
                        <div  @click="comeInDetail(item)">
                            {{item.title}}
                        </div>
                    </div>
                    <div class="list_top" @click="comeInDetail(item)">
                        <div class="btnPart">
                          <!-- <button class="total" >{{item.planLevelName}}</button> -->
                          <button class="action" v-if="item.planTypeCodeName">{{item.planTypeCodeName}}</button>
                          <span class="time_show" v-if="item.publishTime">{{item.publishTime}}</span>
                        </div>
                    </div>
                </li>
            </ul>
            
        </div>
        <div v-if="eventList&&eventList.length==0&&requestStatus=='end'" style="width:100%;text-align:center;padding-top: 10%;">
          <van-empty description="暂无数据" />
          <!-- <img src="../../assets/images/notdata.png" />
           <div style="font-size:1rem;color:#969799;margin-top:-1rem">暂无数据</div> -->
        </div>
        <van-popup v-model="detailHandle.showInfo" v-if="detailHandle.showInfo"  position="right" :style="{ height: '100%' , width: '100%' }" >
          <Infodetail  :requestId="requestId" @close="closeInfo"/>
        </van-popup>
        


    </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import dataHandle from './dataHandle';
import Infodetail from './caseInfo.vue';

@Component({
  components: {
    Infodetail
  },
})
export default class integratedItem extends Vue {
  @Prop(String) private requestFlag :string;
  @Prop(Object) private requestObj :any;
  eventList: any = [];
  keywords: any = "";
  requestStatus: any = "";
  requestId : any = "";
  isInit : any =true;
  detailHandle : any ={ showInfo : false  } 
  closeInfo(){
    let _this=this;
    _this.detailHandle.showInfo = false;
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      try {
          _this['$gsmdp'].goBack(function(){
          })
      } catch (error) {
          console.log("close error")
      }
    })

  }
  searchSet(_keyword){
    this.keywords=_keyword;
  }
  comeInDetail(_item) {
    let _this=this;
    _this.requestId=_item.id;
    console.log("come in detail "+_this.requestId)
    _this.detailHandle.showInfo=true;//显示详情
  }
  queryRequest(pageObj){
    let _this=this;
    let _objPage={pageIndex:1,pageSize:10}
    console.log("_this.requestFlag===>"+_this.requestFlag)
    console.log("load==>"+JSON.stringify(_this.requestObj))
    if(pageObj){
      console.log(pageObj.pageIndex)
      _objPage.pageIndex=pageObj.pageIndex;
    }
    let param={
      eventTypeCode: "",
      keyWord: _this.keywords,
      nowPage: _objPage.pageIndex,
      pageSize: _objPage.pageSize,
      startTime: "",
      typeCode: "",
    };
    //  _this.$emit("handleObj",{uploadMore:false})
    // let res=require("./integrated.json")
    // console.log(res)
    // let resultsObj=res['data'].list;
    // _this.eventList=resultsObj;
    apiServer.findCaseList(param,function(res){
      _this.isInit=false;
        console.log(res)
        let dataHandleList= dataHandle.caseList(res);
        _this.requestStatus="end";
        _this.$emit("handleObj",{uploadMore:false})
        if(dataHandleList.length>0){
          if(_objPage.pageIndex>1){
              _this.eventList = [..._this.eventList, ...dataHandleList];
          }else{
            _this.eventList=dataHandleList;
          }
        }else{
          if(_objPage.pageIndex<=1){
            _this.eventList=[];
          }
        }
    },_this.isInit)
    
  }
  
  created() {
    let _this=this;
    //_this['$apiServer'].setToken();
    console.log("init run")
    _this.queryRequest(null);
    // setTimeout(function() {
      
    // }, 500);
    
  }
    

}
</script>
<style scoped lang="scss">
.item_content_list{
    width: 100%;
    height: 100%;
    background-color: #fff;
    li{
        width: 100%;
        background: white;
        margin-top: 15px;
        padding-top: 10px;
        .list_top{
            width: 100%;
            height: 50%;
            position: relative;
            .btnPart{
              padding: 0px 4vw 4vw 3%;
              .action{
                background: transparent;
                color: #e45050;
                border: 1px solid #e45050;
                -webkit-border-radius: 6px;
                -moz-border-radius: 6px;
                border-radius: 6px;
                padding: 1px 12px;
                margin-left: 5px;
                max-width: 30%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .total{
                background: transparent;
                color: #00a0e9;
                border: 1px solid #00a0e9;
                -webkit-border-radius: 6px;
                -moz-border-radius: 6px;
                border-radius: 6px;
                padding: 1px 12px;
                max-width: 30%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .time_show{
                -webkit-border-radius: 6px;
                -moz-border-radius: 6px;
                border-radius: 6px;
                padding: 1px 0px;
                float: right;
              }
            }
        }
        .list_content{
            width: 100%;
            line-height: 35px;
            padding-left: 3%;
            font-size: 15px;
            div{
                float: left;
            }
            div:nth-of-type(1){
                width: 90%;
                height: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            div:nth-of-type(2){
                width: 10%;
                height: 100%;
                img{
                    width: 15px;
                    vertical-align: middle;
                    margin-top: -5px;
                }
            }
        }
    }
    
    
}
</style>