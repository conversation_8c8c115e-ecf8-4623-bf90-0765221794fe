<template>
  <div class="footer-tag">
    <div>
      <div class="btn" @click="toTask">
        <van-icon :name="require('@/assets/images/quickResponse/ico-know.png')" size="12" />
        任务
      </div>
      <div class="btn" v-for="(item, index) in listTaglist" :key="index" @click="handleFun(item.emit)" v-show="item.isShow === '1'">
        <van-icon :name="require(`@/assets/images/quickResponse/${item.icon}.png`)" :size="item.size || 12" />
        {{ item.label }}
      </div>
      <!-- <div class="btn" @click="toKnowledgeCase" v-show="listTagShow['知识案例'] === '1'">
      <van-icon :name="require('@/assets/images/quickResponse/ico-know.png')" size="12" />
      知识案例
    </div>
    <div class="btn" v-show="listTagShow['当前天气'] === '1'">
      <van-icon :name="require('@/assets/images/quickResponse/ico-weather.png')" size="12" />
      当前天气
    </div>
    <div class="btn" @click="toNotice" v-show="listTagShow['群发公告'] === '1'">
      <van-icon :name="require('@/assets/images/quickResponse/quickResponse06.png')" size="14" />
      群发公告
    </div> -->
      <div class="btn">
        <van-popover v-model="showPopoverInfo" trigger="click" :actions="actionsInfo" placement="top" @select="onSelectInfo">
          <template #reference>
            <van-icon :name="require('@/assets/images/quickResponse/ico-info.png')" size="12" />
            信息专区
          </template>
        </van-popover>
      </div>

      <div class="btn last">
        <van-popover v-model="showPopover" trigger="click" :actions="actions" placement="top" @select="onSelect">
          <template #reference>
            <van-icon :name="require('@/assets/images/quickResponse/menu.png')" size="12" />
            智能助手
          </template>
        </van-popover>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import taskRequest from '@/api/webcross/task';
import { Notify, ImagePreview, Toast } from 'vant';
import { guide } from '@/utils/getLocation';
import gisConfig from '@/components/baseMap/gisConfig.json'

@Component({
  components: {}
})
export default class cardTag extends Vue {
  @Prop() public detailInfo: any;
  @Prop() public listTagShow: any;
  @Prop() public feedback: any;
  @Prop() public locationInfo: any;
  @Prop() private teamInfo: any;

  showPopoverInfo = false;
  showPopover = false;
  private actionsInfo = [
    { text: '天气实况' },
    { text: '实时交通路况' },
    { text: '事件基本信息' },
    { text: '应急指挥体系' },
    { text: '相关知识案例' },
    { text: '力量投入' },
    { text: '力量投送' },
    { text: '任务清单' },
  ];

  private actions = [];
  listTaglist = [
    // { label: '事件信息', icon: 'ico-info', emit: 'toEventInformation', menuId: '74efe731e0f94229b3b0f1f861fda6f7', isShow: '1'},
    // { label: '知识案例', icon: 'ico-know', emit: 'toKnowledgeCase', menuId: 'cf3f80d8a6b245d7b66f5c662ace81d5', isShow: '1'},
    // { label: '当前天气', icon: 'ico-weather', emit: 'toCurrentWeather', menuId: '6c818976df4846b6983a250d2edaf8a1', isShow: '1'},
    { label: '群消息', icon: 'quickResponse06', emit: 'toNotice', menuId: '11c5dc94cbe24e05aa5b88d5f7fc44f9', size: 14, isShow: '0' }
    // { label: '智能助手', icon: 'quickResponse06', emit: '', menuId: '11c5dc94cbe24e05aa5b88d5f7fc44f9', size: 14, isShow: '0'},
  ];

  @Watch('listTagShow', { immediate: true, deep: true })
  getListTagShow(val, oldVal) {
    if (JSON.stringify(val) !== '{}') {
      this.listTaglist = this.listTaglist.map((ele) => {
        ele.isShow = this.listTagShow[ele.menuId];
        return ele;
      });
    }
  }

  @Watch('feedback')
  handleList(val) {
    if (val.length) {
      this.actions = val.map((el) => {
        return {
          ...el,
          text: el.replyTypeName
        };
      });
      this.actions.push({
        text: '直播'
      });
      // console.log(this.actions,'this.actions');
    }
  }

  private mounted() {}

  toTask() {
    this.$router.push({
      path: '/listTask',
      query: {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId,
        locationInfo: JSON.stringify(this.locationInfo)
      }
    });
  }

  onSelect(action) {
    console.log(action.text);
    if (action.text == '直播') {
      const role = JSON.parse(localStorage.getItem('role'));
      let mobile = (this.teamInfo && this.teamInfo.relMobile) || role.loginName;
      if (!this.locationInfo.locInfo.longitude) {
        Notify({ type: 'danger', message: '暂无获取到经纬度,请稍后再试' });
      }

      taskRequest.startLive(
        {
          address: this.locationInfo.locInfo.address,
          eventId: this.detailInfo.eventId,
          latitude: this.locationInfo.locInfo.latitude,
          longitude: this.locationInfo.locInfo.longitude,
          teamId: this.detailInfo.teamId
        },
        (result) => {
           let liveHistoryId;
          if(result.data.status ==200){
               liveHistoryId = result.data.data;
          } else {
            Notify({
              message:result.data.msg,
            });
            return;
          }

          taskRequest.getLiveRecord(
            {
              eventId: this.detailInfo.eventId,
              routeAddress: this.locationInfo.locInfo.address,
              routeLatitude: this.locationInfo.locInfo.latitude,
              routeLongitude: this.locationInfo.locInfo.longitude,
              routeType: 5,
              teamId: this.detailInfo.teamId
            },
            (res) => {
              let eventId = this.detailInfo.eventId || 12345678;
              eventId = eventId.toString().slice(eventId.length - 8, eventId.length);
              if(liveHistoryId){
                  window.open(`${gisConfig.liveUrl}mobile.html?test=122&liveHistoryId=${liveHistoryId}`);
                  //  window.open(`https://*************:8889/cffm/liveRoom/mobile.html?test=122&liveHistoryId=${liveHistoryId}`);
              }

            }
          );
        }
      );
    } else {
      this.$emit('onClickFeedback', action);
    }
  }

  onSelectInfo(action) {
    if (action.text == '天气实况') {
      this.toCurrentWeather();
    } else if (action.text == '实时交通路况') {
      // todo
      console.log(this.locationInfo, 'this.locationInfo');
      if (this.locationInfo && this.locationInfo.locInfo && this.locationInfo.locInfo.longitude) {
        var lng = this.locationInfo.locInfo.longitude;
        var lat = this.locationInfo.locInfo.latitude;
        let address = this.locationInfo.locInfo.address;
        let locInfo = {
          lng,
          lat,
          address
        };
        // 高德地图
        guide('gd', locInfo);
      } else {
        Notify('获取地理位置失败，请稍后再试');
      }
    } else if (action.text == '事件基本信息') {
      this.toEventInformation();
    } else if (action.text == '应急指挥体系') {
      this.$router.push({
        path: '/command',
        query: {
          eventId: this.detailInfo.eventId,
          districtCode: this.detailInfo.districtCode || this.teamInfo.districtCode
        }
      });
    } else if (action.text == '相关知识案例') {
      this.toKnowledgeCase();
    }else if (action.text == '力量投入') {
      this.toPower();
    } else if (action.text == '力量投送') {
      this.toPowerSend();
    }
  }

  handleFun(emit) {
    this[emit]();
  }

  // 当前天气
  toCurrentWeather() {
    this.$router.push({
      path: '/currentWeather',
      query: {
        lat: this.locationInfo.locInfo.latitude,
        lon: this.locationInfo.locInfo.longitude
      }
    });
  }

  //跳转信息页面
  toEventInformation() {
    this.$router.push({
      path: '/event/eventDetail',
      query: {
        type: 'detail',
        infotype: 'event',
        id: this.detailInfo.eventId
      }
    });
    // this.$router.push({
    //   path: '/eventInformation',
    //   query: {
    //     eventTypeCode: this.detailInfo.eventTypeCode,
    //   }
    // });
  }
  //跳转知识案例页面
  toKnowledgeCase() {
    this.$router.push({
      path: '/knowledgeCaseList',
      query: {
        eventTypeCode: this.detailInfo.eventTypeCode || this.teamInfo.eventTypeCode
      }
    });
  }
  // 跳转群发公告列表界面
  toNotice() {
    this.$router.push({
      path: '/noticeList',
      query: {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId
      }
    });
  }
  // 跳转力量投入
  toPower() {
    console.log('toPower');
    this.$router.push({
      path: '/power/list',
      query: {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId
      }
    });
  }
  // 跳转力量投送
  toPowerSend() {
    this.$router.push({
      path: '/power/send',
      query: {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId
      }
    });
  }
}
</script>

<style lang="less" scoped>
.footer-tag {
  overflow-x: scroll;
  ::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .scrollbar::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}

::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}

.last /deep/ .van-icon .van-icon__image {
  // margin-top: 2px;
  vertical-align: top;
  margin-right: -4px;
}
</style>
