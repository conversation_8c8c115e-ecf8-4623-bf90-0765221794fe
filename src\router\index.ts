import Vue from 'vue';
import Router from 'vue-router';
Vue.use(Router);
import Login from '../views/login/index.vue';
import apphome from '../views/apphome.vue';
// import testhome from '../views/home/<USER>';
// import form from '../views/forms/form.vue';
import dispatchTaskList from '../views/dispatch/dispatchTaskList.vue'; //任务调度
import integratedQueryhList from '../views/integratedQuery/integratedQueryhListNew.vue'; //综合查询
import duty from '../views/duty/duty.vue'; //值班安排
import everydaySign from '../views/everydaySign/everydaySign.vue'; //每日签到
import navigatePage from '../views/knowledgeBase/navigatePage.vue'; //知识库
import planList from '../views/knowledgeBase/planList.vue'; //预案列表
import planManageList from '../views/planManage/planManageList.vue'; //预案管理
import caseList from '../views/knowledgeBase/caseList.vue'; //案例列表
import lawList from '../views/knowledgeBase/lawList.vue'; //法律法规列表
import teachingVideoList from '../views/knowledgeBase/teachingVideoList.vue'; //教学视频列表
import specificationsList from '../views/knowledgeBase/specificationsList.vue'; //标准及技术规范
import liveAuthPage from '../views/quickResponse/components/liveAuthPage.vue'; //教学视频列表
import EmergencyResponse from '../views/emergency/emergencyList.vue'; // 应急叫应
import emergencyAdd from '../views/emergency/emergencyAdd.vue'; // 预警发布
import knowledgeList from '../views/knowledgeBase/knowledgeList.vue'; //应急知识列表
import baseKnowledgeList from '../views/knowledgeBase/baseKnowledgeList.vue'; //应急知识列表

import taskList from '../views/taskManage/taskList.vue'; //任务管理

import quickResponse from '../views/quickResponse/quickResponse.vue'; //快速响应
import taskFeedback from '../views/quickResponse/taskFeedback.vue'; //任务反馈
import resourceApplication from '../views/quickResponse/resourceApplication.vue'; //资源申请
import disasterReporting from '../views/quickResponse/disasterReporting.vue'; //灾情上报

// import AllHome from '../views/home/<USER>';
import MainHome from '../views/home/<USER>';
import PassCode from '../views/quickResponse/passCode.vue';
import quicks from '../views/quickResponse/components/detail.vue';

import eventInformation from '../views/quickResponse/eventInformation.vue'; //事件信息
import knowledgeCase from '../views/quickResponse/knowledgeCase.vue'; //知识案例
import authPage from '../views/authPage/index.vue'; // 授权码中转界面
import error from '../views/404/index.vue'; // 404错误界面
import noticeList from '../views/quickResponse/noticeList.vue'; // 群发公告
import noticeListDetail from '../views/quickResponse/noticeListDetail.vue'; // 群发公告详情
import currentWeather from '../views/quickResponse/currentWeather.vue'; // 当前天气
import knowledgeCaseList from '../views/quickResponse/knowledgeCaseList.vue'; // 知识案例列表
import leadership from '../views/leadership/index.vue'; // 领导批示
import taskListview from '../views/taskManage/list.vue'; //任务列表
import reportto from '../views/reportTo/index.vue'; // 呈报列表
import reportaddform from '../views/reportTo/reportAddForm.vue'; // 呈报上报
import reporttodetail from '../views/reportTo/leaderDetail.vue'; // 呈报详情
import command from '../views/quickResponse/command.vue';
import powerList from '../views/power/powerList.vue'; // 力量投入
import powerSend from '../views/power/powerSend.vue'; // 力量投送
import reportList from '../views/report/list.vue'; // 呈报列表
import hazard from '../views/hazard-affected/index.vue';

import MainApp from '../views/mainApp.vue';
// import GroupManage from '../views/groupManage/index.vue'

export const constantRouterMap = [
  {
    path: '*',
    redirect: '/home'
  },
  {
    path: '/login',
    component: Login,
    name: 'Login',
    meta: { navShow: false, keepAlive: true, title: '登录' }
  },
  {
    path: '/home',
    component: MainHome,
    name: 'MainHome',
    meta: { navShow: true, keepAlive: true, title: '首页' }
  },
  // {
  //   path: '/allHome',
  //   component: AllHome,
  //   name: 'AllHome',
  //   meta: { navShow: true, keepAlive: true, title: '首页' },
  // },
  {
    path: '/command',
    component: command,
    name: 'command',
    meta: { navShow: false, keepAlive: true, title: '指挥体系' }
  },
  {
    path: '/searchDetail',
    component: () => import('@/views/quickResponse/components/searchDetail.vue'),
    name: 'searchDetail',
    meta: { navShow: false, keepAlive: true, title: '' }
  },
  {
    path: '/backlog',
    component: () => import('@/views/backlog/index.vue'),
    name: 'backlog',
    meta: { navShow: true, keepAlive: true, title: '待办事项' }
  },
  {
    path: '/addressBook',
    component: () => import('@/views/addressBook/index.vue'),
    name: 'addressBook',
    meta: { navShow: true, keepAlive: true, title: '通讯录' }
  },
  {
    path: '/beVolunteer',
    component: () => import('@/views/beVolunteer/index.vue'),
    name: 'beVolunteer',
    meta: { navShow: false, keepAlive: true, title: '报名志愿者' }
  },
  {
    path: '/beVolunteer/applySuccess',
    component: () => import('@/views/beVolunteer/applySuccess.vue'),
    name: 'applySuccess'
  },
  {
    path: '/addressBook/list/:id',
    component: () => import('@/views/addressBook/addressList.vue'),
    name: 'addressList'
  },
  {
    path: '/addressBook/detail/:id',
    component: () => import('@/views/addressBook/addressDetail.vue'),
    name: 'addressDetail'
  },
  {
    path: '/addressBook/addressInfo',
    component: () => import('@/views/addressBook/addressInfo.vue'),
    name: 'addressInfo'
  },

  //通讯录选中
  {
    path: '/addressBookCheck',
    component: () => import('@/views/addressBookCheck/index.vue'),
    name: 'addressBookCheck'
    // meta: { navShow: true, keepAlive: true, title: '通讯录' }
  },
  {
    path: '/addressBookCheck/list/:id',
    component: () => import('@/views/addressBookCheck/addressList.vue'),
    name: 'addressList'
  },
  {
    path: '/addressBookCheck/detail/:id',
    component: () => import('@/views/addressBookCheck/addressDetail.vue'),
    name: 'addressDetail'
  },
  {
    path: '/addressBookCheck/addressInfo',
    component: () => import('@/views/addressBookCheck/addressInfo.vue'),
    name: 'addressInfo'
  },
  {
    path: '/GroupManage',
    component: () => import('@/views/groupManage/index.vue'),
    name: 'GroupManage'
  },

  {
    path: '/mine',
    component: () => import('@/views/mine/index.vue'),
    name: 'mine',
    meta: { navShow: true, keepAlive: true, title: '我的' }
  },
  // {
  //   path: '/testhome',
  //   component: testhome,
  //   name: 'testhome'
  // },
  {
    path: '/listTask',
    component: taskListview,
    name: 'listTask'
  },

  // {
  //   path: '/form',
  //   component: form,
  //   name: 'form'
  // },
  {
    path: '/dispatchTaskList',
    component: dispatchTaskList,
    name: 'dispatchTaskList'
  },
  {
    path: '/mainApp',
    component: MainApp,
    name: 'mainApp'
  },
  {
    path: '/integratedQueryhList',
    component: integratedQueryhList,
    name: 'integratedQueryhList'
  },
  {
    path: '/duty',
    component: duty,
    name: 'duty'
  },
  {
    path: '/everydaySign',
    component: everydaySign,
    name: 'everydaySign'
  },
  {
    path: '/navigatePage',
    component: navigatePage,
    name: 'navigatePage'
  },
  {
    path: '/planList',
    component: planList,
    name: 'planList'
  },
  {
    path: '/caseList',
    component: caseList,
    name: 'caseList'
  },
  {
    path: '/lawList',
    component: lawList,
    name: 'lawList'
  },
  {
    path: '/knowledgeList',
    component: knowledgeList,
    name: 'knowledgeList'
  },
  {
    path: '/baseKnowledgeList',
    component: baseKnowledgeList,
    name: 'baseKnowledgeList'
  },
  {
    path: '/teachingVideoList',
    component: teachingVideoList,
    name: 'teachingVideoList'
  },
  {
    path: '/specificationsList',
    component: specificationsList,
    name: 'specificationsList'
  },
  {
    path: '/taskList',
    component: taskList,
    name: 'taskList'
  },
  {
    path: '/planManageList',
    component: planManageList,
    name: 'planManageList'
  },
  {
    path: '/emergencyResponse',
    component: EmergencyResponse,
    name: 'emergencyResponse'
  },
  {
    path: '/emergency/add',
    component: emergencyAdd,
    name: 'emergencyAdd'
  },
  // 力量投入
  {
    path: '/power/list',
    component: powerList,
    name: 'powerList'
  },
  // 力量投送
  {
    path: '/power/send',
    component: powerSend,
    name: 'powerAdd'
  },
  // 研判报告
  {
    path: '/report/list',
    component: reportList,
    name: 'reportList'
  },
  // 一键搜
  {
    path: '/search',
    component: () => import('@/views/search/index.vue'),
    name: 'search'
  },
  // 地图图层
  {
    path: '/result',
    component: () => import('@/views/search/result.vue'),
    name: 'result'
  },
  {
    path: '/location',
    component: () => import('@/views/search/location.vue'),
    name: 'location'
  },
  // 承灾体
  {
    path: '/layerList/:id',
    component: hazard,
    name: 'hazard'
  },
  // 快速响应列表页
  {
    path: '/list',
    component: () => import('@/views/quickResponse/list.vue'),
    name: 'list'
  },
  {
    path: '/quickResponse/:id',
    component: quickResponse,
    name: 'quickResponse'
  },
  // 快速响应详情页
  {
    path: '/quicks/:id',
    component: quicks,
    name: 'quicks'
  },
  {
    path: '/passCode', // 通行码
    component: PassCode,
    name: 'PassCode'
  },
  {
    path: '/rescueRegistration',
    component: () => import('@/views/quickResponse/rescueRegistration.vue'),
    name: '/quickResponse/RescueRegistration'
  },
  {
    path: '/affiche',
    component: () => import('@/views/quickResponse/affiche.vue'),
    name: 'Affiche'
  },
  {
    path: '/changeEvent',
    component: () => import('@/views/quickResponse/changeEvent.vue'),
    name: 'ChangeEvent'
  },
  {
    path: '/taskFeedback',
    component: taskFeedback,
    name: 'taskFeedback'
  },
  {
    path: '/applyCoordinate',
    component: () => import('@/views/quickResponse/applyCoordinate.vue'),
    name: 'applyCoordinate'
  },
  {
    path: '/resourceApplication',
    component: resourceApplication,
    name: 'resourceApplication'
  },
  {
    path: '/disasterReporting',
    component: disasterReporting,
    name: 'disasterReporting'
  },
  {
    path: '/signIn',
    component: () => import('@/views/quickResponse/signIn.vue'),
    name: 'signIn'
  },
  // 信息接报页
  {
    path: '/infoReception',
    component: () => import('@/views/infoReception/index.vue'),
    name: 'infoReception'
  },
  // 新增信息接报/信息接报详情
  {
    path: '/infoReception/addForm',
    component: () => import('@/views/infoReception/addForm.vue'),
    name: 'add'
  },
  // 新增信息接报/信息接报详情
  {
    path: '/infoReception/add',
    component: () => import('@/views/infoReception/add.vue'),
    name: 'add'
  },
  // 新增普通信息接报/信息接报详情
  {
    path: '/infoReception/ordinaryAdd',
    component: () => import('@/views/infoReception/ordinaryAdd.vue'),
    name: 'ordinaryAdd'
  },
  // 新增普通信息接报/信息接报详情
  {
    path: '/infoReception/ordinaryAddForm',
    component: () => import('@/views/infoReception/ordinaryAddForm.vue'),
    name: 'ordinaryAdd'
  },
  // 事件管理
  {
    path: '/event',
    component: () => import('@/views/event/index.vue'),
    name: 'event'
  },
  // 事件详情
  {
    path: '/event/detail',
    component: () => import('@/views/event/detail.vue'),
    name: 'eventDetail'
  },
  // 事件详情
  {
    path: '/event/eventDetail',
    component: () => import('@/views/event/eventDetail.vue'),
    name: 'eventDetailForm'
  },
  // 预警信息
  {
    path: '/warnInfo',
    component: () => import('@/views/warnInfo/index.vue'),
    name: 'warnInfo'
  },
  // 预警信息详情
  {
    path: '/warnDetailInfo',
    component: () => import('@/views/warnInfo/warnDetailInfo.vue'),
    name: 'warnDetailInfo'
  },
  // 快速响应，不同队伍切图
  {
    path: '/static/:id',
    component: () => import('@/views/static/chatResponse.vue'),
    name: 'static'
  },
  //事件信息
  {
    path: '/eventInformation',
    component: eventInformation,
    name: 'eventInformation'
  },
  //知识案例列表
  {
    path: '/knowledgeCaseList',
    component: knowledgeCaseList,
    name: 'knowledgeCaseList'
  },
  //知识案例
  {
    path: '/knowledgeCase',
    component: knowledgeCase,
    name: 'knowledgeCase'
  },
  // 群发公告界面
  {
    path: '/noticeList',
    component: noticeList,
    name: 'noticeList'
  },
  // 群发公告详情界面
  {
    path: '/noticeListDetail',
    component: noticeListDetail,
    name: 'noticeListDetail'
  },
  // 当前天气界面
  {
    path: '/currentWeather',
    component: currentWeather,
    name: 'currentWeather'
  },
  //视频直播
  {
    path: '/liveAuthPage',
    component: liveAuthPage,
    name: 'liveAuthPage',
    meta: { freelogin: true }
  },

  // 授权码中转界面
  {
    path: '/authPage',
    component: authPage,
    name: 'authPage',
    meta: { freelogin: true }
  },
  // 404页面
  {
    path: '/404',
    component: error,
    name: '404'
  },
  // 领导批示
  {
    path: '/leadership',
    component: leadership,
    name: 'leadership'
  },
  // 呈报上报列表
  {
    path: '/reportto',
    component: reportto,
    name: 'reportto'
  },
  //呈报上报详情
  {
    path: '/reporttodetail',
    component: reporttodetail,
    name: 'reporttodetail'
  },
  //呈报上报
  {
    path: '/reportaddform',
    component: reportaddform,
    name: 'reportaddform'
  },
  // 领导批示详情
  {
    path: '/leaderDetail',
    component: () => import('@/views/leadership/leaderDetail.vue'),
    name: 'leaderDetail'
  },
  // 通知公告
  {
    path: '/announcement',
    component: () => import('@/views/announcement/index.vue'),
    name: 'announcement'
  },
  // 通知公告详情
  {
    path: '/announcementDeatil',
    component: () => import('@/views/announcement/announcementDeatil.vue'),
    name: 'announcementDeatil'
  }
];
console.log('constantRouterMap------>>>>', constantRouterMap.length);
// 添加如下代码可以防止重复点击一个路径是浏览器报路径重复的错
const VueRouterPush = Router.prototype.push;
Router.prototype.push = function push(to) {
  return VueRouterPush.call(this, to).catch((err) => err);
};
export default new Router({
  // linkActiveClass: 'dataSecond_activity',
  // mode: 'history',
  // base: process.env.BASE_URL,
  // scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap as any
});
