<template>
  <div class="list">
    <Header title="普通事件详情"></Header>
    <div class="list-tab">
      <van-tabs color="#1967f2">
        <van-tab title="基本信息" name="baseInfo">
          <van-form @submit="onSubmit" class="list-card">
            <van-cell-group inset v-for="item in detailsData" :key="item.name">
              <van-cell v-for="ele in item.list" :key="ele.prop" :value="ele.type !== 'label' ? ele.value : null" :label="ele.type === 'label'? ele.value : null">
                <template #title>
                  <van-icon :name="ele.icon" :color="ele.color" style="margin-right: 10px" />
                  <span>{{ele.name}}</span>
                </template>
                <template #label v-if="ele.prop === 'attachList'">
                  <div class="list-casualties">
                    <div class="attach_area" v-for="item,index in ele.label.attachmentList" :key="index" @click="downLoad(item)">
                      <span><img src="../../assets/images/ppt.png" alt="" srcset=""></span>
                      <span>{{item.name}}</span>
                    </div>
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </van-form>
        </van-tab>
        <van-tab title="处理过程" name="process">
          <van-steps direction="vertical" v-if="recordList.list.length > 0">
            <van-step v-for="item in recordList.list" :key="item.id">
              <h4>{{item.content}}</h4>
              <p>{{item.orgpername}}</p>
              <p>{{item.dealtime}}</p>
            </van-step>
          </van-steps>
          <div></div>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';
import axios from 'axios';
@Component({
  components: {
    Header
  }
})
export default class add extends Vue {
  public type: any = ''; // 页面类型，add-新增，detail-详情
  public infoType: any =  ''; // 页面信息类型
  private id: any = ''; // 接报信息id
  private detailsData: any = [
    {
      name: 'baseInfo',
      list: [
        {
          name: '信息标题',
          prop: 'title',
          value: '',
          color: '#c8e3ee',
          icon: 'coupon-o',
        },
        {
          name: '信息备注',
          prop: 'note',
          value: '暂无信息',
          color: '#eed49b',
          icon: 'orders-o',
          type: 'label',
        },
        {
          name: '编辑人',
          prop: 'editerName',
          value: '',
          color: '#e4bfa0',
          icon: 'manager-o',
        },
        {
          name: '签发人',
          prop: 'issuername',
          value: '',
          color: '#e4bfa0',
          icon: 'user-o',
        },
        {
          name: '附件',
          prop: 'attachList',
          value: '',
          color: '#e4bfa0',
          icon: 'photo-o',
        },
      ]
    },
  ]; // 选项卡列表
  public details: any = {}; // 接报信息字段

  private role: any;
  private recordList: any = {
    list: []
  };
  async mounted() {
    console.log('信息接报----》', this.$route.query) 
    this.type = this.$route.query.type;
    this.infoType = this.$route.query.infotype;
    this.id = this.$route.query.id;
    this.role = JSON.parse(localStorage.getItem('role'));    
    if (this.type === 'add') {
      this.details.tenantId = this.role.tenantId;
      this.details.orgcode = this.role.orgId; // 所属机构编码
      this.details.districtCode = '420100' || this.role.district; // 行政区划代码
      this.$set(this.details, 'editerName', this.role.name)
    }
    await this.getDetail();
    await this.getRecord();
  }

  // 获取信息接报详情
  getDetail() {
    if (this.id) {
      this['$api'].InfoRequest.getOrdinaryDetailNew({ordinaryId: this.id}, res => {
        if (res.data.status === 200) {
          this.details = JSON.parse(res.data.data);
          console.log('levelcode-------->',this.details)
          this.detailsData.forEach((item: any) => {
            item.list.forEach((ele: any) => {
              ele.value = this.details[ele.prop];
              if (ele.prop === 'attachList') {
                ele.value = null;
                this.details[ele.prop].forEach(myItem => {
                  myItem.name = myItem.accessoryname || myItem.name
                })
                ele.label = {
                  attachmentList: this.details[ele.prop],
                  showText: false,
                }
                console.log(ele.label)
              }
            })
          })
        } else {
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      })
    }
  }

  private getRecord() {
    this['$api'].InfoRequest.getEventRecordById({receiveId: this.id}, res => {
      if (res.data.status === 200) {
        this.recordList = JSON.parse(res.data.data);
        console.log(this.recordList,"-----------------this.this.recordList")
      } else {
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    })
  }

  //下载
  downLoad(item) {
    const params: any = {
      fileId: item.receiveattachid,
      fileName: item.name,
      fileType: "xxjb"
    }
    axios({
      method: 'post',
      url: '/gapi/gemp-app/api/gemp/app/attachment/download/v1',
      data: params,
      responseType: 'blob'
    }).then(res => {
        window['vm'].$toast.clear();
        let blob = new Blob([res.data], {
          type: 'application/octet-stream'
        });
        let url = window.URL.createObjectURL(blob);
        let link = document.createElement('a');
        link.style.display = 'none';
        link.download = item.name;
        link.href = url;
        document.body.appendChild(link)
        link.click();
      })
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-card {
    margin-top: 5px;
  }
  &-casualties {
    display: flex;
    flex-wrap: wrap;
    color: #323233;
    align-items: center;
    .custom-title {
      width: 50%;
      text-indent: 10px;
      height: 30px;
      line-height: 30px;
      span {
        display: inline-block;
        width: 80px;
        font-size: 3.6vw;
        text-align: right;
        font-weight: 600;
      }
    }
  }
  &-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: auto;
    /deep/.van-form{
      display: flex;
      flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 5px 0px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
      }
    }
  }
  .attach_area{
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
}
</style>