<template>
  <div class="info_div">
    <van-nav-bar title="任务详情" left-text="返回" left-arrow @click-left="GoBack">
      <template #right> </template>
    </van-nav-bar>
    <MeetBack @documentBack="documentBack"></MeetBack>
    <div class="taskDetailsContent" v-if="taskDetails.taskTitle">
      <div class="taskContentTop">
        <div class="taskTitle">
          <p class="text_ellipsis pre">{{ taskDetails.taskTitle }}</p>
          <!-- 进行中-10 已完成-50 -->
          <van-tag :type="taskDetails.teamTaskStatusCd == 10 ? 'priamry' : taskDetails.teamTaskStatusCd == '50' ? 'primary' : 'warning'">{{
            taskDetails.teamTaskStatusCd == 10 ? '进行中' : taskDetails.teamTaskStatusCd == '50' ? '已完成' : '未签收'
          }}</van-tag>
        </div>
        <div class="taskFooter">
          <span>{{ taskDetails.orgName }}<van-icon name="phone" /></span>
          <a>{{ taskDetails.taskDate }}</a>
        </div>
      </div>
      <div class="taskContentCenter">
        <div class="text_ellipsis">任务内容</div>
        <div class="text">
          {{ taskDetails.taskText }}
        </div>
        <div class="location">
          <!-- <van-icon name="guide-o" /> 武汉市青山区 -->
          <img :src="require('@/assets/images/icons/locationBlue.png')" alt="" />
          {{ taskDetails.teamTaskTaskAddress }}
        </div>
        <div class="sendTime">
          <!--  -->
          <img :src="require('@/assets/images/quickResponse/quickResponse03.png')" alt="" />
          {{ taskDetails.taskDate }}
        </div>
      </div>
      <div>
        <p>附件：</p>
        <file-preview direction="left" :fileList="taskDetails.taskAttachment"></file-preview>
      </div>
      <van-button v-if='taskDetails.teamTaskStatusCd !=="50" ' block round  type="primary" @click="finishTask"> 任务完成 </van-button>
    </div>
    <popFeedback
      :showFeedbackPop="showFeedbackPop"
      :teamInfo="teamInfo"
      feedbackType="overTask"
      :locationInfo="locationInfo"
      @closeFeedback="closeFeedback"
      @handleSureFeedback="handleSureFeedback"
    ></popFeedback>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import taskRequest from '@/api/webcross/task';
import FilePreview from '@/components/filePreview.vue';
import MeetBack from '@/components/meet-back/meet-back';
import popFeedback from '@/views/quickResponse/components/popFeedback.vue';
import { Notify, ImagePreview, Toast } from 'vant';

@Component({
  components: {
    FilePreview,
    MeetBack,
    popFeedback
  }
})
export default class integratedItem extends Vue {
  @Prop(String) private requestId: string;
  @Prop(Object) private requestObj: any;
  @Prop(Object) private locationInfo?: any;

  @Prop() private teamTaskId;
  @Prop() private eventId;
  @Prop() private teamId;
  @Prop() private taskId;

  private showFeedbackPop = false;
  private teamInfo: any = {};

  private taskData = {};
  taskDetails: any = {};
  showObject: any = {};
  infoRequest() {
    let _this = this;
    let param = {
      eventId: this.eventId,
      taskId: this.taskId,
      teamId: this.teamId,
      teamTaskId: this.teamTaskId
    };
    apiServer.findTaskInfoNew(param, function (res) {
      if (res.data.data) {
        _this.taskDetails = res.data.data;
        _this.teamInfo = {
            latitude: _this.locationInfo.latitude,
            longitude: _this.locationInfo.longitude,
            replyAddress: _this.locationInfo.address,
            eventId: _this.eventId,
            taskId: _this.taskId,
            teamTaskId: _this.teamTaskId,
            teamId: _this.teamId,
            teamName: _this.taskDetails.orgName,
            teamTask: {
              taskId: _this.taskId,
              teamTaskId: _this.teamTaskId,
              teamId: _this.teamId
            }
          };
      }
    });
  }

  // 反馈弹窗点击确认
  handleSureFeedback(status) {
    if(status =='200'){
        this.taskDetails.teamTaskStatusCd = '50';
    };
    this.closeFeedback();
  }

  closeFeedback() {
    this.showFeedbackPop = false;
  }

  finishTask() {
    this.showFeedbackPop = true;
  }
  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
  documentBack() {
    this.GoBack();
  }
  created() {
    let _this = this;
    //_this['$apiServer'].setToken();
    console.log('init run', this.locationInfo);
    _this.infoRequest();

    // setTimeout(function() {

    // }, 500);
  }
  mounted() {
    let _this = this;
    _this['$gsmdp'].initGoback(function () {
      console.log('调用返回');
      _this.GoBack();
    });
  
    console.log(this.teamInfo, 'this.teamInfo');
  }
}
</script>
<style scoped lang="scss">
.info_div {
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #fff;
  .text_ellipsis {
    font-size: 16px;
  }
  .taskDetailsContent {
    padding: 0 20px;
    margin-top: 10px;
    .taskTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      p {
        font-size: 16px;
        font-weight: bold;
        width: 280px;
        margin: 4px 0;
        &.pre {
          position: relative;
          padding-left: 5px;
          &:after {
            position: absolute;
            left: 0;
            content: '';
            top: 50%;
            width: 3px;
            height: 14px;
            transform: translateY(-50%);
            background: #1a66f2;
          }
        }
      }
      span {
        text-align: right;
      }
    }
    .taskContent {
      font-size: 14px;
    }
    .taskContentTop {
      border-bottom: 1px solid #ccc;
      padding-bottom: 10px;
      margin-bottom: 10px;
    }
    .taskFooter {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      span i {
        color: rgb(67, 89, 223);
      }
      a {
        color: #333;
      }
    }
    .text {
      font-size: 15px;
      margin-bottom: 16px;
    }

    .location,
    .sendTime {
      font-size: 14px;
      margin-left: 20px;
      padding-bottom: 10px;
      img {
        width: 16px;
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
}
.taskContentCenter {
  border-bottom: 1px solid #ccc;
  padding-bottom: 10px;
}
</style>
