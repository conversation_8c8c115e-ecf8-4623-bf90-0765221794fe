<template>
  <div class="list">
    <Header title="突发事件详情"></Header>
    <div class="list-tab">
      <van-tabs color="#1967f2">
        <van-tab title="基本信息" name="baseInfo">
          <van-form @submit="onSubmit" class="list-card">
            <van-cell-group inset v-for="(item, index) in detailsData" :key="index">
              <van-cell
                v-for="ele in item.list"
                :key="ele.prop"
                :value="ele.type !== 'label' ? ele.value : null"
                :label="ele.type === 'label' ? ele.value : null"
              >
                <template #title>
                  <van-icon
                    :name="ele.icon"
                    :color="ele.color"
                    style="margin-right: 10px"
                  />
                  <span>{{ ele.name }}</span>
                </template>
                <template #right-icon v-if="ele.type === 'phone' && ele.value">
                  <van-icon
                    name="phone-circle"
                    class="phone-icon"
                    color="#13b2a1"
                    @click="goToTelPage(ele.value)"
                  />
                </template>
                <template #label v-if="ele.prop.includes('List')">
                  <div class="list-casualties" v-if="ele.prop === 'typeItemVOList'">
                    <div v-for="(obj, idx) in ele.label" :key="idx" class="custom-title">
                      <div>
                        {{ obj.eventTypeName }} <span>{{ obj.itemContent || 0 }}</span>
                      </div>
                      {{ obj.unit || "人" }}
                    </div>
                  </div>
                  <div
                    class="list-casualties"
                    v-else-if="ele.prop.includes('List') && ele.label"
                  >
                    <!-- <fileShow  ref="fileRef" :showObject="ele.label" /> -->
                    <div
                      class="attach_area"
                      v-for="(item, index) in ele.label.attachmentList"
                      :key="index"
                      @click="downLoad(item, ele.type)"
                    >
                      <span
                        ><img src="../../assets/images/ppt.png" alt="" srcset=""
                      /></span>
                      <span>{{ item.name }}</span>
                    </div>
                  </div>
                </template>
              </van-cell>
            </van-cell-group>
          </van-form>
        </van-tab>
        <van-tab title="处理过程" name="process">
          <van-steps direction="vertical">
            <van-step v-for="item in recordList.list" :key="item.id">
              <h4>{{ item.content }}</h4>
              <p>{{ item.orgpername }}</p>
              <p>{{ item.dealtime }}</p>
            </van-step>
          </van-steps>
        </van-tab>
         <van-tab title="研判分析" name="ypfx">
          <van-field
            v-model="radius"
            center
            type="number"
            clearable
            label="周边距离"
            placeholder="请输入周边距离"
          >

            <template #button>
                          <span class="unit">km</span>
              <van-button size="small" type="primary" @click="getQueryBuffer">研判</van-button>
            </template>
          </van-field>

          <van-cell>
            <div v-html="ypResult" style="text-align:justify">
            </div> 
          </van-cell>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import Header from "@/views/common/header.vue";
import { Notify } from "vant";
import fileShow from "@/views/integratedQuery/fileShow.vue";
import { downloadReport } from "@/utils/validate";
import axios from "axios";
import taskRequest from '@/api/webcross/task';

@Component({
  components: {
    Header,
    fileShow,
  },
})
export default class add extends Vue {
  private radius = 5;
  private detailsData: any = [
    {
      name: "baseInfo",
      list: [
        {
          name: "信息标题",
          prop: "title",
          value: "",
          color: "#c8e3ee",
          icon: "coupon-o",
          type: "label",
        },
        {
          name: "信息摘要",
          prop: "infoAbstract",
          value: "暂无信息",
          color: "#eed49b",
          icon: "orders-o",
          type: "label",
        },
        {
          name: "处理情况",
          prop: "dealProcess",
          value: "暂无信息",
          color: "#e4bfa0",
          icon: "newspaper-o",
          type: "label",
        },
        {
          name: "研判分析",
          prop: "ypfx",
          value: "暂无信息",
          color: "#e4bfa0",
          icon: "newspaper-o",
          type: "label",
        },
      ],
    },
    {
      name: "originInfo",
      list: [
        {
          name: "接报时间",
          prop: "receiveTimeStr",
          value: "",
          color: "#b5dbe8",
          icon: "clock-o",
        },
        {
          name: "信息来源",
          prop: "infoSources",
          value: "",
          color: "#c8e3ee",
          icon: "peer-pay",
        },
      ],
    },
    {
      name: "conInfo",
      list: [
        {
          name: "事发时间",
          prop: "occurTimeStr",
          value: "",
          color: "#eed49b",
          icon: "underway-o",
        },
        {
          name: "事发地点",
          prop: "address",
          value: "",
          color: "#e4bfa0",
          icon: "location-o",
        },
        {
          name: "事件类型",
          prop: "eventTypeName",
          value: "",
          color: "#b5dbe8",
          icon: "font-o",
        },
        {
          name: "事件级别",
          prop: "eventLeveName",
          value: "",
          color: "#c8e3ee",
          icon: "bar-chart-o",
        },
        {
          name: "人员伤亡情况信息",
          prop: "typeItemVOList",
          value: "",
          color: "red",
          icon: "friends-o",
          label: [
            {
              name: "聚集场所",
              color: "#c8e3ee",
              value: "0",
            },
            {
              name: "事件起因",
              color: "#eed49b",
              value: "0",
            },
            {
              name: "涉及人群",
              color: "#e4bfa0",
              value: "0",
            },
            {
              name: "聚集人群",
              color: "#b5dbe8",
              value: "0",
            },
            {
              name: "围观人群",
              color: "#ce997b",
              value: "0",
            },
            {
              name: "死亡人数",
              color: "#323233",
              value: "0",
            },
            {
              name: "受伤人数",
              color: "#af5255",
              value: "0",
            },
            {
              name: "被困人数",
              color: "#cdb87a",
              value: "0",
            },
          ],
        },
      ],
    },
    {
      name: "peopleInfo",
      list: [
        {
          name: "承办人",
          prop: "editorName",
          value: "",
          color: "#c8e3ee",
          icon: "manager-o",
        },
        {
          name: "承办人电话",
          prop: "editorTel",
          value: "",
          color: "#eed49b",
          icon: "phone-o",
          type: "phone",
        },
        {
          name: "签发人",
          prop: "issuerName",
          value: "",
          color: "#b5dbe8",
          icon: "user-o",
        },
      ],
    },
    {
      name: "otherInfo",
      list: [
        {
          name: "附件",
          prop: "attachList",
          value: "",
          type: "xxjb",
          color: "#eed49b",
          icon: "photo-o",
        },
      ],
    },
    {
      name: "otherInfo",
      list: [
        {
          name: "收到的文电",
          prop: "receiveJournaList",
          value: "",
          type: "kwls",
          color: "#eed49b",
          icon: "photo-o",
        },
      ],
    },
    {
      name: "otherInfo",
      list: [
        {
          name: "相关文电",
          prop: "journalList",
          value: "",
          type: "kwls",
          color: "#eed49b",
          icon: "photo-o",
        },
      ],
    },
  ]; // 选项卡列表
  public type: any = ""; // 页面类型，add-新增，detail-详情
  public infoType: any = ""; // 页面信息类型
  private id: any = ""; // 接报信息id
  public details: any = {}; // 接报信息字段
  public showPicker: boolean = false; // 显示选择器
  public showCalendar: boolean = false; // 显示日历
  public calendarKey: any = ""; // 日历字段
  public levelList: any = [
    { text: "特别重大", code: "1" },
    { text: "重大", code: "2" },
    { text: "较大", code: "3" },
    { text: "一般", code: "4" },
  ];
  public showTypePicker: boolean = false; // 显示类型选择器
  public eventTypeList: any = []; // 事件类型
  public customFieldName: any = {
    text: "label",
  };
  public rules: any = {
    title: [
      { required: true, message: "请输入信息标题" },
      {
        pattern: /^[a-zA-Z\u4e00-\u9fa5\d-#,，。.!！？?@%&|*……、/+=-_():：：]{1,50}$/,
        message: "请输入50字以内标题",
      },
      // { pattern: /^\d{6}$/, message: '请输入6位数字' }
    ],
    editorId: [
      { required: true, message: "请输入编辑人" },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: "请输入正确的姓名" },
    ],
    editorTel: [
      { required: true, message: "请输入承办人电话" },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/, message: "请输入正确的电话号码" },
    ],
    issuerName: [
      { required: true, message: "请输入姓名" },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: "请输入正确的姓名" },
    ],
    readingPerson: [
      { required: true, message: "请输入姓名" },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: "请输入正确的姓名" },
    ],
    readerName: [
      { required: true, message: "请输入审稿人" },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: "请输入正确的审稿人姓名" },
    ],
  };
  private role: any;
  private ypResult={}
  private recordList: any = [];
  async mounted() {
    console.log("信息接报----》", this.$route.query);
    this.type = this.$route.query.type;
    this.infoType = this.$route.query.infotype;
    this.id = this.$route.query.id;
    this.role = JSON.parse(localStorage.getItem("role"));
    if (this.type === "add") {
      // this.details.reportTime = this["$moment"](new Date()).format("YYYY-MM-DD HH:mm:ss"); // 报送时间
      this.details.occurTime = this["$moment"](new Date()).format("YYYY-MM-DD HH:mm:ss"); // 事发时间
      this.details.tenantId = this.role.tenantId;
      this.details.reporter = this.role.name; // 报送人用户ID
      this.details.org_code = this.role.orgId; // 所属机构编码
      this.details.districtCode = "420100" || this.role.district; // 行政区划代码
    }
    // await this.getEventTypeList(); // 获取事件类型列表
    await this.getDetail();
    await this.getRecord();
  }
  getQueryBuffer() {
 
    taskRequest.queryBuffer({
        "longitude":this.details.longitude,
        'latitude':this.details.latitude,
        radius:Number(this.radius)*1000
      },(res)=>{
        console.log("res",res);
        if(res.data.status===200){
          this.ypResult = res.data.data
        } else {
          this.ypResult = '暂无数据'
        }
        
      })
  }
  // 获取事件类型列表
  getEventTypeList() {
    this["$api"].InfoRequest.getEventTypeList({}, (res) => {
      if (res.data.status === 200) {
        this.eventTypeList = [];
      }
    });
  }
  initEventType() {
    if (this.details.eventTypeCode) {
      this.eventTypeList?.forEach((el) => {
        if (el.id === this.details.eventTypeCode) {
          this.details.eventTypeName = el.label;
          return false;
        } else {
          if (el.children) {
            el.children.forEach((item) => {
              if (item.id === this.details.eventTypeCode) {
                this.details.eventTypeName = item.label;
                return false;
              }
            });
          }
        }
      });
    }
  }
  // 获取信息接报详情
  getDetail() {
    if (this.id) {
      this["$api"].InfoRequest.getEventDetail({ receiveId: this.id }, (res) => {
        console.log("getEventDetail-------->>>>>", JSON.parse(res.data.data));
        if (res.data.status === 200) {
          this.details = JSON.parse(res.data.data);
          this.getQueryBuffer()
          this.details.receiveJournaList = [this.details.receiveJournal];
          this.details.occurTime = this.formateDate(this.details.occurTime);
          this.details.receiveTime = this.formateDate(this.details.receiveTime);
          this.details.reportTime = this.formateDate(this.details.reportTime);
          this.details.updateTime = this.formateDate(this.details.updateTime);
          this.details.editorName = this.details.editorName || this.details.editorId;
          this.details.eventLevelName = this.details.eventLevelCode
            ? this.levelList.find((item) => item.code === this.details.eventLevelCode)
                .text
            : "";
          // this.initEventType();
          this.detailsData.forEach((item: any) => {
            item.list.forEach((ele: any) => {
              if (ele.prop === "address") {
                ele.value =
                  (this.details.districtName || "") + (this.details.cuDistrict || "");
              } else if (ele.prop === "typeItemVOList") {
                ele.label = this.details[ele.prop];
                ele.value = null;
              } else if (
                ele.prop === "attachList" ||
                ele.prop === "receiveJournaList" ||
                ele.prop === "journalList"
              ) {
                ele.value = null;
                this.details[ele.prop].forEach((myItem) => {
                  myItem.name = myItem.journalName || myItem.accessoryname;
                });
                ele.label = {
                  attachmentList: this.details[ele.prop],
                  showText: false,
                };
              } else {
                ele.value = this.details[ele.prop];
              }
            });
          });
        } else {
          Notify({ type: "danger", message: res.data.msg || "请求失败" });
        }
      });
    }
  }
  private getRecord() {
    this["$api"].InfoRequest.getEventRecordById({ receiveId: this.id }, (res) => {
      if (res.data.status === 200) {
        this.recordList = JSON.parse(res.data.data);
      } else {
        Notify({ type: "danger", message: res.data.msg || "请求失败" });
      }
    });
  }
  // 新增/保存信息接报详情
  saveInfo() {
    let params = Object.assign({}, this.details);
    // 格式化日期传参
    params.occurTime = params.occurTime
      ? this["$moment"](params.occurTime).format("YYYY-MM-DDTHH:mm:ss.SSS")
      : "";
    params.receiveTime = params.receiveTime
      ? this["$moment"](params.receiveTime).format("YYYY-MM-DDTHH:mm:ss.SSS")
      : "";
    params.reportTime = params.reportTime
      ? this["$moment"](params.reportTime).format("YYYY-MM-DDTHH:mm:ss.SSS")
      : "";
    params.updateTime = params.updateTime
      ? this["$moment"](params.updateTime).format("YYYY-MM-DDTHH:mm:ss.SSS")
      : "";
    params.reportDistCode = "420100" || this.role.district;
    params.editorId = this.role.name; // 编辑人
    params.dealStateCode = this.type === "add" ? "04" : "14"; // 处置状态,保存时传04，上报时传14
    this["$api"].InfoRequest.saveEvent(params, (res) => {
      if (res.data.status == 200) {
        Notify({ type: "success", message: res.data.msg || "上报成功" });
        this.$router.push("/infoReception");
      } else {
        Notify({ type: "danger", message: res.data.msg || "请求失败" });
      }
    });
  }
  // 选择事件级别
  onConfirmPicker(val, index) {
    // 选择器选中
    console.log("onConfirmPicker-------->", val, index);
    this.details.eventLevelCode = val.code;
    this.details.eventLevelName = val.text;
    this.showPicker = false;
  }
  // 选择事件类型
  onConfirmPickerType(val, index) {
    console.log("onConfirmPicker-------->", val, index);
    this.showTypePicker = false;
    this.details.eventTypeName = val[val.length - 1];
    this.details.eventTypeCode = this.eventTypeList[index[0]].children[index[1]].id;
  }
  // 选择日期
  onConfirmDate(val) {
    console.log("+++++++++++>>>>", val);
    this.showCalendar = false;
    this.details[this.calendarKey] = this["$moment"](val).format("YYYY-MM-DD HH:mm:ss");
  }
  // 确认上报按钮
  onSubmit() {
    console.log("确认上报按钮+++++++++", this.details);
    this.saveInfo();
  }
  formateDate(time) {
    // 兼容之前的时间格式
    if (time instanceof Object) {
      return this["$moment"](time.time).format("YYYY-MM-DD HH:mm:ss");
    } else {
      return this["$moment"](time).format("YYYY-MM-DD HH:mm:ss");
    }
  }
  //上传
  async afterRead(file) {
    let _this = this;
    file.status = "uploading";
    file.message = "上传中...";
    const formData = new FormData();
    formData.append("files", file.file);
    if (!this.details.receiveId) {
      await this.saveInfo();
    }
    formData.append("receiveId ", this.details.receiveId);
    this["$api"].InfoRequest.getAttach(formData, function (res) {
      console.log("getAttach-上传完成--->", res);
      if (res.data.status === 200) {
        file.status = "done";
        file.message = "上传完成";
        file.attachmentList = res.data;
        Notify({ type: "success", message: "上传成功" });
      } else {
        Notify({ type: "danger", message: "上传失败" });
      }
    });
  }

  //下载
  downLoad(item, type) {
    console.log("下载------------》", item, type);
    if (type === "kwls") {
      //  政务微信打开正常
      let type = item.name.split(".")[item.name.split(".").length - 1] || "";
      this["$api"].InfoRequest.getInfoFileUrl({ journalId: item.journalId }, (res) => {
        let fileExtArr = ["doc", "docx", "ppt", "pdf", "pptx", "txt", "xls", "xlsx"];
        if (res.data.status === 200) {
          let fileExt = res.data.data.substr(res.data.data.lastIndexOf(".") + 1);
          if (fileExtArr.indexOf(fileExt.toLowerCase()) > -1) {
            window.open(res.data.data);
          } else {
            window.open(`${res.data.data}.${type}`);
          }
        } else {
          Notify({ type: "danger", message: "获取链接地址失败" });
        }
      });
      return false;
    }
    const params: any = {
      fileId: item.receiveattachid || item.journalId,
      fileName: item.name,
      fileType: type,
    };
    this["$api"].InfoRequest.downLoadv2(params, (res) => {
   
      // downloadReport(res);
       let fileExtArr = ["doc", "docx", "ppt", "pdf", "pptx", "txt", "xls", "xlsx"];
        if (res.data.status === 200) {
          let fileExt = res.data.data.substr(res.data.data.lastIndexOf(".") + 1);
          if (fileExtArr.indexOf(fileExt.toLowerCase()) > -1) {
            window.open(res.data.data);
          } else {
            window.open(`${res.data.data}.${type}`);
          }
        } else {
          Notify({ type: "danger", message: "获取链接地址失败" });
        }
    });
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`;
  }
}
</script>
<style lang="less" scoped>
@url: "~@/assets/images/";
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-card {
    margin-top: 5px;
  }
  &-casualties {
    display: flex;
    flex-wrap: wrap;
    color: #323233;
    align-items: center;
    .custom-title {
      width: 50%;
      text-indent: 10px;
      height: 30px;
      line-height: 30px;
      display: flex;
      &:nth-child(2n + 1) span {
        color: #c8e3ee;
      }
      &:nth-child(2n) span {
        color: #e4bfa0;
      }
      &:nth-child(2n + 2) span {
        color: #af5255;
      }
      &:nth-child(4n) span {
        color: #323233;
      }
      div {
        display: flex;
        justify-content: space-between;
        width: 130px;
      }
      span {
        // display: inline-block;
        // width: 100%;
        font-size: 3.6vw;
        // text-align: right;
        font-weight: 600;
      }
    }
  }
  &-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: auto;
    /deep/.van-form {
      display: flex;
      flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
          // &:nth-child(3) {
          //   flex: 1;
          //   flex-direction: column;
          //   border: none;
          //   .van-cell__title {
          //     width: 100%;
          //     padding-bottom: 12px;
          //   }
          //   .van-cell__value {
          //     padding-top: 12px;
          //     .van-field__body {
          //       height: calc(100% - 25px);
          //       .van-field__control {
          //         height: 100%;
          //       }
          //     }
          //   }
          // }
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
      }
    }
  }
  .attach_area {
    display: flex;
    align-items: center;
    float: left;
    width: calc(100% - 3rem);
    margin-bottom: 10px;
  }
  .phone-icon {
    margin: 5px 0 0 5px;
  }
}
.unit{
  padding: 0 20px;
  display: inline-block;
  vertical-align: middle;
  padding-top: 5px;
  color: #333;
}
</style>
