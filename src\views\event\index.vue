<template>
  <div class="list">
    <Header title="事件管理" backPath="/"></Header>
    <div class="list-main">
      <div class="list-main-search">
        <van-search v-model="searchObj.eventTitle" placeholder="事件标题关键字搜索" @search="onSearch" />
      </div>
      <div class="list-main-cont" style="height: calc(100% - 100px)">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="100" @load="onLoad">
            <van-cell
              v-for="(item, index) in list"
              :key="index"
              :title="item.eventTitle"
              class="van-ellipsis"
              @click="handleToDetail(item)"
            />
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Notify } from 'vant';
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
const dataJson = require('../infoReception/data.json');
@Component({
  components: {
    Header
  }
})
export default class event extends Vue {
  searchObj: any = {
    eventTitle: '',
    nowPage: 1,
    pageSize: 10
  };
  list: any = [];
  total: any = 0;
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;
  mounted() {
    // this.getList()
  }
  getList() {
    const that = this;
    if (this.list.length && this.list.length === this.total) {
      return;
    }
    apiServer.eventList(this.searchObj, function (res) {
      if (res.status === 200) {
        const data = res.data.data;
        if (that.searchObj.nowPage === 1) {
          that.list = data.list;
        } else {
          that.list = [...that.list, ...data.list];
        }
        that.total = data.total;
        that.loading = false;
        that.refreshing = false;
        if (that.list.length >= data.total) {
          that.finished = true;
        } else {
          that.finished = false;
        }
        that.searchObj.nowPage++;
      } else {
        that.finished = true;
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    });
  }
  onSearch() {
    this.searchObj.nowPage = 1;
    this.getList();
  }
  // 跳转详情页面
  handleToDetail(item) {
    // this.$router.push({
    //   path: `/event/detail`,
    //   query: {
    //     eventtype: item.type
    //   }
    // })
    this.$router.push({
      path: '/event/eventDetail',
      query: {
        type: 'detail',
        infotype: 'event',
        id: item.eventId
      }
    });
  }
  // onLoad() {
  //   setTimeout(() => {
  //     if (this.refreshing) {
  //       this.list = [];
  //       this.refreshing = false;
  //     }
  //     this.getList();
  //     // this.list = dataJson.listData;

  //     this.loading = false;

  //     this.finished = true;
  //   }, 1000);
  // }
  // onRefresh() {
  //   // 清空列表数据
  //   this.finished = false;

  //   // 重新加载数据 将 loading 设置为 true，表示处于加载状态
  //   this.loading = true;
  //   this.onLoad();
  // }
  // 下拉刷新
  public onRefresh() {
    this.searchObj.nowPage = 1;
    this.list = [];
    // if (this.finished) return;
    this.getList();
  }
  // 上拉加载刷新
  public onLoad() {
    if (this.finished) return;
    this.getList();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    // position: absolute;
    // z-index: 1;
    // left: 0;
    // top: 46px;
    // height: calc(100vh - 46px);
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    // overflow: auto;
    &-search {
      height: 56px;
    }
    &-cont {
      // flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #03a9f4;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
}
</style>
