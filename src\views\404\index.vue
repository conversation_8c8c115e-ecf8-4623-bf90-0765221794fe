<template>
  <div class="errorpage">
    <van-empty image="error" description="404" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
  }
})
export default class error extends Vue {
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.errorpage {
  position: relative;
  width: 100%;
  height: 100%;
  background: #fff;
  /deep/.van-empty {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    .van-empty__description {
      font-size: 20px;
    }
  }
}
</style>