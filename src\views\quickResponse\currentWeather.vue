<template>
  <div class="weather">
    <Header title="当前天气"></Header>
    <div class="weather-content">
      <div class="left">
        <p><img :src="require(`../../assets/images/quickResponse/${weatherInfo.icon || 'cloudy'}.png`)"></p>
        <p>{{weatherInfo.temperature}}℃</p>
      </div>
      <div class="right">
        <p>{{$moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}}</p>
        <p>{{weatherInfo.phenomena}}</p>
        <p>{{weatherInfo.winddire}}{{weatherInfo.windpower}}</p>
      </div>
    </div>
    <div class="bottom">
      <i class="el-icon-location-information"></i>
      <span>湖北·{{weatherInfo.wrnm}}</span>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
import { Notify, ImagePreview, Toast } from 'vant';
import timeUtil from '../vue-calendar-component/calendar';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header,
  }
})
export default class currentWeather extends Vue {
  private params = {
    districtCode: '',
    endDate: '',
    startDate: '',
    queryDate: ''
  }
  private weatherInfo: any = {
    temperature: 4,
    phenomena: '多云',
    wrnm: '武汉',
    winddire: '东风',
    windpower: '3级',
    icon: 'cloudy'
  }

  mounted() {
    let yesterday = this['$moment'](new Date()).subtract(1, 'days')._d;
    yesterday = timeUtil.dateFormat(yesterday).replaceAll('/','-')
    this.params.queryDate = yesterday;
    this.params.endDate = yesterday;
    this.params.startDate = this['$moment'](new Date()).format('YYYY-MM-DD');
    const role = JSON.parse(localStorage.getItem('role'));
    this.params.districtCode = role.districtCode;
    let lon = this.$route.query.lon;
    let lat = this.$route.query.lat;
    if(lon && lat){
        this.getCurrentCode(lon,lat)
    } else {
       this.getCurrentWeather()
    }
   
  }
  getCurrentWeather() {
    taskRequest.getCurrentWeather(this.params, (res) => {
      if (res.data.status === 200) {
        if(res.data.data.list?.length) {
          this.weatherInfo = res.data.data.list[0];
          this.weatherInfo.icon = this.weatherInfo.phenomena.includes('多云') ? 'cloudy' : this.weatherInfo.phenomena.includes('晴') ? 'sun' : 'rainstorm';
        }
      }
    });
  }
  getCurrentCode(lon,lat){
    // 根据金维度获取当前code 
    apiServer.getCurrentCode({
       longitude:lon+'',
       latitude: lat+'',
        level: 2,
    },(res)=>{
      if(res.status =='200'){
        if(res.data.data.length){
             this.params.districtCode =res.data.data[0].code;
             this.$set(this.weatherInfo,'wrnm',res.data.data[0].name);
             this.getCurrentWeather();
        }
      }
      
    })
    
  }

}
</script>

<style lang="less" scoped>
.weather {
  .weather-content {
    display: flex;
    height: 150px;
    width: 96%;
    margin: 10px auto 3px;
    background-image: linear-gradient(to right, rgba(64,159,246, 0.5), rgba(106,206,238,0.6));
    border-radius: 5px;
    box-shadow: 1px 1px 5px 2px rgba(64,159,246, 0.5);
    color: #333;
    .left {
      width: 40%;
      // padding-left: 10px;
      text-align: center;
      font-size: 24px;
      p:nth-child(2) {
        margin: -20px 0 0 0;
      }
    }
    .right {
      flex: 1;
      padding-right: 20px;
      text-align: right;
      font-size: 18px;
    }
  }
  .bottom {
    background-color: #fff;
    padding: 5px 0;
    width: 60%;
    margin: 0 auto;
    border-radius: 0 0 10px 10px;
    text-align: center;
    font-size: 16px;
    box-shadow: 1px 1px 3px 1px #fff;
    i {
      margin-right: 15px;
    }
  }
}
</style>