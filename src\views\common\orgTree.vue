<template>
  <!-- 机构树单选 -->
  <div class="div_big">
    <div class="city_big">
      <div class="div_tabs">
        <div class="div_tab" @click="clickTab(index)" :class="{active:item.active}" v-for="(item,index) in tabs" :key="index">
          {{item.name}}
          <span :class="{active:item.active}"></span>
        </div>
      </div>
      <div class="div_content" ref="orgTreeDiv">
        <li v-for="(item,index) in listArr"  :key="index">
          <span class="select_moudle" >
            <img src="@/assets/images/icons/icon_org.svg" alt="" srcset="">
          </span>
          <span @click="handleData(item,0)">{{item.orgName}}</span>
          <span @click="handleData(item)" v-if="item.children.length > 0">
            <img src="@/assets/images/icons/icon_next.svg" alt="" srcset=""><font>展开</font>
          </span>
        </li>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component({
  name: 'orgTree',
  components: {}
})
export default class orgTree extends Vue {
  @Prop() options: any;
  private tabs: any = [{ "code": "0", "name": "请选择", "parentId": "0", "active": false }];
  private listArr: any = [];

  mounted() {
    this.getList();
  }

  private getList() {
    this.listArr = this.options;
  }

  private handleData(data, type) {
    if (type === 0) {
      // if (this.tabs[0].code === '0') {
      //   this.tabs[0] = {
      //     code: data.orgCode,
      //     name: data.orgName,
      //     parentId: data.parentCode,
      //     active: true,
      //   }
      // } else {
      //   this.tabs[this.tabs.length - 1] = {
      //     code: data.orgCode,
      //     name: data.orgName,
      //     parentId: data.parentCode,
      //     active: true,
      //   }
      // }
      this.$emit("handleOrg", data)
    } else {
      this.listArr = data.children;
      if (this.tabs[0].code === '0') {
        this.tabs[0] = {
          code: data.orgCode,
          name: data.orgName,
          parentId: data.parentCode,
          active: true,
        }
      } else {
        const obj = {
          code: data.orgCode,
          name: data.orgName,
          parentId: data.parentCode,
          active: true,
        }
        this.tabs.push(obj)
      }
    }
  }

  private clickTab(index) {
    if (index === (this.tabs.length - 1)) return;
    this.tabs.splice(index + 1);
    this.listArr = this.getItemChildren(this.options, this.tabs[index].code);
  }

  private getItemChildren(data, code) {
    let arr: any = [];
    data.forEach((item: any) => {
      if (item.children && item.children.length > 0) {
        this.getItemChildren(item.children, code)
      }
      if (item.orgCode === code) {
        arr = item.children;
      }
    })
    return arr;
  }
}
</script>


<style lang="less" scoped>
@import './menuLinkage/menuLinkage.less';
</style>