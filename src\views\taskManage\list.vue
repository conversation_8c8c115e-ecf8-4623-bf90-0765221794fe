<template>
  <div class="div_big">
    <van-nav-bar title="任务列表" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
  
    </template>
    <MeetBack @documentBack="documentBack"></MeetBack>
   </van-nav-bar>
     <!-- <Header title="任务列表"></Header> -->
    <commonList :loadObj="{name:'listItem'}" :locationInfo='locationInfo' :tabArr="tabData" :searchObj="{whetherHave:true,placeholder:'请输入搜索关键词'}"></commonList>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import commonList from '../common/commonList.vue';
import apiServer from '../../api/request-service';
import taskRequest from '@/api/webcross/task';
import MeetBack from '@/components/meet-back/meet-back';

@Component({
  components: {
    commonList,
    MeetB<PERSON>
  }
})
export default class Home extends Vue {
  tabData = [];
  selected: any = 0;
  refreshing: any = false; 
  loading: any = false;
  showDetail: any = false;
  pageIndex: any = 1;
  pageSize: any = 10;
  keyword:any ="";
  open:any = false;
  position:any = 'left';
  queryMap: any ={};
  locationInfo:any={};
  onClickLeft(){
    console.log("返回点击")

  }
  clickMore(){
    console.log("打开菜单")
    this.open=true;
  }
  handleObj(item){
    let _this=this;
    console.log(item)
    if(!item.uploadMore){
      _this.refreshing=false;
      _this.loading=false;
    }
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.pageIndex=1;
    let pageObj={pageIndex:1,pageSize:_this.pageSize}
    _this.$refs.itemRef['queryRequest'](pageObj)
  }
  load() {
    const _this = this;
    _this.pageIndex++;
    _this.loading = true;
    let pageObj={pageIndex:_this.pageIndex,pageSize:_this.pageSize}
    _this.$refs.itemRef['queryRequest'](pageObj)
  }
  GoBack(){
    this.$router.go(-1)
  //  let _this=this;
  //   try {
  //       _this['$gsmdp'].finish(function(){
  //         console.log("Gback")
  //       })
  //   } catch (error) {
  //       console.log("close error")
  //   }
  }
  closeDetail(){
      this.showDetail=false;
  };
  documentBack() {
    this.GoBack();
  }
  created() {
    let _this=this;
     _this.$enJsBack();
     _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
     // _this.GoBack()
    })
  }
  mounted(){
    let _this = this;
    // window['GoBack']=this.GoBack;//安卓调用GoBack（手机back键返回问题）
         _this['$gsmdp'].initGoback(function() {
      console.log('调用返回');
      _this.GoBack();
    });
    this.locationInfo =JSON.parse (this.$route.query.locationInfo as  any).locInfo;
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div{
    width: 100%;
  }
}
.content_list{
  height: calc( 100% - 46px );
   padding: 0 15px;
   overflow: auto;
  background: #f1efef;
}
.showMenu{
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
      color: white;
      
}

.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  .van-tabs{
    margin-bottom: 10px;
  }
  .van-tabs__line {
    background-color: #1967f2;
  }
  .van-ellipsis {
    margin-bottom: 10px;
  }
  .list_item {
    max-width: 350px;
    background: white;
    .text_ellipsis{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .taskTitle{
        display: flex;
        justify-content: space-between;
        align-items: center;
        p{
            font-size: 16px;
            font-weight: bold;
            width: 280px;
            margin: 10px 0 !important;
        }
        span{
            text-align: right;
        }
    }
    .taskContent{
        font-size: 14px;
    }
    .taskFooter{
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        span i{
            color: rgb(67,89,223);
        }
    }
  }
 
}
</style>
