import { EnvironmentContext } from './environmentContext';
import bufferUtils from './bufferUtils';
let egis = require('egis-2d');

/**
 * 图层工具类，用于在地图上显示图层数据
 */
export default {
  /**
   * 当前活动的点位图层
   */
  activeLayer: null as any,

  /**
   * 缓冲区图层
   */
  bufferLayer: null as any,

  /**
   * 事件点位图层
   */
  eventLayer: null as any,

  /**
   * 当前定位图层
   */
  currentPointLayer: null as any,

  /**
   * 清除当前点位图层
   */
  clearLayer() {
    if (this.activeLayer) {
      EnvironmentContext.egisMap.removeLayer(this.activeLayer);
      this.activeLayer = null;
    }
  },

  /**
   * 清除缓冲区图层
   */
  clearBufferLayer() {
    if (this.bufferLayer) {
      EnvironmentContext.egisMap.removeLayer(this.bufferLayer);
      this.bufferLayer = null;
    }
  },

  /**
   * 显示点位数据
   * @param dataList 数据列表
   * @param options 配置选项
   */
  showPointList(dataList: any[], options: any = {}, callback: any = null) {
    // 清除之前的图层
    this.clearLayer();

    // 创建新的元素图层
    this.activeLayer = new egis.carto.ElementLayer({
      map: EnvironmentContext.egisMap
    });
    this.activeLayer.on('click', (x, y, lon, lat, i) => {
      const element = i.element;
      if (element) {
        callback ? callback({ id: element.id, title: element.name, key: options.key }) : this.locationHighlight(element.id);
      }
    });
    // 添加图层到地图
    EnvironmentContext.egisMap.addLayer(this.activeLayer);

    // 遍历数据创建点
    dataList.forEach((item) => {
      // 获取经纬度
      const longitude = item.longitude || item.lon || item.jd;
      const latitude = item.latitude || item.lat || item.wd;

      // 检查经纬度是否有效
      if (!longitude || !latitude || isNaN(Number(longitude)) || isNaN(Number(latitude))) {
        console.warn('无效的经纬度数据:', item);
        return;
      }

      // 创建点对象
      const point = new egis.sfs.Point({
        x: Number(longitude),
        y: Number(latitude),
        spatialReference: 4326
      });

      // 创建标记符号
      let symbol: any;
      if (options.useImage && options.imageUrl) {
        // 使用图片标记
        symbol = new egis.sfs.PictureMarkerSymbol({
          source: options.imageUrl,
          width: 34,
          height: 46,
          offsetX: 16,
          offsetY: 32,
          opacity: 1,
          rotation: 0,
          scale: 0.8
        });
      } else {
        // 使用简单标记
        symbol = new egis.sfs.SimpleMarkerSymbol({
          fillColor: new egis.sfs.Color({ r: 255, g: 0, b: 0, a: 255 }),
          size: options.size || 8,
          style: 'circle'
        });
      }

      // 创建元素
      const element = new egis.sfs.Element({
        id: item.id,
        name: item.name,
        geometry: point,
        symbol: symbol
      });

      // 添加元素到图层
      this.activeLayer.add(element);
    });

    // 如果有数据，设置地图中心点为第一个点的位置
    if (dataList.length > 0 && options.setCenter) {
      const firstItem = dataList[0];
      const longitude = firstItem.longitude || firstItem.lon || firstItem.jd;
      const latitude = firstItem.latitude || firstItem.lat || firstItem.wd;

      if (longitude && latitude) {
        const point = new egis.sfs.Point({
          x: Number(longitude),
          y: Number(latitude),
          spatialReference: 4326
        });
        EnvironmentContext.egisMap.setCenter(point);
      }
    }

    return this.activeLayer;
  },

  /**
   * 添加点击事件
   * @param layer 图层
   * @param callback 回调函数
   */
  addClickEvent(layer: any, callback: Function) {
    if (!layer) return;

    // 为图层添加点击事件
    layer.on('click', (e: any) => {
      if (e && e.element && e.element.attributes) {
        callback(e.element.attributes);
      }
    });
  },

  /**
   * 在地图上显示缓冲区
   * @param center 中心点坐标 [longitude, latitude]
   * @param radius 半径，单位：公里
   * @param options 配置选项，如颜色、透明度等
   * @returns 创建的缓冲区图层
   */
  showBufferOnMap(center: [number, number], radius: number, options: any = {}) {
    // 清除当前缓冲区图层
    this.clearBufferLayer();
    // 创建缓冲区图层
    this.bufferLayer = new egis.carto.ElementLayer({
      map: EnvironmentContext.egisMap
    });

    // 创建圆对象
    const circle = bufferUtils.createCircle(center, radius);

    // 创建圆符号
    const symbol = new egis.sfs.SimpleFillSymbol({
      fillColor: new egis.sfs.Color(options.fillColor || { r: 255, g: 0, b: 0, a: 0 }), // 默认半透明橙色
      borderColor: new egis.sfs.Color(options.borderColor || { r: 255, g: 0, b: 0, a: 255 }),
      borderThickness: options.borderThickness || 2
    });

    // 创建元素
    const element = new egis.sfs.Element({
      geometry: circle,
      symbol: symbol
    });
    // 添加元素到图层
    this.bufferLayer.add(element);

    // 添加图层到地图
    EnvironmentContext.egisMap.addLayer(this.bufferLayer);

    // 将地图中心移动到圆心;
    this.setBufferCenter(radius);

    return this.bufferLayer;
  },

  setBufferCenter(radius) {
    const center = this.bufferLayer.elements[0].geometry.getCenter();
    EnvironmentContext.egisMap.setCenter(center);
    if (radius < 2) {
      EnvironmentContext.egisMap.zoomTo(14);
    } else if (radius < 4) {
      EnvironmentContext.egisMap.zoomTo(13);
    } else if (radius < 6) {
      EnvironmentContext.egisMap.zoomTo(12);
    } else if (radius < 11) {
      EnvironmentContext.egisMap.zoomTo(11);
    } else {
      EnvironmentContext.egisMap.zoomTo(10);
    }
  },

  // 事件点位上图
  addEventToMap(center) {
    if (this.eventLayer) {
      EnvironmentContext.egisMap.removeLayer(this.eventLayer);
    }
    this.eventLayer = new egis.carto.ElementLayer({
      map: EnvironmentContext.egisMap
    });
    console.log('+++++elementLayer+++++++++++++++++++++', this.eventLayer);
    EnvironmentContext.egisMap.addLayer(this.eventLayer);
    var point = new egis.sfs.Point({
      x: center[0],
      y: center[1],
      spatialReference: 4326
    }); //创建一个点对象

    //创建图片符号
    var pictureSymbol = new egis.sfs.PictureMarkerSymbol({
      source: 'img/marker/event.gif',
      width: 90,
      height: 106,
      offsetX: 45,
      offsetY: 106,
      opacity: 1,
      rotation: 0,
      scale: 0.5
    });
    //创建元素
    var element = new egis.sfs.Element({
      geometry: point,
      // symbol: simpleMarkerSymbol
      symbol: pictureSymbol
    });
    (this.eventLayer as any).add(element); //把创建的元素添加到元素图层中
    // EnvironmentContext.egisMap.setCenter(point);
  },

  /** 画点 */
  addPointToMap(center) {
    if (this.currentPointLayer) {
      EnvironmentContext.egisMap.removeLayer(this.currentPointLayer);
    }
    this.currentPointLayer = new egis.carto.ElementLayer({
      map: EnvironmentContext.egisMap
    });
    EnvironmentContext.egisMap.addLayer(this.currentPointLayer);
    var point = new egis.sfs.Point({
      x: center[0],
      y: center[1],
      spatialReference: 4326
    }); //创建一个点对象
    //创建图片符号
    var pictureSymbol = new egis.sfs.PictureMarkerSymbol({
      source:
        'data:image/png;base64,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',
      width: 32,
      height: 38,
      offsetX: 16,
      offsetY: 38,
      opacity: 1,
      rotation: 0
    });
    //创建元素
    var element = new egis.sfs.Element({
      geometry: point,
      // symbol: simpleMarkerSymbol
      symbol: pictureSymbol
    });
    (this.currentPointLayer as any).add(element); //把创建的元素添加到元素图层中
    EnvironmentContext.egisMap.setCenter(point);
  },

  /**
   * 定位高亮
   */
  locationHighlight(id) {
    if (EnvironmentContext.egisMap) {
      // 如果地图上有图层，则高亮显示选中的点位
      if (this.activeLayer) {
        const elements = this.activeLayer.elements;
        if (elements && elements.length > 0) {
          // 先重置所有点位的样式
          elements.forEach((element: any) => {
            element.symbol.scale = 0.8;
            this.activeLayer.update(element, true);
          });

          // 找到匹配的点位并高亮显示
          const matchedElement = elements.find((element: any) => {
            return element.id && id && element.id === id;
          });

          if (matchedElement) {
            matchedElement.symbol.scale = 1.0;
            this.activeLayer.update(matchedElement, true);
            // 设置地图中心点
            EnvironmentContext.egisMap.setCenter(matchedElement.geometry);
            // 设置缩放级别
            EnvironmentContext.egisMap.zoomTo(16);
          }
        }
      }
    }
  },

  /**
   * 清除高亮状态
   * 将所有点位恢复为正常大小
   */
  clearHighlight() {
    if (EnvironmentContext.egisMap) {
      // 如果地图上有图层，则重置所有点位的样式
      if (this.activeLayer) {
        const elements = this.activeLayer.elements;
        if (elements && elements.length > 0) {
          // 重置所有点位的样式
          elements.forEach((element: any) => {
            element.symbol.scale = 0.8;
            this.activeLayer.update(element, true);
          });
        }
      }
    }
  }
};
