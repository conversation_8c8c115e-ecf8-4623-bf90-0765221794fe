<template>
    <div class="info_div">
      <van-nav-bar title="知识详情" left-text="返回"   left-arrow  @click-left="GoBack" >
        <template #right>
          <!-- <van-icon name="ellipsis" size="25" /> -->
        </template>
      </van-nav-bar>
      <MeetBack @documentBack="documentBack"></MeetBack>
      <div class="info_content">
        <ul>
          <li>
            <p>{{detailInfo.title}}</p>
          </li>
          <li class="info_cell">
            <span>事件类型：</span>
            <span>{{detailInfo.typeCodeName}}</span>
          </li>
          <li class="info_cell">
            <span>应急知识来源：</span>
            <span>{{detailInfo.knoSource}}</span>
          </li>
          <li class="info_cell">
            <span>发布日期：</span>
            <span>{{detailInfo.publishTime}}</span>
          </li>
          <li class="info_cell">
            <span>关键字：</span>
            <span>{{detailInfo.planLevelName}}</span>
          </li>
          <li class="info_cell">
            <span>应急知识类型：</span>
            <span>{{detailInfo.typeName}}</span>
          </li>
          <li class="info_cell">
            <span>数据来源单位：</span>
            <span>{{detailInfo.publishOrgName}}</span>
          </li>
           
        </ul>
        <div class="text_area_div" >
          <div class="text_area_title">应急知识概要</div>
          <div class="text_area_centent">
            {{detailInfo.notes==''||detailInfo.notes==null?"无应急知识概要":detailInfo.notes}}
          </div>
        </div>
        <!-- <div class="info_div_attachment">
          <span>附件：</span>
          <span>
            <div>
              <img src="../../assets/images/ppt.png" alt="" srcset="">
              <div>关于此次行动的可行性.doc</div>
            </div>
            <div>
              <img src="../../assets/images/ppt.png" alt="" srcset="">
              <div>关于此次行动的可行性.doc</div>
            </div>
          </span>
        </div> -->
        <div class="text_area_div" >
          <div class="text_area_title">附件</div>
          <div class="text_area_centent">
            <file-preview direction="left" :fileList="detailInfo.attachmentList"></file-preview>
          </div>
        </div>
      </div>
      
    </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import dataHandle from './dataHandle';
import FilePreview from '@/components/filePreview.vue';
import MeetBack from '@/components/meet-back/meet-back';
@Component({
  components: {
    FilePreview,
    MeetBack
  },
})
export default class integratedItem extends Vue {
  @Prop(String) private requestId :string;
  @Prop(Object) private requestObj :any;
  detailInfo: any = {};
  showObject: any = {};
  infoRequest(){
    let _this=this;
    let param={
      id:_this.requestId
    }
    apiServer.findKnowledgeInfo(param,function(res){
        console.log(res)
        let dataHandleInfo= dataHandle.knowledgeInfo(res);
        _this.detailInfo=dataHandleInfo;
        
    })
    
  }
  GoBack(){
    let _this=this;
    try {
        _this.$emit("close")
    } catch (error) {
        console.log("close error")
    }
  }
  documentBack() {
    this.GoBack();
  }
  created() {
    let _this=this;
    //_this['$apiServer'].setToken();
    console.log("init run")
    _this.infoRequest();
    // setTimeout(function() {
      
    // }, 500);
    
  }
  mounted(){
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      _this.GoBack()
    })
  }
    

}
</script>
<style scoped lang="scss">
.info_div{
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #f4f7f8;
  .info_content{
    width: 100%;
    height: calc( 100% - 46px );
    overflow-y: auto;
    ul{
      width: 100%;
      background: #fff;
    }
    li{
      width: 100%;
      padding: 0px 3%;
      p{
        margin: 0;
        line-height: 3rem;
        font-size: 17px;
        font-weight: bold;
      }
    }
    .info_cell{
      line-height: 2.5rem;
      color: #333333;
      overflow: hidden;
      span{
        display: inline-block;
      }
      span:nth-of-type(1){
        width: 5rem;
         float: left;
      }
      span:nth-of-type(2){
        width: calc( 100% - 5rem );
        
      }
    }
    .text_area_div{
      width: 100%;
      .text_area_title{
        width: 100%;
        height: 2.5rem;
        line-height: 2.5rem;
        padding: 0px 3%;
        color: c0c0c0;
      }
      .text_area_centent{
        width: 100%;
        background: #fff;
        line-height: 2.5rem;
        padding: 0px 3%;
      }
      
    }
    .info_div_attachment{
      width: 100%;
      background: #fff;
      padding: 0px 3%;
      span{
        display: inline-block;
        float: left;
        line-height: 2.5rem;
        img{
          width: 1.5rem;
          vertical-align: middle;
          float: left;
          margin-top: .3rem;
        }
      }
      span:nth-of-type(1){
        width: 3rem;
      }
      span:nth-of-type(2){
        width: calc( 100% - 3rem );
      }
    }
  }
}
</style>