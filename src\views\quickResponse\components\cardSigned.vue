<template>
  <!-- 已签到 -->
  <div class="teamSignIn-item">
    <div class="teamSignIn-user">省应急厅</div>
    <div class="teamSignIn-info">
      <p>省应急厅</p>
      <div class="teamSignIn-signed">
        <div class="sign-box">
          <i></i>
          <van-button type="default">已签到</van-button>
        </div>
      </div>
      <div class="signed"><i></i>{{ teamInfo.teamName }} 已完成签到</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
@Component({
  components: {
  }
})
export default class cardSigned extends Vue {
  @Prop() public teamInfo: any;
}
</script>

<style lang="less" scoped>
</style>