<template>
  <!-- 任务列表 -->
  <div class="teamSignIn-item task">
    <div class="teamSignIn-user">{{ taskInfo.orgName || "省应急厅" }}</div>
    <div class="teamSignIn-info">
      <p style="font-weight: bold">{{ taskInfo.orgName || "省应急厅" }}</p>
      <div class="teamSignIn-boxes">
        <h2 class="task-title" v-if='taskInfo.replyTypeCode!=9'>
          <i></i>
          <span>{{ taskInfo.teamTask ? taskInfo.teamTask.taskTitle : "-" }}</span>
        </h2>
           <h2 class="task-title" v-if='taskInfo.replyTypeCode==9'>
          <i></i>
          <span>应急响应通知</span>
        </h2>
        <template v-if='taskInfo.replyTypeCode!=9'>
              <div class="teamSignIn-maines">
              <h2 class="sign-content">
                <span>任务内容</span>
                <span
                  class="sign"
                  @click="
                        taskInfo.teamTask &&
                      taskInfo.teamTask.teamTaskStatusCd === '0' &&
                      handleTaskSign()
                  "
                  >{{
                    filterStatus(taskInfo.teamTask ? taskInfo.teamTask.teamTaskStatusCd : "0")
                  }}</span
                >
              
              </h2>
          <div class="teamSignIn-maines-p">
            {{ taskInfo.teamTask ? taskInfo.teamTask.taskText : "-" }}
            <p class='btn-list btn-right' v-if='docUrl'>
                  <span  @click="checkDocList">查看调令</span>
            </p>
          </div>
          <p >
            </p>
          <h2>
            拟完成时间
             {{ taskInfo.teamTask ? taskInfo.teamTask.taskEndPlanDate : "-" }}
          </h2>
         
            
          <div class="map-content">
            <!-- <h2>{{ taskInfo.teamTask ? taskInfo.teamTask.teamName : "-" }}</h2> -->
            <p>{{ taskInfo.eventAddress ||"-" }}</p>
            <div class="map" @click="guideLoc('gd')">
              <BaseMap
                :mapId="`map_${
                  taskInfo.teamTask
                    ? taskInfo.teamTask.taskId
                    : new Date(taskInfo.replyDate).getTime()
                }`"
                :longitude="taskInfo.teamTask ? taskInfo.teamTask.teamTaskLongitude : '0'"
                :latitude="taskInfo.teamTask ? taskInfo.teamTask.teamTaskLatitude : '0'"
              ></BaseMap>
            </div>
          </div>
        </div>
        <div class="end-btn">
          <van-button
            block
            round
            :disabled="taskInfo.teamTask && taskInfo.teamTask.teamTaskStatusCd === '50'"
            type="primary"
            @click="overTask"
          >
            任务完成
          </van-button>
        </div>
        </template>
        <template v-else>
                 <div class="teamSignIn-maines">
                  <p class="teamSignIn-maines-p" v-html=title></p>
          <div class="map-content">
           <div class="btn-list">
              <p @click="navToTask(taskInfo)">待开展任务 <b style="margin-right:2px">{{taskInfo.responseNotice.notFinishTaskCount}}</b>条</p>
              <span @click="checkDuty(taskInfo)">查看职责</span>
           </div>
            <div class="map" @click="guideLoc('gd')">
              <BaseMap
                :mapId="`map_${
                  taskInfo.teamTask
                    ? taskInfo.teamTask.taskId
                    : new Date(taskInfo.replyDate).getTime()
                }`"
                :longitude="taskInfo.eventLongitude"
                :latitude="taskInfo.eventLatitude"
              ></BaseMap>
            </div>
          </div>
        </div>
        </template>
      
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import BaseMap from "@/components/baseMap/baseMap.vue";
import taskRequest from "@/api/webcross/task";
import { Notify, ImagePreview, Toast } from "vant";
import {guide} from '@/utils/getLocation'


@Component({
  components: {
    BaseMap,
  },
})
export default class cardTask extends Vue {
  @Prop() public taskInfo: any; // 任务信息
  @Prop() public locationInfo: any; // 地理位置信息
  @Prop() public detailInfo:any;
  @Prop() public docList:any
  private title = '';
  private showCardTask = false;

  private checkDocList(){
    if(this.docUrl){
            window.open(window.location.origin+'/'+ this.docUrl)
    } else {
           Notify({type:'danger',message:'当前暂无相关调令'})
    }
  }
  private docUrl ='';
  @Watch('docList',{
    deep:true,
  })
  handleData(){
    let url ='';
    for(var i=0;i<this.docList.length;i++){
            if(this.docList[i].contactTel == this.detailInfo.relMobile){
                     url = this.docList[i].viewUrl;
                     this.docUrl = url;
                     break;
            }
        };
  }

  overTask() {
    // 结束任务
    this.$emit("onClickFinishTask", this.taskInfo);
  }

  private mounted(){
    this.title = this.taskInfo.responseNotice.eventTitle;
    //  解析文本
                let  levelCount = -1;
                levelCount = this.title.search('级响应');
                
                let levelAlbNum = this.title.slice(levelCount-1,levelCount);
                let levelClass= 0;

                switch(levelAlbNum){
                  case 'Ⅰ':
                  levelClass = 1;
                  break;
                  case 'Ⅱ':
                  levelClass = 2;
                  break;
                  case 'Ⅲ':
                  levelClass = 3;
                  break;
                  case 'Ⅳ':
                  levelClass = 4;
                  break;
                };

                let levelText =`<a class="level${levelClass}">${this.title.slice(levelCount-1,levelCount+3)}</a>`;
                let unqinoLeft  = this.title.indexOf('》');
                let unqinoRight = this.title.indexOf('《');
                let unqinoText =`<a class="level${levelClass}">${this.title.slice(unqinoRight,unqinoLeft+1)}</a>`;
                if(unqinoLeft>-1&& unqinoRight>-1 &&levelCount>-1){
                    this.title = this.title.slice(0,unqinoRight)+unqinoText+this.title.slice(unqinoLeft+1,levelCount-1)+levelText+
                       this.title.slice(levelCount+3,this.title.length);
                }
  }

  // 任务签到
   handleTaskSign() {
    taskRequest.teamTaskSign({ 
        teamTaskId: this.taskInfo.teamTask.teamTaskId,
       latitude:this.locationInfo.locInfo.latitude,
        longitude:this.locationInfo.locInfo.longitude,
        address:this.locationInfo.locInfo.address }, (res) => {
      if (res.data.status === 200) {
        Notify({ type: "success", message: "队伍签到成功" });
        this.$emit("onTeamSign", this.taskInfo);
      } else {
        Notify({ type: "danger", message: res.data.msg || "队伍签到失败" });
      }
    });
  }

  filterStatus(val) {
    let signStatus: any = "";
    switch (val) {
      case "0":
        signStatus = "签收";
        break;
      case "10":
        signStatus = "进行中";
        break;
      case "50":
        signStatus = "已完成";
        break;
    }
    return signStatus;
  }
  //导航
  guideLoc(signMap) {
    let self = this;
    console.log("导航--------？？？AAAA", self.taskInfo.teamTask);
    if ((self.taskInfo.teamTask && self.taskInfo.teamTask.teamTaskLongitude)||(self.taskInfo.longitude)||(self.taskInfo.eventLongitude)) {
      //景点位置partnerAddress 景点经纬度lng lat
      var lng = this.taskInfo.teamTask?.teamTaskLongitude||self.taskInfo.longitude||self.taskInfo.eventLongitude;
      var lat = this.taskInfo.teamTask?.teamTaskLatitude||self.taskInfo.latitude||self.taskInfo.eventLatitude;
      let address = self.taskInfo.eventAddress;
      let locInfo ={
         lng,
         lat,
         address,
      };
      guide(signMap,locInfo);
    } else {
      Notify({ type: "danger", message: "请先获取当前位置" });
    }
  }
  private navToTask(row){
  this.$router.push({
      path: '/listTask',
       query: {
        eventId: row.responseNotice.eventId,
        teamId: row.responseNotice.teamId,
        locationInfo: JSON.stringify(this.locationInfo)
      }
    });
  }
  private checkDuty(row){
    this.$router.push({
      path: '/command',
      query:{
        eventId:this.detailInfo.eventId,
        districtCode:this.detailInfo.districtCode,
       groupId:row.responseNotice.groupId
      }
    });
  }
  
}
</script>

<style lang="less" scoped>
.sign-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .sign {
    width: 80px;
    text-align: right;
    color: #1967f2;
  }
}
.btn-list{
  display: flex;
  justify-content: space-between;
  &.btn-right{
    justify-content: flex-end;
  }
  span{
    text-decoration: underline;
    color: rgb(97,142,255);
  }
  p{
    color: rgba(162,162,162);
  }
  b{
     text-decoration: underline;
    color: rgb(97,142,255);
  }
}




</style>
