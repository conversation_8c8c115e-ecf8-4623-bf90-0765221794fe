import apiServer from '../request-service';

const infoRequest = {
  // 获取突发事件列表 qType='1'
  getEventList(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/importInfo/search/v1`;
    let url = `/gemp-app/api/gemp/app/importInfo/search/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 获取突发事件处理记录
  getEventRecordById(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/importInfo/dealFlow/v1`;
    let url = `/gemp-app/api/gemp/app/importInfo/dealFlow/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 值守平台突发事件附件接口
  getAttach(data, callback) {
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/receive/attach/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 值守平台突发事件接口-保存、上报
  saveEvent(data, callback) {
    // let url = `/gemp-data-sync-zg/api/gemp/dataSync/consume/iamsService/receive/report/v1`;
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/receive/report/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  //批示内容
  approveSearch(data, cb) {
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/approve/check/search/v1/?relativeId=${data.relativeId}`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },

  approveCheck(data, cb) {
    let name = data.userName
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/approve/check/save/v1/?relativeId=${data.relativeId}&contents=${data.contents}&userName=`+ decodeURI(decodeURI(name));
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  
  // 值守平台突发事件信息详情接口, {"receiveId":"xxxxxxx"}
  getEventDetail(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/eventReceive/receive/view/v1`;
    let url = `/gemp-app/api/gemp/app/eventReceive/receive/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 值守平台突发事件信息详情接口, {"receiveId":"xxxxxxx"}
  getLimleadappdealList(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/evnlimleadapprove/limleadappdeal/search/v1`;
    let url = `/gemp-app/api/gemp/app/evnlimleadapprove/limleadappdeal/search/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },

  //领导批示列表1
  reportreviewList(data, callback) {
    let url = `/gemp-app/api/app/gemp/plan/reportreview/list/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },

   //领导批示列表1
   reportreviewAdd(data, callback) {
    let url = `/gemp-app/api/app/gemp/plan/reportreview/add/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },


  // 获取事件类型列表
  getEventTypeList(data, cb) {
    let url = `/gemp-duty/api/gemp/duty/info/event/type/tree/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 值守平台普通事件上报接口
  getOrdinaryEventReport(data, callback) {
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/ordinary/report/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 值守平台普通事件详情接口
  getOrdinaryDetail(data, callback) {
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/ordinary/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 值守平台普通事件详情接口-新
  getOrdinaryDetailNew(data, callback) {
    let url = `/gemp-app/api/gemp/app/ordinary/ordinary/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
   // 值守平台普通事件附件接口
   getOrdinaryAttach(data, callback) {
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/ordinary/attach/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 领导批示详情接口, {"approveId":"xxxxxxx"}
  getLeaderDetail(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/evnlimleadapprove/approve/view/v1`;
    let url = `/gemp-app/api/gemp/app/evnlimleadapprove/approve/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 部领导批示详情接口, {"id":"xxxxxxx"}
  getDeptLeaderDetail(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/evnlimleadapprove/bjApproveView/view/v1`;
    let url = `/gemp-app/api/gemp/app/evnlimleadapprove/bjApproveView/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 批示办理详情接口, {"id":"xxxxxxx"}
  getAppDealDetail(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/evnlimleadapprove/appDealView/view/v1`;
    let url = `/gemp-app/api/gemp/app/evnlimleadapprove/appDealView/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 预警信息列表-新
  monitoringAndEarlyWarningNew(data, callback) {
    // let url = '/gemp-app-zg/api/gemp/app/earlyWarning/list/v1';
    let url = '/gemp-app/api/gemp/app/earlyWarning/list/v1';
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 预警信息详情
  getWarningInfo(data, callback) {
    // let url = '/gemp-app-zg/api/gemp/app/earlyWarning/view/v1';
    let url = '/gemp-app/api/gemp/app/earlyWarning/view/v1';
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  //查询当前用户组织机构-新
  async getCurrentUserTreeListNew(data, callback) {
    // let url = '/gemp-app-zg/api/gemp/app/emsOrg/yjOrgTree/v1';
    let url = '/gemp-app/api/gemp/app/emsOrg/yjOrgTree/v1';
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  //查询当前值班排班信息-新
  async findDutyInfoByDateNew(data, callback) {
    // let url = '/gemp-app-zg/api/gemp/app/dutyPlan/getDutyList/v1';
    let url = '/gemp-app/api/gemp/app/dutyPlan/getDutyList/v1';
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 待办信息
  getBacklogList(data, callback) {
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/getUnreadList/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 待办信息
  downLoad(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/attachment/download/v1`;
    let url = `/gemp-app/api/gemp/app/attachment/download/v1`;
    apiServer.blobRequest(url, data, 'POST', callback, false);
  },
  downLoadv2(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/attachment/download/v1`;
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/download/v2`;
    // apiServer.blobRequest(url, data, 'POST', callback, false);
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 通知公告详情
  announceDetail(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/evnNotice/notice/view/v1`;
    let url = `/gemp-app/api/gemp/app/evnNotice/notice/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 部里通知公告详情
  announcePartDetail(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/evnNotice/exnotice/view/v1`;
    let url = `/gemp-app/api/gemp/app/evnNotice/exnotice/view/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 政务微信登录
  govAppLogin(data, callback) {
    let url = `/gemp-app/api/gemp/app/duty/info/user/govApp/login/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 政务微信免登录
  govAppnoLogin(data, callback) {
    let url = `/mauth/bin/tang/user/info`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },

  // 获取行政区划列表树
  getDistrictList(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/districtcode/base/districtCode/v1`;
    let url = `/gemp-app/api/gemp/app/districtcode/base/districtCode/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 获取事件详情
  getEventById(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/districtcode/base/districtCode/v1`;
    let url = `/gemp-event/api/gemp/event/eventbase/id/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 待办事项设置已读
  realMsg(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/districtcode/base/districtCode/v1`;
    let url = `/gemp-app/api/gemp/app/msg/readMsg/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 获取信息接报下载地址
  getInfoFileUrl(data, callback) {
    // let url = `/gemp-app-zg/api/gemp/app/districtcode/base/districtCode/v1`;
    let url = `/gemp-data-sync/api/gemp/dataSync/consume/iamsService/wpsFileUrl/v1`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  }
}

export default infoRequest;