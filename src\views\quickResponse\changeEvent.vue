<template>
  <div class="changeEvent">
    <Header title="事件切换"></Header>
    <div class="changeEvent-search">
      <van-search v-model="searchObj.keyword" placeholder="事件关键字检索" />
      <el-select v-model="searchObj.type" placeholder="事件类型">
        <el-option
          v-for="item in typeList"
          :key="item.type"
          :label="item.name"
          :value="item.type">
        </el-option>
      </el-select>
      <!-- <van-field
        v-model="searchObj.type"
        is-link
        readonly
        name="picker"
        label="选择器"
        placeholder="点击选择城市"
        @click="showPicker = true"
      />
      <van-popup v-model:show="showPicker" position="bottom">
        <van-picker
          :columns="columns"
          @confirm="onConfirm"
          @cancel="showPicker = false"
        />
      </van-popup> -->
    </div>
    <div class="changeEvent-content">
      <div class="list" v-for="item,index in eventList" :key="index">
        <span class="name">{{item.name}}</span>
        <div class="type-cont">
          <span :class="['tag', 'tag-'+item.type]">{{filterType(item.type)}}</span>
          <van-image
            width="58px"
            height="16px"
            fit="contain"
            :src="require(`@/assets/images/quickResponse/tag-${item.level}.png`)"
          />
        </div>
        <div class="num-cont">
          <span>死亡人数<b>{{item.deadNum}}</b></span>
          <span>受伤人数<b>{{item.injured}}</b></span>
        </div>
        <div class="list-bottom">
          <div>
            <van-image
              width="16px"
              height="12px"
              fit="contain"
              :src="require(`@/assets/images/quickResponse/ico-org.png`)"
            />
            <span>{{item.org}}</span>
          </div>
          <div>
            <van-image
              width="11px"
              height="11px"
              fit="contain"
              :src="require(`@/assets/images/quickResponse/ico-clock.png`)"
            />
            <span class="date">{{item.time}}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
// import { Select, Option } from 'element-ui';
@Component({
  components: {
    Header,
  }
})
export default class ChangeEvent extends Vue {
  private showPicker:boolean = false;
  private searchObj:any = {
    keyword: '',
    type: ''
  }
  private columns = ['杭州', '宁波', '温州', '嘉兴', '湖州'];
  // private typeList = ['危化事故', '道路交通'];
  private typeList:any = [
    {name: '危化事故', type: '01'},
    {name: '道路交通', type: '02'},
  ]
  private eventList:any = [
    {name: '事件名称（XXXX燃气爆炸事故）', type: '01', level: '1', deadNum: 12, injured: 12, org: '湖北省应急指挥中心', time: '2020-09-26 08:30'},
    {name: '事件名称（XXXX燃气爆炸事故）', type: '02', level: '4', deadNum: 12, injured: 12, org: '湖北省应急指挥中心', time: '2020-09-26 08:30'},
    {name: '事件名称（XXXX燃气爆炸事故）', type: '02', level: '2', deadNum: 12, injured: 12, org: '湖北省应急指挥中心', time: '2020-09-26 08:30'},
    {name: '事件名称（XXXX燃气爆炸事故）', type: '01', level: '1', deadNum: 12, injured: 12, org: '湖北省应急指挥中心', time: '2020-09-26 08:30'},
    {name: '事件名称（XXXX燃气爆炸事故）', type: '01', level: '3', deadNum: 12, injured: 12, org: '湖北省应急指挥中心', time: '2020-09-26 08:30'},
    {name: '事件名称（XXXX燃气爆炸事故）', type: '01', level: '1', deadNum: 12, injured: 12, org: '湖北省应急指挥中心', time: '2020-09-26 08:30'},
  ]
  private onConfirm(val) {
    this.searchObj.type = val;
  }
  private filterType(val) {
    let newVal = '';
    switch(val) {
      case '01':
        newVal = '危化事故';
        break;
      case '02':
        newVal = '道路交通';
        break;
    }
    return newVal;
  }
  private onSelect(val) {
    console.log('onSelect--------->', val)
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.changeEvent {
  span {
    display: inline-block;
  }
  &-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 13px;
    background: #fff;
    /deep/.van-search {
      width: 60%;
      padding: 0;
      margin-right: 11px;
      height: 30px;
      .van-search__content {
        height: 100%;
      }
      .van-cell {
        color: #b5b5b5;
        background: #f4f7f8;
      }
    }
    /deep/.el-select {
      flex: 1;
      background: #f4f7f8;
      .el-input__inner {
        height: 30px;
        border: none;
        color: #b5b5b5;
        outline: none;
      }
    }
  }
  &-content {
    height: calc(100% - 90px);
    overflow: auto;
    padding: 10px 12px;
    .list {
      margin-bottom: 10px;
      background: url("@{url}/quickResponse/ico-light.png") 90% top no-repeat;
      background-size: contain;
      background-color: #fff;
      padding: 20px 12px 0 12px;
      .name {
        margin-bottom: 9px;
        font-size: 18px;
        font-weight: bold;
      }
      .type-cont {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        .tag {
          margin-right: 6px;
          padding: 2px 4px;
          background: #e1f4ff;
          color: #3284ff;
          font-size: 12px;
        }
      }
      .num-cont {
        margin-bottom: 16px;
        span {
          color: #646566;
          b {
            margin-left: 6px;
            color: #1c1d1d;
          }
          &:first-child {
            margin-right: 17px;
          }
        }
      }
      .list-bottom{
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #e8eaeb;
        padding: 9px 0;
        color: #a6a8a9;
        /deep/.van-image {
          margin-right: 7px;
        }
        >div {
          width: 50%;
          &:last-child {
            text-align: right;
          }
        }
      }
    }
  }
}
</style>