<template>
  <div class="main-detail">
    <div class="header">选择人员</div>
    <div class="group-content">
      <!-- <van-checkbox-group v-model="checkedGroup" direction="horizontal">
        <van-checkbox :name="item.code" v-for="(item, index) in groupList" :key="index">{{ item.text }}</van-checkbox>
    
      </van-checkbox-group> -->
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import taskRequest from '@/api/webcross/task';
export default class selectUser extends Vue {
  userList: any = [];
  groupList: any = []; // 组别列表
  checkedGroup: any = [];
  mounted() {
    console.log('123');
    this.getGroupList();
  }
  getGroupList() {
    return
    const params = { orgCode: this.$store.state.userInfo.orgCode, type: '1' };
    taskRequest.getGroupManage(params, (res: any) => {
      this.groupList = res.data.list.map((item: any) => {
        return {
          code: item.groupId,
          text: item.groupName
        };
      });
    });
  }
}
</script>
<style lang="scss">
.main-detail {
  width: 100%;
  .header {
    position: relative;
    height: 54px;
    line-height: 54px;
    text-align: center;
    font-size: 18px;
    border-bottom: 1px solid #ccc;
  }
}
</style>
