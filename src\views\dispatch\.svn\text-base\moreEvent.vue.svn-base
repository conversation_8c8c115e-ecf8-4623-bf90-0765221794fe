<template>
  <div class="more_big">
    <div style="height: 45px;width: 100%;">
        <x-header :left-options="{backText: '',preventGoBack: true}"  @on-click-back='goBack' style="position: fixed; top: 0px;width: 100%; z-index: 10;background:#1FB1FE">更多报道</x-header>
    </div>
    <div class="more_new_centent">
      <!-- 更多报道 -->
      <div class="media_more_big">
        <div class="media_more_content">
            <ul class="more_list" ref="container">
            <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
              <li v-for="(item,index) in moreMessageArr" :key="index">
                <div class="media_list_top">
                   <img v-if="item.isv=='1'" src="../../assets/imgs/icon/wbdw.png" />
                  <img v-if="item.isv=='0'" src="../../assets/imgs/icon/ptmz.png" /> <font>{{item.username}}</font>
                  <mu-icon value="keyboard_arrow_down" class="arraw_show" ></mu-icon>
                </div>
                <div class="media_list_center">
                  {{item.label}}
                </div>
                <div class="media_list_buttom">
                  <font> {{item.content}}</font>
                </div>
              </li>
               <div v-if="moreMessageArr&&moreMessageArr.length==0" style="width:100%;text-align:center;padding-top: 10%;">
                  <img src="../../assets/imgs/notdata.png" />
                </div>
              </mu-load-more>
            </ul>
        </div>
      </div>
    </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { XHeader,Popup,VChart, VGuide, VLine, VArea, VScale,VPoint } from 'vux';
@Component({
  components: {
    XHeader,
    Popup,VChart, 
    VGuide,
    VLine,
    VArea,
    VScale,
    VPoint
  },
  props:["queryMore"]
})
export default class Home extends Vue {
  refreshing: any = false; 
  loading: any = false;
  moreMessageArr:any =[];
  currPage: any = 1;
  goBack(){
    this.$emit("close");
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.currPage=1;
    _this.moreMessageArr=[];
    _this.queryMoreRequest("");
    //debugger;
    //_this.$refs.container.scrollTop = 0;
    // setTimeout(() => {
    //   _this.refreshing = false;
    // }, 2000);
  }
  load() {
    const _this = this;
    _this.loading = true;
    _this.currPage++;
    _this.queryMoreRequest("1");
    // setTimeout(() => {
    //   _this.loading = false;
    // }, 2000);
  }
  queryMoreRequest(page_flag){
      let _this=this;
      var p1 = new Promise(function (resolve, reject) {
        //_this.loading = true;
        try {
          let param={"curPage": _this.currPage,"id": _this['queryMore'].id,"pageSize": 10,"isVStr":_this['queryMore'].type};
          let res = _this['$apiServer'].commonRequest('api/event/related/weibo/list/v1',param);
          console.log(res)
          resolve(res)
        } catch (error) {
          reject("500");
        }
      });
      Promise.all([p1]).then(function (results) {
         _this.refreshing = false;
         _this.loading = false;
        let resultsObj=results[0];
        if(resultsObj==null||resultsObj['status']!=200){
          console.log("处理异常");
        }else{
          console.log(resultsObj)
          if(!page_flag){
            _this.moreMessageArr=resultsObj['data'].list;
          }else{
            resultsObj['data'].list.forEach(function(item,index){
              _this.moreMessageArr.push(item);
            });
          }
          
          
        }
      }).catch(function (status) {
        console.log("处理异常")
      });
    }
    created(){
      let _this=this;
      console.log("come in created ")
      _this.queryMoreRequest("");
      
    }
  

}
</script>
<style scoped lang="scss">
.more_big {
  width: 100%;
  height: 100%
}
.more_new_centent{
  height: calc( 100% - 45px - 10px );
  background: white;
}

.media_more_big{
  height:100%;
}
.media_more_content{
   width: 96%;
   height:100%;
   margin: 5px 2%;
   box-shadow: #c1c1c1 0vw 1vw 2vw 0vw;
}
.media_more_content li{
  padding-top: 10px
}

.media_list_top{
  width: 88%;
  height: 20px;
  margin: 5px 6%;
  border-radius: 20px;
  line-height: 20px;
  background: #dfdfdf;
  color: #ea6874;
  text-indent: 2em;//首行缩进
}
.media_list_center{
  width: 88%;
  margin: 5px 6%;
  font-size: 15px;
  color: #ea6874;
  text-indent: 1em;//首行缩进
  text-align:justify;
	clear: both;
}
.media_list_buttom{
  width: 88%;
  margin: 5px 6%;
  font-size: 13px;
  color:black;
  text-indent: 2em;//首行缩进
  padding-bottom: 10px;
  text-align:justify;
  word-break:break-all;
  word-wrap:break-word;

}
.media_list_top img{
    position: absolute;
    width: 35px;
    left: 9px;
    top: 6px;
    border-radius: 30px;
}
.media_more_content li{
  position: relative;
}
.a_more{
  float: right;
  text-decoration: underline;
  color: gray;
  margin-right: -10px;
}
.more_list{
  height:100%;
  overflow: auto;
}


</style>