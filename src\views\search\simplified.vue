<!-- 周边查询 -->
<template>
  <div class="around-info">
    <!-- 操作栏 -->
    <div class="active-content" v-if="isAcive">
      <!-- 定位 -->
      <van-row class="location-box" v-if="searchType == 'location'">
        <van-col span="18">
          <van-field label-width="2.2em" v-model="locInfo.longitude" :border="true" type="number" label="经度" />
        </van-col>
        <van-col span="18"> 
          <van-field label-width="2.2em" v-model="locInfo.latitude" type="number" label="纬度" />
        </van-col>
        <van-col span="6" style="text-align: center"> 
          <van-button type="primary" size="small" @click="handleSet">确定</van-button>
        </van-col>
      </van-row>
      
      <!-- 菜单 -->
      <div class="menu-box">
        <ul>
          <template v-for="(item, index) in menuList">
            <li @click="handleToMenu(item)" :class="{ active: searchType == item.type }" :key="index">
              <van-image width="48px" height="48px" fit="contain" :src="item.icon" />
              <span class="name">{{ item.name }}</span>
            </li>
          </template>
        </ul>
      </div>
    </div>
    
    <!-- 信息内容 -->
    <div class="info-content" v-else>
      <div class="title-box">
        <div class="title">{{ resultInfo.eventTitle }}</div>
        <div class="to-around" @click="onCancel">
          <img class="title-icon" src="@/assets/images/icons/locationGray.png" alt="" />
          搜周边
          <van-icon name="arrow" size="20" />
        </div>
      </div>
      <div class="detail-box">
        <div class="address">{{ resultInfo.infoAddress }}</div>
        <div class="coordinates">经度: {{ resultInfo.longitude }} 纬度: {{ resultInfo.latitude }}</div>
        
        <!-- 周边分析状态提示 -->
        <div class="analysis-status">
          <van-icon name="search" color="#1a67f2" size="16" />
          <span>正在进行周边分析，请在地图上查看结果</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { getLocInfo, openGps } from '@/utils/location';

@Component({
  components: {}
})
export default class simplifiedInfo extends Vue {
  @Prop(Object) private resultInfo: any;
  @Prop(String) private type: any;
  number: any = 0;
  isAcive: any = true;
  
  onCancel() {
    this.isAcive = true;
    this.searchType = 'event';
  }
  
  created() {}
  
  searchType: any = null;
  menuList: any = [
    { name: '搜周边', icon: require(`@/assets/images/home/<USER>'event' },
    {
      name: '定位',
      icon: require(`@/assets/images/home/<USER>
      show: true,
      type: 'location'
    }
  ];
  
  handleToMenu(item) {
    console.log(item.type);
    this.searchType = item.type;
    this.$emit('setType', item.type);

    if (item.type == 'location') {
      this.locInfo.latitude = this.resultInfo.latitude;
      this.locInfo.longitude = this.resultInfo.longitude;
    }
  }
  
  locInfo: any = {
    longitude: '',
    latitude: ''
  };
  
  handleSet() {
    this.$emit('setLocation', this.locInfo);
    this.isAcive = false;
  }
}
</script>

<style lang="less" scoped>
.around-info {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding: 20px 10px;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  
  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .title {
      font-size: 18px;
      font-weight: 600;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .to-around {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      padding: 6px 10px;
      border-radius: 20px;
      font-size: 14px;
      color: #333;
      
      .title-icon {
        width: 14px;
        height: 18px;
        margin-right: 4px;
      }
      
      .van-icon {
        margin-left: 2px;
      }
      
      &:active {
        background-color: #e0e0e0;
      }
    }
  }
  
  .menu-box {
    padding: 6px 10px;
    ul {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      height: 100%;
      li {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        text-align: center;
        span {
          padding-top: 10px;
          color: #1c1d1d;
        }
      }
      .active {
        span {
          padding-top: 10px;
          color: #1a67f2;
          font-weight: bold;
        }
      }
    }
  }
  
  .location-box {
    border: 1px solid #ebedf0;
    border-radius: 8px;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    
    /deep/.van-cell__value.van-field__value {
      border: 1px dashed #ddd;
      padding-left: 10px;
      border-radius: 4px;
    }
    
    .van-button {
      margin-top: 10px;
    }
  }
  
  .info-content {
    .detail-box {
      margin-top: 10px;
      line-height: 24px;
      
      .address {
        margin-bottom: 8px;
        color: #333;
        font-size: 15px;
      }
      
      .coordinates {
        margin-bottom: 15px;
        color: #666;
        font-size: 14px;
      }
      
      .analysis-status {
        display: flex;
        align-items: center;
        background-color: #f0f9ff;
        padding: 10px;
        border-radius: 6px;
        margin-top: 15px;
        
        .van-icon {
          margin-right: 8px;
        }
        
        span {
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
}
</style>
