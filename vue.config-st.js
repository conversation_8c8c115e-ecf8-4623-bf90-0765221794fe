module.exports = {
  publicPath: './',
  productionSourceMap: false,

  devServer: {
    sockHost: 'localhost',
    port: 8091,
    https: true,
    disableHostCheck: true,
    open: true,
    proxy: {
      // "/gapi": {
      //   target: "http://************:8990/edss-euip/",
      //   pathRewrite: {
      //     "/gapi": "",
      //   },
      // },
      // 周吉槟本地服务调试
      // "/gapi/gemp-app/api": {
      //   // target: "http://*************:8109/api/",
      //   target: 'http://*************:8109/api/',
      //   pathRewrite: {
      //     "/gapi/gemp-app/api": "",
      //   },
      // },
      "/gapi": {
        target: "https://hbyjgis.hbsis.gov.cn:8990/",
        changeOrigin: true
      },
      "/gapi/gemp-app": {
        target: "https://hbyjgis.hbsis.gov.cn:8990/",
        changeOrigin: true
      },
      "/gapi/gemp-app-zg": {
        target: "https://************:8109/",
        // target: "http://************:8990/",
        changeOrigin: true
      },
      "/gapi/gemp-user": {
        target: "https://hbyjgis.hbsis.gov.cn:8990/",
        // target: "http://************:8990/",
        changeOrigin: true
      },
      "gapi/gemp-data-sync": {
        target:  "https://hbyjgis.hbsis.gov.cn:8990/",
        // target: "http://************:8990/",
        changeOrigin: true
      },
      "/mauth/bin/":{
        target:  "https://openapi-eme.chinamye.com/mauth/bin/",
        // target: "http://************:8990/",
        pathRewrite: {
          '/mauth/bin/': '',
        },
      }
    }
  },

  css: {
    loaderOptions: {
      sass: {
        data: '@import "@/assets/styles/helpers/mixin.scss";@import "@/assets/styles/common/selfvariable.scss";'
      },
      stylus: {
        'resolve url': true,
        'import': [
          './src/theme'
        ]
      }
    }
  },

  configureWebpack: config => {

    // 浏览器：web, electron : electron-renderer
    config.target = 'web'
  },
  configureWebpack: {
    resolve: {
      alias: {
        'vue$': 'vue/dist/vue.esm.js'
      }
    }
  },
  pluginOptions: {
    'cube-ui': {
      postCompile: true,
      theme: true,

    }
  }
};
