<template>
    <div>
        <div class="item_content_list" v-for="(item,index) in eventList" :key="index">
            <ul>
                <li >
                    <div class="list_top" @click="comeInDetail(item)">
                        <span>
                          <img v-if="item.type=='2'" src="../../assets/images/icons/1.png" alt="" srcset="">
                          <img v-if="item.type=='1'" src="../../assets/images/icons/0.png" alt="" srcset="">
                          <img v-else-if="item.type=='3'" src="../../assets/images/icons/3.png" alt="" srcset="">
                          <img v-else-if="item.type=='4'" src="../../assets/images/icons/4.png" alt="" srcset="">
                        </span>
                        <span>{{item.typeName}}</span>
                        <span>{{item.orgName}}</span>
                        <span>{{item.occurTime}}</span>
                    </div>
                    <div class="list_content">
                        <div  @click="comeInDetail(item)">
                            {{item.title}}
                        </div>
                        <!-- <div v-if="item.longitude&&item.latitude" @click="comeLocation(item)">
                            <img src="../../assets/images/icons/localtion.png" alt="" srcset="">
                        </div> -->
                    </div>
                </li>
            </ul>
            
        </div>
        <div v-if="eventList&&eventList.length==0&&requestStatus=='end'" style="width:100%;text-align:center;padding-top: 10%;">
          <van-empty description="暂无数据" />
          <!-- <img src="../../assets/images/notdata.png" /> -->
        </div>
        <van-popup v-model="detailHandle.showRescueTeamDetail" v-if="detailHandle.showRescueTeamDetail"  position="right" :style="{ height: '100%' , width: '100%' }" >
          <rescueTeamDetail  :requestId="requestId" @close="closeInfo"/>
        </van-popup>
        <van-popup v-model="detailHandle.showEventDetail" v-if="detailHandle.showEventDetail" position="right" :style="{ height: '100%' , width: '100%' }" >
          <eventDetail v-if="detailHandle.showEventDetail" :requestId="requestId" @close="closeInfo"/>
        </van-popup>
        <van-popup v-model="detailHandle.showTaskDetail" v-if="detailHandle.showTaskDetail" position="right" :style="{ height: '100%' , width: '100%' }" >
          <taskDetail v-if="detailHandle.showTaskDetail" :requestId="requestId" @close="closeInfo"/>
        </van-popup>
        <van-popup v-model="detailHandle.showExpertDetail" v-if="detailHandle.showExpertDetail" position="right" :style="{ height: '100%' , width: '100%' }" >
          <expertDetail v-if="detailHandle.showExpertDetail" :requestId="requestId" @close="closeInfo"/>
        </van-popup>


    </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import rescueTeamDetail from './rescueTeamDetail.vue';
import eventDetail from './eventDetail.vue';
import taskDetail from './taskDetail.vue';
import expertDetail from './expertDetail.vue'

@Component({
  components: {
    rescueTeamDetail,
    eventDetail,
    taskDetail,
    expertDetail
  },
})
export default class integratedItem extends Vue {
  @Prop(String) private requestFlag :string;
  eventList: any = [];
  keywords: any = "";
  requestStatus: any = "";
  requestId : any = "";
  isInit : any =true;
  detailHandle : any ={ showRescueTeamDetail : false , showEventDetail : false ,showTaskDetail : false , showExpertDetail : false } 
  closeInfo(){
    let _this=this;
    _this.detailHandle.showRescueTeamDetail = false;
    _this.detailHandle.showEventDetail=false;
    _this.detailHandle.showTaskDetail=false;
    _this.detailHandle.showExpertDetail=false;
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      try {
          _this['$gsmdp'].goBack(function(){
          })
      } catch (error) {
          console.log("close error")
      }
    })

  }
  searchSet(_keyword){
    this.keywords=_keyword;
  }
    comeLocation(_item){
      console.log("come in location")
    }
    comeInDetail(_item) {
      let _this=this;
      console.log("come in detail "+_item.type)
      _this.requestId=_item.id;
      if(_item.type=='1'){
        _this.detailHandle.showRescueTeamDetail = true;
      }else if(_item.type=='2'){
         _this.detailHandle.showEventDetail = true;
      }else if(_item.type=='3'){
         _this.detailHandle.showTaskDetail = true;
      }else if(_item.type=='4'){
         _this.detailHandle.showExpertDetail = true;
      }
    }
    queryRequest(pageObj){
      let _this=this;
      let _objPage={pageIndex:1,pageSize:10}
      console.log("_this.requestFlag===>"+_this.requestFlag)
      if(pageObj){
        console.log(pageObj.pageIndex)
        _objPage.pageIndex=pageObj.pageIndex;
      }
      let param={
          "keyWords":_this.keywords,
          "nowPage": _objPage.pageIndex,
          "pageSize": _objPage.pageSize
      };
      if(_this.requestFlag=='1'||_this.requestFlag=='4'){
        param['keywords']=_this.keywords;
      }
       if(_this.requestFlag=='2'){
        param['eventTitle']=_this.keywords;
      }
      if(_this.requestFlag=='3'){
        param['taskName']=_this.keywords;
      }
      //  _this.$emit("handleObj",{uploadMore:false})
      // let res=require("./integrated.json")
      // console.log(res)
      // let resultsObj=res['data'].list;
      // _this.eventList=resultsObj;
      apiServer.findAllQuery(param,function(res){
        _this.isInit=false;
         console.log(res)
         _this.requestStatus="end";
         _this.$emit("handleObj",{uploadMore:false})
         //res['data'].data.list
         let resultsObj=_this.resultHandle(res);
          console.log(resultsObj)
          if(resultsObj){
            console.log("赋值成功");
            if(_objPage.pageIndex>1){
               _this.eventList = [..._this.eventList, ...resultsObj];
            }else{
              _this.eventList=resultsObj;
            }
            
          }
      },_this.requestFlag,_this.isInit)
      
    }

    resultHandle(res){
      let _this=this;
      let resultArr=[];
      let reslist=res['data'].data.list;
      switch (parseInt(_this.requestFlag)) {
        case 0:
          resultArr = reslist;
          break;
        case 1:
          reslist.forEach((item,index) => {
            let obj={"id":item.teamId,"type":"1","typeName":"队伍","title":item.teamName,"orgCode":item.orgcode,"orgName":item.orgName,"occurTime":item.updateTime,"longitude":item.longitude,"latitude":item.latitude,"address":item.address}
            resultArr.push(obj)
          })
          break;
        case 2:
          reslist.forEach((item,index) => {
            let obj={"id":item.eventId,"type":"2","typeName":"事件","title":item.eventTitle,"orgCode":item.orgCode,"orgName":item.orgName,"occurTime":item.occurTime,"longitude":item.longitude,"latitude":item.latitude,"address":item.address}
            resultArr.push(obj)
          })
          break;
        case 3:
          reslist.forEach((item,index) => {
            let obj={"id":item.taskid,"type":"3","typeName":"任务","title":item.taskname,"orgCode":item.sendorgcode,"orgName":item.sendOrgName,"occurTime":item.sendTimeStr,"longitude":item.longitude,"latitude":item.latitude,"address":item.address}
            resultArr.push(obj)
          })
          break;
        case 4:
          reslist.forEach((item,index) => {
            let obj={"id":item.expertId,"type":"4","typeName":"专家","title":item.expertName,"orgCode":item.orgCode,"orgName":item.orgName,"occurTime":item.occurTime,"longitude":item.longitude,"latitude":item.latitude,"address":item.address}
            resultArr.push(obj)
          })
          break;
        default:
          break;
      }
      return resultArr;
    }
    setZero(num){
      num=num+"";
      if(num&&num.length==1){
        return "0"+num;
      }else{
        return num;
      }
    }
    created() {
      let _this=this;
      //_this['$apiServer'].setToken();
      console.log("init run")
      _this.queryRequest(null);
      // setTimeout(function() {
        
      // }, 500);
     
    }
    

}
</script>
<style scoped lang="scss">
.item_content_list{
    width: 100%;
    height: 100%;
    li{
        width: 100%;
        background: white;
        margin-top: 15px;
        padding-top: 10px;
        .list_top{
            width: 100%;
            height: 50%;
            position: relative;
            span{
                display:inline-block;
                float: left;
                height:35px;
            }
            span:nth-of-type(1){
                width: 10%;
                line-height: 35px;
                img{
                    width:37px;
                    margin-top: -3px;
                    vertical-align: middle;
                }
            }
            span:nth-of-type(2){
                line-height: 35px;
                font-weight: bold;
                padding-left: 5px;
                font-size: 15px;
            
            }
            span:nth-of-type(3){
                position: absolute;
                top: 11px;
                font-size: 12px;
                padding-left: 3%;
                color: #959595;
            }
            span:nth-of-type(4){
                float: right;
                line-height: 35px;
                padding-right: 4%;
                color: #7F7F7F;
            }
               
        }
        .list_content{
            width: 100%;
            height: 45px;
            line-height: 35px;
            padding-left: 3%;
            font-size: 15px;
            div{
                float: left;
            }
            div:nth-of-type(1){
                width: 90%;
                height: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            div:nth-of-type(2){
                width: 10%;
                height: 100%;
                img{
                    width: 15px;
                    vertical-align: middle;
                    margin-top: -5px;
                }
            }
        }
    }
    
    
}
</style>