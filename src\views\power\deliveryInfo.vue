<template>
  <div class="div_big" style="height: 100%; overflow: hidden">
    <van-nav-bar :title="detailInfo.title" left-text="返回" @click-left="GoBack">
      <template #right> </template>
    </van-nav-bar>
    <div class="info_content">
      <div class="text_area_div">
        <div class="text_area_title">基本信息</div>

        <ul>
          <li class="info_cell">
            <span>联系人</span>
            <span>{{ detailInfo.relName }}</span>
          </li>
          <li class="info_cell">
            <span>通知时间:</span>
            <span>{{ detailInfo.startTime }}</span>
          </li>
          <li class="info_cell">
            <span>响应时间：</span>
            <span>{{ detailInfo.levelName }}</span>
          </li>
          <li class="info_cell">
            <span>预警时间：</span>
            <span>{{ detailInfo.startTime }}</span>
          </li>
        </ul>

        <div class="text_area_title">任务清单（2）</div>
        <div class="bottom_div" v-for="(item, index) in detailInfo.taskList" :key="index">
          <div class="text_area_centent">{{ item.taskDesc }}</div>
          <ul>
            <li class="info_cell">
              <span>下发时间:</span>
              <span>{{ item.taskStartTime }}</span>
            </li>
            <li class="info_cell">
              <span> 拟完成时间:</span>
              <span>{{ item.taskArrivelTime }}</span>
            </li>
            <li class="info_cell">信息反馈：</li>
            <li class="info_cell">{{ item.feekback }}</li>
            <li class="info_cell">
              <span> 反馈时间:</span>
              <span>{{ item.taskArrivelTime }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import _ from 'lodash';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import apiServer from '../../api/request-service';

@Component({
  components: {}
})
export default class planManageItem extends Vue {
  @Prop(String) private requestId: string;
  @Prop(Object) private requestObj: any;
  infoObj: any = {};
  infolist: any = [];
  detailInfo: any = {
    title: '危险品救援队',
    relName: '张三',
    relMobile: '12345678541',
    startTime: '2025-03-09 10:00:00',
    endTime: '2025-03-09 10:00:00',
    arrivelTime: '2025-03-09 10:00:00',
    taskList: [
      {
        taskId: '1',
        taskName: '任务一',
        taskDesc: '1、请立刻赶赴事发现场开展紧急救援任务，并确保群众安全。',
        address: '武汉市洪山区化工2路1号',
        feekback: '已抵达现场开展救援工作。',
        taskStartTime: '2025-03-09 10:00:00',
        taskEndTime: '2025-03-09 10:00:00',
        taskArrivelTime: '2025-03-09 10:00:00'
      },
      {
        taskId: '2',
        taskName: '任务一',
        taskDesc: '1、请立刻赶赴事发现场开展紧急救援任务，并确保群众安全。',
        address: '武汉市洪山区化工2路1号',
        feekback: '已抵达现场开展救援工作。',
        taskStartTime: '2025-03-09 10:00:00',
        taskEndTime: '2025-03-09 10:00:00',
        taskArrivelTime: '2025-03-09 10:00:00'
      }
    ]
  };
  mounted() {
    let _this = this;
    // _this.queryRequest();
  }
  queryRequest() {
    let _this = this;
    let param = {
      planId: _this.requestId
    };
    apiServer.findStructurInfo(param, function (res) {
      if (res.data && res.data.status == 200) {
        let infoObj = res.data.data;
        _this.infoObj = infoObj;
      }
    });
  }

  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .info_content {
    width: 100%;
    height: calc(100% - 46px);
    overflow-y: auto;
    background: #eee;
    ul {
      width: 100%;
      background: #fff;
    }
    li {
      width: 100%;
      padding: 0px 3%;
      p {
        margin: 0;
        line-height: 3rem;
        font-size: 17px;
        font-weight: bold;
      }
    }
    .info_cell {
      line-height: 2.5rem;
      color: #333333;
      overflow: hidden;
      display: flex;
      span {
        display: inline-block;
        float: left;
      }
      span:nth-of-type(1) {
        width: 4.6rem;
        color: #000000;
      }
      span:nth-of-type(2) {
        width: calc(100% - 5rem);
      }
      .arr-text {
        display: flex;
        span {
          width: auto;
        }
      }
    }
    .text_area_div {
      width: 100%;
      .text_area_title {
        width: 100%;
        height: 2.5rem;
        line-height: 2.5rem;
        padding: 0px 3%;
        color: c0c0c0;
      }
      .text_area_centent {
        width: 100%;
        background: #fff;
        line-height: 1.5rem;
        padding: 0px 3%;
      }
    }
    .bottom_div {
      margin-bottom: 0.5rem;
    }
  }
}
</style>
