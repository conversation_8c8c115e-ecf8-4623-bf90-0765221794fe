<template>
  <div class="report-list">
    <Header title="研判报告"></Header>
    <div class="report-list-container">
      <div class="search-bar">
        <van-search v-model="searchKeyword" placeholder="请输入报告名称" @search="onSearch" />
      </div>
      <!-- 报告列表 -->
      <div class="report-list-content">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="loadReports">
            <div v-if="reportList.length > 0">
              <div v-for="(item, index) in reportList" :key="index" class="report-item">
                <div class="report-info">
                  <div class="report-name">{{ item.reportName || '未命名报告' }}</div>
                  <div class="report-time">{{ formatTime(item.createTime) }}</div>
                </div>
                <div class="report-actions">
                  <van-button type="primary" size="small" @click="downloadReport(item)" :loading="item.downloading"> 下载 </van-button>
                </div>
              </div>
            </div>
            <van-empty v-else description="暂无报告" />
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '@/api/request-service';
import { Toast, Dialog } from 'vant';
import moment from 'moment';

@Component({
  components: {
    Header
  },
  beforeCreate() {
    // 注册Dialog组件
    Vue.use(Dialog);
  }
})
export default class ReportList extends Vue {
  private searchKeyword: string = '';
  private reportList: any[] = [];
  private loading: boolean = false;
  private refreshing: boolean = false;
  private finished: boolean = false;
  private eventId: string = '';

  created() {
    // 从路由参数中获取事件ID
    this.eventId = (this.$route.query.eventId as string) || '';
    if (!this.eventId) {
      Toast('未获取到事件ID');
    }
  }

  mounted() {
    this.loadReports();
  }

  /**
   * 加载研判报告列表
   */
  private loadReports() {
    if (this.refreshing) {
      this.reportList = [];
      this.refreshing = false;
    }

    const params = {
      eventId: this.eventId,
      listOrder: { prop: '', sort: '' },
      nowPage: 1,
      pageSize: 5000,
      reportName: this.searchKeyword,
      schemeId: '',
      schemeVersion: 0,
      schemeTypeCode: '01'
    };

    apiServer.getSchemeList(params, (res: any) => {
      this.loading = false;

      if (res.data && res.data.status === 200 && res.data.data) {
        const list = res.data.data.list || [];

        // 为每个报告添加下载状态标记
        this.reportList = list.map((item: any) => ({
          ...item,
          downloading: false
        }));

        this.finished = true;
      } else {
        Toast.fail(res.data?.msg || '获取报告列表失败');
        this.finished = true;
      }
    });
  }

  /**
   * 下拉刷新
   */
  private onRefresh() {
    this.finished = false;
    this.loadReports();
  }

  /**
   * 搜索报告
   */
  private onSearch() {
    this.finished = false;
    this.reportList = [];
    this.loadReports();
  }

  /**
   * 下载报告
   */
  private downloadReport(item: any) {
    if (!item.docUrl) {
      Toast.fail('报告链接不存在');
      return;
    }

    // 设置下载状态
    const index = this.reportList.findIndex((report) => report.schemeId === item.schemeId);
    if (index !== -1) {
      this.$set(this.reportList[index], 'downloading', true);
    }

    try {
      // 解析docUrl获取文件名
      let fileName = '';

      // 提取fileName参数值
      const fileNameMatch = item.docUrl.match(/fileName=([^&]+)/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = decodeURIComponent(fileNameMatch[1]);
      } else {
        Toast.fail('无法解析文件路径');
        return;
      }

      // 获取当前IP地址
      const currentIP = process.env.NODE_ENV === 'development' ? 'http://**************:8990/' : window.location.origin;

      // 构建最终的下载URL
      const downloadUrl = `${currentIP}/upload${fileName}`;

      // 检测设备类型
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile) {
        // 移动端：在新窗口中打开文件
        Toast.loading({
          message: '正在打开文件...',
          duration: 0
        });

        // 使用iframe加载文件，避免页面跳转
        const iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.src = downloadUrl;
        document.body.appendChild(iframe);

        // 设置超时，如果文件无法加载则提示用户
        setTimeout(() => {
          document.body.removeChild(iframe);
          Toast.clear();

          // 提示用户
          this.$dialog
            .confirm({
              title: '文件查看',
              message: '您可以选择在浏览器中查看文件，或复制链接后使用其他应用打开',
              confirmButtonText: '在浏览器中查看',
              cancelButtonText: '复制链接'
            })
            .then(() => {
              // 在浏览器中查看
              window.open(downloadUrl, '_blank');
            })
            .catch(() => {
              // 复制链接
              if (navigator.clipboard && navigator.clipboard.writeText) {
                // 使用现代的 Clipboard API
                navigator.clipboard
                  .writeText(downloadUrl)
                  .then(() => {
                    Toast.success('链接已复制到剪贴板');
                  })
                  .catch(() => {
                    // 如果 Clipboard API 失败，回退到传统方法
                    this.fallbackCopyTextToClipboard(downloadUrl);
                  });
              } else {
                // 回退到传统方法
                this.fallbackCopyTextToClipboard(downloadUrl);
              }
            });
        }, 2000);
      } else {
        // 桌面端：使用传统下载方式
        const downloadElement = document.createElement('a');
        downloadElement.href = downloadUrl;
        downloadElement.target = '_blank';
        downloadElement.download = item.reportName || '研判报告.docx';
        document.body.appendChild(downloadElement);
        downloadElement.click();
        document.body.removeChild(downloadElement);

        Toast.success('下载已开始');
      }

      console.log('文件链接:', downloadUrl);
    } catch (error) {
      console.error('操作失败:', error);
      Toast.fail('操作失败');
    } finally {
      // 重置下载状态
      if (index !== -1) {
        this.$set(this.reportList[index], 'downloading', false);
      }
    }
  }

  /**
   * 格式化时间
   */
  private formatTime(time: string | number) {
    if (!time) return '未知时间';
    return moment(time).format('YYYY-MM-DD HH:mm:ss');
  }

  /**
   * 传统的复制文本到剪贴板方法（用于不支持 Clipboard API 的浏览器）
   */
  private fallbackCopyTextToClipboard(text: string) {
    try {
      const textarea = document.createElement('textarea');
      textarea.value = text;

      // 设置样式使其不可见
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';

      document.body.appendChild(textarea);
      textarea.focus();
      textarea.select();

      // 使用已弃用的方法，但作为后备方案
      const successful = document.execCommand('copy');

      document.body.removeChild(textarea);

      if (successful) {
        Toast.success('链接已复制到剪贴板');
      } else {
        Toast.fail('复制失败，请手动复制');
        // 显示链接让用户手动复制
        this.$dialog.alert({
          title: '请手动复制链接',
          message: text
        });
      }
    } catch (err) {
      Toast.fail('复制失败，请手动复制');
      console.error('复制失败:', err);
    }
  }
}
</script>

<style lang="less" scoped>
.report-list {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  &-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
  }

  .report-item {
    margin: 10px 0;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .report-info {
      flex: 1;
      overflow: hidden;

      .report-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        // overflow: hidden;
        // text-overflow: ellipsis;
        white-space: wrap;
      }

      .report-time {
        font-size: 12px;
        color: #999;
      }
    }

    .report-actions {
      margin-left: 10px;
    }
  }
}
</style>
