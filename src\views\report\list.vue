<template>
  <div class="report-list">
    <Header title="研判报告"></Header>
    <div class="report-list-container">
      <!-- 报告列表 -->
      <div class="report-list-content">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model="loading" :finished="finished" finished-text="没有更多了" @load="loadReports">
            <div v-if="reportList.length > 0">
              <div v-for="(item, index) in reportList" :key="index" class="report-item">
                <div class="report-info">
                  <div class="report-name">{{ item.reportName || '未命名报告' }}</div>
                  <div class="report-time">{{ formatTime(item.createTime) }}</div>
                </div>
                <div class="report-actions">
                  <van-button type="primary" size="small" @click="previewReport(item)" :loading="item.downloading"> 预览 </van-button>
                </div>
              </div>
            </div>
            <van-empty v-else description="暂无报告" />
          </van-list>
        </van-pull-refresh>
      </div>
    </div>

    <!-- 文档预览弹窗 -->
    <van-popup v-model="showPreview" closeable close-icon-position="top-right" round :style="{ width: '100%', height: '90%' }">
      <div class="preview-container">
        <div class="preview-header">
          <div class="preview-title">{{ currentReport ? currentReport.reportName : '文档预览' }}</div>
        </div>
        <div class="preview-content" ref="docPreview"></div>
        <div class="preview-footer">
          <van-button type="default" size="small" @click="showPreview = false">关闭</van-button>
          <van-button type="primary" size="small" @click="downloadCurrentReport" style="margin-left: 10px">下载</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '@/api/request-service';
import { Toast, Popup, Button } from 'vant';
import moment from 'moment';
import axios from 'axios';
import { renderAsync } from 'docx-preview';
import JSZip from 'jszip';

// 设置全局JSZip
(window as any).JSZip = JSZip;

@Component({
  components: {
    Header,
    [Popup.name]: Popup,
    [Button.name]: Button
  }
})
export default class ReportList extends Vue {
  private searchKeyword: string = '';
  private reportList: any[] = [];
  private loading: boolean = false;
  private refreshing: boolean = false;
  private finished: boolean = false;
  private eventId: string = '';

  // 预览相关属性
  private showPreview: boolean = false;
  private currentReport: any = null;

  created() {
    // 从路由参数中获取事件ID
    this.eventId = (this.$route.query.eventId as string) || '';
    if (!this.eventId) {
      Toast('未获取到事件ID');
    }
  }

  mounted() {
    this.loadReports();
  }

  /**
   * 加载研判报告列表
   */
  private loadReports() {
    if (this.refreshing) {
      this.reportList = [];
      this.refreshing = false;
    }

    const params = {
      eventId: this.eventId,
      listOrder: { prop: '', sort: '' },
      nowPage: 1,
      pageSize: 5000,
      reportName: this.searchKeyword,
      schemeId: '',
      schemeVersion: 0,
      schemeTypeCode: '01'
    };

    apiServer.getSchemeList(params, (res: any) => {
      this.loading = false;

      if (res.data && res.data.status === 200 && res.data.data) {
        const list = res.data.data.list || [];

        // 为每个报告添加下载状态标记
        this.reportList = list.map((item: any) => ({
          ...item,
          downloading: false
        }));

        this.finished = true;
      } else {
        Toast.fail(res.data?.msg || '获取报告列表失败');
        this.finished = true;
      }
    });
  }

  /**
   * 下拉刷新
   */
  private onRefresh() {
    this.finished = false;
    this.loadReports();
  }

  /**
   * 搜索报告
   */
  private onSearch() {
    this.finished = false;
    this.reportList = [];
    this.loadReports();
  }

  /**
   * 下载报告
   */
  private downloadReport(item: any) {
    if (!item.docUrl) {
      Toast.fail('报告链接不存在');
      return;
    }

    // 设置下载状态
    const index = this.reportList.findIndex((report) => report.schemeId === item.schemeId);
    if (index !== -1) {
      this.$set(this.reportList[index], 'downloading', true);
    }

    try {
      // 解析docUrl获取文件名
      let fileName = '';

      // 提取fileName参数值
      const fileNameMatch = item.docUrl.match(/fileName=([^&]+)/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = decodeURIComponent(fileNameMatch[1]);
      } else {
        Toast.fail('无法解析文件路径');
        return;
      }
      // 获取当前IP地址
      const currentIP = process.env.NODE_ENV === 'development' ? 'http://**************:8990/' : window.location.origin;

      // 构建最终的下载URL
      const downloadUrl = `${currentIP}/upload${fileName}`;

      // 创建下载链接
      const downloadElement = document.createElement('a');
      downloadElement.href = downloadUrl;
      downloadElement.target = '_blank';
      downloadElement.download = item.reportName || '研判报告.docx';
      document.body.appendChild(downloadElement);
      downloadElement.click();
      document.body.removeChild(downloadElement);

      console.log('下载链接:', downloadUrl);
      Toast.success('下载已开始');
    } catch (error) {
      console.error('下载失败:', error);
      Toast.fail('下载失败');
    } finally {
      // 重置下载状态
      if (index !== -1) {
        this.$set(this.reportList[index], 'downloading', false);
      }
    }
  }

  /**
   * 格式化时间
   */
  private formatTime(time: string | number) {
    if (!time) return '未知时间';
    return moment(time).format('YYYY-MM-DD HH:mm:ss');
  }

  /**
   * 预览报告
   */
  private previewReport(item: any) {
    if (!item.docUrl) {
      Toast.fail('报告链接不存在');
      return;
    }

    // 设置当前报告
    this.currentReport = item;

    // 设置下载状态
    const index = this.reportList.findIndex((report) => report.schemeId === item.schemeId);
    if (index !== -1) {
      this.$set(this.reportList[index], 'downloading', true);
    }

    try {
      // 解析docUrl获取文件名
      let fileName = '';

      // 提取fileName参数值
      const fileNameMatch = item.docUrl.match(/fileName=([^&]+)/);
      if (fileNameMatch && fileNameMatch[1]) {
        fileName = decodeURIComponent(fileNameMatch[1]);
      } else {
        Toast.fail('无法解析文件路径');
        return;
      }

      // 获取当前IP地址
      const currentIP = process.env.NODE_ENV === 'development' ? 'http://**************:8990' : window.location.origin;

      // 获取用户名作为accessToken
      const userInfo = JSON.parse(localStorage.getItem('role') || '{}');
      const accessToken = userInfo.name || '';

      // 构建最终的下载URL
      // 确保fileName开头有斜杠
      if (!fileName.startsWith('/')) {
        fileName = '/' + fileName;
      }

      // 构建URL并添加accessToken参数
      const downloadUrl = `${currentIP}/upload${fileName}?accessToken=${encodeURIComponent(accessToken)}`;

      // 显示预览弹窗
      this.showPreview = true;

      // 使用axios获取文档内容
      Toast.loading({
        message: '加载文档中...',
        duration: 0
      });

      axios({
        method: 'get',
        url: downloadUrl,
        responseType: 'blob'
      })
        .then((response) => {
          // 使用docx-preview渲染文档
          renderAsync(response.data, this.$refs.docPreview)
            .then(() => {
              Toast.clear();
            })
            .catch((error: any) => {
              console.error('渲染文档失败:', error);
              Toast.fail('渲染文档失败');
            });
        })
        .catch((error) => {
          console.error('获取文档失败:', error);
          Toast.fail('获取文档失败');
        })
        .finally(() => {
          // 重置下载状态
          if (index !== -1) {
            this.$set(this.reportList[index], 'downloading', false);
          }
        });
    } catch (error) {
      console.error('预览失败:', error);
      Toast.fail('预览失败');

      // 重置下载状态
      if (index !== -1) {
        this.$set(this.reportList[index], 'downloading', false);
      }
    }
  }

  /**
   * 下载当前预览的报告
   */
  private downloadCurrentReport() {
    if (!this.currentReport) {
      Toast.fail('没有可下载的报告');
      return;
    }

    // 调用原来的下载方法
    this.downloadReport(this.currentReport);
  }
}
</script>

<style lang="less" scoped>
.report-list {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  &-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 10px;
  }

  .report-item {
    margin: 10px 0;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .report-info {
      flex: 1;
      overflow: hidden;

      .report-name {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        // overflow: hidden;
        // text-overflow: ellipsis;
        white-space: wrap;
      }

      .report-time {
        font-size: 12px;
        color: #999;
      }
    }

    .report-actions {
      margin-left: 10px;
    }
  }
}

// 文档预览样式
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .preview-header {
    padding: 15px;
    border-bottom: 1px solid #eee;

    .preview-title {
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      color: #333;
    }
  }

  .preview-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background-color: #f9f9f9;

    // docx-preview样式覆盖
    :deep(.docx-wrapper) {
      padding: 0 !important;
      display: inherit !important;
    }
  }

  .preview-footer {
    padding: 10px 15px;
    border-top: 1px solid #eee;
    text-align: right;
  }
}
</style>
