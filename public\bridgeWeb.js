/** 轮询hasNativeInit的次数，超过50次后（共计时5s）结束轮询 */
var readyTimerCount = 0;
var readyTimer = null;
if (bridgeNames[0].jsInterfaceName) {
  if (!readyTimer) { // 如果不存在readytimer，则注册定时器
    readyTimer = setInterval(function () {
      console.log("try to init, count:" + readyTimerCount);
      if (readyTimerCount > 50) {
        clearInterval(readyTimer);
      }
      var initVal = '';
      if (window["isUseWkWebView"]) {
        var postMsg = { "methodName": "hasNativeInit", "url": location.origin, "json": "" };
        postMsg = JSON.stringify(postMsg);
        initVal = window.prompt("_api", postMsg);
      } else {
        initVal = window[bridgeNames[0].jsInterfaceName].enqueue('hasNativeInit', location.origin, "");
      }
      if (initVal) {
        initVal = JSON.parse(initVal);
      }
      console.log('initVal is', initVal);
      var documentEventList = document.getEventListeners();
      console.log('documentEventList is:', documentEventList);
      /** 判断native端方法是否准备完毕，同时判断js document上是否有监听onBridgeReady事件 */
      if (initVal &&
        initVal.code == 0 &&
        initVal.result &&
        documentEventList &&
        documentEventList['onBridgeReady']) {
        console.log("native inited and document has 'onBridgeReady' listener");
        clearInterval(readyTimer);
        var doc = window.document;
        var eventName = 'onBridgeReady';
        var readyEvent = doc.createEvent('HTMLEvents');
        readyEvent.initEvent(eventName);
        doc.dispatchEvent(readyEvent);
        console.log("dispatchEvent onBridgeReady");
        /** 调用完onBridgeReady之后清除定时器 */
        clearInterval(readyTimer);
      }
      readyTimerCount += 1;
    }, 200);
  }
}
