<!-- 追加预警信息 -->
<template>
  <van-popup
    v-model:show="showAppendPop"
    :close-on-click-overlay="false"
    position="bottom"
    @click-overlay="closePop"
    @click-close-icon="closePop"
    :style="{ height: '40%' }"
  >
    <div class="main">
      <div class="header">
        <van-icon name="cross" size="25" @click="closePop" />
        追加预警信息
        <div class="sureBtn" @click="handleSure">
          <!-- {{ feedbackType === "overTask" ? "追加" : "保存" }} -->
          追加
        </div>
      </div>
      <div class="content">
        <van-cell is-link title="接收单位" @click="unitShow = true">
          <template #default>
            <van-tag closeable v-for="item in formObj.districts" :key="item.code" size="medium" type="primary" @close="closeDist(item)">
              {{ item.text }}
            </van-tag>
          </template>
        </van-cell>
        <van-cell is-link title="接收人" @click="unitShow = true">
          <template #default>
            <van-tag closeable v-for="item in formObj.districts" :key="item.code" size="medium" type="primary" @close="closeDist(item)">
              {{ item.text }}
            </van-tag>
          </template>
        </van-cell>
      </div>
      <!-- <div class="footer">
        <div class="sureBtn" @click="handleSure">{{feedbackType === 'overTask' ? '确认完成任务' : '确认'}}</div>
      </div> -->
    </div>
    <van-popup
      v-model:show="unitShow"
      position="bottom"
      round
      :style="{ height: '50%' }"
      @click-overlay="unitShow = false"
      @click-close-icon="unitShow = false"
    >
      <div class="main">
        <div class="header">
          选中接收单位
          <div class="sureBtn" @click="unitShow = false">确定</div>
        </div>
        <van-checkbox-group v-model="formObj.districts">
          <van-cell-group>
            <van-cell v-for="(item, index) in orgList" clickable :key="item.code" :title="`${item.text}`" @click="toggle(index)">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxes" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
  </van-popup>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import { Notify } from 'vant';
import taskRequest from '@/api/webcross/task';
import apiServer from '@/api/request-service';

@Component
export default class popFeedback extends Vue {
  @Prop() showAppendPop!: boolean;
  @Prop() feedbackType!: string;
  @Prop() teamInfo!: any;
  @Prop() locationInfo!: any;
  @Prop() curFeedbackTypeInfo!: any; // 当前选中反馈类型
  unitShow: boolean = false;
  orgList: any = [];
  feedbackObj: any = {
    replyText: '',
    attachmentList: []
  };
  formObj: any = {
    districts: []
  };
  created() {
    this.feedbackObj.replyText = '';
    this.feedbackObj.attachmentList = [];
    this.getOrgData();
  }
  toggle(index) {
    this.$refs.checkboxes[index].toggle();
  }
  closeDist(item) {
    this.formObj.districts.splice(this.formObj.districts.indexOf(item), 1);
  }
  closePop() {
    this.$emit('closeFeedback');
  }
  getOrgData() {
    let _this = this;
    const params = {
      parentCode: JSON.parse(window.localStorage.getItem('role')).districtCode
    };
    apiServer.getOrgList(params, function (res) {
      _this.orgList = res.data.data.map((item) => {
        return { text: item.districtName, code: item.districtCode };
      });
    });
  }
  async handleSure() {
    let locInfo = JSON.parse(window.localStorage.getItem('locInfo')) || {};
    if (Object.keys(locInfo).length > 0) {
      this.locationInfo.locInfo = locInfo;
    }
    let params: any = {
      id: this.$route.params.id,
      receiptOrgList: [{ participatorId: 'GJ.HUBS.WUHS.JIANGHO', receiptOper: '江汉区' }],
      receiptUserList: [{ participatorId: '8af28c2c7b09f9a6017b25953a0d0db1', receiptOper: '湖北省武汉市应急管理局管理员' }]
    };
    apiServer.additionalPush(params, (res) => {
      console.log('finishTask------->', res);
      if (res.data.status === 200) {
        Notify({ type: 'success', message: res.data.msg || '结束成功' });
        this.$emit('handleSureFeedback', res.data.status);
        this['$socketApi'].onsend({
          eventId: this.$route.params.id,
          requestFrom: 'app'
        });
      } else {
        Notify({ type: 'danger', message: res.data.msg || '结束失败' });
      }
    });
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.main {
  .header {
    position: relative;
    height: 54px;
    line-height: 54px;
    text-align: center;
    font-size: 18px;
    border-bottom: 1px solid #ccc;
    /deep/.van-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .content {
    padding: 10px 20px;
    /deep/.van-field {
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    /deep/.van-uploader {
      margin-top: 15px;
    }
  }
  /deep/.footer {
    height: 80px !important;
    border: none !important;
    .sureBtn {
      min-width: 100px;
      padding: 0 10px;
      text-align: center;
      background: #326fff;
      color: #fff;
      border-radius: 4px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      float: right;
      margin-top: 20px;
      margin-right: 20px;
    }
  }
  .sureBtn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    color: #326fff;
    font-size: 16px;
  }
}
</style>
