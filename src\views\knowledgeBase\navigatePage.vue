<template>
  <div class="div_big" id="div_big">
    <van-nav-bar title="知识库" left-text="返回" left-arrow @click-left="GoBack">
      <template #right>
        <!-- <van-icon name="ellipsis" size="25" /> -->
      </template>
    </van-nav-bar>
    <MeetBack @documentBack="documentBack"></MeetBack>
    <div class="div_navigate">
      <van-grid :border="false" :column-num="1">
        <van-grid-item v-for="(item, index) in barlist" :key="index" @click="comeInModel(item.pageUrl)">
          <van-image :src="item.img" />
        </van-grid-item>
      </van-grid>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import MeetBack from '@/components/meet-back/meet-back';
@Component({
  components: {
    MeetBack
  }
})
export default class navigatePage extends Vue {
  barlist = [];
  baseMenus = [
    // {
    //   menu_key:"plan_list",
    //   img:require('../../assets/images/knowledgeList/plan.png'),
    //   pageUrl:'/planList'
    // }
    {
      menu_key: 'knowledge_list',
      img: require('../../assets/images/knowledgeList/knowledge.png'),
      pageUrl: '/knowledgeList'
    },
    {
      menu_key: 'base_knowledge_list',
      img: require('../../assets/images/knowledgeList/baseKnowledge.png'),
      pageUrl: '/baseKnowledgeList'
    },
    {
      menu_key: 'law_list',
      img: require('../../assets/images/knowledgeList/law.png'),
      pageUrl: '/lawList'
    },
    {
      menu_key: 'case_list',
      img: require('../../assets/images/knowledgeList/case.png'),
      pageUrl: '/caseList'
    },
    {
      menu_key: 'teaching_video_list',
      img: require('../../assets/images/knowledgeList/teachVideo.png'),
      pageUrl: '/teachingVideoList'
    },
    {
      menu_key: 'specifications_list',
      img: require('../../assets/images/knowledgeList/guifan.png'),
      pageUrl: '/specificationsList'
    }
  ];
  // app物理返回处理
  documentBack() {
    this.GoBack();
  }
  downloadUrl(){
    let _this=this;
    _this['$gsmdp'].downloadIOFile({
      url:"https://img1.baidu.com/it/u=3246628741,3439955235&fm=26&fmt=auto",
      header:{
        token:"ddddsa",
      },
      fileName:"test.png",
      request:"get",
      filePath: '/storage/emulated/0/com.gsafety.main/file/',
      success: function(res) {
        alert(JSON.stringify(res))
        console.log("success-------"+JSON.stringify(res))
        const filePath = res.filePath
        alert("下载成功打开"+filePath)
        _this['$gsmdp'].openFile({
          filePath: filePath,
          success: function(res) {
            //alert(JSON.stringify(res))
            console.log('打开文档成功')
          }
        })
      },
      taskRunning(result){
        //_this.$toast("start==>"+taskres)
        console.log("taskRunning-------"+JSON.stringify(result))
      }
      ,taskComplete(result) {
        console.log('Download taskComplete: ' + JSON.stringify(result));
         const filePath = result.filePath
        _this['$gsmdp'].openFile({ 
          filePath: filePath,
          success: function(res) {
            //alert(JSON.stringify(res))
            console.log('打开文档成功')
          }
        })
      }
    })
  }
  comeInModel(_url) {
    console.log('url==>' + _url);
    let _this=this;
    // _this['$gsmdp'].openVoice({
    //     success: function(res) {
    //       console.log(JSON.stringify(res))
    //       alert(JSON.stringify(res))
    //     }
    // })

    //_this.downloadUrl()
    this.$router.push({ path: _url, query: {menu: 'navigatePage'} });
  }
  GoBack() {
    this.$router.push('/');
    // let _this = this;
    // try {
    //   _this['$gsmdp'].finish(function() {
    //     console.log('Gback');
    //   });
    // } catch (error) {
    //   console.log('close error');
    // }
  }
  created() {
    let _this = this;
    console.log(JSON.stringify(window['myconfig']));
    //console.log(JSON.stringify(_this.$store.state.app.configs.rule))
    _this.$enJsBack();
  }
  mounted() {
    let _this = this;

    // let windowRule=window['myconfig'].rule.menu;
    // console.log(windowRule)
    let ruleArr = _this.$store.state.knowledgeRule;
    if (ruleArr.length <= 0) {
      window['GitConfig'].then(res => {
        console.log("kkkkk->",res);
        let windowRule = res.rule.menu;
        windowRule.forEach((item, index) => {
          if (item.showmenu) {
            _this.baseMenus.forEach((itemch, indexch) => {
              if (item.menu_key == itemch.menu_key) {
                itemch['title'] = item.title;
                itemch['children'] = item.children;
                ruleArr.push(itemch);
              }
            });
          }
        });
        _this.$store.dispatch('SET_KNOWLEDGERULE', ruleArr);
        _this.barlist = ruleArr;
      });
    } else {
      _this.barlist = ruleArr;
    }

    // _this['$gsmdp'].initGoback(function() {
    //   console.log('调用返回');
    //   _this.GoBack();
    // });
    //  this.$gsmdp.initGoback(function(){
    //   _this.GoBack()
    // })
  }
}
</script>
<style scoped lang="scss">
#div_big {
  width: 100%;
  height: 100%;
  background: #fff;
  .div_navigate {
    height: calc(100% - 46px);
    overflow: auto;
  }
  .van-grid-item__content {
    padding: 2vw 4vw;
  }
}
</style>
