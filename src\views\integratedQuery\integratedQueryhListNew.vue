<template>
  <div class="div_big">
    <van-nav-bar title="综合查询" left-text="返回"   left-arrow  @click-left="GoBack" >
      <template #right>
      <!-- <van-icon name="ellipsis" size="25" /> -->
      </template>
   </van-nav-bar>
   <!-- invoke common List -->
    <div class="list">
      <div class="list-main">
        <van-tabs v-model="curInfoType" @change="onClickTab">
          <van-tab :title="item.name" :name="item.type" v-for="item in infoType" :key="item.type"></van-tab>
        </van-tabs>
        <div class="list-main-search">
          <van-search v-model="keyword" placeholder="关键字搜索" @search="onSearch" :maxlength="20" />
        </div>
        <div class="list-main-cont">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              :offset="20"
              @load="getList"
            >
              <van-cell v-for="item, index in list" :key="index" class="van-ellipsis" @click="handleToDetail(item)">
                <template #title>
                  <div class="list_item">
                    <div class="list_content">
                      <div v-if="activeProp.title">
                        {{ item[activeProp.title] }}
                      </div>
                    </div>
                    <div class="list_top">
                      <div class="btnPart">
                        <button v-if="activeProp.tagName1 && item[activeProp.tagName1]" class="total text_ellipsis">{{ item[activeProp.tagName1] }}</button>
                        <button v-if="activeProp.tagName2 && item[activeProp.tagName2]" class="action text_ellipsis">{{ item[activeProp.tagName2] }}</button>
                        <span v-if="activeProp.time" class="time_show">{{ item[activeProp.time] }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </van-cell>
            </van-list>
          </van-pull-refresh>
        </div>
        <van-popup v-model="detailHandle.showInfo" v-if="detailHandle.showInfo"  position="right" :style="{ height: '100%' , width: '100%' }" >
          <component :is="activeProp.loadName" :requestId="requestId" @close="closeInfo"/>
        </van-popup>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import commonList from '../common/commonList.vue';
import apiServer from '../../api/request-service';
import { Notify } from 'vant';
import dataHandle from '../knowledgeBase/dataHandle';
import planInfo from '@/views/planManage/planManageInfo.vue';
import Infodetail from '@/views/knowledgeBase/caseInfo.vue';
import knowledgeInfo from '@/views/knowledgeBase/knowledgeInfo.vue';
import lawInfo from '@/views/knowledgeBase/lawInfo.vue';
@Component({
  components: {
    commonList,
    planInfo,
    Infodetail,
    knowledgeInfo,
    lawInfo
  }
})
export default class integratedQueryhListNew extends Vue {
  private curInfoType: any = ''; // 当前tabs
  private activeProp: any = {
    title: 'title',
    tagName1: 'establishOrgName',
    tagName2: 'planTypeCodeName',
    time: 'publishTime',
    loadName: 'planInfo'
  };
  private infoType: any = [ // tabs
    {
      name: '预案',
      type: 'findPlanManageList',
      prop: {
        title: 'title',
        tagName1: 'establishOrgName',
        tagName2: 'planTypeCodeName',
        time: 'publishTime',
        loadName: 'planInfo',
      }
    },
    {
      name: '案例',
      type: 'findCaseList',
      prop: {
        title: 'title',
        tagName2: 'planTypeCodeName',
        time: 'publishTime',
        loadName: 'Infodetail',
      }
    },
    {
      name: '知识',
      type: 'findKnowledgeList',
      prop: {
        title: 'title',
        tagName1: 'publishOrgName',
        tagName2: 'typeCodeName',
        time: 'publishTime',
        loadName: 'knowledgeInfo',
      }
    },
    {
      name: '法规',
      type: 'findLawList',
      prop: {
        title: 'title',
        tagName1: 'publishOrgName',
        time: 'publishTime',
        loadName: 'lawInfo',
      }
    }
  ];
  private keyword: any = ''; //关键字搜索
  private paging: any = {
    total: null,
    nowPage: 1,
    pageSize: 15,
  }
  private list: any = [];
  refreshing: boolean = false;
  loading: boolean = false;
  finished: boolean = false;
  detailHandle : any ={ showInfo : false };
  requestId : any = "";
  
  GoBack(){
    this.$router.push('/home');
    // let _this=this;
    // try {
    //     _this['$gsmdp'].goBack(function(){
    //       console.log("fanhui")
    //     })
    // } catch (error) {
    //     console.log("close error")
    // }
  }

  private onClickTab(data) {
    this.curInfoType = data;
    this.paging.nowPage = 1;
    this.infoType.forEach((item: any) => {
      if(item.type === data) {
        this.activeProp = item.prop;
      }
    })
    this.keyword = '';
    this.list = [];
    this.getList(true);
  }

  private onRefresh() {
    // 清空列表数据
    this.finished = false;
    this.list = [];
    // 重新加载数据 将 loading 设置为 true，表示处于加载状态
    this.loading = true;
    this.refreshing = true;
    this.paging.nowPage = 1;
    this.getList();
  }

  // 跳转详情页面
  handleToDetail(item) {
    const path = '/planManageList';
    this.requestId = item.id;
    this.detailHandle.showInfo = true;
    console.log(item,'=============item')
    // this.$router.push({
    //   path: path,
    //   query: {
    //     type: 'detail',
    //     infotype: this.curInfoType,
    //     id: item.id
    //   }
    // })
  }

  onSearch(value) {
    this.paging.nowPage = 1;
    this.getList(true);
  }

  // 获取列表数据
  getList(first=false) {
    const that = this;
    let params: any = {
      keyWord: this.keyword,
      nowPage: this.paging.nowPage,
      pageSize: this.paging.pageSize,
    };
    if (this.list.length > 0 && this.list.length === this.paging.total && !first) {
      return
    }
    this.loading = true;
    setTimeout(() => {
      apiServer[this.curInfoType](params, res => {
        if (res.data.status === 200) {
          if(that.paging.nowPage === 1) {
            that.list = []
          }
          let dataHandleList: any = [];
          let arr: any = [];
          if (that.curInfoType === 'findPlanManageList') {
            dataHandleList = dataHandle.planList(res);
          } else if (that.curInfoType === 'findCaseList') {
            dataHandleList = dataHandle.caseList(res);
          } else if (that.curInfoType === 'findKnowledgeList') {
            dataHandleList = dataHandle.knowledgeList(res);
          } else if (that.curInfoType === 'findLawList') {
            dataHandleList = dataHandle.lawList(res);
          }
          
          if (dataHandleList.length > 0) {
           arr = [...dataHandleList];
          }
          if (first) {
            that.list = arr;
          } else {
            that.list = [...that.list, ...arr];
          }
          that.paging.total = res.data.data.total;
          that.loading = false;
          that.refreshing = false;
          that.finished = false;
          if(that.list.length >= res.data.data.total) {
            that.finished = true;
          }
          that.paging.nowPage++;
        } else {
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      })
    }, 0)
  }

  private closeInfo(){
    let _this=this;
    _this.detailHandle.showInfo = false;
    _this['$gsmdp'].initGoback(function(){
      try {
          _this['$gsmdp'].goBack(function(){
          })
      } catch (error) {
          console.log("close error")
      }
    })

  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div{
    width: 100%;
  }
}
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  .van-tabs__line {
    background-color: #1967f2;
  }
  .van-ellipsis {
    margin-bottom: 10px;
  }
  .list_item {
    max-width: 350px;
    background: white;
    .text_ellipsis{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .list_top {
      width: 100%;
      height: 50%;
      position: relative;
      .btnPart {
        padding: 0px 4vw 4vw 3%;
        .action {
          max-width: 40%;
          background: transparent;
          color: #e45050;
          border: 1px solid #e45050;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          margin-left: 5px;
        }
        .total {
          max-width: 40%;
          background: transparent;
          color: #00a0e9;
          border: 1px solid #00a0e9;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          
        }
        .time_show {
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 0px;
          float: right;
        }
      }
    }
  }
  .list_content {
    width: 100%;
    line-height: 35px;
    padding-left: 3%;
    font-size: 15px;
    div {
      float: left;
    }
    div:nth-of-type(1) {
      width: 90%;
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    div:nth-of-type(2) {
      width: 10%;
      height: 100%;
      img {
        width: 15px;
        vertical-align: middle;
        margin-top: -5px;
      }
    }
  }
}
</style>