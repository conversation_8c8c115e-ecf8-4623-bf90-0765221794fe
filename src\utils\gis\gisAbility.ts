import { EnvironmentContext } from './environmentContext';
import gisConfig from '@/components/baseMap/gisConfig.json';
let egis = require('egis-2d');

export default {
  /** 初始化egisMap对象 */
  initEgisMap(mapId) {
    EnvironmentContext.egisMap = new egis.carto.Map(gisConfig.baseMapConfig.map);
    EnvironmentContext.egisMap.init({ targetId: mapId || 'g2map' });
  },
  /** 创建TileLayer图层实例，加载天地图WMTS服务数据 */
  createMapTileLayer() {
    //创建服务请求对象
    var restHttp = new egis.core.RestHttp({
      client_id: gisConfig.internetEgisConfig.clientId, //用户id
      client_secret: gisConfig.internetEgisConfig.clientSecret //用户密码
    });
    var tiandituvec = new egis.carto.TileLayer({
      restHttp: restHttp, //服务请求对象
      name: '天地图矢量',
      layers: 'vec', // "图层名称",
      matrix: 21, // "切图级别小于等于切图级别",
      matrixSet: 'c', // "切图策略",
      matrixPrefix: '', // "切图策略加冒号：",
      format: 'tiles', // "图层格式",
      projection: 'EPSG:4490', // "投影参考",
      layerType: 1, // "图层类型",
      tileType: 102, // "瓦片类型",
      opacity: 1.0, // "透明度",
      visible: true, // "是否显示",
      crossOrigin: 'anonymous',
      style: 'default',
      extent: { minx: -180, miny: -90, maxx: 180, maxy: 90 },
      wrapX: true, // "是否展示循环图",
      url: gisConfig.internetEgisConfig.url + '/wmts'
    });
    var tianditucta = new egis.carto.TileLayer({
      restHttp: restHttp, //服务请求对象
      name: '天地图中文标注',
      layers: 'cva',
      matrixSet: 'c',
      format: 'tiles',
      projection: 'EPSG:4490',
      extent: { minx: -180, miny: -90, maxx: 180, maxy: 90 },
      matrixPrefix: '',
      matrix: 21,
      tileType: 102,
      opacity: 1.0,
      visible: true,
      url: gisConfig.internetEgisConfig.url + '/wmts'
    });
    EnvironmentContext.egisMap.addLayer(tiandituvec); //添加图层到地图
    EnvironmentContext.egisMap.addLayer(tianditucta); //添加中文图层到地图
  },
  getLocationCode(location) {
    //构造逆地理编码服务对象
    let WRGSService = new egis.ews.RestWRGSService({
      url: gisConfig.internetEgisConfig.url, //服务
      clientId: gisConfig.internetEgisConfig.clientId, //用户id
      clientSecret: gisConfig.internetEgisConfig.clientSecret, //用户密码
      authType: gisConfig.internetEgisConfig.authType,
      tokenUrl: gisConfig.internetEgisConfig.tokenUrl
    });
    let WRGSInput = new egis.ews.WRGSInput({
      // location: '116.4091658,39.9580264'
      location: location
    });
    let WRGSInputCode: any = WRGSService.regeocode(WRGSInput);
    return new Promise((resolve, reject) => {
      WRGSInputCode.then((data) => {
        // locationInfo = data;
        let locInfo: any = {};
        locInfo.longitude = data.location.x;
        locInfo.latitude = data.location.y;
        locInfo.address = data.formatted_address;
        window.localStorage.setItem('locInfo', JSON.stringify(locInfo));
        resolve(locInfo);
      });
    });
  },
  getByAddress(address, center) {
    //构造地图搜索服务对象
    let wpssService = new egis.ews.RestWPSSService({
      url: gisConfig.internetEgisConfig.url, //服务
      clientId: gisConfig.internetEgisConfig.clientId, //用户id
      clientSecret: gisConfig.internetEgisConfig.clientSecret, //用户密码
      authType: gisConfig.internetEgisConfig.authType,
      tokenUrl: gisConfig.internetEgisConfig.tokenUrl
    });
    let AroundInput = new egis.ews.AroundInput({
      keyword: address,
      region: '武汉市',
      radius_limit: false,
      query_type: 2,
      scope: '2',
      bounds: '113,29,116,32',
      location: `${center[0]},${center[1]}`,
      radius: 5000,
      page_num: 1,
      page_size: 30,
      source: 'baidu'
    });
    let AroundInputCode: any = wpssService.around(AroundInput);
    return new Promise((resolve, reject) => {
      AroundInputCode.then(async (data) => {
        resolve(data.baidu || []);
      });
    });
  },
  // 创建互联网定位服务对象
  locationServer() {
    let WILSService = new egis.ews.RestWILSService({
      url: gisConfig.internetEgisConfig.url,
      clientId: gisConfig.internetEgisConfig.clientId, //用户id
      clientSecret: gisConfig.internetEgisConfig.clientSecret, //用户密码
      authType: gisConfig.internetEgisConfig.authType,
      tokenUrl: gisConfig.internetEgisConfig.tokenUrl
    });
    var WILSInput = new egis.ews.WILSInput({
      accesstype: 1,
      macs: 'a2:88:69:72:30:d0,-43,DIRECT-EFDESKTOP-KS6AQ10ms8M|88:25:93:d3:51:bb,-49,gsafety|88:25:93:d3:51:b8,-49,gsafety|dc:fe:18:8f:dd:1a,-50,TP-LINK_DD1A|a2:c5:89:fd:d9:e1,-50,DIRECT-XIXIAOMIAIRmsQO|88:25:93:d3:50:57,-52,gsafety|88:25:93:d3:51:b9,-56,gsafety|88:25:93:d3:50:3f,-56,gsatety|88:25:93:d3:50:3d,-61,gsafety|ba:b2:f8:ae:98:f8,-61,iPhonezhao|dc:fe:18:8f:dd:1c,-63,TP-LINK_5G_DD1A|88:25:93:d3:50:55,-65,gsafety|2a:56:5a:80:92:fe,-77,DIRECT-fe-HP M427 LaserJet',
      imei: '868938035421678',
      ctime: new Date().getTime(),
      nearbts: '460,1,7141,39232,-81|460,1,7141,39232,-77|460,1,7141,63502,-77',
      cdma: 0,
      network: 'GPRS',
      need_rgc: 'Y',
      coor: 'CGCS2000'
    });
    var promise = WILSService.location(WILSInput);
    promise.then(function (data) {
      console.log('互联网地址定位----->111', data); //未出错时，data即为互联网智能设备定位结果
      let locInfo: any = {};
      locInfo.longitude = data.location.x;
      locInfo.latitude = data.location.y;
      locInfo.address = `${data.province}${data.city}${data.district}${data.road}`;
      window.localStorage.setItem('locInfo', JSON.stringify(locInfo));
    });
    // var promise = WILSService.locationByIP('************'||window.location.hostname);
    // promise.then(function (data) {
    //   console.log('互联网IP地址定位----->', data);//未出错时，data即为互联网智能设备定位结果
    // })
  }
};
