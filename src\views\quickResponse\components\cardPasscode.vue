<template>
  <!-- 通行二维码 -->
  <div class="teamSignIn-item qrcode" :class="autoCreate?'fullPage':''">
          <div class="teamSignIn-user" v-if='!autoCreate'>{{detailCodeData.orgName || '省应急厅'}}</div>
         <div class="teamSignIn-info">
      <p  v-if='!autoCreate'>{{detailCodeData.orgName || '省应急厅'}}</p>
      <div class="teamSignIn-box">
        <h2>
          <van-icon name="arrow-left" v-if='autoCreate' class='leftside' @click="back" />
          通行二维码</h2>
        <div class="teamSignIn-main">
          <p class='teamSignIn-flex'><span>任务地点</span>
            <a >{{detailCodeData.passCode.address||detailCodeData.address||'暂无'}}</a>
          </p>
           <p class='teamSignIn-flex'><span>通行区域</span>
             <a >{{detailCodeData.passCode.passAreaDesc||detailCodeData.passAreaDesc||'暂无'}}</a>
           </p>

           <p>{{date}} {{nowTime}}<span class="green">{{seconds}}</span></p> 

          <div class="teamSignIn-code">
            <img :src="detailCodeData.passCode.passCode||detailCodeData.passCode" />
          </div>
          <div class="teamSignIn-msg" >
               <h2>
                  <i></i>通行信息
                 <span class="checkBtn" @click='download'>查看调令</span>
              </h2>
             
            <div>
              <span>联 &nbsp;系 &nbsp;人</span>
              <span>{{detailCodeData.passCode.relName||detailCodeData.relName || '-'}}</span>
            </div>
            <div>
              <span>联系方式</span>
              <span>{{detailCodeData.passCode.relMobile||detailCodeData.relMobile || '-'}}</span>
            </div>
            <div>
              <span>单位名称</span>
              <span>{{detailCodeData.passCode.teamName||detailCodeData.teamName || '-'}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import taskRequest from "@/api/webcross/task";
import { Notify } from 'vant';


@Component({
  components: {
  }
})
export default class cardPasscode extends Vue {
  @Prop() public details: any; // 队伍信息
  @Prop() public eventInfo: any; // 事件信息
  @Prop() public teamInfo: any;
  @Prop() public relName:any;
  @Prop({default:false}) private autoCreate;

  @Prop() public locationInfo: any; // 地理位置信息

  private detailCodeData ={};

  private loadOver = false;

 

  private download(){
   let downloadElement = document.createElement('a');
   let flag = false;
    // downloadElement.href = 'http://************:8990/gapi/gemp-plan/api/gemp/dispatch/order/v1/download/2c92882685046c58018504fabdc6000b?tmp=1670826851223';
    // downloadElement.target = '_blank'
    // downloadElement.download = '测试下载'
    // document.body.appendChild(downloadElement)
    // downloadElement.click()
    // document.body.removeChild(downloadElement)
    // window.URL.revokeObjectURL( downloadElement.href) // 释放掉blob对象
    // window.open('http://223.75.236.113:19355/gapi/gemp-plan/api/gemp/dispatch/order/v1/download/2c92882685046c58018504fabdc6000b?tmp=1670826851223')
    taskRequest.getOrderDoc({
      eventId:this.eventInfo.eventId,
      status:1
    },(res)=>{
      let url = '';
      if(res.data.data.list && res.data.data.list.length>0){
        let list =  res.data.data.list;
        for(var i=0;i<list.length;i++){
            if(
              ( list[i].contactTel ==this.teamInfo.relMobile )){
                     url = res.data.data.list[i].viewUrl;
                      flag = true;
                     break;
            }
        };
        if(flag){
              window.open(window.location.origin+'/'+ url)
          } else {
          Notify({type:'danger',message:'当前暂无相关调令'})
        }
        
      } else {
        Notify({type:'danger',message:'当前暂无相关调令'})
      }
    })

  
  }

  private nowTime: any = new Date().toTimeString().split(' ')[0] // 当前时间,时分秒
  private timer=null;
  private seconds=null;
  private date:any= '';
  formateDate(time) {
    // 兼容之前的时间格式
    if (time instanceof Object) {
      return this['$moment'](time.time).format('YYYY-MM-DD');
    } else {
      return this['$moment'](time).format('YYYY-MM-DD');
    }
  };
  private mounted(){
      this.date = this.formateDate(new Date())
      this.timer = setInterval(() => {
      this.$set(this, 'nowTime',new Date().toTimeString().split(' ')[0].slice(0,6))
      this.$set(this, 'seconds',new Date().toTimeString().split(' ')[0].slice(6));
    }, 1000);
    let currentTime = this.date +' '+new Date().toTimeString().split(' ')[0];
      if(this.autoCreate){
        if(!this.locationInfo.locInfo?.address){
          Notify('未获取到当前经纬度,请重新再试');
          setTimeout(()=>{
             this.back();
          },400)
          return;
        }
          taskRequest.addNewRouter({
                "eventId": this.eventInfo.eventId,
                "routeAddress": this.locationInfo.locInfo?.address ,
                "routeDate": currentTime,
                "routeLatitude": this.locationInfo.locInfo?.latitude,
                "routeLongitude":this.locationInfo.locInfo?.longitude,
                "routeType": "4",
                "teamId": this.eventInfo.teamId,
          },(res)=>{
               this.loadOver = true;
               console.log(res,'res');
              this.detailCodeData = res.data.data;

          })
      } else {
        this.detailCodeData = this.details;
      }
  }
  private beforeDestroy(){
    window.clearInterval(this.timer);
    console.log('clear')
  }
  private back(){
    this.$emit('back')
  }
}
</script>

<style lang="less" scoped>
.green{
  color:#1967f294 ;
  font-weight: bold;
  font-size: 18px;
}
.teamSignIn-flex{
  display: flex;
  align-items: center;
  span{
    width: 80px;
  }
  a{
    flex: 1;
    color: #1c1d1d;
    text-align: left;
  }
}
.fullPage{
  width: 100% !important;
  height: 100% !important;
  /deep/ .teamSignIn-info{
    width: 100%;
    display: block;
    margin: 0 auto;
  }
  h2{
    position: relative;
  }
  .leftside{
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    color: white;
  }
}
.checkBtn{
  position: absolute;
  right: 12px;
  top: 4px;
  padding: 0px 6px;
  border: 1px solid #1967f294;
  color: #1967f294;
  text-align: center;
  height: 32px;
  line-height: 32px;
  font-weight: normal;
  font-size: 14px;
}
</style>