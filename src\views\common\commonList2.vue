<template>
    <div class="my_list">
      <div v-if="tabArr&&tabArr.length>0">
        <van-tabs>
          <van-tab v-for="(item,index) in tabArr" :title="item.name" :key="index">
            <div class="search_div">
              <van-search v-model="keyword" placeholder="请输入搜索关键词"  />
            </div>
            <div class="content_list">
              <ul class="content_ul" ref="container" >
                  <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
                    <integratedItem ref="itemsRef" @handleObj="handleObj" />
                  </mu-load-more>
              </ul>
            </div>
          </van-tab>
        </van-tabs>
      </div>
      <div v-else>

      </div>
      
    </div>
</template>
<script lang="ts">
import integratedItem from '../integratedQuery/integratedItem.vue';
export default {
  components:{
    integratedItem
  },
  data() {
    return {
      keyword:"",
      refreshing:false,//是否正在刷新
      loading:false,//是否正在加载

    };
  },
  props: ["tabArr"],
  created(){
    console.log("tabArr",this.tabArr)
  },
  methods: {
    GoBack(){
      let _this=this;    
      console.log("go back")
      window['gsmdp'].showToast({title: 'Go back'})
      if(_this.isNotRoute){
        _this.$emit("close")
      }else{
        
        if(_this.$isAndroid()){
          if(this.whetherNotParent){
            // window['dsBridge'].register('GoBack', function (ret) {
            //   return useHtml;
            // })
            window['app'].close()
          }else{
             _this.$router.go(-1)
          }
        }else{
           _this.$router.go(-1)
        }
        
      }
      //_this.$isAndroid()
    },
    handleRight(_url){
      this.$emit("handleRight",_url)
    },
    handleObj(item){
      let _this=this;
      console.log(item)
      if(!item.uploadMore){
        _this.refreshing=false;
        _this.loading=false;
      }
    },
    gother() {
      if(this.title.flag){
        this.$router.push("/other"); //其他区域
      }
    },
    refresh() {
      let _this = this;
      _this.refreshing = true;
      _this.pageIndex=1;
      let pageObj={pageIndex:1,pageSize:_this.pageSize}
      _this.$refs.itemsRef.queryRequest(pageObj)
    },
    load() {
      const _this = this;
      _this.pageIndex++;
      _this.loading = true;
      let pageObj={pageIndex:_this.pageIndex,pageSize:_this.pageSize}
      _this.$refs.itemsRef.queryRequest(pageObj)
    }
  },mounted:function () {
    window['GoBack'] = this.GoBack;//返回
    let _this=this;
    window['dsBridge'].register('GoBack', function (ret) {
      _this.GoBack();
    })
  }
};
</script>
<style scoped>
.my_list{
  height: calc( 100% - 46px );
  background: #f4f7f8;
}
.content_list{
  height: calc( 100% - 106px );
   padding: 0 15px;
   overflow: auto;
  background: #f1efef;
}
</style>
