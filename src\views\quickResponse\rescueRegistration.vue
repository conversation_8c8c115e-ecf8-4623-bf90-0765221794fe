<template>
  <div class="rescueRegistration">
    <Header title="救援登记"></Header>
    <div class="rescueRegistration-form">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="formObj.arriveNum"
            name="arriveNum"
            label="抵达人数"
            placeholder="0"
            input-align="right"
            :rules="[{ pattern, required: true, message: '请输入抵达人数' }]"
          />
          <van-field
            v-model="formObj.goods"
            type="textarea"
            name="goods"
            label="携带物资装备"
            :placeholder="'请输入携带物资装备名称及数量\n例：手套，10双'"
            :rules="[{ required: true, message: '请输入携带物资装备名称及数量，例：手套，10双' }]"
          />
        </van-cell-group>
        <div class="submit-btn">
          <van-button block type="primary" native-type="submit">
            确认提交
          </van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
  }
})
export default class RescueRegistration extends Vue {
  private username:any = '';
  private password = '';

  private formObj:any = {
    arriveNum: null,
    goods: ''
  }
  private pattern:any = /\d/;
  private onSubmit(values) {
    console.log('onSubmit---------->', values)
  }
  private onSubmit1(values) {
    console.log('onSubmit1---------->', values)
  }
}
</script>

<style lang="less" scoped>
.rescueRegistration {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #f5f6f6;
  &-form {
    flex: 1;
    // padding: 12px 10px;
    /deep/.van-form{
      display: flex;
      flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
          border-bottom: 1px solid #cecece;
          &:nth-child(2) {
            flex: 1;
            flex-direction: column;
            border: none;
            .van-cell__title {
              width: 100%;
              padding-bottom: 12px;
              border-bottom: 1px solid #cecece;
            }
            .van-cell__value {
              padding-top: 12px;
              .van-field__body {
                height: calc(100% - 25px);
                .van-field__control {
                  height: 100%;
                }
              }
            }
          }
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
      }
    }
  }
  .submit-btn {
    width: 100%;
    background: #fff;
    padding: 16px;
  }
  /deep/.van-field__error-message {
    text-align: right;;
  }
}
</style>