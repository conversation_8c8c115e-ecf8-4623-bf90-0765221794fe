<template>
  <div class="list">
    <Header title="呈报上报" backPath="/reportto"></Header>
    <div class="list-tab">
      <van-form @submit="onSubmit" class="list-card">
        <van-cell-group inset v-for="(item, index) in detailsData" :key="index">
          <template v-for="ele in item.list">
            <van-field
              v-if="ele.inputType === 'textarea'"
              v-model="details[ele.prop]"
              :label="ele.name"
              rows="4"
              type="textarea"
              :maxlength="ele.maxlength || ''"
              :placeholder="ele.placeholder || '请输入内容'"
              show-word-limit
              :rules="rules[ele.prop]"
            >
              <template #left-icon>
                <van-icon :name="ele.icon" :color="ele.color" />
              </template>
            </van-field>
            <van-field
              v-else-if="ele.inputType === 'popup'"
              v-model="details[ele.prop]"
              :label="ele.name"
              :name="ele.typeCode ? ele.typeCode : ele.prop"
              is-link
              readonly
              :placeholder="ele.placeholder || '请输入内容'"
              :rules="rules[ele.prop] || rules[ele.typeCode] || []"
              @click="
                showPicker = true;
                popupType = ele.popupType;
                calendarKey = ele.prop;
              "
            >
              <template #left-icon>
                <van-icon :name="ele.icon" :color="ele.color" />
              </template>
            </van-field>

            <div v-else-if="ele.type === 'issuerName'" class="van-cell van-field van-field--min-height">
              <!-- 领导审批 -->
              <div class="van-field__left-icon">
                <van-icon :name="ele.icon" :color="ele.color" />
              </div>
              <div class="van-cell__title van-field__label">
                <template> {{ ele.name }} </template>
              </div>

              <!-- <template native-type="button" @click="clickAdd">添加</template> -->
              <div class="boxPerson">
                <div v-for="(el, j) of personArr" :key="j">
                  <span>{{ el.personName }}</span>
                </div>
                <van-button round native-type="button" @click="clickAdd()">添加</van-button>
              </div>
            </div>

            <!-- 联系方式 -->
            <div v-else-if="ele.type === 'editorTel'" class="van-cell van-field van-field--min-height">
              <div class="van-field__left-icon">
                <van-icon :name="ele.icon" :color="ele.color" />
              </div>
              <div class="van-cell__title van-field__label">
                <template> {{ ele.name }} </template>
              </div>
              <div v-for="(el, j) of personArr" :key="j">
                <span>{{ el.telNumber }}</span>
              </div>
            </div>

            <!-- 事件标题 -->
            <div v-else-if="ele.type === 'title'" class="van-cell van-field van-field--min-height">
              <div class="van-field__left-icon">
                <van-icon :name="ele.icon" :color="ele.color" />
              </div>
              <div class="van-cell__title van-field__label">
                <template> {{ ele.name }} </template>
              </div>
              <div class="boxPerson" @click="clickTitleFn">
                {{ eventTitle }}
              </div>
            </div>

            <!-- {{content}} -->
            <!-- <div v-else-if="ele.type === 'content'" class="van-cell van-field van-field--min-height">
              <div class="van-field__left-icon">
                <van-icon :name="ele.icon" :color="ele.color" />
              </div>
              <div class="van-cell__title van-field__label">
                <template> {{ ele.name }} </template>
              </div>
              <div class="boxPerson" @click="clickTitleFn">
                {{ content }}
              </div>
            </div> -->

            <van-field
              v-else
              v-model="details[ele.prop]"
              :label="ele.name"
              :name="ele.typeCode ? ele.typeCode : ele.prop"
              :placeholder="ele.placeholder || '请输入内容'"
              clearable
              :rules="rules[ele.prop] || rules[ele.typeCode] || []"
            >
              <template #left-icon>
                <van-icon :name="ele.icon" :color="ele.color" />
              </template>
              <template #input v-if="ele.type === 'file'">
                <van-uploader v-model="details.attachment" :max-size="100 * 1024 * 1024" :max-count="5" />
              </template>
            </van-field>
          </template>
        </van-cell-group>
        <div class="submit-btn">
          <van-button block type="primary" native-type="submit"> 确认{{ btnTitle }} </van-button>
        </div>
      </van-form>

      <!-- <div @click="clickAdd">添加</div> -->
      <Popup
        :type="popupType"
        :showPicker="showPicker"
        :keys="calendarKey"
        @onConfirm="onConfirm"
        @onCancel="
          showPicker = false;
          popupType = '';
        "
      ></Popup>
    </div>

    <div class="bigAction">
      <van-action-sheet v-model="showInfoType" @select="onSelect" class="aaaa">
        <div class="searchTop">
          <van-search v-model="keywordInfo" placeholder="请输入搜索关键词" @search="onSearch" @cancel="onClear" />
        </div>

        <div class="serachCon">
          <div v-if="infoData.length > 0">
            <div :class="['searchItem']" @click="clickInfo(el, index)" v-for="(el, index) of infoData" :key="index">
              {{ el.eventTitle }}<van-icon name="arrow" />
            </div>
          </div>
          <div v-else><van-empty description="暂无数据" /></div>
        </div>
      </van-action-sheet>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { downloadReport } from '@/utils/validate';
import { Cascader, Notify } from 'vant';
import Popup from '@/components/popup.vue';
import apiServer from '../../api/request-service';
// 校验函数返回 true 表示校验通过，false 表示不通过
let validator = (val) => val.length <= 50;
@Component({
  components: {
    Header,
    Popup
  }
})
export default class addForm extends Vue {
  public detailsData: any = [
    {
      name: 'baseInfo',
      list: [
        {
          name: '事件标题',
          // prop: 'title',
          color: '#c8e3ee',
          icon: 'coupon-o',
          // inputType: 'textarea',
          placeholder: '填写事发时间、事发地点及要素信息后，自动生成信息标题',
          maxlength: '50',
          type: 'title'
        },
        // {
        //   name: '事件内容',
        //   color: '#eed49b',
        //   icon: 'orders-o',
        //   placeholder: '填写事发时间、事发地点及要素信息后，自动生成信息摘要',
        //   maxlength: '1000',
        //   type: 'content'
        // },

        //content
        {
          name: '呈报内容',
          prop: 'content',
          color: '#c8e3ee',
          icon: 'bar-chart-o',
          inputType: 'textarea',
          placeholder: '填写内容',
          maxlength: '1000'
        },

        {
          name: '审批领导',
          color: '#b5dbe8',
          icon: 'user-o',
          type: 'issuerName'
        },
        {
          name: '联系方式',
          color: '#eed49b',
          icon: 'phone-o',
          type: 'editorTel'
        },
        {
          name: '呈报时间',
          prop: 'reviewDate',
          color: '#eed49b',
          icon: 'underway-o',
          type: 'popup',
          inputType: 'popup',
          placeholder: '请选择时间',
          popName: 'showCalendar',
          popupType: 'datetime'
        },
        {
          name: '备注',
          prop: 'remarks',
          color: '#e4bfa0',
          icon: 'newspaper-o',
          inputType: 'textarea',
          placeholder: '填写备注',
          maxlength: '1000'
        }
        // {
        //   name: '附件',
        //   prop: 'attachments',
        //   type: 'file',
        //   color: '#eed49b',
        //   icon: 'photo-o'
        // }
      ]
    }
  ];
  public rules: any = {
    content: [{ required: true, message: '请输入1000字以内呈报内容' }],
    remarks: [{ required: true, message: '请输入1000字以内备注' }],
    reviewDate: [{ required: true, message: '请选择接报时间' }]
  };

  //事件
  private eventTitle: any = '请选择上报的事件';
  private keywordInfo: any = ''; //事件列表关键字搜索
  private reviewEvent: any = '';
  // private content: any = '事件内容';
  private reviewEventTitle: any = '';
  private showInfoType: boolean = false;
  private active: any = '';
  private infoData: any = [];
  private selectInfo:any = {};

  @Watch('keywordInfo')
  private onSearch() {
    this.getListInfo();
  }
  private onClear() {}
  private onSelect() {}
  private getListInfo() {
    let params = {
      eventTitle: this.keywordInfo,
      nowPage: 1,
      pageSize: 10
    };
    apiServer.eventList(params, (res) => {
      if (res.status === 200) {
        this.infoData = res.data.data.list;
        console.log(this.infoData, '事件事件列表');
      } else {
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    });
  }
  private clickTitleFn() {
    this.showInfoType = true;
    this.getListInfo();
  }
  private clickInfo(val, i) {
    this.showInfoType = false;
    debugger;
    this.$store.commit('SET_ENENTINFO', val);
    this.selectInfo = this.$store.state.app.eventInfo;
    this.eventTitle = this.selectInfo.eventTitle;
    this.reviewEvent = this.selectInfo.eventId;
    this.reviewEventTitle = this.selectInfo.eventTitle;
    // this.content=val.content
  }

  // 是否显示面板
  public showPopupPicker: any = false;
  public popupType: any = '';

  public showCalendar = false;
  public showPicker = false;
  public showTypePicker = false;
  public showAreaAddress = false;
  // 选项面板
  public eventAreaList = [];

  public type: any = ''; // 页面类型，add-新增，detail-详情
  public infoType: any = ''; // 页面信息类型
  public details: any = {
    attachment: []
  }; // 接报信息字段

  public calendarKey = '';
  // public maxDate = new Date();

  public eventTypeList: any = []; // 事件类型

  private role: any;

  public btnTitle = '';
  public formatterCalendar = (type, val) => {
    switch (type) {
      case 'year':
        return `${val}年`;
      case 'month':
        return `${val}月`;
      case 'day':
        return `${val}日`;
      case 'hour':
        return `${val}时`;
      case 'minute':
        return `${val}分`;
      default:
        return val;
    }
  };
  private clickAdd() {
    this.$router.push({ path: `/addressBookCheck` });
  }
  private personArr = [];
  async mounted() {
    debugger;
    this.personArr = this.$store.state.app.personDetail;
    let initEvent = this.$store.state.app.eventInfo;
    let isObject = Object.keys(initEvent)
    if (isObject.length != 0) {
      this.eventTitle = initEvent.eventTitle;
      this.reviewEvent = initEvent.eventId;
      this.reviewEventTitle = initEvent.eventTitle;
    }
    // console.log(this.selectInfo, '获取审批林丹-========');
    // console.log('信息接报----》', this.$route.query);
    // this.role = JSON.parse(localStorage.getItem('role'));
    // this.btnTitle = this.role.tenantId.split('.').length === 2 ? '录入' : '上报';
    // // this.details.reporter = '497e923741c846b38b3fd8f13f7006dd'; // 报送人用户ID
    // this.details.editorId = this.role.personId; // 报送人用户ID
    // // this.details.reporter = this.role.name; // 报送人用户ID
    // this.details.districtCode = this.role.districtCode; // 行政区划代码
    // this.$set(this.details, 'editorTel', this.role.cellphone);
    // this.$set(this.details, 'editorName', this.role.name);
    // this.$set(this.details, 'readerName', this.role.name);
    // this.$set(this.details, 'title', '报送人用户ID报送人用户ID报送人用户ID');
  }

  initEventType() {}

  // 新增/保存信息接报详情
  saveInfo(file = false) {
    let params = Object.assign({}, this.details);
    // 格式化日期传参
    params.reviewDate = params.reviewDate
      ? this['$moment'](params.reviewDate).format('YYYY-MM-DD HH:mm:ss')
      : this['$moment'](new Date()).format('YYYY-MM-DD HH:mm:ss');

    params.reviewEvent = this.reviewEvent;
    params.reviewEventTitle = this.reviewEventTitle;
    params.reviewLeaderName = this.personArr[0].personName;
    params.reviewLeaderId = this.personArr[0].userId;
    params.reviewLeaderPhone = this.personArr[0].telNumber;

    console.log(params, 'paramsparamsparamsparams====');
    this['$api'].InfoRequest.reportreviewAdd(params, (res) => {
      if (res.data.status == 200) {
        this.details.receiveId = res.data.data;
        Notify({ type: 'success', message: res.data.msg || '上报成功' });
        this.$router.push('/reportto');
        this.afterRead(res.data.data);
      } else {
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    });
  }

  onConfirm(val) {
    debugger;
    console.log('onConfirm------->' + val, 'calendarKey------->' + this.calendarKey, 'popupType------->' + this.popupType);
    this.showPicker = false;
    if (this.popupType === 'datetime') {
      this.details[this.calendarKey] = this['$moment'](val).format('YYYY-MM-DD HH:mm:ss');
    } else if (this.popupType === 'cascader') {
      this.details.districtName = val.map((option) => option.fullName).join(' ');
    } else if (this.popupType === 'typePicker') {
      let valObj = JSON.parse(val);
      this.details.eventTypeName = valObj.val[valObj.val.length - 1];
      this.details.eventTypeCode = valObj.eventTypeList[valObj.index[0]].children[valObj.index[1]].id;
    } else if (this.popupType === 'levelPicker') {
      let valObj = JSON.parse(val);
      this.details.eventLevelCode = valObj.val.code;
      this.details.eventLevelName = valObj.val.text;
    }
    // this.popupType = "";
  }
  // 确认上报按钮
  onSubmit() {
    console.log(this.details.reviewDate);
    if (this.reviewEvent == '') {
      Notify({ type: 'danger', message: ' 请选择上报的事件' });
      return;
    }
    if (this.personArr.length == 0) {
      Notify({ type: 'danger', message: ' 请添加审批领导' });
      return;
    }

    this.saveInfo();
  }

  formateDate(time) {
    // 兼容之前的时间格式
    if (time instanceof Object) {
      return this['$moment'](time.time).format('YYYY-MM-DD HH:mm:ss');
    } else {
      return this['$moment'](time).format('YYYY-MM-DD HH:mm:ss');
    }
  }

  //上传
  async afterRead(receiveId) {
    let _this = this;
    const formData = new FormData();
    this.details.attachment.map((ele) => {
      formData.append('files', ele.file);
    });
    formData.append('receiveId ', receiveId);
    this['$api'].InfoRequest.getAttach(formData, function (res) {
      console.log('getAttach-上传完成--->', res);
    });
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.bigAction::v-deep .van-popup--bottom.van-popup--round {
  height: 70%;
}
.serachCon {
  padding: 0 10px;
  .searchItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgb(211, 203, 203);
    padding: 10px 0;
  }
  .searchItem.active {
    color: #1e549e;
  }
  .searchItem:last-child {
    border: 0;
  }
}
/deep/.van-cell {
  align-items: center;
}
.boxPerson {
  display: flex;
  align-items: center;
  /deep/.van-button {
    padding: 5px 5px;
    height: auto;
    line-height: 1;
    margin: 0 0 0 10px;
  }
}
.list {
  // display: flex;
  // flex-direction: column;
  width: 100%;
  height: 100%;
  // overflow: hidden;
  &-card {
    margin-top: 5px;
  }
  &-casualties {
    display: flex;
    flex-wrap: wrap;
    color: #323233;
    align-items: center;
    .custom-title {
      width: 50%;
      text-indent: 10px;
      height: 30px;
      line-height: 30px;
      display: flex;
      &:nth-child(2n + 1) span {
        color: #c8e3ee;
      }
      &:nth-child(2n) span {
        color: #e4bfa0;
      }
      &:nth-child(2n + 2) span {
        color: #af5255;
      }
      &:nth-child(4n) span {
        color: #323233;
      }
      div {
        display: flex;
        justify-content: space-between;
        width: 130px;
      }
      span {
        // display: inline-block;
        // width: 100%;
        font-size: 3.6vw;
        // text-align: right;
        font-weight: 600;
      }
    }
  }
  &-tab {
    width: 100%;
    height: calc(100% - 100px);
    overflow: auto;
    /deep/.van-form {
      // height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        // height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        // height: 100%;
        .van-cell {
          padding: 12px 0;
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .attach_area {
    display: flex;
    align-items: center;
    float: left;
    width: calc(100% - 3rem);
    margin-bottom: 10px;
  }
  .submit-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
  }
}
</style>
