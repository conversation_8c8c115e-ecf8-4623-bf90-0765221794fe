<template>
  <div class="list">
    <Header title="事件详情" backPath="/event"></Header>
    <div class="list-main">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field
            v-model="formObj.title"
            name="title"
            label="事件标题"
            disabled
            input-align="right"
            placeholder="请输入事件标题"
            :rules="[{ required: true, message: '请输入事件标题' }]"
          />
          <van-cell title="事发时间" :value="formObj.eventDate" @click="showCalendar = true" is-link />
          <van-field
            v-model="formObj.title"
            name="title"
            label="事件级别"
            disabled
            input-align="right"
            placeholder="请输入签发人"
            :rules="[{ required: true, message: '请输入签发人' }]"
          />
          <van-field
            v-model="formObj.level"
            is-link
            name="picker"
            label="事件级别"
            placeholder="请选择事件级别"
            @click="showLevel = true"
          />
          <van-field v-model="formObj.mark" type="textarea" name="mark" label="信息摘要" placeholder="请输入信息摘要" />
          <div class="text">附件</div>
          <van-uploader v-model="formObj.fileList" multiple style="margin-top: 16px" />
        </van-cell-group>
        <div class="submit-btn">
          <van-button block type="primary" native-type="submit"> 确认上报 </van-button>
        </div>
      </van-form>
    </div>
    <van-calendar v-model:show="showCalendar" color="#1967f2" @confirm="onConfirmDate" />
    <van-popup v-model:show="showLevel" position="bottom">
      <van-picker :columns="levelList" @confirm="onConfirmLevel" @cancel="showLevel = false" />
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header
  }
})
export default class add extends Vue {
  formObj: any = {};
  eventtype: any = ''; // 事件信息类型
  showCalendar: boolean = false; // 展示日历
  showLevel: boolean = false; // 展示事件级别选择器
  levelList: any = [
    { text: '温州', code: 0 },
    { text: '温州', code: 0 },
    { text: '温州', code: 0 }
  ];
  mounted() {
    console.log('信息接报----》', this.$route.query);
    this.eventtype = this.$route.query.eventtype;
  }
  onSubmit() {}
  formatDate(date) {
    return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
  }
  // 选择日期
  onConfirmDate(val) {
    this.showCalendar = false;
    this.formObj.eventDate = this.formatDate(val);
  }
  // 选择事件级别
  onConfirmLevel(val) {
    this.showLevel = false;
    this.formObj.level = val;
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    /deep/.van-form {
      display: flex;
      flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
          border-bottom: 1px solid #cecece;
          &:nth-child(3) {
            flex: 1;
            flex-direction: column;
            border: none;
            .van-cell__title {
              width: 100%;
              padding-bottom: 12px;
              border-bottom: 1px solid #cecece;
            }
            .van-cell__value {
              padding-top: 12px;
              .van-field__body {
                height: calc(100% - 25px);
                .van-field__control {
                  height: 100%;
                }
              }
            }
          }
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
      }
    }
  }
  /deep/.van-field__error-message {
    text-align: right;
  }
}
</style>
