<template>
  <div class="affiche">
    <Header title="群消息">
      <template slot="oper">
        <div class="msg" @click="openMsg">
          <van-icon name="chat-o" />
        </div>
      </template>
    </Header>
    <div class="affiche-content">
      <div class="search_div">
        <van-search
          v-model="keyword"
          @search="onSearch"
          @cancel="onCancel"
          input-align="center"
          placeholder="请输入搜索关键词"
          :maxlength="20"
        />
      </div>
      <div class="affiche-content-today">
        <!-- <span class="time-range">今天</span> -->
        <ul class="list">
          <template v-if="noticeList.length">
            <li v-for="(item, index) in noticeList" :key="index" @click="goDetailPage(item)">
              <span class="time">{{ item.replyDate }}</span>
              <span class="content">{{ item.replyText }}</span>
              <div class="contact">
                <van-image width="15px" height="15px" fit="contain" :src="require(`@/assets/images/quickResponse/ico-org.png`)" />
                <span class="title">{{ item.orgName }}</span>
                <!-- <span class="name">张凤霞</span>
              <span class="phone">13148766777</span> -->
              </div>
            </li>
          </template>
          <template v-else>
            <div class="data-error">没有更多了</div>
          </template>
        </ul>
      </div>
    </div>
    <van-popup
      v-model="show"
      position="bottom"
      :close-on-click-overlay="false"
      @click-overlay="show = false"
      @click-close-icon="show = false"
      height="66%"
    >
      <van-form>
        <van-field
          required
          v-model="replyText"
          name="内容:"
          label="内容:"
          placeholder="内容:"
          type="textarea"
          :rules="[{ required: true, message: '请填写内容' }]"
        />
        <van-field name="uploader" label="文件上传">
          <template #input>
            <van-uploader v-model="uploader" accept="image/*,video/*" :after-read="afterRead" />
          </template>
        </van-field>
      </van-form>
      <div style="margin: 16px">
        <van-button round block type="info" @click="onSubmit">提交</van-button>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
import { Notify, ImagePreview, Toast } from 'vant';
import FileUpload from '@/components/fileUpload.vue';
import apiServer from '@/api/request-service';

@Component({
  components: {
    Header,
    FileUpload
  }
})
export default class noticeList extends Vue {
  public noticeList: any = [];
  private keyword = '';
  private show = false;
  private onSearch() {
    this.getReplyList();
  }

  private replyText = '';
  private uploader = [];

  private onSubmit() {
    let files: any = [];
    this.uploader.forEach((el) => {
      files.push(el.attachmentList);
    });
    taskRequest.noticeSave(
      {
        eventId: this.$route.query.eventId,
        replyText: this.replyText,
        attachmentList: files
      },
      (res) => {
        if (res.data.status === 200) {
          this.$toast('回复成功');
          this.getReplyList();
          this.replyText = '';
          this.uploader =[];
          this.show = false
        } else {
          this.$toast(res.data.message);
        }
      }
    );
  }
  afterRead(file) {
    // 此时可以自行将文件上传至服务器
    console.log('afterRead----------->', file);
    let _this = this;
    file.status = 'uploading';
    file.message = '上传中...';
    const formData = new FormData();
    formData.append('file', file.file);
    apiServer.uploadFile(formData, function (_resultObj) {
      if (_resultObj.data.status == 200) {
        file.status = 'done';
        file.message = '上传完成';
        file.attachmentList = _resultObj.data;
        // _this.feedbackObj.attachmentList.push(_resultObj);
        console.log(_this.uploader);
      }
    });
  }

  onCancel() {}

  private openMsg() {
    this.show = true;
  }

  submitFile(file, flag?) {}

  mounted() {
    console.log('群发公告--------》', this.$route);
    this.getReplyList();
  }
  // 获取全部回复数据
  private getReplyList() {
    let params = {
      eventId: this.$route.query.eventId,
      teamId: this.$route.query.teamId,
      taskId: '',
      replyTypeCode: '6', // 回复类型编码，为空默认为一般回复
      requestFrom: 'app',
      keywords: this.keyword
    };
    taskRequest.getAllReply(params, (res) => {
      console.log('++++getAllReply---->', res);
      if (res.data.status === 200) {
        this.noticeList = res.data.data || [];
      } else {
        Notify({ type: 'danger', message: res.data.msg || '获取回复数据失败' });
      }
    });
  }

  private goDetailPage(item) {
    this.$store.commit('NOTICEDETAIL', item);
    this.$router.push({ path: '/noticeListDetail' });
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.affiche {
  background: #f5f6f6;
  span {
    display: inline-block;
  }
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 0 11px;
    .time-range {
      display: inline-block;
      width: 100%;
      // padding: 10px 0;
      padding-top: 10px;
      text-align: center;
      color: #b2b4b5;
    }
    .list {
      li {
        display: flex;
        flex-direction: column;
        padding: 10px;
        background: #fff;
        border-radius: 2px;
        margin-top: 10px;
        .time {
          display: flex;
          align-items: center;
          margin-bottom: 9px;
          color: #a6a8a9;
          &::before {
            content: '';
            display: inline-block;
            width: 38px;
            height: 20px;
            margin-left: -13px;
            margin-right: 7px;
            background: url('@{url}/quickResponse/ico-message.png') center no-repeat;
            background-size: cover;
          }
        }
        .content {
          margin-bottom: 13px;
          color: #1c1d1d;
          font-weight: bold;
          line-height: 28px;
        }
        .contact {
          display: flex;
          align-items: center;
          border-top: 1px solid #eae8eb;
          padding-top: 11px;
          color: #1c1d1d;
          .title {
            margin-left: 6px;
            margin-right: 10px;
          }
          .name {
            margin-right: 14px;
          }
        }
      }
    }
  }
  .data-error {
    color: #969799;
    font-size: 3.73333vw;
    line-height: 13.33333vw;
    text-align: center;
  }
}
.msg {
  position: absolute;
  right: 20px;
  top: 8px;
  font-size: 24px;
  color: white;
  z-index: 99;
}

.upload-main {
  padding: 10px 20px;
  .upload-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .upload-title {
      font-weight: 600;
      color: #666;
      font-size: 16px;
    }
    .upload-sendFile {
      display: flex;
      align-items: center;
      .sureBtn {
        color: #326fff;
        font-size: 16px;
      }
    }
  }
}
</style>