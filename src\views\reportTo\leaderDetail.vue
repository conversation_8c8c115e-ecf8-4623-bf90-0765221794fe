<template>
  <div class="list">
    <Header title="批示信息"></Header>
    <div class="list-card">
      <van-cell-group inset>
        <van-cell v-for="ele in listData" :key="ele.prop" :value="ele.type !== 'label' ? ele.value : null" :label="ele.type === 'label'? ele.value : null">
          <template #title>
            <van-icon :name="ele.icon" :color="ele.color" style="margin-right: 10px" />
            <span>{{ele.name}}</span>
          </template>
          <template #right-icon v-if="ele.type === 'phone' && ele.value">
            <van-icon name="phone-circle" class="phone-icon" color="#13b2a1" @click="goToTelPage(ele.value)" />
          </template>
          <!-- <template #label v-if="ele.prop.includes('List') && ele.fileType">
            <div class="list-casualties" v-if="(ele.prop.includes('List')) && ele.label">
              <div class="attach_area" v-for="item,index in ele.label.attachmentList" :key="index" @click="downLoad(item, ele.fileType)">
                <span><img src="../../assets/images/ppt.png" alt="" srcset=""></span>
                <span>{{item.accessoryname || item.journalName}}</span>
              </div>
            </div>
          </template> -->
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import axios from 'axios';
import { downloadReport } from '@/utils/validate';

@Component({
  components: {
    Header
  }
})

export default class leaderDetail extends Vue {

   //  reviewDate: item.reviewDate,
      //   content:item.content,
      //   remarks:item.remarks,
      //   reviewEventName:item.reviewEventName,
      //   reviewLeaderName:item.reviewLeaderName
  private infoList: any = [
     {
      name: '批示事件',
      prop: 'reviewEventName',
      value: '暂无信息',
      color: '#b5dbe8',
      icon: 'peer-pay',
      // type: 'label',
    },
    {
      name: '批示领导',
      prop: 'reviewLeaderName',
      value: '',
      color: '#c8e3ee',
      icon: 'manager-o',
    },
    // {
    //   name: '批示类型',
    //   prop: 'limtypeName',
    //   value: '',
    //   color: '#eed49b',
    //   icon: 'coupon-o',
    // },
    // {
    //   name: '批示文电',
    //   prop: 'file',
    //   value: '暂无信息',
    //   color: '#e4bfa0',
    //   icon: 'newspaper-o',
    //   type: 'label',
    //   label: [
    //     {
    //       name: 'xxxx文电1'
    //     },
    //     {
    //       name: 'xxxx文电2'
    //     },
    //   ]
    // },
    {
      name: '批示时间',
      prop: 'reviewDate',
      value: '',
      color: '#b5dbe8',
      icon: 'clock-o',
    },
   
    {
      name: '批示内容',
      prop: 'content',
      value: '暂无信息',
      color: '#eed49b',
      icon: 'orders-o',
      type: 'label',
    },
    {
      name: '备注',
      prop: 'remarks',
      value: '暂无信息',
      color: '#e4bfa0',
      icon: 'newspaper-o',
      type: 'label',
    },
    // {
    //   name: '附件',
    //   prop: 'attachList',
    //   value: '',
    //   color: '#b5dbe8',
    //   icon: 'photo-o',
    //   fileType: 'ldps',
    // },
    // {
    //   name: '批示文电',
    //   prop: 'receiveJournalList',
    //   value: '',
    //   color: '#b5dbe8',
    //   icon: 'photo-o',
    //   fileType: 'kwls',
    // },
    // {
    //   name: '已导文电',
    //   prop: 'journalList',
    //   value: '',
    //   color: '#b5dbe8',
    //   icon: 'photo-o',
    //   fileType: 'kwls',
    // },
    // {
    //   name: '审阅意见',
    //   prop: 'read',
    //   value: '暂无信息',
    //   color: '#b5dbe8',
    //   icon: 'coupon-o',
    //   type: 'label',
    // },
  ];

  private feedBackInfo: any = [
    {
      name: '标题',
      prop: 'title',
      value: '',
      color: '#c8e3ee',
      icon: 'coupon-o',
      type: 'label'
    },
    {
      name: '报送单位',
      prop: 'reportunit',
      value: '',
      color: '#eed49b',
      icon: 'peer-pay',
    },
    // {
    //   name: '办理文电',
    //   prop: 'file',
    //   value: '暂无信息',
    //   color: '#e4bfa0',
    //   icon: 'newspaper-o',
    //   type: 'label',
    //   label: [
    //     {
    //       name: 'xxxx文电1'
    //     },
    //     {
    //       name: 'xxxx文电2'
    //     },
    //   ]
    // },
    {
      name: '阅读人',
      prop: 'readingperson',
      value: '',
      color: '#b5dbe8',
      icon: 'manager-o',
    },
    {
      name: '编辑人',
      prop: 'editName',
      value: '',
      color: '#b5dbe8',
      icon: 'manager-o',
    },
    {
      name: '编辑人电话',
      prop: 'editortel',
      value: '',
      color: '#eed49b',
      icon: 'phone-o',
      type: 'phone'
    },
    {
      name: '审核人',
      prop: 'readername',
      value: '',
      color: '#e4bfa0',
      icon: 'manager-o',
    },
    {
      name: '签发人',
      prop: 'issuername',
      value: '',
      color: '#b5dbe8',
      icon: 'manager-o',
    },
    {
      name: '报送时间',
      prop: 'reporttimeStr',
      value: '',
      color: '#b5dbe8',
      icon: 'clock-o',
    },
    {
      name: '办理日期',
      prop: 'updatetimeStr',
      value: '',
      color: '#b5dbe8',
      icon: 'clock-o',
    },
    {
      name: '摘要',
      prop: 'dealabstract',
      value: '暂无信息',
      color: '#eed49b',
      icon: 'newspaper-o',
      type: 'label'
    },
    {
      name: '正文内容',
      prop: 'bodymatter',
      value: '暂无信息',
      color: '#eed49b',
      icon: 'newspaper-o',
      type: 'label'
    },
    {
      name: '附件',
      prop: 'attachList',
      value: '',
      color: '#eed49b',
      icon: 'photo-o',
      fileType: 'ldps',
    },
    {
      name: '办理文电',
      prop: 'receiveJournalList',
      value: '',
      color: '#b5dbe8',
      icon: 'photo-o',
      fileType: 'kwls',
    },
    {
      name: '导入文电',
      prop: 'journalList',
      value: '',
      color: '#b5dbe8',
      icon: 'photo-o',
      fileType: 'kwls',
    },
  ];

  private deptLeader: any = [
    {
      name: '标题',
      prop: 'docTitle',
      value: '',
      color: '#c8e3ee',
      icon: 'coupon-o',
    },
    {
      name: '发布单位',
      prop: 'areaName',
      value: '',
      color: '#eed49b',
      icon: 'peer-pay',
    },
    {
      name: '公文类型',
      prop: 'docType',
      value: '',
      color: '#eed49b',
      icon: 'coupon-o',
    },
    {
      name: '发文时间',
      prop: 'publishTime',
      value: '',
      color: '#b5dbe8',
      icon: 'clock-o',
    },
    {
      name: '附件',
      prop: 'attachList',
      value: '',
      color: '#eed49b',
      icon: 'photo-o',
      fileType: 'bjldps',
    },
  ];

  private listData: any = [];

  mounted() {
    // const id = this.$route.query.id;
    // let requestType = this.$route.query.infotype;
    // this.getDetail(id, requestType);

     
    debugger
    console.log(this.$route.query,"aaaa")
    


   
    this.listData = this.getData(this.infoList,this.$route.query)

    console.log(this.listData,"bbbb")
    
    // this.listData = [
    //   {
    //     leadername:"111"
    //   }
    // ]
  }

  // getDetail(id, requestType) {
  //   if (requestType === 'leader') {
  //     this['$api'].InfoRequest.getLeaderDetail({approveId: id}, res => {
  //       this.listData = this.getData(this.infoList, JSON.parse(res.data.data));
  //     })
  //   } else if (requestType === 'feedback') {
  //     this['$api'].InfoRequest.getAppDealDetail({id: id}, res => {
  //       this.listData = this.getData(this.feedBackInfo, JSON.parse(res.data.data));
  //     })
  //   } else if (requestType === 'dleader') {
  //     this['$api'].InfoRequest.getDeptLeaderDetail({id: id}, res => {
  //       this.listData = this.getData(this.deptLeader, JSON.parse(res.data.data));
  //     })
  //   }
  // }

  getData(arr,data) {
    // const propList = ['attachList', 'journalList', 'receiveJournalList']
    // data.receiveJournalList = [data.receiveJournal]
    // arr.forEach((item: any) => {
    //   item.value = data[item.prop];
    //   if (propList.includes(item.prop)) {
    //     item.value = null;
    //     item.label = {
    //       attachmentList: data[item.prop],
    //       showText: false,
    //     }
    //   }
    // })

    arr.forEach(item=>{
       item.value = data[item.prop];
    })
    return arr;
  }

  //下载
  downLoad(item, fileType) {
    const params: any = {
      fileId: item.attachid || item.journalId,
      fileName: item.accessoryname ||item.journalName,
      fileType
    }
    this['$api'].InfoRequest.downLoad(params, res => {
      downloadReport(res)
    })
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`
  }
}
</script>

<style lang="less" scoped>
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-card {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    .van-cell-group {
      display: flex;
      flex-direction: column;
      margin: 12px 10px;
      padding: 0 10px;
      .van-cell {
        padding: 12px 0;
      }
    }
  }
  .attach_area {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
  }
  .list-casualties {
    overflow: auto;
    max-height: 280px;
  }
}
</style>
<style lang="less">
.phone-icon {
  margin: 5px 0 0 5px;
}
</style>