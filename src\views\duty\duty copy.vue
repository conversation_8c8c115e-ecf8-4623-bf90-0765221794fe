<template>
  <div class="div_big">
    <van-nav-bar title="值班安排" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
     <van-icon name="ellipsis" size="25" /> 
  </template>
   </van-nav-bar>
    <!-- 主体 -->
  <div class="main_box">
    <!-- <span class="Go_back" @click="GoBack">
      <van-icon size="18" style="vertical-align: middle;margin-top: -3px;padding-right: 3px;" name="arrow-left" />返回
    </span> -->
    <div class="time_show_div" @click="monthRange()">
      <p>{{dataStr}}</p>
      <span>
        <img src="../../assets/images/icons/calendar.png" alt="" srcset="">
      </span>
    </div>
    <!-- 日历 -->
    <div class="datePicker" @touchstart="moveClick" @touchmove="moveEv" @touchend="moveEnd">
      <Calendar
        :now="false"
        :responsive="false"
        lunar
        clean
        @select="selected"
        @selectYear="selectYeared"
        @selectMonth="selectMonthed"
        @prev="preved"
        @next="nexted"
        :multi="false"
        :tileContent="tileContent"
        ref="calendar"
      />
    </div>
    <!-- 值班内容 -->
    <div class="duty_box">
      <div >
        <p><span>今日值班：</span><span>{{selected_date}}{{selected_date_other}}</span> </p>
        <!-- <ul class="duty_singgle">
         
          <li>带班领导：
             <span >张三&nbsp;&nbsp;</span>
             <span>13120332520&nbsp;&nbsp;</span>
             <span><img src="../../assets/images/icons/sms.png" alt="" srcset=""></span>  
             <span><img src="../../assets/images/icons/callphone.png" alt="" srcset=""></span> 
             
          </li>
          <li>值班员： <span>张三&nbsp;&nbsp;</span><span>13120332520&nbsp;&nbsp;</span> 
          <span><img src="../../assets/images/icons/sms.png" alt="" srcset=""></span>  
            <span><img src="../../assets/images/icons/callphone.png" alt="" srcset=""></span> 
             
           </li>
        </ul> -->
        <ul class="duty_singgle" v-for="(item,index) in listArr" :key="index">
           <li v-if="item.personName&&item.personPhone">
             <div class="duty_person_info">
                {{item?item.groupName :""}}：
                <span >{{item?item.personName:""}}&nbsp;&nbsp;</span>
                <span>{{item?item.personPhone:""}}&nbsp;&nbsp;</span>
             </div>
            <span v-if="item.personPhone" @click="theAndroidH(0,item.personPhone)"><img src="../../assets/images/icons/callphone.png" alt="" srcset=""></span> 
             <span v-if="item.personPhone" @click="theAndroidH(1,item.personPhone)"><img src="../../assets/images/icons/sms.png" alt="" srcset=""></span>  
             
            </li>
        </ul>
        <ul class="duty_singgle" v-if="listArr.length>0">
          <li>
            <div class="duty_person_info_empty_title">
              值班电话：
             </div>
             <div class="duty_person_info_empty_content">
               <span>{{orgObj.dutyTel}}</span> 
             </div>
          </li>
        </ul>
        <ul class="duty_singgle" v-if="listArr.length>0">
          <li>
            <div class="duty_person_info_empty">
                传真：<span>{{orgObj.fax}}</span> 
             </div>
          </li>
        </ul>
      </div>
      <!-- 无数据状态 -->
      <div v-show="listArr.length == 0" class="list_item_empty2"></div>
    </div>
  </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Calendar from '../mpvueCalendar/mpvue-calendar.vue';
import '../mpvueCalendar/browser-style.css';
import apiServer from '../../api/request-service';
@Component({
  name: 'dynamic',
  components: { 
    Calendar
  }
})
export default class dynamic extends Vue {
  private value = '';
  //DOM切换
  private dataList = '';
  private openSimple = false;
  private org_code = this.$store.state.userInfo.orgCode; //组织机构
  private listArr = [];
  private isCreate =true;
  private allDuty = [];//有值班信息的集合
  private dataStr ='';
  private orgObj={};//组织机构信息
  private tileContent = [
      // {date: '2020-6-22', className: 'workday', content: ' '},
      // {date: '2020-6-10', className: 'workday', content: ' '},
      // {date: '2020-6-24', className: 'workday', content: ' '}
  ];
  //时间选择器
  private year = '';
  private mounth = '';
  private year_mounth = '';
  private selected_date="";
  private selected_date_other="";
  //组织选择期
  private orgList = [];
  private pageXI = 0;
	private pageYI = 0;
	private swcount = 0;
  private defaultProps = {
    children: 'children',
    label: 'org_name'
  };
  private baseurl = '';
  setDateTitle(datastr){
    let _this=this;
    let dateArr=datastr.split("-");
    if(dateArr.length==2){
      _this.dataStr=dateArr[0]+"年"+dateArr[1]+"月"
    }else{
      _this.dataStr=dateArr[0]+"年"+dateArr[1]+"月"+dateArr[1]+"日"
    }
    
  }
  //点击日期
  private selected(val, val2) {
    console.log(val2);
    let mounth = val[1];
    if(mounth < 9){
      mounth = '0' + mounth
    }
    let day = val[2];
    if(day <9){
      day = '0' + day
    }
    // this.value = val2.date;
    let  value = val[0] + '-' + mounth + '-' + day;
    //点击页面日历显示详情
    this.showInfo(value);
  }
  showInfo(value){
    let _date=new Date(value);
    console.log(_date)
    let weekindex=_date.getDay();
    this.selected_date=value;
    this.selected_date_other="星期"+this.$refs.calendar['weeks'][weekindex]
    //交互
    // this.dynamic_list();
    this.listArr = [];
    try {
      this.allDuty.forEach(element => {
        if(value == element.date){
          this.listArr = element.dutyArray;
          console.log(this.listArr);
          throw new Error("break")
        }
      });
    } catch (error) {
      console.log(error)
    }
  }
  private monthRange(){
    let _this=this;
    //debugger
    _this.$refs.calendar['changeYear']()
  }
  //点击年份
  private selectYeared(y) {
    this.year_mounth = y + '-' + this.mounth;
    this.mounthnamic_list();
  }
  //点击月份
  private selectMonthed(m, y) {
    if(m.toString().length==1){
      m="0"+m;
    }
    this.year_mounth = y + '-' + m;
    this.mounthnamic_list();
  }
  //上一月
  private preved(y, m, W) {
    if(m.toString().length==1){
      m="0"+m;
    }
    this.year_mounth = y + '-' + m;
    this.mounthnamic_list();
  }
  //下一月
  private nexted(y, m, W) {
    if(m.toString().length==1){
      m="0"+m;
    }
    this.year_mounth = y + '-' + m;
    this.mounthnamic_list();
  }
  private openOrg() {
    this.openSimple = true;
  }
  private handleNodeClick(data) {
    //组织机构
    this.openSimple = false;
    console.log(data);
    this.org_code = data.emorgid;
    //交互
  }
  private getNowYearAndMounth() {
    let _this=this;
    var myDate = new Date();
    var tYear = myDate.getFullYear();
    var tMonth = myDate.getMonth();
    var m = JSON.stringify(tMonth + 1);
    if (m.toString().length == 1) {
        m = "0" + m;
    }
    this.year_mounth =  tYear + '-' + m;
    //console.log("fff"+this.year_mounth)
    _this.mounthnamic_list();
  }
  private dataHandle(_dataArr){
    let _this=this;
    _this.allDuty =[];//清空当月值班信息
    //处理后台接口返回
    if(_dataArr&&_dataArr.length>0){
      _dataArr.forEach((item,index) => {
        if(item.dataFlag=="1"){
          _this.allDuty.push(item);
          let dateStr= this.$formatTimeToZero(item.date,1)
          let obj={date: dateStr, className: 'workday', content: ' '}
          _this.tileContent.push(obj)
        }
      });
      if(_this.isCreate){
        let myDate = new Date();
        let _days= myDate.getDate().toString();
        if (_days.length == 1) {
            _days = "0" + _days;
        }
        _this.showInfo(_this.year_mounth+"-"+_days)
        _this.isCreate=false;
      }
    }
  }
  //获取组织机构
  private getAllOrg() {
    let that = this;
    let parma = {
      orgCode: '',
      updateTime: '',
      userId: ''
    };
    
  }
  theAndroidH(_type,_phoneNum){
    let _this=this;
    console.log("_type==>"+_type)
    if(_type==0){
       _this['$gsmdp'].makePhoneCall({
        phoneNumber:_phoneNum
      })
    }else if(_type==1){
      _this['$gsmdp'].sendMessage({
        data: {
          phoneNumber: _phoneNum,
          message: '',  //仅为示例，并非真实的短信内容
          sms: false
        }
      })
    }
  }
  GoBack(){
    this.$router.go(-1);
    // let _this=this;
    // try {
    //     _this['$gsmdp'].goBack(function(){
    //       console.log("fanhui")
    //     })
    // } catch (error) {
    //     console.log("close error")
    // }
  }
  private filterArray(data, id) {
    //组织机构的递归
    var fa = function(parentid) {
      var _array = [];
      for (var i = 0; i < data.length; i++) {
        var n = data[i];
        if (n.parentcode === parentid) {
          n.children = fa(n.emorgid);
          _array.push(n);
        }
      }
      return _array;
    };
    return fa(id);
  }
  private clearTree(obj) {
    this.openSimple = false;
    this.org_code = '';
    //交互
  }
  private mounthnamic_list() {
    let _this = this;
    let param={
      date:_this.year_mounth,
      orgCode:_this.$store.state.userInfo.orgcode,
    }
    console.log(param);
    apiServer.findDutyInfoByMonth(param,function(res){
      let dataArr=res.data.data;
      _this.dataList=dataArr;
      _this.dataHandle(dataArr)
    })
   this.setDateTitle(this.year_mounth)
  }
  //取月份数据遍历方法
  private sort(data) {}
  moveEv(e){
    this.swcount++;
    if(this.swcount>5){
      let pageY=e.changedTouches[0].pageY;
      //console.log("yvalue=>"+(pageY-this.pageYI)*4)
      let pageX=e.changedTouches[0].pageX;
    }
  }
  moveEnd(e){
    let _this=this;
    let pageX=e.changedTouches[0].pageX;
    console.log(pageX+"===>"+this.pageXI)
    /*左右滑*/
    if(pageX+100<this.pageXI){
        console.log("进入右滑")
        //this.NextMonth();
        _this.$refs.calendar['next']()
        this.swcount=0;
    }else if(pageX>this.pageXI+100){
        console.log("进入左滑")
         _this.$refs.calendar['prev']()
        //this.PreMonth();
        this.swcount=0;
    }
  }
  moveClick(e){
    this.swcount=0;
    console.log("moveClick==>"+e.changedTouches[0].pageX)
    this.pageXI=e.changedTouches[0].pageX;
    this.pageYI=e.changedTouches[0].pageY;
  }
  requestOrg(){
    let _this=this;
    let params={
      orgId:_this.$store.state.userInfo.orgcode,
    }
    //orgObj
    apiServer.findOrgObByCode(params,function(res){
      console.log("org info==>",res)
      _this.orgObj=res.data.data;
      console.log(_this.orgObj)
    })
  }
  private created() {
    let _this = this;
    _this.$enJsBack();
    // this.getAllOrg();
    _this.requestOrg();
    _this.getNowYearAndMounth();
    // setTimeout(function() {
    //   _this.mounthnamic_list();
    // }, 100);
  }
  mounted(){
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      alert("ffffffffffffff")
      console.log("调用返回")
      _this.GoBack()
    })

  }
}
</script>
<style scoped lang="scss">
.div_big {
  [class*=van-hairline]::after{
        border: none;
  }
  // height: 667px;
  .main_box {
    height: calc( 100% );
    overflow: auto;
    margin: 0;
    background: white;
    
    .time_show_div{
      width: 100%;
      height: 8vh;
      background: #1541e2;
      color: white;
      position: relative;
      p{
        margin: 0;
        font-size: 20px;
        height: 100%;
        line-height: 8vh;
        text-align: center;
      }
      span{
        width: 8vw;
        height: 8vw;
        position: absolute;
        top: 2.66667vw;
        right: 15px;
        img{
          width: 30px;
          height: 30px;
        }
      }
    }
  }
  .duty_box {
    width: 100%;
    p{
      padding: 5px 10px;
      span:nth-of-type(1){
        font-size: 18px;
      }

    }
    .duty_div {
      width: 100%;
      background: #fff;
      padding: 8px 20px;
      margin: 8px 0;
      font-size: 15px;
      .time { 
        height: 40px;
        line-height: 40px;
        font-weight: bold;
      }
      .duty_ul {
        padding: 5px 0;
        border-top: 1px solid #ddd;
        li {
          height: 25px;
          line-height: 25px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .duty_singgle {
      width: 100%;
      background: #fff;
      padding: 2px 20px;
      margin: 0;
      li {
        height: 30px;
        line-height: 30px;
        font-size: 17px;
        margin-bottom: 5px;
        font-family: "微软雅黑";
        
        .duty_person_info{
          width: 82%;
          float: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .duty_person_info_empty{
          width: 92%;
          float: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .duty_person_info_empty_title{
          width: 26%;
          float: left;
        }
        .duty_person_info_empty_content{
          width: 70%;
          float: left;
        }
        span{
          color:#4277BF;
          display: inline-block;
        }
        >span:nth-of-type(1){
          width: 25px;
          float: left;
          
        }
        >span:nth-of-type(2){
          width: 25px;
           margin-left: 5px;
        }
        img{
          width: 25px;
          vertical-align: middle;
          margin-top: -4px;
        }
      }
    }
  }
}
</style>