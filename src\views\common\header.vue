<template>
  <div class="common-header">
    <van-nav-bar v-if="hiddenBack" :title="title">
      <template #right v-if="showCode">
        <van-icon name="search" size="18" @click="search" style="margin-right: 20px" />
        <van-icon name="apps-o" size="18" @click.stop="showCodePage" />
      </template>
    </van-nav-bar>
    <van-nav-bar
      v-else
      :title="title"
      :left-text="leftText"
      :right-text="rightText"
      left-arrow
      @click-left="onClickLeft"
      @click-right="onClickRight"
    >
      <template #right v-if="showCode">
        <van-icon name="search" size="18" @click="search" style="margin-right: 20px" />
        <van-icon @click.stop="showCodePage" name="apps-o" size="18" />
      </template>
    </van-nav-bar>
    <slot name="oper"></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
@Component
export default class Header extends Vue {
  @Prop() public title: any;
  @Prop() public leftText: any;
  @Prop() public rightText: any;
  @Prop() public backPath: any;
  @Prop({ default: false }) public hiddenBack: boolean; // 是否隐藏返回按钮
  @Prop({ default: false }) public showCode: boolean; // 亮码操作按钮

  @Prop() public detailInfo;

  mounted() {
    console.log('+++++++++++', this.hiddenBack);
  }
  onClickLeft() {
    if (this.backPath) {
      this.$router.push(this.backPath);
    } else {
      history.back();
    }
  }
  onClickRight() {}
  private showCodePage() {
    this.$emit('showCodePage');
  }
  private search() {
    console.log(this.detailInfo, 'dddd');
    this.$router.push({
      path: '/searchDetail',
      query: {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId
      }
    });
  }
}
</script>

<style lang="less" scoped>
.common-header {
  background: #1967f2;
  /deep/.van-nav-bar {
    background: unset;
    .van-icon,
    .van-nav-bar__title {
      background: unset;
      color: #fff;
      font-size: 16px;
    }
  }
  [class*='van-hairline']::after {
    border: none;
  }
}
</style>
