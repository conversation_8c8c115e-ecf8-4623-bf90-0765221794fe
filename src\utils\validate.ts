
import utils from '@/utils/wx'
const wx = require('weixin-js-sdk');
/* eslint no-useless-escape:off */
// 邮箱验证
// https://stackoverflow.com/questions/46155/how-to-validate-email-address-in-javascript
export function validateByEmail(email: string) {
  // tslint:disable-next-line
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(email);
}

// 合法uri
export function validateURL(textval: string) {
  // tslint:disable-next-line
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return urlregex.test(textval);
}

// 小写字母
export function validateLowerCase(str: string) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

// 大写字母
export function validateUpperCase(str: string) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

// 大小写字母
export function validatAlphabets(str: string) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

export function getFileType(id) {
  let arr = [
    { type: "doc", application: "application/msword" },
    {
      type: "docx",
      application:
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    },
    { type: "dot", application: "application/msword" },
    {
      type: "dotx",
      application:
        "application/vnd.openxmlformats-officedocument.wordprocessingml.template"
    },
    { type: "xls", application: "application/vnd.ms-excel" },
    {
      type: "xlsx",
      application:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    },
    { type: "ppt", application: "application/vnd.ms-powerpoint" },
    {
      type: "pptx",
      application:
        "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    },
    { type: "pdf", application: "application/pdf" },
    { type: "txt", application: "text/plain" },
    { type: "gif", application: "image/gif" },
    { type: "jpeg", application: "image/jpeg" },
    { type: "jpg", application: "image/jpeg" },
    { type: "png", application: "image/png" },
    { type: "css", application: "text/css" },
    { type: "html", application: "text/html" },
    { type: "htm", application: "text/html" },
    { type: "xsl", application: "text/xml" },
    { type: "xml", application: "text/xml" },
    { type: "mpeg", application: "video/mpeg" },
    { type: "mpg", application: "video/mpeg" },
    { type: "avi", application: "video/x-msvideo" },
    { type: "movie", application: "video/x-sgi-movie" },
    { type: "bin", application: "application/octet-stream" },
    { type: "exe", application: "application/octet-stream" },
    { type: "so", application: "application/octet-stream" },
    { type: "dll", application: "application/octet-stream" },
    { type: "ai", application: "application/postscript" },
    { type: "dir", application: "application/x-director" },
    { type: "js", application: "application/x-javascript" },
    { type: "swf", application: "application/x-shockwave-flash" },
    { type: "xhtml", application: "application/xhtml+xml" },
    { type: "xht", application: "application/xhtml+xml" },
    { type: "zip", application: "application/zip" },
    { type: "mid", application: "audio/midi" },
    { type: "midi", application: "audio/midi" },
    { type: "mp3", application: "audio/mpeg" },
    { type: "rm", application: "audio/x-pn-realaudio" },
    { type: "rpm", application: "audio/x-pn-realaudio-plugin" },
    { type: "wav", application: "audio/x-wav" },
    { type: "bmp", application: "image/bmp" }
  ];
  return findArrayReturnValue(arr, "type", id, "application");
}

/**
 * @param arr 查找数组
 * @param property 查找属性
 * @param value 查找值
 * @des:查找到值后返回其值
 */
 export const findArrayReturnValue = (arr, property, value, findKey) => {
  let len, findValue;
  if (!Array.isArray(arr)) {
    return;
  }
  len = arr.length;
  for (var i = 0; i < len; i++) {
    if (
      arr[i].hasOwnProperty(property) &&
      arr[i][property] == value &&
      arr[i].hasOwnProperty(findKey)
    ) {
      findValue = arr[i][findKey];
      break;
    }
  }
  return findValue;
};

//新的下在模板文件
export const downloadReport = (obj) => {
  console.log(obj,'obk')
  if(obj==null) return false
  // 兼容ie的文件下载方法
  let flag = window.navigator.userAgent.indexOf('Trident') > -1 && window.navigator.userAgent.indexOf('rv:11.0') > -1
  if (flag) {
    (window.navigator as any).msSaveBlob(obj.blobStream, obj.filename)
    try {
    } catch (e) {
      console.log(e)
    }
  } else {
    // 谷歌的下载方法
    let type = obj.filename.split(".")[obj.filename.split(".").length - 1] || "";
    let blob = new Blob([obj], {type:getFileType(type)});
    // 获取heads中的filename文件名
    let downloadElement = document.createElement('a');
    downloadElement.href = URL.createObjectURL(blob) ;
    console.log(blob,'blob');
    if(utils.isWeiXin()){
      console.log('微信预览',URL.createObjectURL(blob),blob.size);
      // console.log(b)
      wx.previewFile({
        url:  URL.createObjectURL(blob), // 需要预览文件的地址(必填，可以使用相对路径)
        name: obj.filename, // 需要预览文件的文件名(不填的话取url的最后部分)
        size: blob.size, // 需要预览文件的字节大小(必填)
    });
    } else {
      downloadElement.target = '_blank'
      downloadElement.download = obj.filename
      document.body.appendChild(downloadElement)
      downloadElement.click()
      document.body.removeChild(downloadElement)
      window.URL.revokeObjectURL(obj.url) // 释放掉blob对象
    }
   
  }
}
