import Vue from 'vue';
import { v1 as uuidv1 } from 'uuid';
import sm3 from 'sm3';
import Axios from 'axios';
import { Notify } from 'vant';
// const wx = require('weixin-js-sdk');
import { Base64 } from 'js-base64';
import apiServer from '@/api/request-service';

let utils: any = {
  isWeiXin() {
    let ua = navigator.userAgent.toLowerCase();
    return !!ua.match(/micromessenger/i) && !!ua.match(/zwwx/i);
  },
  getsignature() {
    let timeStamp = String(parseInt((new Date().getTime() / 1000) as any));
    let appSecret = '06d6633b9b934ced89b3789392547f3c';
    let uid = uuidv1().replace(/-/g, '');
    console.log(uid);
    return {
      'jr-appkey': 'jr_zhxx_01',
      'jr-timestamp': timeStamp,
      'jr-nonce': uid,
      'jr-signature': sm3(timeStamp + appSecret + uid + timeStamp).toUpperCase()
    };
  },
  getCurrentUrl() {
    if (Base64.extendString) {
      Base64.extendString();
    }
    let url = Base64.encode(location.href.split('#')[0] + '');
    return url;
  },
  async getLocation() {
    return new Promise((resolve, reject) => {
      Axios.post(
        'http://hbyj.hbsis.gov.cn:5002/ga/admin/app/ga/admin/wechat/qrFourElements',
        {
          url: this.getCurrentUrl(),
          company: 'yjt',
          app: '1000016'
        },
        {
          headers: this.getsignature()
        }
      ).then((res: any) => {
        // console.log(res.data,'res')
        if (res.data.code == '200') {
          let { appId, timestamp, noncestr, signature } = res.data.data;
          this.getAddress(appId, timestamp, noncestr, signature)
            .then((res) => {
              resolve(res);
            })
            .catch((ex) => {
              reject(ex);
            });
        }
      });
    });
  },
  openAppNew(){
    return new Promise((resolve, reject) => {
      Axios.post(
        'http://hbyj.hbsis.gov.cn:5002/ga/admin/app/ga/admin/wechat/qrFourElements',
        {
          url: this.getCurrentUrl(),
          company: 'yjt',
          app: '1000016'
        },
        {
          headers: this.getsignature()
        }
      ).then((res: any) => {
        // console.log(res.data,'res')
        if (res.data.code == '200') {
          let { appId, timestamp, noncestr, signature } = res.data.data;
          this.getAppConfig(appId, timestamp, noncestr, signature)
            .then((res) => {
              resolve(res);
            })
            .catch((ex) => {
              reject(ex);
            });
        }
      });
    });
  },
  getAppConfig(appId, timestamp, nonceStr, signature) {
    return new Promise((resolve, reject) => {
      window['wx'].config({
        beta: true, // 必须这么写，否则 window['wx'].invoke 调用形式的 jsapi 会有问题
        debug: false,
        appId: appId, // 必填，企业微信的 corpID
        timestamp: Number(timestamp), // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，生成签名的随机串
        signature: signature, // 必填，签名，
        jsApiList: [
          'checkJsApi',
          'chooseImage',
          'previewImage',
          'getLocalImgData',
          'invoke',
          'uploadImage',
          'showWatermark', //设置水印接口
          'chooseVideo',
          'uploadVideo',
          'openLocation',
          'onVoicePlayEnd',
          'startRecordVoiceBuffer',
          'getLocation',
          'hideWatermark',
          'launch3rdApp'
        ]
      });

      window['wx'].ready(function (res) {
        window['wx'].invoke(
          'launch3rdApp',
          {
            appName: '融合通信',
            packageName: 'cn.vsx.vc',
            "param":"webview?excludeLauncherApp=true"
          },
          function (res) {
            if (res.err_msg != 'launch3rdApp:ok') {
              console.log(res,'1111');
            } else {
              console.log(res);
            }
          }
        );
      });
      window['wx'].error(function (res) {
        // if (wxCallback && Tools.isFunction(wxCallback)) {
        //     wxCallback(res);
        //   }
        console.log(res, '调用微信报错');
      });
    });
  },
  getAddress(appId, timestamp, nonceStr, signature) {
    return new Promise((resolve, reject) => {
      window['wx'].config({
        beta: true, // 必须这么写，否则 window['wx'].invoke 调用形式的 jsapi 会有问题
        debug: false,
        appId: appId, // 必填，企业微信的 corpID
        timestamp: Number(timestamp), // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，生成签名的随机串
        signature: signature, // 必填，签名，
        jsApiList: [
          'checkJsApi',
          'chooseImage',
          'previewImage',
          'getLocalImgData',
          'invoke',
          'uploadImage',
          'showWatermark', //设置水印接口
          'chooseVideo',
          'uploadVideo',
          'openLocation',
          'onVoicePlayEnd',
          'startRecordVoiceBuffer',
          'getLocation',
          'hideWatermark',
          'launch3rdApp'
        ]
      });

      window['wx'].ready(function (res) {
        window['wx'].invoke(
          'agentConfig',
          {
            agentid: '1000016', //必填，单位应用的 agentid
            corpid: appId, //必填，政务微信的 corpid
            timestamp: Number(timestamp), //必填，生成签名的时间戳,int类型,如 1539100800
            nonceStr, //必填，生成签名的随机串
            signature, //必填，签名，见附录 5
            jsApiList: ['openUserProfile', 'previewFile', 'getLocation','launch3rdApp'] // 必填
          },
          function (res) {
            if (res.err_msg != 'agentConfig:ok') {
              console.log(res);
            } else {
              console.log(res);
            }
          }
        );
        window['wx'].getLocation({
          type: 'gcj02',
          success: function (res) {
            var latitude = res.latitude; // 纬度，浮点数，范围为90 ~ -90
            var longitude = res.longitude; // 经度，浮点数，范围为180 ~ -180。
            var speed = res.speed; // 速度，以米/每秒计
            var accuracy = res.accuracy; // 位置精度
            resolve(res);
          },
          cancel: function (res) {
            Notify('查看附件机器，首先要授权位置信息');
          },
          fail: function (res) {
            console.info('ready getLocation fail=' + JSON.stringify(res));
            Notify('授权位置信息失败');
            return '2222fail';
          }
        });
      });
      window['wx'].error(function (res) {
        // if (wxCallback && Tools.isFunction(wxCallback)) {
        //     wxCallback(res);
        //   }
        console.log(res, '调用微信报错');
      });
    });
  },
  choiseVideo() {
    window['wx'].invoke(
      'chooseVideo',
      {
        sourceType: ['camera', 'album']
      },
      function (res) {
        var localIds = typeof res.localIds == 'object' ? res.localIds : JSON.parse(res.localIds); // 返回生成视频的本地ID，需要typeof判断兼容各个终端
        var thumbnail = res.thumbnail; // 视频封面缩略图
        // console.log('获取拍摄后的视频对象',res)
        console.log(localIds, 'localIds');
        localIds.forEach(function (el) {
          window['wx'].invoke(
            'uploadVideo',
            {
              localId: el, // 需要上传的视频的本地ID，由chooseVideo接口获得
              isShowProgressTips: 1 // 默认为1，显示进度提示
            },
            function (result) {
              // console.log('返回视频的服务器端ID',result)
              var serverId = result.serverId; // 返回视频的服务器端ID
              let url = 'http://hbyj.hbsis.gov.cn:5002/yjt/1000016/cgi-bin/media/get?media_id=' + serverId;
              Axios({
                method: 'get',
                url: url,
                responseType: 'blob'
              }).then((resData) => {
                console.log(resData.data, '获取临时素材文件');
                let urls = '';
                var file: any = new File([resData.data], uuidv1().slice(0, 10) + '.mp4', { type: 'video/mp4', lastModified: Date.now() });
                const formData: any = new FormData();
                let attachmentList = [];
                let type = file.type;
                file.status = 'start';
                file.timeStammp = new Date().getTime();
                formData.append('file', file);
                console.log(formData, 'formData');
                if ((window as any).createObjectURL != undefined) {
                  //basic
                  urls = (window as any).createObjectURL(file);
                } else if (window.URL != undefined) {
                  //mozilla(firefox)兼容火狐
                  urls = window.URL.createObjectURL(file);
                } else if (window.webkitURL != undefined) {
                  //webkit or chrome
                  urls = window.webkitURL.createObjectURL(file);
                }
                attachmentList.push({
                  url: urls,
                  name: type == '' ? file.name + '.mp4' : file.name,
                  status: 'pending',
                  timeStammp: file.timeStammp,
                  percentage: 0,
                  path: urls
                });

                window['vm'].messsageBus.emit('submitFile', {
                  attachmentList: attachmentList,
                  notUpload: true
                });

                apiServer.uploadFile(formData, function (_resultObj) {
                  attachmentList = [];
                  if (_resultObj.status == 200) {
                    file.status = 'done';
                    file.message = '上传完成';
                    file.attachmentList = _resultObj.data;

                    attachmentList.push({
                      ...file.attachmentList,
                      timeStammp: file.timeStammp
                    });
                    console.log(attachmentList, 'attachmentList');
                    window['vm'].messsageBus.emit('submitFile', {
                      attachmentList: attachmentList
                    });
                  }
                });
              });
            }
          );
        });
      }
    );
  },
 
  // preViewFile:{
  //   window['wx'].previewFile({
  //     url: '', // 需要预览文件的地址(必填，可以使用相对路径)
  //     name: '', // 需要预览文件的文件名(不填的话取url的最后部分)
  //     size: 1048576, // 需要预览文件的字节大小(必填)
  //     hidePreviewMenuList : [] // 要隐藏的菜单项，只能隐藏“传播类”和“保护类”按钮，所有menu项见附录3
  //     showPreviewMenuList : [] // 要显示的菜单项，所有menu项见附录3
  // });
  // }
};

export default utils;
