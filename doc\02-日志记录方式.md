# 日志记录方式

## 一、安装组件

```
npm install @gsafety/cad-glog --save  
```  

## 二、项目内全局初始化   
* `utils\logger.ts`内进行初始化：  
```  js
import { initLogger, LogOpts, InjectClsLog, InjectLog, logFactory, Logger } from '@gsafety/cad-glog';

export const initLog = function() {
  const logOpts: LogOpts = {
    logToElectron: true,   // 是否发送日志到Electron主进程
    logLevel: -1,   // 日志等级，对应jsnlog等级
    systemName: 'cad-log-test', // TODO edit 记录系统名称
    electronEventName: 'renderlog-electron-event' // TODO edit 发送到electron-main主进程的事件名称
  };
  initLogger(logOpts);
};

const gblog = logFactory.getLogger('violet-seed-logger');
export { logFactory, gblog, Logger, InjectClsLog, InjectLog };

```  

* vue启动时进行初始化调用  
``` js  
import { initLog, gblog } from './utils/logger';  

// 初始化日志
initLog();

```

## 三、使用约定

### 3.1 vue 内使用方式

通过注入的方式使用日志：

```js
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { InjectLog, Logger } from '../../utils/logger';

@Component()
export default class Table extends Vue {
  @InjectLog('TableVue') logger!: Logger;  // OR @InjectLog() logger!: Logger;  会自动使用class name命名loggername
  mounted() {
    this.logger.info('table vue mounted 123');
  }
}
</script>
```

### 3.2 ts 内使用方式

通过引入 logFactory 创建实例的方式使用

```js
import store from '@/store';
import { logFactory } from '../utils/logger';

const logger = logFactory.getLogger('table-service');
export default {
  getList() {
    logger.debug('test table-service log 1');
  }
};
```

### 3.3 全局使用方式

使用全局的`logname`, （其实任意地方都可以使用以下方式，只是没有对 logger 实例赋值对应的 loggerName，而是使用了一个全局指定的 name）：

```js
import { gblog } from './utils/logger';

gblog.info('hello logger'); // TODO  delete in project
```


### 3.4 日志使用规范 
日志内容格式：logger.info('方法名称或常量名称，操作 -> 值')

1、主线业务流程中方法入口、方法结束和重要时间点，如：接警保存动作中的获取表单信息完成后、保存动作之前、表单保存完成。
``` js
function saveIncidentAppeal(){
  logger.info('[saveIncidentAppeal], saveIncidentAppeal -- enter');

  var appealInfomaiton = XXXX;

  logger.info('[saveIncidentAppeal], saveIncidentAppealFunc -> start');
  const result = await saveIncidentAppealFunc(appealInfomaiton);
  logger.info('[saveIncidentAppeal], saveIncidentAppealFunc -< end, result:${result}');

  logger.info('[saveIncidentAppeal], saveIncidentAppeal -- leave');
}
```

2、watch
```js
@Watch('timeSheetBindInfosState', {deep: true})
handleTimeSheetBindInfos(newVal: TimeSheetBindInfo[]) {
  logger.debug('[handleTimeSheetBindInfos], store.timeSheetBindInfos <- newVal.count：${newVal.count()}');
  this.timeSheetBindInfos = [...newVal];
}

```

3、与electron通讯
``` js
    // 发送
sendMsgToElectron() {
    const n = 1;
    IpcRenderer.send(EventType.TEST, n);
    logger.info('[sendMsgToElectron], EventType.TEST -> n：${ n }');
  }

// 订阅electron主进程事件
created(){
    IpcRenderer.on(EventType.ONTEST).subscribe((r: any) => {
      if (r.args[0]) {
        logger.info('[created], EventType.TEST <- r.args[0]：${ r.args[0] }');
      }
    });
}
```

4、catch
```js
const readObject = (key: string) => {
  const text: any = read(key);
  let data: any;
  try {
    data = JSON.parse(text);
  } catch (error) {
    data = null;
    logger.error('[readObject], JSON.parse <- error：${ error }');
  }

  return data;
};
```

