import store from '@/store';

const levelMap: any = {
  // 去除0后，不同级别区划对应的编码长度
  0: [0], // 国家级
  1: [1, 2], // 省级
  2: [3, 4], // 市级
  3: [5, 6], // 县级
  4: [7, 8, 9], // 乡镇级
  5: [10, 11, 12] // 村级
};

const levelName: any = {
  // 去除0后，不同级别区划对应的编码长度
  0: '国', // 国家级
  1: '省', // 省级
  2: '市', // 市级
  3: '县', // 县级
  4: '乡镇', // 乡镇级
  5: '村' // 村级
};

export const telCall = (phone) => {
  //参数：电话 2名字  3职位 4、部门
  (store as any).state.eventModule.vsx.callPhone(phone);
};

export const layerSet = {
  district_county_polygon: {
    id: 'district_county_polygon',
    name: '县级区划面',
    styles: 'edss:polygon_style_hl',
    countyCount: {
      layers: 'edss:region_counties',
      zIndex: 6
    },
    townCount: {
      layers: 'edss:region_towns',
      zIndex: 4
    }
  },
  district_point_cun: {
    id: 'district_point_cun',
    name: '村庄点',
    key: 'cunCount'
  },
  district_county_polygon_hl: {
    id: 'district_county_polygon_hl',
    name: '区划面高亮'
  }
};
export const formatDateToM = (stamp: any) => {
  if (!stamp) {
    return '';
  }
  const date = new Date(stamp);
  const YY = date.getFullYear() + '-';
  const MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  const hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  const mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();

  return YY + MM + DD + ' ' + hh + mm;
};

export const formatDate = (stamp: any) => {
  if (!stamp) {
    return '';
  }
  const date = new Date(stamp);
  const YY = date.getFullYear() + '-';
  const MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
  const DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
  const hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
  const mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
  const ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  return YY + MM + DD + ' ' + hh + mm + ss;
};
export const downloadFile = (obj: any) => {
  // 兼容ie的文件下载方法
  const flag = window.navigator.userAgent.indexOf('Trident') > -1 && window.navigator.userAgent.indexOf('rv:11.0') > -1;
  if (flag) {
    (window.navigator as any).msSaveBlob(obj.blobStream, obj.filename);
  } else {
    // 谷歌的下载方法
    const downloadElement = document.createElement('a');
    downloadElement.href = obj.url;
    downloadElement.target = '_blank';
    downloadElement.download = obj.filename;
    document.body.appendChild(downloadElement);
    downloadElement.click();
    document.body.removeChild(downloadElement);
    window.URL.revokeObjectURL(obj.url); // 释放掉blob对象
  }
};
export default {
  /**
   * 元素的属性集合转换为对象
   * @param attributeSet
   */
  attributeSet2Object(attributeSet: any) {
    const obj: any = {};
    for (let i = 0; i < attributeSet.getCount(); i++) {
      const attribute = attributeSet.getItem(i);
      obj[attribute.name] = attribute.value;
    }
    return obj;
  },

  /**
   * 检查属性变化
   * @param oldInstance {Object}
   * @param newInstance {Object}
   * @param keys {Array} 属性名数组
   */
  detectChange(oldInstance: any, newInstance: any, keys: any): boolean {
    let changeCount = 0;
    if (oldInstance && newInstance && keys) {
      for (const key of keys) {
        const oldVal: any = oldInstance[key];
        const newVal: any = newInstance[key];
        if (oldVal === newVal) {
          continue;
        }
        if (Object.prototype.toString.call(oldVal) === '[object Object]' || Object.prototype.toString.call(oldVal) === '[object Array]') {
          if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
            changeCount++;
          }
        } else {
          changeCount++;
        }
      }
    }
    return changeCount > 0;
  },
  /**
   *
   * @param obj
   */
  toJSON(obj: any) {
    return JSON.parse(JSON.stringify(obj || {}));
  },
  /**
   *
   * @param code {string}
   */
  getDistrictLevelByCode(code: string) {
    let level = 0;
    const subcode = code.replace(/(0+)$/g, ''); // 区划代码去除结尾的0
    const len = subcode.length;
    for (const key in levelMap) {
      if (Object.prototype.hasOwnProperty.call(levelMap, key)) {
        const charlen = levelMap[key];
        if (charlen.includes(len)) {
          level = parseInt(key + '', 10);
        }
      }
    }
    return level;
  },
  /**
   * 获取区划有效匹配编码,解决有效区划编码末位为0的情况下，去0后的匹配bug：如370610去除0后会匹配多个区县
   * @param code {string}
   */
  getDistrictMatchCode(districtCode: string) {
    let code = districtCode.replace(/(0+)$/g, '');
    const len = code.length;
    switch (len) {
      case 1: // 省
        code = code + '0';
        break;
      case 3: // 市
        code = code + '0';
        break;
      case 5: // 县
        code = code + '0';
        break;
      case 7: // 乡镇
        code = code + '00';
        break;
      case 8: // 乡镇
        code = code + '0';
        break;
      case 10: // 村
        code = code + '00';
        break;
      case 11: // 村
        code = code + '0';
        break;
      default:
        break;
    }
    return code;
  },
  /**
   * 获取区划的父区划的有效数
   * @param districtCode {string}
   */
  getParentCodeByCode(districtCode: string) {
    const levelIndexArr: any = levelMap[this.getDistrictLevelByCode(districtCode)];
    const arr: any = districtCode.split('');
    const result = arr
      .map((e: any, i: any) => {
        if (levelIndexArr.includes(i + 1)) {
          e = '0';
        }
        return e;
      })
      .join('');
    return this.getDistrictMatchCode(result);
  },
  /**
   * 根据code获得级别名字
   * @param districtCode {string}
   */
  getLevelNameByCode(districtCode: string) {
    return levelName[this.getDistrictLevelByCode(districtCode)];
  },
  /**
   * 无法适配12位区划编码，慎用
   * @param code {string}
   */
  getDistrictLevelByCode2(code: string) {
    if (/^\d{0}000000$/.test(code)) {
      return 0; // 国家
    } else if (/^\d{2}0000$/.test(code)) {
      return 1; // 省
    } else if (/^\d{4}00$/.test(code)) {
      return 2; // 市
    } else if (/^\d{6}$/.test(code)) {
      return 3; // 县
    } else if (/^\d{9}000$/.test(code)) {
      return 4; // 乡镇
    } else if (/^\d{12}$/.test(code)) {
      return 5; // 村
    }
  },
  getMinDistrictCode(code: string) {
    const codeLenMap: any = {
      0: 6,
      1: 6,
      2: 6,
      3: 6,
      4: 9
    };
    code = code.replace(/(0+)$/g, '');
    const level = this.getDistrictLevelByCode(code);
    const len = codeLenMap[level];
    code = code.padEnd(len, '0');
    return code;
  },
  /**
   * 获取两点之间距离（入参经纬度，出参为米）
   * @param p1 点位1
   * @param p1.x 点位1 x
   * @param p1.y 点位1 y
   * @param p2
   * @param p2.x 点位1 x
   * @param p2.y 点位1 y
   * @returns
   */
  getDistance(p1: any, p2: any) {
    const r = 6371 * 1000;
    const x1 = (p1.x * Math.PI) / 180;
    const x2 = (p2.x * Math.PI) / 180;
    const y1 = (p1.y * Math.PI) / 180;
    const y2 = (p2.y * Math.PI) / 180;
    const dx = Math.abs(x1 - x2);
    const dy = Math.abs(y1 - y2);

    const h = this._HaverSin(dy) + Math.cos(y1) * Math.cos(y2) * this._HaverSin(dx);
    const distance: any = 2 * r * Math.asin(Math.sqrt(h));
    return distance;
    // const pnt1 = new mars3d.LngLatPoint(p1.x, p1.y)
    // const pnt2 = new mars3d.LngLatPoint(p2.x, p2.y)
    // return mars3d.MeasureUtil.getDistance([pnt1, pnt2])
  },
  // Haversine公式
  _HaverSin(theta: any) {
    const v = Math.sin(theta / 2);
    return v * v;
  },
  individalPhoneCall(no, name, type) {
    const url = 'individualCall-start';
    const paramMsg = {
      deviceNo: no,
      appId: 'vsx20200710'
    };
    const msgBody = JSON.stringify({
      path: url,
      param: paramMsg
    });
    debugger;

    store.state.eventModule.ws.send(msgBody);
  },
  individualCall(no, name, type) {
    const url = 'video-open';
    const paramMsg = {
      deviceNo: no,
      appId: 'vsx20200710'
    };
    const msgBody = JSON.stringify({
      path: url,
      param: paramMsg
    });
    store.state.eventModule.ws.send(msgBody);
  }
};

export const isImg = (name) => {
  const reg = /(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/;
  return reg.test(name);
};
export const isAudio = (name) => {
  const reg = /(.*)\.(wav|mp3)$/;
  return reg.test(name);
};
export const isVideo = (name) => {
  const reg = /(.*)\.(mp4|mov|MOV|wmv|flv|avi|avchd|webm|mkv)$/;
  return reg.test(name);
};
export const isText = (name) => {
  const reg = /(.*)\.(doc|docx|xlsx|xls|pdf|txt)$/;
  return reg.test(name);
};
export const getPath = () => {
  return process.env.NODE_ENV === 'development' ? 'https://59.208.147.80:8990' : window.location.origin;
};
export const getRoleInfo = () => {
  return JSON.parse(window.sessionStorage.getItem('role') || '{}');
};
export const showLegend = (show: boolean, code: string, store: any, isFuzzy = false) => {
  const list = store.state.legendModule.legendList;
  list.some((item) => {
    if (item.type === code) {
      item.onMap = show;
      return true;
    }
    let isReturn = false;
    (item?.children || []).forEach((child) => {
      if (isFuzzy) {
        if (child.type.includes(code)) {
          child.onMap = show;
          isReturn = true;
        }
      } else {
        if (child.type === code) {
          child.onMap = show;
          isReturn = true;
        }
      }
    });
    return isReturn;
  });
  store.commit('legendModule/SET_LEGENDLIST', list);
};

export function UrlDecode(zipStr: any) {
  var uzipStr = '';
  for (var i = 0; i < zipStr.length; i += 1) {
    var chr = zipStr.charAt(i);
    if (chr === '+') {
      uzipStr += ' ';
    } else if (chr === '%') {
      var asc = zipStr.substring(i + 1, i + 3);
      if (parseInt('0x' + asc) > 0x7f) {
        uzipStr += decodeURI('%' + asc.toString() + zipStr.substring(i + 3, i + 9).toString());
        i += 8;
      } else {
        uzipStr += AsciiToString(parseInt('0x' + asc));
        i += 2;
      }
    } else {
      uzipStr += chr;
    }
  }

  return uzipStr;
}

export function StringToAscii(str: any) {
  return str.charCodeAt(0).toString(16);
}

export function AsciiToString(asccode: any) {
  return String.fromCharCode(asccode);
}

export function setPosition(attr: 'left' | 'top', v, el) {
  el.style[attr] = autoUnit(v);
}

export function addEvent(el: any, event: any, handler: (e: any) => void) {
  if (!el) {
    return;
  }
  if (el.attachEvent) {
    el.attachEvent(`on${event}`, handler);
  } else if (el.addEventListener) {
    el.addEventListener(event, handler);
  } else {
    el[`on${event}`] = handler;
  }
}
// 事件解除绑定
export function removeEvent(el: any, event: any, handler: (e: any) => void) {
  if (!el) {
    return;
  }
  if (el.detachEvent) {
    el.detachEvent(`on${event}`, handler);
  } else if (el.removeEventListener) {
    el.removeEventListener(event, handler);
  } else {
    el[`on${event}`] = null;
  }
}

// 处理传入的单位问题
export function autoUnit(value: number | string) {
  if (typeof value === 'number' || (typeof value === 'string' && /^[0-9]*$/.test(value))) {
    return `${value}px`;
  } else {
    return value;
  }
}

// 事件绑定处理
export function bindMouseDrag(callback) {
  addEvent(document.documentElement, 'mousemove', handleMove);
  addEvent(document.documentElement, 'mouseup', handleUp);

  function handleMove(e) {
    e.preventDefault();
    e.stopPropagation();
    callback(e);
  }

  function handleUp(e: any) {
    e.preventDefault();
    e.stopPropagation();
    removeEvent(document.documentElement, 'mousemove', handleMove);
    removeEvent(document.documentElement, 'mouseup', handleUp);
  }
}

export const dragOption = {
  mounted(el, binding) {
    // 获取拖动的容器
    const container = document.querySelector(binding.value);
    // 设置目标元素基础属性
    el.style.cursor = 'move';
    // 监听鼠标在目标元素上按下
    addEvent(el, 'mousedown', (e) => {
      // 当前目标元素的left与top
      const left = container.offsetLeft;
      const top = container.offsetTop;
      // 保存按下的鼠标的X与Y
      const mouseX = e.clientX;
      const mouseY = e.clientY;

      // 监听鼠标移动
      addEvent(document.documentElement, 'mousemove', handleMove);
      addEvent(document.documentElement, 'mouseup', handleUp);
      function handleMove(e: any) {
        e.preventDefault();
        e.stopPropagation();
        // 鼠标移动的距离
        let disX = e.clientX - mouseX;
        let disY = e.clientY - mouseY;

        container.style.left = left + disX + 'px';
        container.style.top = top + disY + 'px';
        return false; // 防止选中文本，文本拖动的问题
      }

      // 监听鼠标抬起
      function handleUp(e: any) {
        e.preventDefault();
        e.stopPropagation();
        removeEvent(document.documentElement, 'mousemove', handleMove);
        removeEvent(document.documentElement, 'mouseup', handleUp);
      }
    });
  }
};

export const getType = (field) => {
  switch (field) {
    case '晴转小雨':
    case '晴转阵雨':
    case '晴转中雨':
      return '1';
    case '晴':
      return '2';
    case '阴':
    case '多云转阴':
    case '阴转多云':
      return '3';
    case '霾':
      return '4';
    case '雨':
    case '小雨':
    case '大雨':
    case '中雨':
    case '大雨转小雨':
    case '中雨转大雨':
    case '小雨转暴雨':
    case '小雨转中雨':
    case '阴转小雨':
    case '中雨转小雨':
      return '5';
    case '多云转小雨':
    case '小雨转多云':
    case '阵雨转多云':
    case '阵雨':
    case '阵雨转晴':
    case '小雨转阴':
    case '小雨转晴':
    case '多云转阵雨':
      return '6';
    case '晴转多云':
    case '晴转阴':
    case '阴转晴':
    case '多云转晴':
    case '多云':
      return '7';
    case '雨夹雪':
      return '8';
    case '雪':
    case '小雪':
      return '9';
    case '大雪':
      return '10';
    case '雾':
      return '11';
    case '雷阵雨伴有冰雹':
      return '12';
    default:
      return '7';
  }
};
