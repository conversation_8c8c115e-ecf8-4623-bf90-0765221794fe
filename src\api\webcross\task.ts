import apiServer from '../request-service';

const taskRequest = {
  // 应急响应救援队伍任务
  getTeamTaskList(data, callback) {
    let url = `/gemp-app/api/gemp/app/task/emTeamTask/v1/app/list`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
  // 分页查询-救援队伍关联的事件列表信息
  getTeamEventList(data, callback)  {
    let url = `/gemp-app/api/gemp/app/task/emTeam/v1/getEventList`;
    apiServer.commonRequest(url, data, 'POST', callback, false);
  },
   //获取任务列表
    findTaskListNew(data, callback) {
    let url = '/gemp-app/api/gemp/app/task/emTeamTask/v2/app/list';
    apiServer.commonRequest(url, data, 'POST', callback,false);
  },
  // 队伍结束任务
  finishTask(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/emTeamTask/v1/finish`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 发送消息回复
  reply(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/reply/v2/save`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 查看所有的回复消息
  getAllReply(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/reply/v2/getAllList`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  noticeSave(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/reply/v2/noticeSave`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  getGroupManage(data, cb) {
    let url = `/gemp-user/api/gemp/user/maillist/group/list/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  getGroupMenberList(data, cb){
    let url = `/gemp-user/api/gemp/user/maillist/group/person/list/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  addMenberList(data, cb){
    let url = `/gemp-user/api/gemp/user/maillist/group/add/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  deleteMenber(data, cb){
    let url = `/gemp-user/api/gemp/user/maillist/group/person/delete/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  deleteGroup(data, cb){
    let url = `/gemp-user/api/gemp/user/maillist/group/delete/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  queryBuffer(data, cb){
    let url = `/gemp-event/api/gemp/event/decision/scheme/near/risk/info/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },

  // app端分页查询回复信息
  getAppReply(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/reply/v2/getAppReplyListByPage`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 获取事件下队伍信息
  getTeamDetail(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/emTeam/v1/app/getTeamDetail`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 生成通行码
  getPassCode(data, cb) {
    let url = `/gemp-app/api/gemp/app/speedCode/v2/generateQrCode`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 队伍签到
  teamSign(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/reply/v2/teamSign`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 获取事件下反馈类型
  getFeedbackType(data, cb) {
    let url = `/gemp-app/api/gemp/app/emTaskReplyType/v1/getListForEvent`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 物资签收操作
  resourceSign(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/taskResourceApply/v1/resourceSign`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 解析授权码
  parseAuthcode(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/emTaskAuth/v1/parseAuth`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // app执行对象任务签到
  teamTaskSign(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/emTeamTask/v2/teamTaskSign`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 清除未读消息
  clearUnread(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/reply/v2/clearUnread`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 根据登录用户userId获取最新teamId
  getTeamId(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/emTaskAuth/v1/getBindTeamId/${data.userId}`;
    apiServer.commonRequest(url, data, 'GET', cb, false);
  },
  //  获得app端快捷菜单列表
  getShortcutMenuList(data, cb) {
    let url = `/gemp-app/api/gemp/app/task/shortcutMenu/v1/getShortcutMenuList`;
    apiServer.commonRequest(url, data, 'GET', cb, false);
  },
  // 知识案例列表接口
  getCaseList(data, cb) {
    let url = `/gemp-plan/api/gemp/knowledge/case/list/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  //  知识案例详情接口
  getCaseById(data, cb) {
    let url = `/gemp-plan/api/gemp/knowledge/case/id/v1`;
    apiServer.commonRequest(url, data, 'POST', cb, false);
  },
  // 当前天气接口
  getCurrentWeather(data, callback) {
   let url = `/gemp-data-sync/api/gemp/dataSync/consume/cityWeatherForecast/listByPage/v1`;
   apiServer.commonRequest(url, data, 'POST', callback, false);
 },
 getCurrentCode(data, callback) {
  let url = `/edss-gapi/edss-service/api/district/querydistrictbylonlat/v1`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

 // 下载文件
 download(data, callback) {
  let url = `/gemp-user/api/attachment/download/v1`;
  apiServer.blobRequest(url, data, 'POST', callback, false);
},
// app  修改任务状态
modifyTaskStatus(data,callback){
  // /api/gemp/app/task/sms/emTaskSms/updateConfirmStatusByAuthCode
  let url = `/gemp-app/api/gemp/app/task/sms/emTaskSms/updateConfirmStatusByAuthCode`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},
// 新增通行记录
addNewRouter(data,callback){
  let url = `/gemp-app//api/gemp/app/task/emTeamRoute/emTeamRoute/v1/showPassCode`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

//  查询组织机构数据
getEventCommand(data,callback){
  let url = `/gemp-app/api/gemp/app/task/plan/v2/getGroupList`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},
// 获取对象职责
getEventComandDutyDetail(data,callback){
  let url = `/gemp-app/api/gemp/app/task/plan/v1/getGroupDuty`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},
// 直播行径路径
getLiveRecord(data,callback){
  let url = `/gemp-app/api/gemp/app/task/emTeamRoute/emTeamRoute/v1/simpleRoute`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

startLive(data,callback){
  let url = `/gemp-app/api/gemp/app/task/emLiveHistory/v1/add`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},
// 查看下载调令
getOrderDoc(data,callback){
  // gemp-plan/api/gemp/dispatch/order/v1/download/2c92882685046c58018504fabdc6000b?tmp=1670826851223
  let url = `/gemp-plan/api/gemp/dispatch/order/v1/list`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

getSystemInfo(data,callback){
  // gemp-plan/api/gemp/dispatch/order/v1/download/2c92882685046c58018504fabdc6000b?tmp=1670826851223
  let url = `/gemp-app/api/gemp/app/task/plan/v1/getCommandGroupData`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

// 获取预案list 
// gemp-event/api/gemp/event/eventplan/planlist/show/v1
getPlanList(data,callback){
  // gemp-plan/api/gemp/dispatch/order/v1/download/2c92882685046c58018504fabdc6000b?tmp=1670826851223
  let url = `/gemp-event/api/gemp/event/eventplan/planlist/show/v1`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

 getSearchByPlanId(data,callback){
  let url = `/gemp-plan/api/gemp/gemp/qrpbase/planId/v2`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},
// edss-emis/gemp-user/api/dic/code/v1
 getPhaseGroup(data,callback){
  let url = `/gemp-user/api/dic/code/v1`;
  apiServer.commonRequest(url, data, 'POST', callback, false);
},

}
export default taskRequest;