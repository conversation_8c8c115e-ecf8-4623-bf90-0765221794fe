<template>
  <!-- 队伍反馈 -->
  <div class="teamSignIn-item feedback">
    <div class="teamSignIn-user">省应急厅</div>
    <div class="teamSignIn-info">
      <p>省应急厅</p>
      <div class="teamSignIn-feedback">
        <p>为保障现场救援工作有序高效进行，请任务执行单位及时反馈现场情况</p>
        <ul class="feedback">
          <li :class="['feedback-item', item.replyTypeCode === feedbackType ? 'active' : '']" v-for="item in feedbackList" :key="item.type" @click="handleFeedback(item)">
            {{item.replyTypeDesc}}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
@Component({
  components: {
  }
})
export default class filePreview extends Vue {
  @Prop() feedbackType: any;
  @Prop() feedbackList: any; // 反馈类型
  
  handleFeedback(item) { // 点击回复类型
    this.$emit('onClickFeedback', item);
  }
}
</script>

<style lang="less" scoped>
</style>