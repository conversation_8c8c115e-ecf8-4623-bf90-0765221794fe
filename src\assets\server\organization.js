import axios from 'axios';
import qs from 'qs'
// 请求组织机构与人员信息
// const baseurl = 'http://***********:8085/hg/mobile';
// const baseurl = 'http://**************:8000/gemp-user';
// const token = window['vm'].$store.state.userInfo.token;
// let baseurl = "";
// let token = ""
// setTimeout(()=>{
//     baseurl = window['vm'].$myConfig.ip;
//     token = window['vm'].$store.state.userInfo.token;
// },200)
const getGroup = (data) => {
    return axios({
        method: 'get',
        url: window['g'].IP + 'gemp-user/getOrgandPersonInfo.mvc?userid=' + data.userid + '&orgcode=' + data.orgcode + '&parentid=' + data.parentid,
    })
}
const getAllGroup = (data) => {
    let token = localStorage.getItem("token");
    return axios({
        method: 'post',
        url: window['g'].IP + 'gemp-user/api/gemp/user/org/findEmsOrgListByOrgCode/v1',
        headers:{'token':token},
        data: qs.stringify(data)
    })
}
const organization = {
    getGroup,
    getAllGroup
}
export default organization;