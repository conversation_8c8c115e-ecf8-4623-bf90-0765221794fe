<template>
  <div class="div_big">
    <van-nav-bar title="测试首页" left-text="返回"   left-arrow  @click-left="onClickLeft" >
      <template #right>
      right
    </template>
    </van-nav-bar>
    <div class="form_big_class">
       <!-- <form-create v-model="fApi" :rule="rule" :option="option"></form-create> -->
      <div id="form-create"></div>
      <div id="myform"></div>
       <div class="add_class">
         <el-button type="success" @click="showAnync=true">添加表单组件</el-button>
         <p><el-button type="primary" @click="clickItem('Input')" plain>添加文本</el-button></p>
          <p><el-button type="primary" @click="createTemple()" plain>添加组件</el-button></p>
       </div>
       <van-dialog v-model="showAnync" title="选择动态表单组件" :showConfirmButton="false" :closeOnClickOverlay="true" >
           <div class="add_class_d">
             <p><el-button type="primary" @click="clickItem('Input')" plain>添加文本</el-button></p>
             <p><el-button type="primary" @click="createTemple()" plain>添加组件</el-button></p>
             <p><el-button type="primary" plain>添加文本</el-button></p>
             <p><el-button type="primary" plain>添加文本</el-button></p>

             <p><el-button type="primary" plain>添加文本</el-button></p>
             <p><el-button type="primary" plain>添加文本</el-button></p>
          </div>
      </van-dialog>
            
    </div>
  
    
  </div>
</template>
<script>
import formjs from '../formConfig/table_vant';
import createform from './createform'
import Vue from 'vue/dist/vue.js';
// import Vue from 'vue';
export default {
  name: "appform",
  data() {
    return {
      fApi:{},
      showAnync:false,
      //表单生成规则
      rule:formjs.rule,
      option:{
        //表单提交事件
        onSubmit:function (formData) {
          alert(JSON.stringify(formData));
        }
      }
    }
  },
  created(){
    console.log("apphome",this.$store)
  },
  methods: {
    clicks(url) {
      console.log("click form")
      this.$router.push({path: '/form' });
    },
    createTemple(){
      let _this=this;
      console.log("createTemple")
      // 创建构造器
      var Profile = Vue.extend({
        template: '<van-search v-model="value" placeholder="请输入搜索关键词" />',
        data: function () {
          return {
            firstName: 'Walter',
            lastName: 'White',
            alias: 'Heisenberg',
            value:"ddd"
          }
        }
      })
      // Profile.use(vant);
      // 创建 Profile 实例，并挂载到一个元素上。
      new Profile().$mount('#myform')
       _this.showAnync=false;
    },
    clickItem(_item){
      let _this=this;
      console.log("click==>"+_item)
      let obj={ type:'input',field:'goods_name2',title:'商品名称'}
      this.rule.push(obj);
      _this.showAnync=false;
    
    },
    onClickLeft(){
        console.log("返回点击")
      }
    },mounted(){
    console.log("com in mounted")
    const root = document.getElementById('form-create');
    let _this=this;
    createform.findFormJson(formjs.rule)
    // const $f = window['formCreate'].create(
    //   //表单生成规则
    //   [
    //     {
    //       type:'input',
    //       field:'goods_name',
    //       title:'商品名称'
    //     },
    //     {
    //       type:'datePicker',
    //       field:'created_at',
    //       title:'创建时间'
    //     },
        
        
        
    //   ],
    //   //组件参数配置
    //   {
    //     el:root,
    //     //显示表单重置按钮
    //     resetBtn:true,
    //     //表单提交事件
    //     onSubmit:function (formData) {
    //       //按钮进入提交状态
    //       $f.btn.loading();
    //     }
    // });
    // window['formCreate'].maker.template('<el-button type="text" @click="addLine">增行</el-button>',this,'btn','自定义按钮')

    // setTimeout(()=>{
    //   console.log("execute mounted timeout")
    //   let rule = [
    //       {
    //         type:'template',
    //         template:'<el-button type="text" @click="addLine">增行</el-button>',
    //         vm:_this
    //       }
    //     ]
    //     window['formCreate'].create(rule);
    // },4000)

  }
};
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  background:white;
  .form_big_class{
    padding-top:5% ;
  }
  .add_class{
    width: 100%;
    height: 40px;
    padding-top: 10px;
    text-align: center;
    
  }
  .add_class_d{
     width: 100%;
    text-align: center;
  }
}
</style>