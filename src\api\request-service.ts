//import * as httpClient from '@gsafety/vue-httpclient/dist/httpclient';
import axios from 'axios';
import vm from '@/main';
import { gsmdp } from '@/common/gsmdp/gsmdp';
import store from '@/store';
// const baseServe = 'http://**************:19267/';
//const baseServe = 'http://**************:19290/gapi';
//const baseServe = 'http://***************:39090/gapi';
const baseServe = window['g'].IP;

let token = localStorage.getItem('token');
axios.defaults.headers.common['token'] = token;
const serveApi = {
  // serveApi.commonRequest(url, data, 'POST', callback, false);
  async commonRequest(url, data, requestType, callback, isList, customIp = true) {
    let requestUrl = customIp ? window['g'].IP + url : url;
    // console.log(customIp,'customIp',url, window['g'].IP)
    // console.log(requestUrl,'requestUrl');
    // console.log(data,'ddddddd')
    setTimeout(() => {
      let res;
      try {
        axios({
          method: requestType,
          url: requestUrl,
          data: data,
          responseType: 'json'
        })
          .then((res) => {
            window['vm'].$toast.clear();
            callback(res);
          })
          .catch(function (error) {
            // console.log(error, 'eeror');
            window['vm'].$toast.clear();
            let errorOut = {};
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              // console.log(error.response.data,error.response.status,error.response.headers);
              errorOut = error.response;
            } else if (error.request) {
              // The request was made but no response was received
              // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
              // http.ClientRequest in node.js
              //console.log(error.request);
              errorOut = error.request;
            } else {
              // Something happened in setting up the request that triggered an Error
              // console.log('Error', error.message);
            }
            let errorObj = { status: 'error', message: error.message, errorUrl: requestUrl, errorOut: errorOut };
            // console.log(JSON.stringify(errorObj));

            window['vm'].$toast('服务器繁忙');
            callback(errorObj);
          });
      } catch (error) {
        // console.log('param===> come in res error', error);
        return null;
      }
      return res;
    }, 200);
  },
  async blobRequest(url, data, requestType, callback, isList, customIp = true) {
    let requestUrl = customIp ? window['g'].IP + url : url;
    setTimeout(() => {
      if (!isList) {
        window['vm'].$toast.loading({
          overlay: false,
          message: '加载中...',
          forbidClick: true,
          duration: 20000
        });
      }
      let res;
      try {
        axios({
          method: requestType,
          url: requestUrl,
          data: data,
          headers: {
            'Content-Type': 'application/json;charset=UTF-8'
          },
          responseType: 'blob'
        })
          .then((res) => {
            window['vm'].$toast.clear();
            // console.log('blobRequest---->', res);
            let filename = decodeURI(res.headers.filename);
            filename = filename === 'undefined' ? JSON.parse(res.config.data).fileName : filename;
            let fileType = filename.split('.')[1];
            let type; // 产生二进制blob的MIME类型
            switch (fileType) {
              case 'xls' || 'xlsx':
                // 为excel文件
                type = 'application/vnd.ms-excel';
                break;
              case 'doc':
                type = 'application/msword';
                break;
              case 'docx':
                type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                break;
              case 'pdf':
                type = 'application/pdf';
                break;
              case 'bmp':
                type = 'image/bmp';
                break;
              case 'png':
                type = 'image/png';
                break;
              case 'gif':
                type = 'image/gif';
                break;
              case 'jpe' || 'jpeg' || 'jpg':
                type = 'image/jpeg';
                break;
            }
            let blobStream = new Blob([res.data], { type: type });
            let url = window.URL.createObjectURL(blobStream);
            // return { filename, url, blobStream}
            callback({ filename, url, blobStream });
          })
          .catch(function (error) {
            window['vm'].$toast.clear();
            let reader = new FileReader();
            reader.onload = function (event) {
              let resJson = this.result ? eval('(' + this.result + ')').msg : '下载失败';
              window['vm'].$toast(resJson);
            };
            reader.readAsText(res.data);
            callback(null);

            // let errorOut = {};
            // if (error.response) {
            //   errorOut = error.response;
            // } else if (error.request) {
            //   errorOut = error.request;
            // } else {
            //   console.log('Error', error.message);
            // }
            // let errorObj = { status: 'error', message: error.message, errorUrl: requestUrl, errorOut: errorOut };
            // console.log(JSON.stringify(errorObj));

            // window['vm'].$toast(errorObj.message);
            // callback(errorObj);
          });
      } catch (error) {
        // console.log('param===> come in res error', error);
        return null;
      }
      return res;
    }, 200);
  },
  getResponseData(response) {
    return response.data;
  },
  /**
   * 转换对象为url参数
   * @param data
   */
  jsonjoin(data) {
    let str = '?';
    for (let key in data) {
      str += '&' + key + '=' + data[key];
    }
    if (str.indexOf('?&') >= 0) {
      str = '?' + str.split('?&')[1];
    }
    return str;
  },
  //获取反馈列表
  async findFeekList(data, callback) {
    let url = '/gemp-app/api/gemp/app/event/dispatchtaskback/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //获取调度任务列表
  async findDispatchList(data, callback, isInit) {
    let url = '/gemp-app/api/gemp/app/event/dispatchtask/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  getCurrentCode(data, callback) {
    let url = `/edss-gapi/edss-service/api/district/querydistrictbylonlat/v1`;
    serveApi.commonRequest(url, data, 'POST', callback, false, false);
  },

  //综合查询全部
  async findAllQuery(data, callback, type, isInit) {
    let url = '';
    let project = '/gemp-app';
    switch (parseInt(type)) {
      case 0:
        url = project + '/api/gemp/app/comprehensive/search/list/v1';
        break;
      case 1:
        url = project + '/api/gemp/resource/rescueteam/list/v1';
        break;
      case 2:
        url = project + '/api/gemp/app/event/eventbase/list/v1';
        break;
      case 3:
        url = project + '/api/gemp/app/event/dispatchtask/list/v1';
        break;
      case 4:
        url = project + '/api/gemp/resource/expert/list/v1';
        break;
      default:
        break;
    }
    if (url) {
      serveApi.commonRequest(url, data, 'POST', callback, !isInit);
    } else {
      alert('请求地址为空');
    }
  },
  //综合查询详情查询
  async findDetailQuery(data, callback, type) {
    let url = '';
    let project = '/gemp-app';
    switch (parseInt(type)) {
      case 0:
        url = project + '/api/gemp/app/comprehensive/search/list/v1';
        break;
      case 1:
        url = project + '/api/gemp/resource/rescueteam/team/detail/v1';
        break;
      case 2:
        url = project + '/api/gemp/app/event/eventbase/id/v1';
        break;
      case 3:
        url = project + '/api/gemp/app/event/dispatchtask/findOne/v1';
        break;
      case 4:
        url = project + '/api/gemp/resource/expert/detail/v1';
        break;
      default:
        break;
    }
    if (url) {
      serveApi.commonRequest(url, data, 'POST', callback, false);
    } else {
      alert('请求地址为空');
    }
  },
  //获取值班安排根据月份
  async findDutyInfoByMonth(data, callback) {
    let url = '/gemp-app/api/gemp/app/duty/plan/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //获取值班安排按日期
  async findDutyInfoByDate(data, callback) {
    let url = '/gemp-app/api/gemp/app/duty/plan/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  async findOrgObByCode(data, callback) {
    let url = '/gucs-user/api/euip/org/getByOrgCode';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  async findOrgByGroup(data, callback) {
    let url = '/gemp-duty-yt/api/gemp/duty/org/list/v1';
    serveApi.commonRequest(url, {}, 'POST', callback, false);
  },
  //查询当前用户组织机构
  async getCurrentUserTreeList(data, callback) {
    let url = '/gucs-user/api/euip/org/getCurrentUserTreeList';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  // async baseuserUser(data, callback) {
  //   let url = '/gemp-user/api/gemp/user/baseuser/page/v2';
  //   serveApi.commonRequest(url, data, 'POST', callback, false);
  // },
  async baseuserUser(data, callback) {
    let url = '/gucs-user/api/external/euip/person/getSubPersons';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //查询用户子级
  async findOrgChildrenById(data, callback) {
    let url = '/gucs-user/api/euip/org/getTreeByParentOrgCode';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //查询用户子级下人员列表
  async getSubPersons(data, callback) {
    let url = '/gucs-user/api/external/euip/person/getSubPersons';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  async findEverydayInfoByMonth(data, callback) {
    let url = '/gemp-app/api/gemp/app/user/maillist/person/findSignInMonth/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  async signIn(data, callback) {
    let url = '/gemp-app/api/gemp/app/user/maillist/person/click/signIn/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //预案列表请求
  async findPlanList(data, callback, isInit) {
    let url = '/gemp-app/api/app/gemp/plan/base/list/plan';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //预案管理一级菜单请求
  async findPlanManageMenu(data, callback, isInit) {
    let url = '/gemp-plan/api/gemp/plan/emergencyplantemplate/type/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //预案管理列表请求
  async findPlanManageList(data, callback, isInit) {
    let url = '/gemp-plan/api/gemp/plan/emergency/statistic/getList/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //预案管理事件类型
  async findEventType(data, callback, isInit) {
    let url = '/gemp-duty/api/gemp/duty/info/event/type/tree/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },

  //预案详情请求
  async findPlanInfo(data, callback) {
    let jsonStr = serveApi.jsonjoin(data);
    let url = '/gemp-app/api/app/gemp/plan/base/id/v1' + jsonStr;
    serveApi.commonRequest(url, null, 'POST', callback, false);
  },
  //预案结构化请求详情
  async findStructurInfo(data, callback) {
    let url = '/gemp-plan/api/gemp/plan/emergency/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //案例列表请求
  async findCaseList(data, callback, isInit) {
    let url = '/gemp-app/api/gemp/app/extend/knowledge/case/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //案例详情请求
  async findCaseInfo(data, callback) {
    //debugger
    //let jsonStr=serveApi.jsonjoin(data)
    //axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';
    let url = '/gemp-app/api/gemp/app/extend/knowledge/case/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //法律法规列表请求
  async findLawList(data, callback, isInit) {
    let url = '/gemp-app/api/app/gemp/knowledge/law/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //法律法规详情请求
  async findLawInfo(data, callback) {
    let url = '/gemp-app/api/app/gemp/knowledge/law/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //应急知识列表请求
  async findKnowledgeList(data, callback, isInit) {
    let url = '/gemp-plan/api/gemp/knowledge/genekno/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //应急知识详情请求
  async findKnowledgeInfo(data, callback) {
    let url = '/gemp-plan/api/gemp/knowledge/genekno/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //基础知识列表请求
  async findBaseKnowledgeList(data, callback, isInit) {
    let url = '/gemp-plan/api/gemp/knowledge/base/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //基础知识详情请求
  async findBaseKnowledgeInfo(data, callback) {
    let url = '/gemp-plan/api/gemp/knowledge/base/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //教学视频列表请求
  async findTeachingVideoList(data, callback, isInit) {
    let url = '/gemp-plan/api/gemp/knowledge/video/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //教学视频详情请求
  async findTeachingVideoInfo(data, callback) {
    let url = '/gemp-plan/api/gemp/knowledge/video/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },

  //标准及技术规范列表请求
  async findSpecificationsList(data, callback, isInit) {
    let url = '/gemp-plan/api/gemp/knowledge/stand/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },
  //标准及技术规范详情请求
  async findSpecificationsInfo(data, callback) {
    let url = '/gemp-plan/api/gemp/knowledge/stand/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },

  // 获取任务列表新版
  async findTaskList(data, callback, isInit) {
    let url = '/gemp-event/api/gemp/event/eventtask/app/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, !isInit);
  },

  //判断用户是否上下级
  async isUpOrDownUser(data, callback) {
    let url = '/gucs-user/api/euip/user/isUpOrDownUser';
    serveApi.commonRequest(url, {}, 'POST', callback, false);
  },
  //获取任务反馈列表
  async findTaskFeekList(data, callback) {
    let url = '/gemp-event/api/gemp/event/eventtaskfeedback/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //获取任务详情
  async findTaskInfo(data, callback) {
    let url = '/gemp-event/api/gemp/event/eventtask/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  async findTaskInfoNew(data, callback) {
    let url = '/gemp-app//api/gemp/app/task/emTeamTask/v2/app/listDetail';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //获取任务反馈详情
  async findTaskFkInfo(data, callback) {
    let url = '/gemp-event/api/gemp/event/eventtaskfeedback/id/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //保存任务反馈信息
  async taskfeedbackSave(data, callback) {
    let url = '/gemp-event/api/gemp/event/eventtaskfeedback/app/add/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //上传附件
  async uploadFiles(data, callback) {
    let url = `/gemp-user/api/attachment/upload/v1`;
    // let url = `gemp-app/api/gemp/app/basement/attachment/upload/v1`;
    let urls = window['g'].IP + url;
    let resultArr = [];
    let paramArr = [];
    data.uplist.forEach((item, index) => {
      if (item) {
        paramArr.push(item);
      }
    });
    data.upvideolist.forEach((item, index) => {
      if (item) {
        paramArr.push(item);
      }
    });
    console.log('待上传=>' + JSON.stringify(paramArr));
    //alert("待上传=>"+JSON.stringify(paramArr))
    resultArr = await serveApi.invokeUp(paramArr, urls);
    console.log('已上传==>' + JSON.stringify(resultArr));
    //alert("已上传==>"+JSON.stringify(resultArr))
    if (resultArr.length == paramArr.length) {
      callback(resultArr);
    } else {
      callback(null);
    }
  },
  async invokeUp(paramArr, urls) {
    let resultArr = [];
    for (const item of paramArr) {
      try {
        const info = await serveApi.syncUploadFile(urls, item);
        //alert("返回"+JSON.stringify(info))
        let upres = JSON.parse(info['responseStr']);
        resultArr.push(upres);
      } catch (error) {
        // console.log(error, 'error');
        window['vm'].$toast.clear();
        alert('上传失败');
      }
    }
    return resultArr;
  },
  syncUploadFile(fileUrl, path) {
    let _this = vm;
    return new Promise((resolve, reject) => {
      gsmdp.uploadFile({
        key: {
          url: fileUrl,
          filePath: path,
          name: 'file',
          header: {
            token: store.state.userInfo.token
          }
        },
        taskRunning: (result) => {
          console.log(fileUrl, 'fileUrl');
          console.log('上传任务进行中' + JSON.stringify(result));
        },
        taskComplete: (result) => {
          console.log('附件上传成功==>' + JSON.stringify(result));
          resolve(result);
        },
        taskFail: (error) => {
          console.log('附件上传失败==>' + JSON.stringify(error));
          reject(error);
        }
      });
    });
  },
  //获取任务列表
  getTaskList(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/emTeamTask/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  uploadFile(data, callback) {
    axios.defaults.headers.post['Content-Type'] = 'multipart/form-data';
    let url = '/gemp-user/api/attachment/upload/v1';
    // gemp-app/api/gemp/app/basement/attachment/upload/v1
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //获取速达码
  getSpeedcodeGenerate(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/speedcode/generate/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //生成二维码
  getGenerateCode(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/speedcode/generateCode/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //签到
  signInAndArrive(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/reply/sign/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //回复
  saveSubmit(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/reply/save/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //历史回复数据
  historyReplyhistory(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/reply/replyhistory/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //结束任务
  overTsakApi(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/emTeamTask/update/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //查询是否签到
  isSignIn(data, callback) {
    let url = window['g'].APPIP + '/api/gemp/app/task/reply/issign/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //登录
  userLogin(data, callback) {
    let url = '/gemp-user/api/gemp/duty/info/user/login/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //单点登录
  appLogin(data, callback) {
    let url = '/gemp-user/api/gemp/user/cas/appLogin?userToken=' + data.token;
    serveApi.commonRequest(url, {}, 'GET', callback, false);
  },
  //加密密钥
  titleName(data, callback) {
    let url = '/gemp-user/api/config/system/batchkeys/v1';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  loginOut(data, cb) {
    let url = '/gemp-user/api/gemp/duty/info/user/logout';
    serveApi.commonRequest(url, data, 'POST', cb, false);
  },
  // 事件管理列表
  eventList(data, callback) {
    let url = '/gemp-event/api/gemp/event/eventbase/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  eventbase(data, callback) {
    let url = '/gemp-app/api/gemp/event/eventbase/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  // 监测预警列表
  monitoringAndEarlyWarning(data, callback) {
    let url = '/edss-gapi/gemp-data-sync/api/gemp/dataSync/consume/warnsignal/page/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true, false);
  },
  //查询当前用户组织机构的详情信息
  async getUserInfo(data, callback) {
    let url = '/gucs-user/api/euip/org/getByOrgCode';
    serveApi.commonRequest(url, data, 'POST', callback, false);
  },
  //预案管理事件类型
  findWarningPage(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/findPage/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  // 获取机构列表
  getOrgList(data, callback) {
    let url = '/gemp-user/api/gemp/user/district/list/byparmentdistrictcode/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  // 根据id查询预警
  findWarningById(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/findOne/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },

  // 根据id解除预警
  liftWarning(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/liftWarning/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  // 预警级别
  warningLevel(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/warningLevel/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  // 预警类型
  warningType(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/warningType/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  // 获取预警类型
  levelInfo(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/levelInfo/list/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },

  // 获取救援力量
  getMemberUnitList(data, callback) {
    let url = '/gemp-app/api/gemp/app/task/emTask/v1/getMemberUnitList';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },
  //追加
  additionalPush(data, callback) {
    let url = '/gemp-duty/api/gemp/earlyWarning/additionalPush/v1';
    serveApi.commonRequest(url, data, 'POST', callback, true);
  },

  // 获取历史方案
  getSchemeList(data, callback) {
    const url = `/gemp-event/api/gemp/event/eventscheme/list/v1`;
    serveApi.commonRequest(url, data, 'POST', callback, true);
  }
};

export default serveApi;
