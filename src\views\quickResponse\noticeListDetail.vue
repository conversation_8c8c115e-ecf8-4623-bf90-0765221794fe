<template>
  <div class="list">
    <Header title="群发公告详情"></Header>
    <div class="list-main">
        <van-cell-group inset>
          <van-field label="时间" v-model="formObj.replyDate" readonly />
          <van-field label="发布单位" v-model="formObj.orgName" readonly />
          <van-field
            v-model="formObj.replyText"
            rows="2"
            autosize
            label="内容"
            type="textarea"
            readonly
          />
          <div class="text">附件</div>
          <!-- <fileShow ref="fileRef" :showObject="showObject" /> -->
          <file-preview direction="left" :fileList="formObj.attachmentList"></file-preview>
        </van-cell-group>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import FilePreview from '@/components/filePreview.vue';
import fileShow from '../integratedQuery/fileShow.vue';
import commonFile from '@/views/common/commonFile.vue'
@Component({
  components: {
    Header,
    commonFile,
    fileShow,
    FilePreview
  }
})
export default class noticeListDetail extends Vue {
  formObj: any = {};
  mounted() {
    this.formObj = this.$store.state.app.noticeDetail;
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    /deep/
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        height: 100%;
        .van-cell {
          padding: 12px 0;
          border-bottom: 1px solid #cecece;
        
        }
        .van-uploader__upload {
            display: none;
        }
      }
    .text {
        color: #646566;
    }
  }
}
</style>