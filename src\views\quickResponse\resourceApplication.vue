<template>
  <div class="resourceApplication">
    <Header title="资源申请"></Header>
    <div class="resourceApplication_content">
      <h2>我的申请(1)</h2>
      <div class="resourceApplication_item">
        <p>现需物资装备手套10双，呼吸机2台，防护服10套，口罩2个。</p>
        <div class="application_info"><span>申请事由</span><div>因防护物资用完，现场缺少物资影响下一轮搜救</div></div>
        <div class="application_info"><span>申请时间</span>2022-03-10 19:32</div>
        <div class="application_info"><span>审批结果</span><i class="tongguo"></i>已通过</div>
        <div class="application_info"><span>审批意见</span><div>现调派所需物资赶赴现场</div></div>
        <div class="application_info"><span>联&nbsp; 系&nbsp; 人</span>张飞<i class="phone"></i><i class="ph_number">13698745120</i></div>
        <div>
          <van-button plain type="primary" native-type="submitForm" style="width: 100%;color:#326eff;border:1px solid #326eff;border-radius: 4px;font-size: 16px;font-weight: bold;background-color:#f0f4ff;">
            确认签收物资
          </van-button>
        </div>
      </div>
    </div>
    <div style="background: #fff;padding: 16px;position: absolute;width: 100%;bottom: 0;">
      <van-button block type="primary" @click="submit" native-type="submit" style="width: 100%;color:#fff;border:1px solid #326eff;border-radius: 4px;font-size: 16px;font-weight: bold;background-color:#326eff;">
        资源申请
      </van-button>
    </div>
    <van-popup
      v-model="show"
      closeable
      position="bottom"
      :style="{ height: '75%' }"
    >
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <!-- <van-field
            name="资源申请"
            label="资源申请"
            readonly
          /> -->
          <div class="text">资源申请</div>
          <van-field
            v-model="formObj.goods"
            type="textarea"
            rows="5"
            name="goods"
            label="申请物资装备"
            :placeholder="'请输入申请物资装备名称及数量\n例：手套，10双'"
            :rules="[{ required: true, message: '请输入申请物资装备名称及数量，例：手套，10双' }]"
          />
          <van-field
            v-model="formObj.reason"
            type="textarea"
            rows="5"
            name="reason"
            label="申请事由"
            placeholder="请输入申请事由"
            :rules="[{ required: true, message: '请输入申请事由' }]"
          />
        </van-cell-group>
        <div style="margin: 16px;position: fixed;bottom: 0;width: calc(100% - 32px);">
          <van-button block type="primary" native-type="submit">
            确认提交
          </van-button>
        </div>
      </van-form>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
// import { ref } from 'vue';
import { Uploader } from 'vant';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
  }
})
export default class resourceApplication extends Vue {
  private show = false;
  private submit() {
    this.show = true;
  };
  private submitForm(){};
  private formObj:any = {
    arriveNum: null,
    goods: '',
    reason: ''
  }
  private pattern:any = /\d/;
  private onSubmit() {
    
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.resourceApplication {
  background: #f5f6f6;
  .resourceApplication_content{
    padding:2px 10px 13px 10px;
    h2{
      padding:0 10px;
      font-size:17px;
      color:#1c1d1d;
    }
    .resourceApplication_item{
      font-size:15px;
      background:#fff;
      padding:13px 10px;
      p{
        margin:0px 0 15px 0;
        font-size:15px;
        color:#1c1d1d;
        line-height:30px;
        padding-bottom:10px;
        border-bottom:1px solid #cecece;
      }
      .application_info{
        margin-bottom:10px;
        .tongguo{
          display: inline-block;
          width: 12px;
          height: 12px;
          background: url('../../assets/images/quickResponse/quickResponse15.png') center no-repeat;
          background-size: contain;
          margin-right: 5px;
        }
        .phone{
          display: inline-block;
          width: 13px;
          height: 13px;
          background: url('../../assets/images/quickResponse/quickResponse16.png') center no-repeat;
          background-size: contain;
          margin: 0px 20px;
          position: relative;
          top: 1px;
        }
        .ph_number{
          color:#326eff;
        }
        span{
          display: inline-block;
          color:#666;
          margin-right:10px;
          float:left;
        }
        div{
          float:left;
          display: inline-block;
          width:79%;
          margin:0;
        }
      }
    }
  }
}
/deep/.van-form{
  .van-cell-group {
    padding: 0 10px;
    .text {
      padding: 16px 0;
      border-bottom: 1px solid #cecece;
      font-size:17px;
      color:#1c1d1d;
    }
    .van-cell {
      padding: 12px 0;
      border-bottom: 1px solid #cecece;
      flex-direction: column;
      height: 300x;
      border: none;
      .van-cell__title {
        width: 100%;
        padding-bottom: 12px;
        color:#1c1d1d;
        border-bottom: 1px solid #cecece;
        font-size: 15px;
      }
      .van-cell__value {
        padding-top: 12px;
      }
    }
  }
  .van-button--primary {
    background-color: #326eff;
    border: 1px solid #326eff;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
  }
}
</style>
