<template>
  <div class="home">
    <!-- <ul>
      <li v-for="item in menuList" :key="item.path">
        <van-button type="primary" @click="handleMenu(item)">{{item.name}}</van-button>
      </li>
    </ul> -->
    <van-grid>
      <van-grid-item v-for="item in menuList"
                     :key="item.path"
                     :icon="item.icon"
                     :text="item.name"
                     :to="item.path" />
    </van-grid>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      menuList: [
        { name: '任务调度', path: '/dispatchTaskList', icon: require(`@/assets/images/icons/tel_btn.png`) },
        { name: '综合查询', path: '/integratedQueryhList', icon: require(`@/assets/images/icons/phoneBtn.png`) },
        { name: '值班安排', path: '/duty', icon: require(`@/assets/images/icons/fix_ico.png`) },
        { name: '每日签到', path: '/everydaySign', icon: require(`@/assets/images/icons/fileicon.png`) },
        { name: '知识库', path: '/navigatePage', icon: require(`@/assets/images/icons/back_picture.png`) },
        { name: '预案列表', path: '/planList', icon: require(`@/assets/images/icons/locationBlue.png`) },
        { name: '预案管理', path: '/planManageList', icon: require(`@/assets/images/icons/widget_upload_pic.png`) },
        { name: '案例列表', path: '/caseList', icon: require(`@/assets/images/icons/tel_btn.png`) },
        { name: '法律法规列表', path: '/lawList', icon: require(`@/assets/images/icons/phoneBtn.png`) },
        { name: '教学视频列表', path: '/teachingVideoList', icon: require(`@/assets/images/icons/tel_btn.png`) },
        { name: '标准及技术规范', path: '/specificationsList', icon: require(`@/assets/images/icons/duty_tel_ico.png`) },
        { name: '应急知识列表', path: '/knowledgeList', icon: require(`@/assets/images/icons/tel_btn.png`) },
        { name: '基础知识列表', path: '/baseKnowledgeList', icon: require(`@/assets/images/icons/audio.png`) },
        { name: '任务管理', path: '/taskList', icon: require(`@/assets/images/icons/orgqj.png`) },
        { name: '快速响应', path: '/quickResponse', icon: require(`@/assets/images/icons/orgqj.png`) }
      ]
    };
  },
  methods: {
    handleMenu(item) {
      this.$router.push(item.path);
    }
  }
};
</script>

<style lang="less" scoped>
.home {
  ul {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 20px;
    li {
      width: 50%;
      margin: 10px 0;
    }
  }
}
</style>