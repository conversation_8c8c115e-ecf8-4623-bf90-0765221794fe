<template>
    <div class="info_div">
      <van-nav-bar title="新增反馈" left-text="返回"   left-arrow  @click-left="GoBack" >
        <template #right>
          <!-- <van-icon name="ellipsis" size="25" /> -->
        </template>
      </van-nav-bar>
      <MeetBack @documentBack="documentBack"></MeetBack>
      <div class="big_content">
        <div class="feedback_top">
          <span class="span_content" >
            <!-- <span>
              <div class="select_text">应急管理指挥中心</div>
            </span> -->
            <input type="text" :style="{width:$store.state.configInfo.needVoice&&$store.state.configInfo.needVoice=='true'?'87%':'95%'}" v-model="submitObj.feedbackTitle" :maxlength="50" placeholder="请输入反馈标题">
            <span v-if="$store.state.configInfo.needVoice&&$store.state.configInfo.needVoice=='true'" class="input_voice" @click="inputVoice('feedbackTitle')">
              <img src="../../assets/images/yuyin.png" alt="" srcset="">
            </span>
          </span>
        </div>
        <div class="feedback_top">
          <span class="span_content">
            <van-radio-group v-model="submitObj.status" direction="horizontal">
              <van-radio name="1">进行中</van-radio>
              <van-radio name="2">已完成</van-radio>
            </van-radio-group>
          </span>
        </div>
        <div class="feed_input">
          <van-field
            v-model="submitObj.feedbackContent"
            rows="3"
            maxlength="200"
            label=""
            type="textarea"
            placeholder="请输入反馈信息"
            show-word-limit
          />
          <span v-if="$store.state.configInfo.needVoice&&$store.state.configInfo.needVoice=='true'" class="input_voice" @click="inputVoice('feedbackContent')">
            <img src="../../assets/images/yuyin.png" alt="" srcset="">
          </span>
        </div>
        <!-- <div class="big_location">
          <div class="feedback_location clearfix">
         
          <textarea class="address_span" :readonly="addressReadonly" @click="addressReadonly?$showBottomToast('请先点击右侧选择定位点！'):''" :style="{marginTop:marginTop,height:addressheight}" ref="address" v-model="submitObj.position">
          </textarea>
          <span class="locationSet_span " @click="manualLocation">
            <span><img src="../../assets/images/icons/locationWhite.png" alt="" srcset=""> </span>
          </span> 
        </div>
        </div> -->
        
        <commonFile :typeflag="1" ref="fileObj" @fliws="getFiles" @uplist="muplist"/>
        
        <!-- <div class="file_div">
          <fileObj :typeflag="1" ref="fileObj" @fliws="getFiles" @uplist="muplist" />
          <div class="file_select">
            <span @click="openSelect('menu1')">
              <img src="../../assets/images/icons/back_picture.png" alt="" srcset="">
              <font>照片</font>
            </span>
            <span @click="openSelect('menu2')">
              <img src="../../assets/images/icons/back_vedio.png" alt="" srcset="">
              <font>视频</font>
            </span>
            <span @click="openSelect('menu3')">
              <img src="../../assets/images/icons/audioselect.png" alt="" srcset="">
              <font>音频</font>
            </span>
          </div>
        </div> -->
        <span class="send_btn" @click="submitBtn">提交反馈</span>
       
      </div>
      <!-- <div class="info_content">

      <van-cell-group>
        <van-field v-model="submitObj.feedTitle" label="" placeholder="请输入用户名" />
        <van-radio-group v-model="submitObj.taskState" direction="horizontal">
          <van-radio name="1">单选框 1</van-radio>
          <van-radio name="2">单选框 2</van-radio>
        </van-radio-group>
      </van-cell-group>
      </div> -->
      
    </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import commonFile from '../common/commonFile.vue'
import MeetBack from '@/components/meet-back/meet-back';
@Component({
  components: {
    commonFile,
    MeetBack
  },
})
export default class integratedItem extends Vue {
  @Prop(String) private requestId :string;
  @Prop(Object) private queryMap :any;
  detailInfo: any = {};
  showObject: any = {};
  lontatStr:any ="";
  marginTop: any="3%";
  addressheight:any="45px";
  addressReadonly:any =true;
  commonMapModel:boolean = true;
  paramObj:any = {};
  fileList:any = [];
  submitObj: any ={
    feedbackTitle:"",
    eventId:this['queryMap'].eventId,
    taskId:this['queryMap'].taskId,
    status:"1",
    feedbackContent:"",
    longitude:"",
    latitude:"",
    position:"",
    attachmentList:[]
  }
  getFiles(data){
      this.fileList=data;
  }
  muplist(data){
    this.paramObj=data;
  }
  manualLocation(){
      let _this=this;
      _this.commonMapModel=true;
      // setTimeout(()=>{
      //   _this.$refs.CMap.locationSet(_this.lontatStr);
      // },1000)
  }
  refreshLocation(){
    let _this=this;
    if(this.$isAndroid()){
      console.log("invoking android!")
      // window.android.getCurrentLocation();
      _this.$gsmdp.getLocation({
      data: {
      type: 'gcj02',
      altitude: 'true'
      },
      success(result) {
        //alert('getLocation' + JSON.stringify(result));
        //alert(result.longitude+"==>"+result.latitude)
        _this.$gsmdp.getLocationInfo({
              data:{
                "longitude":result.longitude,
                "latitude":result.latitude,
                "address":"",
              },
              success(res){
                if(res.longitude!=""&&res.latitude!=""&&res.address!=""){
                  let addressStr=res.longitude+"_"+res.latitude+"_"+res.address;
                  _this.locationSet(addressStr)
                }
              }
              
            })
          },
          fail(err) {
          console.log(err);
          }
        });
    }else{
      this.locationSet("93.793409_31.896125_四川省雅安市拉亚镇桑木卡拉亚镇桑木卡亚镇桑木卡")
    }
  }
  locationSet(lontatStr){
      let _this=this;
      _this.lontatStr=lontatStr;
      console.log("locationSet===>"+lontatStr)
      _this.submitObj.longitude=lontatStr.split("_")[0];
      _this.submitObj.latitude=lontatStr.split("_")[1];
      _this.submitObj.position=lontatStr.split("_")[2];
      _this.addressReadonly=false;
      // setTimeout(()=>{
      //   _this.initAddressHeight();
      // },100)
  }
  submitBtn(){
      let _this=this;
      if(!_this.$verification(_this.submitObj.feedbackTitle,0,"任务反馈标题")){
        return;
      }
      if(!_this.$verification(_this.submitObj.feedbackContent,0,"任务反馈信息")){
        return;
      }
     const toastObj=_this.$toast.loading({
          overlay:false,
          message: '',
          forbidClick: true,
          duration:0,
      });
      console.log(JSON.stringify(_this.submitObj))
      try{
				if(!_this.paramObj.uplist&&!_this.paramObj.upvideolist){
          toastObj.message="保存中.."
          _this.sendFeedback();
          //alert("无附件选择")
				}else{
					console.log(_this.paramObj.uplist.join(','))
          console.log(_this.paramObj.upvideolist.join(','))
          toastObj.message="上传中.."
          // window.android.uploadFiles(_this.paramObj.uplist.join(',')+"",_this.paramObj.upvideolist.join(',')+"","mobile/saveattach.mvc");
          apiServer.uploadFiles(_this.paramObj,function(_resultObj) {
              console.log("页面上传成功回调==>"+JSON.stringify(_resultObj))
              //alert("页面上传成功回调==>"+JSON.stringify(_resultObj))
              if(_resultObj){
                console.log("调用uploadFiles 成功")
                console.log("resultObj==>"+_resultObj)
                _this.submitObj.attachmentList=_resultObj;
                toastObj.message="保存中.."
                _this.sendFeedback();
              }
             
            })
					
				}
			}catch(e){
        console.log(e)
				console.log("调用uploadFiles 失败")
				//TODO handle the exception
			}

    }
    sendFeedback(){
      console.log("to commit;")
      let _this=this;
      // _this.submitObj.attachmentList=[
      //   {
      //     "$type": "AttachmentOutDTO,http://www.dv.com",
      //     "name": "magazine-unlock-06-2.3.5272-12D62DD529082E9436A61(1).jpg",
      //     "url": "/upload/uploadFile/app/5e055af1b10d4462949949717d60fc93.jpg",
      //     "msg": "上传成功",
      //     "status": "200",
      //     "attachId": "5e055af1b10d4462949949717d60fc93",
      //     "urlOuterNet": "http://119.53.209.178:58990/upload/uploadFile/app/5e055af1b10d4462949949717d60fc93.jpg"
      //   },
      //   {
      //     "$type": "AttachmentOutDTO,http://www.dv.com",
      //     "name": "magazine-unlock-01-2.3.5292-52360E9D336BE0CEB233A.jpg",
      //     "url": "/upload/uploadFile/app/c7f3053361674107a38ad70ade31e224.jpg",
      //     "msg": "上传成功",
      //     "status": "200",
      //     "attachId": "c7f3053361674107a38ad70ade31e224",
      //     "urlOuterNet": "http://119.53.209.178:58990/upload/uploadFile/app/c7f3053361674107a38ad70ade31e224.jpg"
      //   }
      // ]
      let param=_this.submitObj;
        apiServer.taskfeedbackSave(param,function(res){
           _this.$toast.clear()
          console.log("save taskfeedbackSave==>",res)
          if(res.status=='200'&&res.data.status==200){
            _this.$showBottomToast("保存成功")
            // _this.$emit("close");
            //_this.GoBack()
            _this.$emit("success");
          }else{
            if(res.message){
              _this.$showBottomToast(res.message);
            }else{
               _this.$showBottomToast(res.data.msg);
            }
            
          }
        })
      
    }
  inputVoice(_key){
    let _this=this;
    _this['$gsmdp'].openVoice({
        success: function(res) {
          console.log(JSON.stringify(res))
          _this.submitObj[_key]=res;
        }
    })
  }
  infoRequest(){
    let _this=this;
    let param={
      id: _this.requestId
    };
    apiServer.findPlanInfo(param,function(res){
        console.log(res)
        
        
    })
    
  }
  GoBack(){
    let _this=this;
    try {
        _this.$emit("close")
    } catch (error) {
        console.log("close error")
    }
  }
  documentBack() {
    this.GoBack();
  }
  created() {
    let _this=this;
    console.log("init run")
    //_this.infoRequest();
    // setTimeout(function() {
      
    // }, 500);
    
  }
  mounted(){
    let _this=this;
    console.log(_this.$store.state.configInfo.needVoice)
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      _this.GoBack()
    })
    if(_this.$store.state.configInfo.needVoice&&_this.$store.state.configInfo.needVoice=='true'){
      document.querySelector(".van-field__word-limit")['style'].width='95%';
    }else{
      document.querySelector(".van-field__word-limit")['style'].width='100%';
    }
    //请求位置信息
    //_this.refreshLocation()
  }
    

}
</script>
<style scoped lang="scss">
.info_div{
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #f4f7f8;
  .info_content{
    width: 100%;
    height: calc( 100% - 46px );
    overflow-y: auto;
    
  }
  .big_content{
    width: 100%;
    height:calc( 100% - 46px - 8.5vh) ;
    overflow-x: hidden;
    overflow-y: auto;
    border-top: solid 1px rgba(255, 255, 255, 0.15);
    
    .feedback_top{
      width: 100%;
      height: 55px;
      line-height: 55px;
      // padding-left: 5%;
      font-size: 16px;
      background: #ffffff;
      span{
        display: inline-block;
      }
      .span_title{
        width: 16%;
        height: 100%;
        float: left;
      }
      .span_content{
        width: 100%;
        height: 100%;
        padding-left: 5%;
        color: #000;
        position: relative;
        input{
          width: 87%;
          height: 70%;
          border: #afafaf 1px solid;
          border-radius: 6px;
          padding-left: 10px;
        }
        .input_voice{
          position: absolute;
          right: .3rem;
          top: .35rem;
          width: 1.5rem;
          height: 1.5rem;
          img{
            width: 100%;
          }
        }
        span:nth-child(1){
          display: inline-block;
          width: 94%;
          height: 70%;
          margin: 2.8%;
          border: #afafaf 1px solid;
          border-radius: 6px;
          .select_text{
            display: inline-block;
            width: 80%;
            height: 100%;
            line-height: 5vh;
            padding-left: 10px;
            float: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .select_icon{
            position: absolute;
            right: 8px;
            padding-bottom: 6%;
            width: 8%;
            height: 12%;
            img{
               width: 63%;
               margin-bottom: 20%;
            }
          }
        }
      }
    }
    .big_location{
       width: 100%;
       height: 12%;
       background: #fff;
       .feedback_location{
          width: 95%;
          margin-left: 5%;
          height: 100%;
          border-top: solid 1px #c7c7c7;
          //border-bottom: solid 1px #c7c7c7;
          span{
            display: inline-block;
          }
          .address_span{
            width: 88%;
            height: 35px;
            float: left;
            font-size: 15px;
            border: none;
            // padding-bottom: 10px;
            // padding-top: 3%;
          }
          
          .locationSet_span{
            width: 10%;
            height:100%;
            position: relative;
            span{
                background: #1f4ae3;
                width: 9vw;
                height: 9vw;
                // padding: 1.5vw 2.2vw;
                margin-right: 5px;
                border-radius: 6px;
                position: absolute;
                top: calc( ( 10vh - 9vw ) / 2);
                img{
                  width: 4.5vw;
                  position: absolute;
                  top: 1.5vw;
                  left: 2vw;
                }
            }
          }
          .address_span_all{
            width: 94%;
            margin-left: 3%;
            height: 10vh;
            border: 1px solid #afafaf;
            border-radius: 6px;
            margin-bottom: .2rem;
            padding: .1rem;
            position: relative;
            textarea{
              width: 90%;
              height: 8vh;
              font-size: 16px;
              color: #898989;
            }
            .relocation{
              width: 9vw;
              height: 9vw;
              position: absolute;
              right: 0px;
              bottom: 0px;
              display: inline-block;
              background: #1f4ae3;
              border-radius: 6px 0px 6px 0px;
              img{
                width: 44%;
                margin: 25% 28%;
              }
            }
          }
        
        }
    }
    
    .feed_input{
      position: relative;
      margin-top: 10px;
      .pit{
        position: absolute;
        right: 5px;
        bottom: 5px;
        z-index: 99;
        background: #409eff;
        width: 9vw;
        height: 9vw;
        padding: 1.5vw 2.2vw;
        margin-right: 5px;
        border-radius: 6px;
        img{
          width: 15px;
        }
      };
      .van-field__word-limit{
        width: 95%;
      }
      .input_voice{
        position: absolute;
        bottom: .35rem;
        right: .3rem;
        width: 1.3rem;
        height: 1.3rem;
        img{
          width: 100%;
        }
      }
    }
    .file_div{
      width: 100%;
      height: 35%;
      border-top: solid 1px #c7c7c7;
      .file_select{
        height: 50%;
        padding-left: 10%;
        padding-top: 6%;
        margin-top: 4%;
        border-top: solid 1px #c5c5c5;
        span{
          width: 25%;
          height: 80%;
          margin-left: 3%;
          display: inline-block;
          font{
            width: 100%;
            display: block;
            text-align: center;
            margin-top: -4px;
          }
          img{
            width: 60%;
            margin-left: 20%;
          }
        }
      }
    }
    .send_btn{
      width: 100%;
      height: 14vw;
      display: block;
      line-height: 14vw;
      color: #ffffff;
      font-size: 17px;
      text-align: center;
      background: #1f4ae3;
      position: absolute;
      bottom: 0px;
    }
  }
}
</style>
