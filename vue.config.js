module.exports = {
  publicPath: './',
  productionSourceMap: false,
  devServer: {
    sockHost: 'localhost',
    port: 8091,
    // https: true,
    disableHostCheck: true,
    open: true,
    proxy: {
      // "/gapi": {
      //   target: "http://************:8990/edss-euip/",
      //   pathRewrite: {
      //     "/gapi": "",
      //   },
      // },
      // 周吉槟本地服务调试
      // "/gapi/gemp-app/api": {
      //   // target: "http://*************:8109/api/",
      //   target: 'http://*************:8109/api/',
      //   pathRewrite: {
      //     "/gapi/gemp-app/api": "",
      //   },
      // },
      '/gapi': {
        // target: "https://*************:8990/",
        target: 'http://**************:8990',
        changeOrigin: true
      },
      '/upload': {
        // target: "https://*************:8990/",
        target: 'http://**************:8990',
        changeOrigin: true
      },
      '/gapi/gemp-duty': {
        // target: "https://*************:8990/",
        target: 'http://*************:8080/',
        changeOrigin: true,
        pathRewrite: {
          '/gapi/gemp-duty': '/gapi/gemp-duty'
        }
      },
      '/gapi/gemp-app': {
        // target: "https://*************:8990/",
        target: 'http://*************:8080/',
        changeOrigin: true
      },
      '/gapi/gemp-app-zg': {
        target: 'https://192.168.3.98:8109/',
        // target: "http://************:8990/",
        changeOrigin: true
      },
      '/gapi/gemp-user': {
        target: 'http://**************:8990/',
        // target: "https://*************:8990/",
        // target: "http://************:8990/",
        changeOrigin: true
      },
      'gapi/gemp-data-sync': {
        // target:  "https://*************:8990/",
        target: 'http://*************:8080/',
        // target: "http://************:8990/",
        changeOrigin: true
      },
      '/mauth/bin/': {
        target: 'https://openapi-eme.chinamye.com/mauth/bin/',
        // target: "http://************:8990/",
        pathRewrite: {
          '/mauth/bin/': ''
        }
      },
      '/gemp-app/': {
        // target:  "https://hbyjgis.hbsis.gov.cn:8990/gemp-app/",
        target: 'http://*************:8080/',
        // target: "http://************:8990/",
        pathRewrite: {
          '/gemp-app/': ''
        }
      },
      '/edss-egis/': {
        target: 'http://**************:8990/',
        changeOrigin: true,
        pathRewrite: {
          '^/edss-egis/': ''
        }
      },
      '/egisUrl/': {
        target: 'http://************:590/',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/egisUrl/': ''
        },
        onProxyReq: function (proxyReq, req) {
          // 移除可能导致WAF拦截的头部
          proxyReq.removeHeader('x-requested-with');
          proxyReq.removeHeader('origin');

          // 设置标准的浏览器请求头，模拟直接访问
          proxyReq.setHeader('Host', '************:590');
          proxyReq.setHeader(
            'User-Agent',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          );
          proxyReq.setHeader(
            'Accept',
            'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
          );
          proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
          proxyReq.setHeader('Accept-Encoding', 'gzip, deflate');
          proxyReq.setHeader('Connection', 'keep-alive');
          proxyReq.setHeader('Upgrade-Insecure-Requests', '1');

          // 如果是GET请求，设置Cache-Control
          if (req.method === 'GET') {
            proxyReq.setHeader('Cache-Control', 'max-age=0');
          }

          console.log(`代理请求: ${req.method} ${req.url}`);
        },
        onProxyRes: function (proxyRes, req, res) {
          // 添加 CORS 头部
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');

          // 记录响应状态（用于调试）
          console.log(`代理响应: ${req.url} -> ${proxyRes.statusCode}`);
        }
      },
      // 政务微信API代理
      '/cgi-bin/': {
        target: 'https://zwwx.wuhan.gov.cn/',
        changeOrigin: true,
        secure: false, // 如果是https接口，需要配置这个参数
        pathRewrite: {
          '^/cgi-bin/': '/cgi-bin/'
        },
        headers: {
          Origin: 'https://zwwx.wuhan.gov.cn',
          Referer: 'https://zwwx.wuhan.gov.cn/',
          Host: 'zwwx.wuhan.gov.cn'
        }
      }
    }
  },

  css: {
    loaderOptions: {
      sass: {
        data: '@import "@/assets/styles/helpers/mixin.scss";@import "@/assets/styles/common/selfvariable.scss";'
      },
      stylus: {
        'resolve url': true,
        import: ['./src/theme']
      }
    }
  },

  configureWebpack: (config) => {
    // 浏览器：web, electron : electron-renderer
    config.target = 'web';
  },
  configureWebpack: {
    resolve: {
      alias: {
        vue$: 'vue/dist/vue.esm.js'
      }
    }
  },
  pluginOptions: {
    'cube-ui': {
      postCompile: true,
      theme: true
    }
  }
};
