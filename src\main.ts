import Vue from 'vue';

import axios from 'axios';

import App from './App.vue';
import router from './router';
import store from '@/store';
import '@/assets/fonts/iconfont';
import requestService from './api/request-service';
import zwwx from '@/utils/zwwx'; // 政务微信SDK工具
// import wx from '@/utils/wx'; // 旧的工具类保留，避免引用错误

import { messagePlugin } from '@/utils/message';

// 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue全局错误:', err, info);
  // 可以在这里添加错误上报逻辑
};

// 全局Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.warn('未处理的Promise错误:', event.reason);
  // 防止在控制台显示错误
  event.preventDefault();
});

// 设置政务微信配置
if (process.env.VUE_APP_ZWWX_CORPID && process.env.VUE_APP_ZWWX_AGENTID) {
  zwwx.setConfig({
    corpId: process.env.VUE_APP_ZWWX_CORPID,
    agentId: process.env.VUE_APP_ZWWX_AGENTID,
    secret: process.env.VUE_APP_ZWWX_SECRET || ''
  });
}

Vue.use(messagePlugin, 'messsageBus');

import { gsmdp } from './common/gsmdp/gsmdp';
// import config from './utils/appconfig';
import { initLog, gblog } from './common/logger/logger';
import i18n from '@/lang';
import apiServer from './api/base-service';
import './moduleArr'; //引入第三方库
//引入公共的ts
import MyPluginAddConfig from './common/utils/myPlugin';
import { initData } from '../src/common/constant/initData';
Vue.use(MyPluginAddConfig);

// import Mui from 'vue-awesome-mui';
// Vue.use(Mui);
import { Steps, Step } from 'vant';
Vue.use(Steps).use(Step);

import 'element-ui/lib/theme-chalk/index.css';
import ElSelect from 'element-ui/lib/select';
import ElOption from 'element-ui/lib/option';
import ElTimeline from 'element-ui/lib/timeline';
import ElTimelineItem from 'element-ui/lib/timeline-item';
import Eautocomplete from 'element-ui/lib/autocomplete';
//

// import AlloyFinger from 'alloyfinger'
// 	import AlloyFingerPlugin from 'alloyfinger/vue/alloy_finger_vue'

// 	Vue.use(AlloyFingerPlugin,{
// 	  AlloyFinger
// 	})

import 'element-ui/lib/theme-chalk/cascader.css';
import 'element-ui/lib/theme-chalk/icon.css';
Vue.component('el-select', ElSelect);
Vue.component('el-option', ElOption);
Vue.component('el-timeline', ElTimeline);
Vue.component('el-timeline-item', ElTimelineItem);
Vue.component('el-autocomplete', Eautocomplete);
let egis = require('egis-2d');
// Vue.use(ElementUI);

window['eventBus'] = new Vue(); // 事件总线

import * as dd from 'dingtalk-jsapi';
Vue.prototype.$dd = dd;

// 挂载政务微信工具到Vue实例
Vue.prototype.$zwwx = zwwx;
Vue.prototype.wxUtils = zwwx; // 兼容旧的引用方式

import Api from '@/api/api';
Vue.prototype.$api = Api; // 请求接口方法封装
// 引入水印文件地址
import watermark from '@/utils/waterMark';
Vue.prototype.$watermark = watermark;

import moment from 'moment';
Vue.prototype.$moment = moment; // 时间格式化工具
moment.locale('zh-cn'); // 汉化

// WebSocket封装方法
// import * as socketApi from '@/utils/websocket';
import * as socketApi from '@/utils/socket';
Vue.prototype.$socketApi = socketApi;

//Vue.prototype.eventBus = new Vue()
Vue.prototype.$apiServer = apiServer;
Vue.prototype.$gsmdp = gsmdp;
// Vue.use(config)
// 初始化日志
initLog();
gblog.info('init main log 1'); // TODO  delete in project

// 响应拦截器
axios.interceptors.response.use(
  function (response) {
    if (response.data.code === 10010 || response.data.code === 10011) {
      // 删除已经失效或过期的token（不删除也可以，因为登录后覆盖）
      //跳转到登录页重新登录
    } else if (response.data.token) {
      // 判断token是否存在，如果存在说明需要更新token
      axios.defaults.headers.common['token'] = response.data.token; // 覆盖原来的token(默认一天刷新一次)
    }
    return response;
  },
  function (error) {
    return Promise.reject(error);
  }
);
declare module 'vue/types/vue' {
  interface Vue {
    $isAndroid: any;
    $commonAjax: any;
    $formatTimeStr: any;
    $enJsBack: any;
    $GetMenusByKey: any;
    $whetherImg: any;
    $formatTimeToZero: any;
    $showBottomToast: any;
    $gsmdp: any;
    $verification: any;
    $findFileType: any;
    $changeVal: any;
  }
}

router.beforeEach((to, from, next) => {
  let locationInfo = window.location.search || window.location.hash || '';

  let hashArrObj: any = {};
  if (locationInfo) {
    let hashArr = locationInfo.split('?').length > 1 ? locationInfo.split('?')[1].split('&') : [];
    hashArr.forEach((item) => {
      let itemArr = item.split('=');
      hashArrObj[itemArr[0]] = itemArr[1];
    });
    // console.log('hashArrNew---->', hashArrObj)
  }
  let token = localStorage.getItem('token');
  // console.log('++++++++++++++token---->', token);
  const tk = hashArrObj.userToken || hashArrObj.token;
  if (!!tk) {
    const params = {
      token: tk
    };
    requestService.appLogin(params, function (res) {
      console.log('++++++++++++++auto login', res);
      if (res.data.status === 200) {
        localStorage.setItem('token', res.data.data.token);
        localStorage.setItem('role', JSON.stringify(res.data.data.role));
        localStorage.setItem('teamId', res.data.data.teamId);
        axios.defaults.headers.common['token'] = res.data.data.token;
        next('home');
      } else {
        next('login');
      }
    });
  }
  if (hashArrObj.code && to.name === 'MainHome') {
    // console.log('MainHome---->', hashArrObj.code)
    next();
  } else if (token || to.meta.freelogin === true) {
    // 不受token影响跳转界面
    // console.log('MainHome---->', hashArrObj.code)
    next();
  } else {
    if (!token && to.name != 'Login') {
      //如果没有token 并且当前页不是登陆页
      if (!token && to.name == 'mainApp') {
        next();
        return;
      }
      next('Login'); //去登陆页
    }
    if (token && to.name == 'Login') {
      //如果有token 并且当前页是登陆页
      next('/home'); //去首页
    }
    next(); //如果有token 并且当前页不是登陆页,则继续它的操作
  }
});

let vm = new Vue({
  router,
  i18n,
  store,
  render: (h: any) => h(App)
}).$mount('#app');
window['vm'] = vm;

// initData((resultObj) => {
//   console.log("res=aaaaaaaaaa=>", JSON.stringify(resultObj))
//   console.log(resultObj)
//   if (resultObj.IP) {
//     window['g'].IP = resultObj.IP;
//     console.log("IP setObj==>" + window['g'].IP)
//   }
//   if (resultObj.BASEIP) {
//     window['g'].BASEIP = resultObj.BASEIP;
//   }
//   let loginObj = resultObj.userDto;
//   Object.defineProperties(loginObj, {
//     'userid': {
//       value: loginObj.id
//     },
//     'orgcode': {
//       value: loginObj.orgId
//     },
//     'token': {
//       value: resultObj.token
//     }
//   });
//   console.log("kkkk", loginObj)
//   sessionStorage.setItem('role', JSON.stringify(loginObj));
//   console.log("token---->" + loginObj.token)
//   axios.defaults.headers.common['token'] = loginObj.token;
//   store.commit('SET_USER_INFO', loginObj);
// });
export default vm;
