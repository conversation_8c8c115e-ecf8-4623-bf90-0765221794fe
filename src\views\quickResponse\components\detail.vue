<template>
  <div class="teamSignIn" id="teamSignIn" ref="teamSignIn">
    <Header
      :title="detailInfo.eventTitle"
      backPath="/list"
      :showCode="true"
      :hiddenBack="authData.authCode && authData.loginCode ? true : false"
      @showCodePage="showCodePage"
      :detailInfo="detailInfo"
    ></Header>
    <div class="teamSignIn-content" id="assPCon" ref="myScrollbar">
      <!-- 签到 -->
      <card-sign v-if="showTeamSign" @teamSign="teamSign"></card-sign>
      <!-- 已签到 -->
      <card-signed :teamInfo="teamInfo" v-if="teamInfo && teamInfo.isSign === '1'"></card-signed>
      <!-- 全部回复内容 -->
      <van-pull-refresh v-model="refreshing" @refresh="getReplyListHistory" pulling-text="" loosing-text="">
        <template v-for="item in replyList">
          <!-- 通行码 -->
          <card-passcode v-if="item.replyTypeCode === '701'" :eventInfo="detailInfo" :relName='relName' :teamInfo="teamInfo"  :details="item"></card-passcode>
          <!-- 任务卡片 -->
          <card-task
            v-else-if="(item.replyTypeCode === '702' && item.taskType !='10' ) || item.replyTypeCode === '8'||item.replyTypeCode=='9'"
            :task-info="item"
            :locationInfo="locationInfo"
            :detailInfo="detailInfo"
            :docList='docList'
            @onClickFinishTask="overTask"
            @onTeamSign="onTeamSign"
          ></card-task>
          <!-- 队伍反馈 -->
          <card-feedback
            v-else-if="item.replyTypeCode === 'feedback'"
            :feedbackList="feedback"
            :feedbackType="feedbackType"
            @onClickFeedback="onClickFeedback"
          ></card-feedback>
          <div v-else-if="item.replyTypeCode === '10'" >
            <div class="teamSignIn-item task teamSignIn-itemes right">
              <div class="teamSignIn-user">{{ teamInfo.teamName.slice(0, 4) || '省应急厅' }}</div>
              <div class="teamSignIn-info">
                <p style="font-weight: bold">{{ teamInfo.teamName || '省应急厅' }}</p>
                <div class="teamSignIn-boxes">
                  <div class="teamSignIn-maines">
                    <div class="map-content" >
                      <p style="min-width:200px;overflow:hidden;text-align:left">{{item.replyAddress}}</p>
                      <div class="map" @click="guideLoc('gd')">
                        <BaseMap
                          :mapId="`map_${new Date().getTime()}`"
                           :longitude="item.longitude"
                          :latitude="item.latitude"
                        ></BaseMap>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <p style="text-align: center; font-size: 12px; color: #666">
              {{ item.replyDate }}
            </p>
            <div :class="['teamSignIn-itemes', item.replyFrom === 'team' ? 'right' : 'left']">
              <div class="teamSignIn-user">
                {{ item.replyFrom === 'team' ? item.teamName.slice(0, 4) : '省应急厅' }}
              </div>
              <!-- 资源申请审核消息推送 -->
              <template v-if="item.resourceApply && item.resourceApply.length > 0" v-for="check in item.resourceApply">
                <div class="feedCard" v-if="item.replyText === check.checkOpinion">
                  <p>{{ item.replyFrom === 'team' ? item.teamName : '省应急厅' }}</p>
                  <div class="feedCard-container">
                    <div class="title">
                      <i class="el-icon-s-check"></i>
                      <span>{{ item.replyTypeCode === '5' ? '物资装备申请审核' : '申请审核' }}</span>
                    </div>
                    <span class="replyText">{{ check.taskReplyText }}</span>
                    <div class="checkDetail">
                      <span>审核意见：{{ check.checkOpinion || '--' }}</span>
                      <span>联系人：{{ check.contact || '--' }}</span>
                      <span>联系方式：{{ check.contactTel || '--' }}</span>
                    </div>
                    <div class="receiveResult">
                      <span class="sign" v-if="check.receiveStatus === '0'" @click="handleSignResource(check, item.replyId)">物资签收</span>
                      <span class="notReceive" v-if="check.receiveStatus === '0'">没有收到</span>
                      <span class="received" v-if="check.receiveStatus === '1'">已签收</span>
                    </div>
                    <div :class="['checkResult', check.checkResult === '1' ? 'pass' : 'refuse']"></div>
                  </div>
                </div>
              </template>
              <div class="teamSignIn-info" v-if="!item.resourceApply || item.resourceApply.length === 0">
                <p>{{ item.replyFrom === 'team' ? item.teamName : '省应急厅' }}</p>
                <div class="teamSignIn-boxes" v-if="item.replyText">
                  <span>{{ item.replyText }}</span>
                </div>
                <file-preview :direction="item.replyFrom === 'team' ? 'right' : 'left'" :fileList="item.attachmentList"></file-preview>
              </div>
            </div>
          </div>
        </template>
      </van-pull-refresh>
    </div>
    <card-tag
      :detailInfo="detailInfo"
      :locationInfo="locationInfo"
      @onClickFeedback="onClickFeedback"
      :listTagShow="listTagShow"
      :teamInfo='teamInfo'
      :feedback="feedback"
    ></card-tag>
    <div class="footer-send">
      <!-- 录音时显示音波图和时长 -->
      <div class="begin-record" v-if="beginRecoding">
        <div class="audio-pie_audio--times" v-if="nowDuration">
          {{ nowDuration }}
        </div>
        <div v-show="false" class="audio-pie_audio--osc">
          <canvas id="canvas"></canvas>
        </div>
        <div v-if="drawColuList.length > 0" class="audio-pie_audio--osc">
          <div class="audio-pie_audio--osc_item" v-for="(item, idx) in drawColuList" :key="idx" :style="{ height: item + 'px' }"></div>
        </div>
      </div>
      <div class="first">
        <van-icon name="volume-o" class="audio-icon" v-if="!isAudio" size="20px" @click="changeType" />
        <van-icon name="more-o" v-else @click="changeType" size="20px" />
      </div>
      <div class="second">
        <van-cell-group :style="{ bottom: inputBottom + 'px', width: '100%' }">
          <van-field v-model="replyObj.replyText" @focus="focusTextarea" v-if="!isAudio" />
          <div class="second-audio" v-else @touchstart.prevent="touchstart" @touchend.prevent="touchend" @touchmove.prevent="touchmove">
            {{ beginRecoding ? '松开发送' : '按住说话' }}
          </div>
        </van-cell-group>
      </div>
      <van-icon class="icon_add" name="add-o" @click="uploadLayer" size="20px" />
      <div class="thred" v-if="replyObj.replyText">
        <van-button type="primary" @click="submit">发送</van-button>
      </div>
    </div>
    <van-popup
      v-model="show"
      position="bottom"
      :close-on-click-overlay="false"
      @click-overlay="show = false"
      @click-close-icon="show = false"
    >
      <div class="upload-main">
        <div class="upload-header">
          <span class="upload-title">附件</span>
        </div>
        <div class="upload-content">
          <FileUpload @submitFile="submitFile" @sendLocation="sendLocation" :locationInfo="locationInfo"></FileUpload>
        </div>
      </div>
    </van-popup>
    <popFeedback
      :showFeedbackPop="showFeedbackPop"
      :teamInfo="teamInfo"
      :feedbackType="feedbackType"
      :curFeedbackTypeInfo="curFeedbackTypeInfo"
      :locationInfo="locationInfo"
      @closeFeedback="closeFeedback"
      @handleSureFeedback="handleSureFeedback"
    ></popFeedback>

    <van-popup v-model="showCode" v-if="showCode" position="right" :style="{ height: '100%', width: '100%' }">
      <card-passcode :teamInfo="teamInfo" :locationInfo="locationInfo" autoCreate="true" :eventInfo="detailInfo" @back="back"></card-passcode>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
import apiServer from '@/api/request-service';
import { getLocInfo, openGps } from '@/utils/location';
import { Notify, ImagePreview, Toast } from 'vant';
import popFeedback from './popFeedback.vue';
import BaseMap from '@/components/baseMap/baseMap.vue';
// import { HZRecorder } from "@/utils/HZRecorder";
import FilePreview from '@/components/filePreview.vue';
import FileUpload from '@/components/fileUpload.vue';
// import CardFeedback from "./cardFeedback.vue";
import CardTask from './cardTask.vue';
import CardPasscode from './cardPasscode.vue';
import CardTag from './cardTag.vue';
import CardSign from './cardSign.vue';
import CardSigned from './cardSigned.vue';
import Recorder from 'js-audio-recorder';
import lamejs from 'lamejs';
import gisAbility from '@/utils/gis/gisAbility';
let egis = require('egis-2d');
import gisConfig from '@/components/baseMap/gisConfig.json';
import { customBrowserVersion, guide } from '@/utils/getLocation';

@Component({
  components: {
    Header,
    popFeedback,
    BaseMap,
    FilePreview,
    FileUpload,
    // CardFeedback,
    CardTask,
    CardPasscode,
    CardTag,
    CardSign,
    CardSigned
  }
})
export default class teamSignIn extends Vue {
  authData: any = {}; // 任务授权信息
  detailInfo: any = {
    eventId: '',
    eventTitle: '',
    nowPage: 1,
    pageSize: 20,
    taskId: '',
    teamId: '',
    teamTaskStatusCd: '',
    districtCode:'',
    eventTypeCode:'',
    relName:''
  };
  percentage = '0';
  showTeamSign: boolean = false; // 是否显示签到
  taskList: any = []; // 任务列表
  curTask: any = {}; // 当前定位任务
  curTaskInd: number = 0; // 当前定位任务index
  public refreshing: boolean = false; // 查看历史回复列表loading
  public historyReplyList: any = []; // 历史回复消息列表
  public newsReplyList: any = []; // 新消息列表
  public replyList: any = []; // 总的回复列表
  public todayStamp: any = this['$moment']().startOf('day').format('X'); // 以当天零点时间戳获取区分历史消息和新消息
  replyObj: any = {
    // 直接回复内容
    replyText: '',
    attachmentList: []
  };
  show = false;
  lists: any = {
    emTeamTaskResultDTO: {}
  };
  showSign = false; // 显示抵达签到
  showFeedbackPop: boolean = false; // 展示反馈弹窗
  feedbackType: string = ''; // 反馈弹窗类型
  curFeedbackTypeInfo: any = {}; // 当前选中反馈类型
  feedback: any = []; // 反馈类型
  teamInfo: any = {}; //
  locationInfo: any = {
    getLocSuccess: false, // 获取当前位置是否成功
    locInfo: {
      longitude: '',
      latitude: '',
      address: ''
    } as any // 当前位置
  };
  inputBottom = 0;

  // 是否录音
  isAudio = false;
  // 是否开始录音
  audioStart = false;
  beginRecoding = false; // 开始录音
  blackBoxSpeak = false; // 是否上滑取消
  startY = ''; // 手指滑动开始Y轴坐标
  timeOutEvent = null; // 定时器
  waveCanvas = null;
  ctx = null;
  recorder = null; // 音频录入的实例
  drawRecordId = null; // 音频绘画波动图时的音频id
  nowDuration = null; // 当前时长
  limitDuration = 60; // 限制时长，单位-秒
  drawColuList = []; // 录音时的柱状波动图，数据集合
  curAudioTime = 0;

  // 标签是否显示
  listTagShow = {};

  focusTextarea(e) {
    this.inputBottom = e.target.height / 2;
    console.log('++++++++++++++++>>>>>>', e.target.height);
  }
  created() {
    this.getLocation();
    this.getShortcutMenuList();
    this.getPermission();
  }
  getCurrentLocation() {
    //   统一使用
  }

  async mounted() {
    this.initAudio();
    // window.addEventListener('scroll', this.handleScroll,true);
    this.authData = JSON.parse(localStorage.getItem('authData')) || {}; // 获取授权信息
    let eventInfo = JSON.parse(sessionStorage.getItem('eventInfo')) || {};
    this.detailInfo.eventId = this.$route.params.id;
    this.detailInfo.districtCode = this.$route.query.districtCode;
    this.detailInfo.teamId = eventInfo.teamId || this.authData.teamId || localStorage.getItem('teamId');
    this.detailInfo.eventTitle = eventInfo.eventTitle || this.authData.eventTitle;
    this.detailInfo.eventTypeCode =  this.$route.query.eventTypeCode;
    this.websocket();
    this.init();
    if (this['wxUtils'].isWeiXin()) {
      this['wxUtils']
        .getLocation()
        .then((res) => {
          let { latitude, longitude } = res;
          this.getLocationCode(`${longitude},${latitude}`);
        })
        .catch((ex) => {
          console.log(ex, 'eeeee');
        });
    }
  }
  private getLocation() {
    if (!this['wxUtils'].isWeiXin()) {
      if (navigator.geolocation) {
        console.log('获取经纬度');
        navigator.geolocation.getCurrentPosition(this.showPosition, this.showError);
      } else {
        alert('当前浏览器不支持地理定位');
      }
    } else {
      // alert('当前是微信环境')
      //  console.log(this['wxUtils'].getLocation(),'aaaaa') ;
    }
  }
  private async showPosition(position) {
    console.log('获取经纬度成功-------》', position);
    let location_lon = position.coords.longitude;
    let location_lat = position.coords.latitude;
    this.locationInfo.locInfo.longitude = location_lon;
    this.locationInfo.locInfo.latitude = location_lat;
    console.log('当前位置----------》' + position.coords.address, '经度：' + location_lon, '纬度：' + location_lat);
    // await gisAbility.getLocationCode(`${location_lon},${location_lat}`);
    this.getLocationCode(`${location_lon},${location_lat}`);
    // let locInfo = JSON.parse(window.localStorage.getItem("locInfo")) || {};
    // this.locationInfo.locInfo = locInfo;
  }

  private sendLocation() {
    if (this.locationInfo.locInfo) {
      if (this.locationInfo.locInfo.latitude && this.locationInfo.locInfo.longitude) {
         let param = {
            latitude: this.locationInfo.locInfo.latitude,
            longitude: this.locationInfo.locInfo.longitude,
            replyAddress: this.locationInfo.locInfo.address,
            replyText:this.locationInfo.locInfo.address,
            attachmentList: [],
            replyTypeCode: 10, // 回复类型编码，0：一般回复，1：任务结束回复
            teamId: this.detailInfo.teamId,
            eventId: this.detailInfo.eventId,
            replyFrom: 'team',
            timestamp: false
    };
    taskRequest.reply(param, (res) => {
      console.log('+++++++++huifu', res);
     this.getReplyListNews();
      this.replyObj.replyText = '';
      this.replyObj.attachmentList = [];
      this.show = false;
      setTimeout(() => {
        (this.$refs['myScrollbar'] as any).scrollTop = (this.$refs['myScrollbar'] as any).scrollHeight + 500;
      }, 400);
      this['$socketApi'].onsend({
        eventId: this.detailInfo.eventId,
        requestFrom: 'app'
      });
    });
     
    }
    } else {
      this.show = false;
    }
  }

  private guideLoc(signMap, loc) {
    let self = this;
    if (this.locationInfo.locInfo.latitude && this.locationInfo.locInfo.longitude) {
      //景点位置partnerAddress 景点经纬度lng lat
      var lng = this.locationInfo.locInfo.longitude;
      var lat = this.locationInfo.locInfo.latitude;
      let address = this.locationInfo.locInfo.address;
      let locInfo = {
        lng,
        lat,
        address
      };
      guide('bd', locInfo);
    } else {
      Notify({ type: 'danger', message: '请先获取当前位置' });
    }
  }

  private getLocationCode(location) {
    //构造逆地理编码服务对象
    let WRGSService = new egis.ews.RestWRGSService({
      url: gisConfig.internetEgisConfig.url, //服务
      clientId: gisConfig.internetEgisConfig.clientId, //用户id
      clientSecret: gisConfig.internetEgisConfig.clientSecret, //用户密码
      authType: gisConfig.internetEgisConfig.authType,
      tokenUrl: gisConfig.internetEgisConfig.tokenUrl
    });
    let WRGSInput = new egis.ews.WRGSInput({
      // location: '116.4091658,39.9580264'
      location: location
    });
    let WRGSInputCode: any = WRGSService.regeocode(WRGSInput);
    WRGSInputCode.then((data) => {
      // locationInfo = data;
      let locInfo: any = {};
      locInfo.longitude = data.location.x;
      locInfo.latitude = data.location.y;
      locInfo.address = data.formatted_address;
      window.localStorage.setItem('locInfo', JSON.stringify(locInfo));
      // let locInfo = JSON.parse(window.localStorage.getItem("locInfo")) || {};
      this.locationInfo.locInfo = locInfo;
      // return new Promise((resolve,reject)=>{
      //   resolve(locInfo)
      // })
      console.log('逆编码获取地理位置----->locInfo', locInfo);
    });
  }
  private showError(error) {
    console.log('获取经纬度失败-------》', error);
    switch (error.code) {
      case error.TIMEOUT:
        alert('请求超时！请再试一次!');
        break;
      case error.POSITION_UNAVAILABLE:
        alert('我们找不到你的位置 Sorry!  必须https才能访问定位');
        break;
      case error.PERMISSION_DENIED:
        alert('请允许地理位置访问！');
        break;
      case error.UNKNOWN_ERROR:
        alert('发生了未知错误！');
        break;
      default:
        alert('获取地理位置放生了未知错误');
        break;
    }
  }
  // 获取按钮权限
  getShortcutMenuList() {
    taskRequest.getShortcutMenuList({}, (res) => {
      if (res.data.status === 200) {
        let list = {};
        res.data.data.forEach((ele) => {
          list[ele.menuId] = ele.isShow;
        });
        this.listTagShow = list;
      } else {
        this.listTagShow = {};
      }
    });
  }

  // 录音初始化
  private initAudio() {
    this.hasPermissions();
    // 创建录音实例
    this.recorder = new Recorder({
      sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
      sampleRate: 48000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值
      numChannels: 1 // 声道，支持 1 或 2， 默认是1
      // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
    });
    // 监听录音变化
    const vm = this;
    this.recorder.onprogress = function (params) {
      console.log(params.duration);
      if (Math.floor(params.duration) >= this.limitDuration) {
        vm.touchend();
      }
      let d: any = Math.floor(params.duration);
      vm.curAudioTime = params.duration;
      d = Number(d) < 10 ? '0' + d : d;
      d = '0:' + d;
      vm.$set(vm, 'nowDuration', d);
    };
  }
  private websocket() {
    this['$socketApi'].init(this.detailInfo.teamId); // 拉起websocket服务
    window['eventBus'].$on('onsocket', (data: any) => {
      console.log('数据接收1212+++++++++++>>>>', data);
      if (JSON.parse(data.data).msg === 'reload') {
        this.getReplyListNews(); // 获取最新回复列表
      }
    });
  }

  private docList:any = [];
  private async getDocList(){
       taskRequest.getOrderDoc({
        eventId:this.detailInfo.eventId,
        status:1
    },(res)=>{
        this.docList =  res.data.data.list||[];
    })
  }
  private relName = '';

  private async init() {
    console.log('$route------------>', this.$route);
    this.replyList = [];
    this.historyReplyList = [];
    this.newsReplyList == [];
   await this.getTeamInfo(); // 获取当前事件下队伍信息
    // this.getTasks(); // 获取任务列表
    await this.getReplyListHistory(); // 获取当天零点前历史回复列表
   await this.getFeedbackType(); // 获取事件下反馈类型
   await this.getDocList();
    setTimeout(() => {
      this.getReplyListNews(); // 获取当天零点后数据
    }, 500);
  }
  private handleScroll() {
    window.scroll(0, 0);
  }
  private async getFeedbackType() {
    taskRequest.getFeedbackType({ eventId: this.detailInfo.eventId }, (res) => {
      if (res.data.status === 200) {
        console.log('getFeedbackType----->', res);
        this.feedback = res.data.data || [];
      } else {
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    });
  }
  // 获取事件下队伍信息，包含是否签到、通行码信息
  private async getTeamInfo() {
    taskRequest.getTeamDetail(
      {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId
      },
      (res) => {
        if (res.data.status === 200) {
          this.teamInfo = res.data.data;
          if(!this.detailInfo.districtCode){
               this.detailInfo.districtCode = res.data.data.districtCode;
          }
          this['$watermark'].set(`${this.teamInfo.teamName || ''} ${this.teamInfo.relMobile || ''}`); // 水印
          if (!this.teamInfo.passCode) {
            // 没有通行码则生成通行码
            this.getPassCode();
          }
          if (this.teamInfo.isSign === '0') {
            // 未签到时调取签到接口
            this.teamSign(); // 5公里范围内可签到
          }
        } else {
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      }
    );
  }
  // 获取通行码
  private getPassCode() {
    let params = {
      codeType: '0', // 生成的二维类型:0、通行码；1、签到码；2、任务码
      eventId: this.detailInfo.eventId,
      teamId: this.detailInfo.teamId
    };
    taskRequest.getPassCode(params, (res) => {
      if (res.data.status === 200) {
        this.teamInfo.passCode = res.data.data;
      } else {
        Notify({ type: 'danger', message: res.data.msg || '生成通行码失败' });
      }
    });
  }
  // 队伍签到
  async teamSign() {
    await this.getLocation(); // 获取当前定位
    // if (!this.locationInfo.locInfo.latitude || !this.locationInfo.locInfo.longitude) {
    //   Notify({ type: 'danger', message: '获取当前位置失败，请检查定位权限是否打开' });
    //   return false;
    // }
    let distance = this.getDistance();
    if (distance <= 5) {
      // 5公里范围内可签到
      this.showTeamSign = true;
      let params = {
        eventId: this.detailInfo.eventId,
        teamId: this.detailInfo.teamId,
        latitude: this.locationInfo.locInfo.latitude,
        longitude: this.locationInfo.locInfo.longitude,
        signAddr: this.locationInfo.locInfo.address
      };
      taskRequest.teamSign(params, (res) => {
        if (res.data.status === 200) {
          Notify({ type: 'success', message: res.data.msg || '签到成功' });
          this.getTeamInfo(); // 签到成功后更新队伍签到状态
        } else {
          Notify({ type: 'danger', message: res.data.msg || '签到失败' });
        }
      });
    } else {
      // Notify({ type: 'danger', message: '请达到指定范围内再签到' });
    }
  }
  private paging: any = {
    nowPage: 1,
    pageSize: 10,
    total: null
  };
  // 获取历史回复消息数据，查看当前时间戳之前的数据
  public getReplyListHistory() {
    this.refreshing = true;
    let params = {
      endTimestamp: this.todayStamp,
      eventId: this.detailInfo.eventId,
      teamId: this.detailInfo.teamId,
      nowPage: this.paging.nowPage,
      pageSize: this.paging.pageSize
      // "endTimestamp":"","eventId":"c0e1188a828c6e5a0182a951f4d55007",
      // "teamId":"2c92832d84bd9a4b0184bd9eaa4a000d","nowPage":1,"pageSize":10
    };
    taskRequest.getAppReply(params, (res) => {
      if (res.data.status === 200) {
        if (this.historyReplyList.length >= res.data.data.total) {
          this.refreshing = false;
          return false;
        }
        let pageReply = res.data.data.list || [];
        this.historyReplyList = this.historyReplyList.concat(pageReply.reverse()); // 更新历史消息
        this.replyList = [...pageReply, ...this.replyList].filter(item=>{return item.taskType!=10&&item.taskType!=30
        }); // 更新总的回复消息列表

          //  this.replyList = this.moveSameReply([...this.replyList, ...pageReply]).filter((el)=>{return (el.taskType!=10&&el.taskType!=30)});

        // .filter((item)=>{return 
        // (item.taskType!=10&&item.taskType!=30)}); // 更新总的回复消息列表
        console.log('++++historyReplyList---->', this.historyReplyList, this.replyList);
        this.$nextTick(() => {
          if (this.paging.nowPage === 1) {
            (this.$refs['myScrollbar'] as any).scrollTop = (this.$refs['myScrollbar'] as any).scrollHeight + 200;
          }
          this.refreshing = false;
          this.paging.total = res.data.data.total;
          this.paging.nowPage++;
        });
      } else {
        Notify({ type: 'danger', message: res.data.msg || '获取回复数据失败' });
        this.refreshing = false;
      }
    });
  }
  // 获取最新回复数据，当前时间戳之后的数据
  public getReplyListNews() {
    let params = {
      startTimestamp: this.todayStamp,
      eventId: this.detailInfo.eventId,
      teamId: this.detailInfo.teamId,
      nowPage: 1,
      pageSize: 1000
    };
    taskRequest.getAppReply(params, (res) => {
      this.newsReplyList = [];
      if (res.data.status === 200) {
        let pageReply = res.data.data.list || [];
        let fakeData = this.replyList.filter((el) => {
          return el.isFakeData;
        });
        console.log('本地当前的视频list', fakeData);
        for (let i = 0; i < fakeData.length; i++) {
          for (let j = 0; j < pageReply.length; j++) {
            if (fakeData[i].timestamp == pageReply[j].timestamp) {
              if (pageReply[j].attachmentList) {
                pageReply[j].hide = true;
                continue;
              }
            }
          }
        }
        pageReply = pageReply.filter((el) => {
          return !el.hide;
        });
        console.log('过滤掉本地视频后的消息list', pageReply);
        console.log(this.replyList, '本地list数据');
        this.newsReplyList = this.newsReplyList.concat(pageReply.reverse());
        let replyListNew = [...this.replyList, ...pageReply];
        console.log('未去重前的数据', replyListNew);
        // ((el)=> !el.hide  && el.attachmentList))
        // this.replyList = this.moveSameReply(replyListNew);
        this.replyList = this.moveSameReply([...this.replyList, ...pageReply]).filter((el)=>{return (el.taskType!=10&&el.taskType!=30)});
        // this.replyList = this.moveSameReply(replyListNew).filter((el)=>{
        //   return !el.isFakeData
        // });
        let replyArr = this.replyList.filter((item) => item.replyTypeCode === 'feedback');
        let diffTime =
          replyArr.length === 0
            ? 11
            : this['$moment'](this['$moment']().format('YYYY-MM-DD HH:mm:ss')).diff(
                this['$moment'](replyArr[replyArr.length - 1].sendTime),
                'minute'
              );
        console.log('diffTime+++++++++>>>', diffTime);
      //  this.handleScroll();
     setTimeout(() => {
        (this.$refs['myScrollbar'] as any).scrollTop = (this.$refs['myScrollbar'] as any).scrollHeight + 500;
      }, 400);
  } else {
        Notify({ type: 'danger', message: res.data.msg || '获取回复数据失败' });
      }
    });
  }
  // 获取当天消息去重
  private moveSameReply(arr) {
    const res = new Map();
    let list = [];
    return arr.filter((el) => !res.has(el.replyId) && res.set(el.replyId, el.replyId));
    // for(let i=0;i<arr.length;i++){
    //     if(arr[i].replyId){
    //         if(list.length){
    //             for(let j=0;j<list.length;j++){
    //                 if(list[j].replyId == arr[i].replyId){
    //                     // continue
    //                 } else{
    //                   list.push(arr[i])
    //                 }
    //             }
    //         } else{
    //               list.push(arr[i])
    //         }
    //     } else {
    //       list.push(arr[i])
    //     }
    // }
    // return list;
  }
  private getTasks() {
    taskRequest.getTeamTaskList(this.detailInfo, (res) => {
      if (res.data.status === 200) {
        this.taskList = res.data.data.list;
        [this.curTask] = this.taskList;
        console.log('+++++++++>>>curTask', this.curTask);
      }
    });
  }
  async uploadLayer() {
    await this.getLocation();
    this.show = true;
    this.stopRecorder();
  }
  onOversize(file) {
    console.log('文件大小超过限制 onOversize--------->', file);
  }
  //上传
  afterRead(file, audio = false) {
    console.log('2222222222211111afterRead', file);
    let _this = this;
    const typeAudio = typeof audio === 'boolean';
    file.status = 'uploading';
    file.message = '上传中...';
    // setTimeout(() => {
    //   file.status = 'failed';
    //   file.message = '上传失败';
    //   return false;
    // }, 15000);
    const formData = new FormData();
    if (typeAudio && audio) {
      let number: any = Math.ceil(Math.random() * 100000);
      formData.append('file', file.file, number + '.mp3');
    } else {
      formData.append('file', file.file);
    }
    apiServer.uploadFile(formData, function (_resultObj) {
      if (_resultObj.status == 200) {
        file.status = 'done';
        file.message = '上传完成';
        file.attachmentList = _resultObj.data;
        console.log('999999999999999>>>>>>', file.attachmentList, _this.curAudioTime);
        if (typeAudio && audio) {
          file.attachmentList.msg = Math.ceil(_this.curAudioTime);
          _this.submit([file.attachmentList]);
        }
      }
    });
  }
  private timer = null;
  private uploadVideoStatus = false;
  private progressFn(file) {
    let percentage = 0;
    this.timer = setInterval(() => {
      let isDone = false;
      this.replyList.map((el, index) => {
        if (el.isFakeData) {
          for (let i = 0; i < el.attachmentList.length; i++) {
            for (let j = 0; j < file.length; j++) {
              if (el.attachmentList[i].timeStammp == file[j].timeStammp) {
                percentage = percentage + Math.ceil(Math.random());
                if (percentage > 100) {
                  clearInterval(this.timer);
                  isDone = true;
                  return;
                } else {
                  if (el.attachmentList[i].timeStammp == file[j].timeStammp) {
                    this.$set(this.replyList[index].attachmentList[i], 'percentage', percentage);
                  }
                }
              }
            }
            // if(isDone){
            //   break;
            // }
          }
        }
      });
    }, 600);
  }

  private timestamp: any = '';

  // 附件上传附件回复
  async submitFile(file, flag?) {
    console.log(file);
    await this.getLocation();
    this.timestamp = new Date().getTime();
    this.stopRecorder();
    console.log('附件上传附件回复--->', file);
    this.replyObj.attachmentList = [];
    file.forEach((item) => {
      console.log(item.status);
      this.replyObj.attachmentList.push({
        attachmentList: item
      });
    });
    let param = {
      latitude: this.locationInfo.locInfo.latitude,
      longitude: this.locationInfo.locInfo.longitude,
      replyAddress: this.locationInfo.locInfo.address,
      replyText: this.replyObj.replyText,
      attachmentList: file,
      replyTypeCode: '0', // 回复类型编码，0：一般回复，1：任务结束回复
      teamId: this.detailInfo.teamId,
      eventId: this.detailInfo.eventId,
      replyFrom: 'team',
      teamName: this.teamInfo.teamName,
      isFakeData: true,
      timestamp: file && file[0].timeStammp,
      replyId: Math.floor(Math.random() * 10000000)
    };
    if (!flag) {
      window.clearInterval(this.timer);
      this.submit();
      this.replyList.map((el, index) => {
        if (el.isFakeData) {
          console.log(el);
          //  for(let i=0;i<el.attachmentList.length;i++){
          //       for(let j=0;j<file.length;j++){
          //           if(el.attachmentList[i].timeStammp == file[j].timeStammp){
          //             this.$set(this.replyList[index].attachmentList[i],'percentage','100');
          //             console.error('上传100%')
          //           }
          //       }
          //   }
          if (el.timestamp == file[0].timeStammp) {
            this.$set(this.replyList[index].attachmentList[0], 'percentage', '100');
            console.error('上传100%');
          }
        }
      });
      console.warn(this.replyList);
    } else {
      this.replyList.push(param);
      this.show = false;
      this.$nextTick(() => {
        (this.$refs['myScrollbar'] as any).scrollTop = (this.$refs['myScrollbar'] as any).scrollHeight + 500;
        this.progressFn(file);
      });
    }
    // this.replyObj.attachmentList;
    //
  }
  //回复
  async submit(audioList = null) {
    await this.getLocation();
    // await getLocInfo(this.locationInfo); // 获取当前定位
    if (!this.replyObj.replyText && this.replyObj.attachmentList.length === 0 && !audioList) {
      return false;
    }
    // if (!this.locationInfo.locInfo.latitude || !this.locationInfo.locInfo.longitude) {
    //   Notify({ type: 'danger', message: '获取当前位置失败，请检查定位权限是否打开' });
    //   return false;
    // }
    let files: any = [];
    if (!this.replyObj.replyText) {
      this.replyObj.attachmentList?.forEach((el) => {
        files.push(el.attachmentList);
      });
    }

    let exitVideoflag = files.some((el) => {
      if (/(.*)\.(mp4|avi|MOV|mov|rmvb|rm|3gp)$/.test(el.name)) {
        return true;
      } else {
        return false;
      }
    });
    let param = {
      latitude: this.locationInfo.locInfo.latitude,
      longitude: this.locationInfo.locInfo.longitude,
      replyAddress: this.locationInfo.locInfo.address,
      replyText: this.replyObj.replyText,
      attachmentList: Array.isArray(audioList) ? audioList : files,
      replyTypeCode: this.feedbackType || '0', // 回复类型编码，0：一般回复，1：任务结束回复
      teamId: this.detailInfo.teamId,
      eventId: this.detailInfo.eventId,
      replyFrom: 'team',
      timestamp: files && files.length > 0 && files[0].timeStammp
    };
    taskRequest.reply(param, (res) => {
      console.log('+++++++++huifu', res);
      if (!exitVideoflag) {
        this.getReplyListNews();
      }
      console.error(this.replyList);
      this.replyObj.replyText = '';
      this.replyObj.attachmentList = [];
      this.show = false;
      this['$socketApi'].onsend({
        eventId: this.detailInfo.eventId,
        requestFrom: 'app'
      });
      // if (res.data.status === 200) {
      //   this.getReplyListNews();
      //   this.replyObj.replyText = "";
      //   this.replyObj.attachmentList = [];
      //   this.show = false;
      //   this["$socketApi"].onsend({
      //     eventId: this.detailInfo.eventId,
      //     requestFrom: "app",
      //   });
      // } else {
      //   Notify({ type: "danger", message: res.data.msg || "消息发送失败" });
      // }
    });
  }
  //结束任务
  overTask(item) {
    this.showFeedbackPop = true;
    this.feedbackType = 'overTask';
    this.teamInfo = item;
  }
  // 队伍任务签到成功
  onTeamSign(data) {
    // this.refreshReplyList();
    console.log('队伍任务签到成功---->', data);
    this.replyList.forEach((item) => {
      // 避免全部刷新
      if (item.teamTask && item.teamTask.taskId === data.teamTask.taskId) {
        item.teamTask.teamTaskStatusCd = '10'; // 未签到状态改为进行中状态
      }
    });
    this.getReplyListNews();
  }
  // 刷新界面回复信息
  async refreshReplyList() {
    this.replyList = [];
    this.historyReplyList = [];
    this.newsReplyList == [];
    await this.getReplyListHistory(); // 获取当天零点前历史回复列表
    setTimeout(() => {
      this.getReplyListNews(); // 获取当天零点后数据
    }, 500);
  }
  // hanldPreview(item) {
  //   ImagePreview([item.path]);
  // }
  onClickFeedback(item) {
    // 点击回复类型
    // if (item.replyTypeCode !== '4') {
    this.showFeedbackPop = true;
    // }
    this.feedbackType = item.replyTypeCode;
    this.curFeedbackTypeInfo = item;
  }
  // 反馈弹窗点击确认
  handleSureFeedback() {
    if (this.feedbackType === 'overTask') {
      // 结束任务反馈
      this.replyList.forEach((item) => {
        // 避免全部刷新
        if (item.teamTask && item.teamTask.taskId === this.teamInfo.teamTask.taskId) {
          item.teamTask.teamTaskStatusCd = '50'; // 状态改为已完成
        }
      });
    }
    // this.refreshReplyList();
    this.getReplyListNews(); // 获取最新回复信息
    this.closeFeedback();
  }
  closeFeedback() {
    this.showFeedbackPop = false;
    setTimeout(() => {
      this.feedbackType = '';
      this.curFeedbackTypeInfo = {};
    }, 500);
  }
  // 经纬度转换成三角函数中度分表形式。
  rad(d) {
    return (d * Math.PI) / 180.0;
  }
  // 根据经纬度计算距离，参数分别为第一点的纬度，经度；第二点的纬度，经度
  getDistance() {
    let lat1 = this.locationInfo.locInfo.latitude;
    let lng1 = this.locationInfo.locInfo.longitude;
    let lat2 = this.teamInfo.latitude;
    let lng2 = this.teamInfo.latitude;
    let radLat1 = this.rad(lat1);
    let radLat2 = this.rad(lat2);
    let a = radLat1 - radLat2;
    let b = this.rad(lng1) - this.rad(lng2);
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    s = s * 6378.137; // EARTH_RADIUS;
    s = Math.round(s * 10000) / 10000; //输出为公里

    let distance: any = s;
    let distance_str: any = '';

    if (parseInt(distance) >= 1) {
      distance_str = distance.toFixed(1) + 'km';
    } else {
      distance_str = distance * 1000 + 'm';
    }
    console.info('lyj 距离是s---->', s);
    console.info('lyj 距离是distance_str--->', distance_str);
    return distance;
  }
  handleSignResource(item, replyId) {
    taskRequest.resourceSign({ id: item.id }, (res) => {
      if (res.data.status === 200) {
        this.replyList.forEach((el) => {
          // 避免全部刷新
          if (el.replyId === replyId) {
            el.resourceApply.forEach((j) => {
              if (j.id === item.id) {
                j.receiveStatus = '1'; // 改为已签收状态
              }
            });
          }
        });
        this.getReplyListNews();
        this['$socketApi'].onsend({
          eventId: this.detailInfo.eventId,
          requestFrom: 'app'
        });
      } else {
        Notify({ type: 'danger', message: res.data.msg || '签收失败' });
      }
    });
  }

  // -------------------语音消息录入-----------------------
  // 更改消息输入状态
  changeType() {
    this.isAudio = !this.isAudio;
  }

  // ---------app获取麦克风权限------------
  hasPermissions() {
    const that = this;
    this['$gsmdp'].hasPermissions({
      data: ['android.permission.RECORD_AUDIO'],
      success(result) {
        if (!result) {
          that.getPermission();
          // that.getPermission();
        }
      },
      fail(err) {
        that.getPermission();
        console.log(err);
      }
    });
  }

  permissions() {
    this['$gsmdp'].permissions({
      data: ['android.permission.RECORD_AUDIO'],
      success(result) {
        // alert(JSON.stringify(result))
      },
      fail(err) {
        console.log(err);
      }
    });
  }
  // -------app获取麦克风权限-------------

  // pc获取麦克风权限
  getPermission() {
    (Recorder as any).getPermission().then(
      () => {
        console.log('录音给权限了');
      },
      (error) => {
        console.log(`${error.name} : ${error.message}`);
      }
    );
  }
  // 波浪图canvas 配置
  startCanvas() {
    // 录音波浪
    this.waveCanvas = document.getElementById('canvas');
    this.waveCanvas.width = 145;
    this.waveCanvas.height = 44;
    this.ctx = this.waveCanvas.getContext('2d');
  }
  // 手指开始触发
  touchstart(e) {
    this.blackBoxSpeak = true;
    this.startY = e.touches[0].clientY;
    this.timeOutEvent = 0;
    // 长按1000毫秒后执行
    this.timeOutEvent = setTimeout(() => {
      this.startRecorder();
      this.beginRecoding = true;
    }, 500);
    return false;
  }
  // 手指离开屏幕触发
  touchend() {
    const that = this;
    // 清空定时器
    clearTimeout(this.timeOutEvent);
    console.log('手指离开屏幕触发', this.timeOutEvent, this.blackBoxSpeak, this.curAudioTime);
    if (this.timeOutEvent !== 0) {
      this.beginRecoding = false;
      // 长按结束调用保存录音或者返回录音数据
      console.log('+++++++长按结束调用保存录音或者返回录音数据', this.blackBoxSpeak, this.curAudioTime);
      if (this.curAudioTime >= 1) {
        console.log('11111111111111111111111afterRead');
        that.afterRead({ file: this.convertToMp3(this.recorder.getWAV()) }, true);
      } else if (this.curAudioTime < 1) {
        that.$toast('说话时间太短');
      }
      this.nowDuration = null;
      this.drawColuList = [];
    }
    this.stopRecorder(); // 停止录音
  }
  // 滑动触发
  touchmove(e) {
    const endY = e.touches[0].clientY;
    this.blackBoxSpeak = this.startY > endY;
  }
  // 长按超过x毫秒-- 开始录音
  startRecorder() {
    this.initAudio();
    this.recorder.start().then(
      () => {
        this.beginRecoding = true;
        // this.drawRecordWave();//开始绘制
        this.drawRecordColu(); // 开始绘制
        console.log('开始录音------>', this.curAudioTime);
      },
      (error) => {
        console.log(`${error.name} : ${error.message}`);
      }
    );
  }
  // 录音绘制柱状图
  drawRecordColu() {
    // 用requestAnimationFrame稳定60fps绘制(官方写法，录音过程中，会一直自我调用，因此能得到持续的数据，然后根据数据绘制音波图)
    this.drawRecordId = requestAnimationFrame(this.drawRecordColu);
    // 实时获取音频大小数据
    const dataArray = this.recorder.getRecordAnalyseData();
    const transit = [];
    this.splitArr([...dataArray], transit);

    const rstArr = [];
    for (let i = 0; i < transit.length; i++) {
      rstArr.push(Math.max(...transit[i]));
    }
    this.drawColuList = [];
    for (let i = 0; i < rstArr.length; i++) {
      // var v = rstArr[i] / 128.0;
      // var h = v * this.waveCanvas.height / 3;
      // this.drawColuList.push(h)
      // 根据数值大小，设置音波柱状的高度
      const newDb = rstArr[i];
      let waveH = 10;
      if (newDb >= 128 && newDb <= 140) {
        waveH = 15;
      } else if (newDb >= 141 && newDb <= 160) {
        waveH = 20;
      } else if (newDb >= 161 && newDb <= 180) {
        waveH = 25;
      } else if (newDb >= 181 && newDb <= 200) {
        waveH = 30;
      } else if (newDb > 200) {
        waveH = 35;
      }
      this.drawColuList.push(waveH);
    }
    this.$forceUpdate();
  }
  // 拆分数组
  splitArr(arr, rst, idx = 0) {
    if (!arr || arr.length === 0) {
      return;
    }
    rst.push(arr.splice(0, idx || 32));
    this.splitArr(arr, rst);
  }
  // 结束录音
  stopRecorder() {
    if (this.recorder) {
      console.log('结束录音');
      this.recorder.stop();
      this.drawRecordId && cancelAnimationFrame(this.drawRecordId);
      this.drawRecordId = null;
      this.destroyRecorder();
    }
  }
  // 销毁录音
  destroyRecorder() {
    const vm = this;
    if (vm.recorder) {
      vm.recorder.destroy().then(function () {
        vm.recorder = null;
        vm.drawRecordId && cancelAnimationFrame(vm.drawRecordId);
        vm.drawRecordId = null;
      });
    }
  }
  // wav转换成mp3格式
  convertToMp3(wavDataView) {
    // 获取wav头信息
    const wav = lamejs.WavHeader.readHeader(wavDataView); // 此处其实可以不用去读wav头信息，毕竟有对应的config配置
    const { channels, sampleRate } = wav;
    const mp3enc = new lamejs.Mp3Encoder(channels, sampleRate, 128);
    // 获取左右通道数据
    const result = this.recorder.getChannelData();
    const buffer = [];

    const leftData = result.left && new Int16Array(result.left.buffer, 0, result.left.byteLength / 2);
    const rightData = result.right && new Int16Array(result.right.buffer, 0, result.right.byteLength / 2);
    const remaining = leftData.length + (rightData ? rightData.length : 0);

    const maxSamples = 1152;
    for (let i = 0; i < remaining; i += maxSamples) {
      const left = leftData.subarray(i, i + maxSamples);
      let right = null;
      let mp3buf = null;

      if (channels === 2) {
        right = rightData.subarray(i, i + maxSamples);
        mp3buf = mp3enc.encodeBuffer(left, right);
      } else {
        mp3buf = mp3enc.encodeBuffer(left);
      }

      if (mp3buf.length > 0) {
        buffer.push(mp3buf);
      }
    }
    const enc = mp3enc.flush();
    if (enc.length > 0) {
      buffer.push(enc);
    }
    return new Blob(buffer, { type: 'audio/mp3' });
  }
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll, true);
    this['$watermark'].clear();
    this.destroyRecorder();
  }

  private showCode = false;
  //  亮码操作
  showCodePage() {
    this.showCode = true;
  }

  private back() {
    this.showCode = false;
  }
}
</script>

<style lang="less">
@url: '~@/assets/images/';
.teamSignIn-item {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-content: stretch;
  justify-content: flex-start;
  align-items: baseline;
  padding-bottom: 23px;
  .teamSignIn-user {
    width: 40px;
    height: 40px;
    background: #1a67f2;
    font-size: 14px;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    line-height: 20px;
    margin-right: 7px;
  }
  .teamSignIn-info {
    width: 75%;
    p {
      font-size: 14px;
      color: #666;
    }
    .teamSignIn-box {
      background: linear-gradient(to bottom, #1967f2 0%, #fff 30%);
      border-radius: 5px;
      padding: 0 6px 10px 6px;
      h2 {
        height: 45px;
        line-height: 45px;
        text-align: center;
        font-size: 19px;
        color: #fff;
        margin: 0;
      }
      .teamSignIn-main {
        background: #fff;
        border-radius: 5px;
        padding: 0 6px;
        text-align: center;
        p {
          color: #1c1d1d;
          font-size: 16px;
          text-align: center;
          width: 90%;
          line-height: 24px;
          margin: 0 auto;
          padding-top: 10px;
        }
        .teamSignIn-code {
          width: calc(100% - 60px);
          margin: 12px 30px;
          text-align: center;
          background: url('@{url}/quickResponse/code-border.png') center no-repeat;
          background-size: contain;
        }
        img {
          width: 100%;
          height: 100%;
          padding: 5px;
        }
        .teamSignIn-msg {
          padding-bottom: 10px;
          background: #f0f4fc;
          border-radius: 5px;
          h2 {
            color: #1c1d1d;
            font-size: 16px;
            height: 40px;
            line-height: 40px;
            text-align: left;
            border-bottom: 1px solid #dadada;
            padding: 0 8px;
            i {
              display: inline-block;
              width: 17px;
              height: 17px;
              background: url('@{url}/quickResponse/teamSignIn02.png') center no-repeat;
              background-size: contain;
              position: relative;
              top: 4px;
              margin-right: 10px;
            }
          }
          div {
            display: flex;
            padding: 0 8px;
            margin-top: 10px;
            color: #1c1d1d;
            font-size: 14px;
            text-align: left;
            span:nth-child(1) {
              display: inline-block;
              width: 65px;
              margin-right: 8px;
              color: #666;
            }
          }
        }
      }
    }
    .teamSignIn-boxes {
      background: linear-gradient(to bottom, #fac56b 0%, #fff 30%);
      border-radius: 5px;
      padding: 0 6px 10px 6px;
      h2 {
        height: 45px;
        line-height: 45px;
        font-size: 19px;
        color: #724511;
        margin: 0;
        i {
          display: inline-block;
          width: 26px;
          height: 26px;
          background: url('@{url}/quickResponse/teamSignIn05.png') center no-repeat;
          background-size: contain;
          position: relative;
          top: 5px;
          margin-right: 5px;
        }
        span {
          display: inline-block;
          flex: 1;
        }
        &.task-title {
          display: flex;
          padding: 10px 0;
          align-items: baseline;
          height: unset;
          line-height: unset;
        }
      }
      .teamSignIn-maines {
        background: #fff;
        border-radius: 5px;
        padding: 5px 6px;
        h2 {
          font-size: 14px;
          color: #666;
          height: 23px;
          line-height: 23px;
          margin-top: 5px;
        }
        p {
          color: #333;
          margin: 0;
        }
        .teamSignIn-maines-p {
          padding-bottom: 10px;
          border-bottom: 1px solid #dadada;
        }
        .map-content {
          background: #f0f4fc;
          border-radius: 5px;
          padding: 12px 8px;
          h2 {
            font-size: 16px;
            color: #333;
            margin: 0px 0;
          }
          p {
            color: #666;
            margin: 0;
          }
        }
      }
      .end-btn {
        .van-button--primary {
          background-color: #326eff;
          border: 1px solid #326eff;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
    .teamSignIn-sign {
      // background: #fff;
      border-radius: 5px;
      padding: 12px;
      background: url('@{url}/quickResponse/bg-sign.png') center no-repeat;
      background-size: cover;
      .sign-box {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-content: center;
        justify-content: space-around;
        align-items: center;
        i {
          display: inline-block;
          width: 28px;
          height: 30px;
          margin-right: 10px;
          background: url('@{url}/quickResponse/teamSignIn03.png') center no-repeat;
          background-size: contain;
        }
        /deep/.van-button--primary {
          width: calc(100% - 50px);
          background-color: #326eff;
          border: 1px solid #326eff;
          border-radius: 4px;
          font-size: 16px;
          font-weight: bold;
          span {
            color: #fff !important;
            margin-top: 0;
          }
        }
      }
    }
    .teamSignIn-signed {
      // background: #fff;
      border-radius: 5px;
      padding: 12px;
      background: url('@{url}/quickResponse/bg-sign.png') center no-repeat;
      background-size: cover;
      .sign-box {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-content: center;
        justify-content: space-around;
        align-items: center;
        i {
          display: inline-block;
          width: 28px;
          height: 30px;
          margin-right: 10px;
          background: url('@{url}/quickResponse/teamSignIn03.png') center no-repeat;
          background-size: contain;
        }
        /deep/.van-button--default {
          width: calc(100% - 50px);
          background-color: #eee;
          border: 1px solid #eee;
          border-radius: 4px;
          font-size: 16px;
          font-weight: bold;
          span {
            color: #999 !important;
            margin-top: 0;
          }
        }
      }
    }
    .teamSignIn-feedback {
      border-radius: 5px;
      padding: 0 6px 10px 6px;
      background: #fff;
      color: #333;
      p {
        padding-top: 10px;
      }
      .feedback {
        border-top: 1px solid #ccc;
        &-item {
          padding: 10px 0;
          font-size: 13px;
          color: #326eff;
          border-bottom: 1px solid #ccc;
          &.active {
            color: #f59a23;
          }
        }
      }
    }
  }
  .signed {
    font-size: 14px;
    color: #999;
    margin-top: 8px;
    i {
      display: inline-block;
      width: 11px;
      height: 11px;
      background: url('@{url}/quickResponse/teamSignIn04.png') center no-repeat;
      background-size: contain;
      position: relative;
      top: 2px;
      margin-right: 5px;
    }
  }
}
.teamSignIn {
  &-content {
    padding: 10px 12px;
    height: calc(100% - 120px);
    overflow: auto;

    .teamSignIn-itemes {
      display: flex;
      padding-bottom: 23px;
      flex-direction: row-reverse;
      align-items: baseline;
      .teamSignIn-user {
        width: 40px;
        height: 40px;
        background: #1a67f2;
        font-size: 14px;
        color: #fff;
        text-align: center;
        border-radius: 5px;
        line-height: 20px;
        margin-right: 7px;
      }
      .feedCard {
        width: 70%;
        align-items: flex-start !important;
        &-container {
          width: 100%;
          position: relative;
          background: #fff;
          border-radius: 5px;
          padding: 10px;
          .title {
            color: #f59a23;
            i {
              margin-right: 8px;
              font-size: 18px;
            }
            span {
              font-weight: 600;
              font-size: 16px;
            }
          }
          .replyText {
            display: block;
            padding: 10px 0;
            font-weight: 600;
            font-size: 18px;
            text-align: start;
          }
          .checkDetail {
            display: flex;
            flex-direction: column;
          }
          .receiveResult {
            margin: 10px 0;
            text-align: center;
            span {
              display: inline-block;
              width: 40%;
              margin: 0 5px;
              padding: 5px 0;
              text-align: center;
              border: 1px dashed #aaa;
              &.sign {
                border-color: #f59a23;
                background: #fde7ca;
                color: #f59a23;
              }
              &.notReceive {
                background: #ececec;
                color: #aaa;
              }
              &.received {
                border-color: #07c160;
                background: #89e1b3;
                color: #07c160;
              }
            }
          }
        }
      }
      .teamSignIn-info,
      .feedCard {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        max-width: 70%;
        p {
          display: flex;
          justify-content: flex-end;
          margin-right: 8px;
          font-size: 14px;
          color: #666;
          font-weight: bold;
          margin-bottom: 10px;
        }
        .teamSignIn-boxes {
          width: max-content;
          max-width: 100%;
          min-height: 40px;
          background: #fff;
          border-radius: 5px;
          padding: 10px;
          margin-right: 8px;
          color: #333;
          font-size: 15px;
          img {
            width: 100%;
          }
          &.onlyImg {
            padding: 0;
            background: unset;
            img {
              border-radius: 5px;
            }
          }
        }
        .replyFile {
          width: 100%;
          margin-top: 10px;
          margin-right: 8px;
          text-align: end;
          img {
            width: 100%;
          }
        }
      }
      &.left {
        flex-direction: inherit;
        .teamSignIn-info {
          align-items: flex-start;
        }
        .replyFile {
          text-align: start;
        }
      }
    }
  }
  .footer-tag {
    align-items: baseline;
    padding: 0 10px;
    padding-bottom: 6px;
    > div {
      display: flex;
      justify-content: space-between;
    }
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80px;
      font-size: 12px;
      // margin-right: 6px;
      border-radius: 20px;
      border: 1px solid #326eff;
      color: #326eff;
      /deep/.van-icon {
        padding-right: 5px;
      }
    }
  }
  .footer-send {
    display: flex;
    align-items: center;
    height: 50px;
    position: relative;
    bottom: 0;
    background: #f5f5f5;
    // height: 75rem;
    display: flex;
    > div {
      display: flex;
      align-items: center;
      height: 100%;
      &.second {
        /deep/.van-cell-group {
          width: 100%;
        }
      }
    }
    .begin-record {
      position: absolute;
      top: -100px;
      left: 50%;
      transform: translateX(-50%);
      padding: 0 10px;
      background-color: #326eff;
      text-align: center;
      border-radius: 5px;
      &::before {
        display: block;
        content: '';
        position: absolute;
        bottom: -3px;
        left: 50%;
        border: 5px solid #326eff;
        border-width: 0 6px 6px 0;
        transform: rotate(-45deg);
      }

      .audio-pie_audio--times {
        width: 50px;
        padding: 5px 0;
        // height: 20px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        // line-height: 20px;
        // background-color: #000;
      }
      .audio-pie_audio--osc {
        width: 100px;
        height: 24px;
        //      background: #6DD400;
        margin: auto 0;
        display: flex;
        align-items: center;

        .audio-pie_audio--osc_item {
          width: 4px;
          background-color: #fff;
          border-radius: 1000px;

          &:not(:first-child) {
            margin-left: 3px;
          }
        }
      }
    }
    .first {
      // width: 25%;
      font-size: 1rem;
      text-align: center;
      position: relative;
      padding: 0 10px;
      .audio-icon {
        border-radius: 50%;
      }
    }
    .second {
      // width: 65%;
      flex: 1;
      margin-right: 10px;
      position: relative;
      .second-audio {
        line-height: 6.4vw;
        padding: 2.66667vw 4.26667vw;
        text-align: center;
      }
    }
    .icon_add {
      margin-right: 10px;
    }
    .thred {
      // width: 20%;
      width: 80px;
    }
  }
  .sendFile {
    margin-bottom: 10px;
    text-align: right;
  }
}
.map {
  width: 100%;
  height: 150px;
  margin-top: 10px;
  background: #eee;
  overflow: hidden;
}
.uploadFile-text {
  margin: 10px 0;
  padding-top: 8px;
  font-weight: 600;
  color: #666;
}
.upload-main {
  padding: 10px 20px;
  .upload-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .upload-title {
      font-weight: 600;
      color: #666;
      font-size: 16px;
    }
    .upload-sendFile {
      display: flex;
      align-items: center;
      .sureBtn {
        color: #326fff;
        font-size: 16px;
      }
    }
  }
}
</style>
