<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"> -->
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <!-- <script src="//cdn.bootcss.com/eruda/1.5.2/eruda.min.js"></script>
    <script>eruda.init();</script> -->
    <script src="https://cdn.bootcdn.net/ajax/libs/axios/0.19.0/axios.js"></script>
    <!-- <link rel="stylesheet" href="https://cdn.bootcss.com/material-design-icons/3.0.1/iconfont/material-icons.css"> -->
    <title>武汉市应急指挥信息系统</title>

    <!-- <script>
      if (typeof require === 'function')  {
        window.electron = require('electron');
        document.addEventListener('dragover', (event) => event.preventDefault());
        document.addEventListener('drop', (event) => event.preventDefault());
      }
    </script> -->
  </head>

  <body>
    <noscript>
      <strong>We're sorry but v-cli-test doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <!-- <script>
      const script = document.createElement("script");
      script.src = "./bridgeWeb.js";
      document.body.appendChild(script);
      const clientId = '049c623cdfea4d5a929192d9b90d38ee';
      window.onload = () => {
    // 监听js能力已准备的回调
    document.addEventListener('onBridgeReady', bridgeReady);

    // 回调函数
    function bridgeReady() {
      // 从app获取一次性免登码
      window.api.requestAuthCode({
        h5AppId: clientId,
      }, (res) => {
        console.log(res,'resss');
        if(res.authCode){
          getUserInfo(res.authCode)
        }
       
      });
    };
     function  getUserInfo(code) {
    const data = new FormData();
    data.append('clientId', this.clientId);
    data.append('code', code);
    // 此处会跨域，获取信息请在项目中使用代理(测试用可在类似postman等软件中使用)
    axios.post(`/mauth/bin/tang/user/info`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
      .then((res) => {
        const user = res.data.data;
        console.log('user is: ', user.username);
        submit(user.username);
        // 拿到用户存储
      })
      .catch((err) => {
        console.log('err is: ', err);
      });
  };
  function  submit(username) {
    const that = this;
    const params = {
      username,
      password: "2sZKK1Icu+qpXzxaMDYVcQ=="
    };
    axios.post('/gemp-user/api/gemp/duty/info/user/login/v1',params, function (res) {
      console.log('!!!!login', res);
      if (res.data.status === 200) {
        localStorage.setItem('token', res.data.data.token);
        localStorage.setItem('role', JSON.stringify(res.data.data.role));
        localStorage.setItem('teamId', res.data.data.teamId);
        axios.defaults.headers.common['token'] = res.data.data.token;
        // that.$router.push('/home');
        window.location.hash ='#/home';
      } else {
        alert(res.data.msg);
        // that.$router.push('/login');
      }
    });
  }
    // 根据code获取用户信息
  }


    </script> -->
    <script>
      (function (doc, win) {
        var docEl = doc.documentElement,
          resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
          recalc = function () {
            var clientWidth = docEl.clientWidth;
            if (!clientWidth) return;
            docEl.style.fontSize = 20 * (clientWidth / 375) + 'px';
          };
        if (!doc.addEventListener) return;
        win.addEventListener(resizeEvt, recalc, false);
        doc.addEventListener('DOMContentLoaded', recalc, false);
      })(document, window);
    </script>
    <!-- <div class="app_bar"></div> -->
    <div id="app"></div>

    <script src="./myConfig.js"></script>
    <!-- built files will be auto injected -->
    <script src="./requestConfig.js"></script>
    <!-- <script src="./gsmdp.js"></script> -->
    <!-- <script src="./apps.js"></script> -->
  </body>
</html>
<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
<script type="text/javascript" src="./vconsole.min.js"></script>
<script>
  // // 初始化
  var vConsole = new VConsole();
  // console.log('Hello world');
</script>
