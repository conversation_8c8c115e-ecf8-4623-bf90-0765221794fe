应急资源信息详情组件
<template>
  <div class="resource-info">
    <div class="title-box">
      <div class="title">{{ resourceInfo.name || resourceInfo.eventTitle || '应急资源详情' }}</div>
      <!-- <div class="to-around" @click="onSurround">
        <img class="title-icon" src="@/assets/images/icons/locationGray.png" alt="" />
        搜周边
        <van-icon name="arrow" size="20" />
      </div> -->
    </div>
    <div class="detail-box">
      <div class="coordinates" v-if="resourceInfo.longitude && resourceInfo.latitude">
        经度: {{ resourceInfo.longitude }} 纬度: {{ resourceInfo.latitude }}
      </div>
      <div class="resource-fields" v-if="!loading">
        <div v-for="(field, index) in displayFields" :key="index" class="field-item">
          <span class="field-label">{{ field.label }}：</span>
          <span class="field-value">
            <template v-if="field.type === 'Tel' && resourceInfo[field.name]">
              {{ resourceInfo[field.name] }}
              <van-icon name="phone-circle" class="phone-icon" color="#13b2a1" @click.stop="goToTelPage(resourceInfo[field.name])" />
            </template>
            <template v-else>
              {{ resourceInfo[field.name] || '暂无' }}
              <span v-if="resourceInfo[field.name] && field.unit"> {{ field.unit }}</span>
            </template>
          </span>
        </div>
        <div v-if="displayFields.length === 0" class="empty-data">
          <van-empty description="暂无数据" />
        </div>
      </div>
      <div v-else class="loading-container">
        <van-loading color="#1989fa" />
        <span class="loading-text">加载中...</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import fieldConfig from '@/utils/fieldConfig';

@Component({
  components: {}
})
export default class ResourceInfo extends Vue {
  @Prop(Object) private resourceInfo!: any;
  @Prop({ type: String, default: '' }) private layerKey!: string;

  // 用于显示的字段
  displayFields: any[] = [];
  loading: boolean = true;

  // 在组件创建后立即加载字段
  created() {
    // 延迟一点加载，确保resourceInfo数据已经准备好
    this.$nextTick(() => {
      this.loadFieldConfig(this.layerKey);
    });
  }

  // 根据layerKey获取相应字段配置
  @Watch('layerKey')
  onLayerKeyChange(val: string) {
    if (val) {
      this.loadFieldConfig(val);
    }
  }

  @Watch('resourceInfo', { deep: true })
  onResourceInfoChange() {
    // 当resourceInfo变化时，如果当前没有字段配置，尝试重新加载或生成默认配置
    if (this.displayFields.length === 0) {
      this.loadFieldConfig(this.layerKey);
    }
  }

  // 加载字段配置
  loadFieldConfig(layerKey: string) {
    this.loading = true;

    try {
      // 尝试获取指定的字段配置
      if (layerKey && fieldConfig[layerKey]) {
        this.displayFields = fieldConfig[layerKey];
      }
      // 尝试获取通用配置
      else if (fieldConfig.common) {
        this.displayFields = fieldConfig.common;
      }
    } catch (error) {
      console.error('加载字段配置失败:', error);
    } finally {
      // 最终都会关闭加载状态
      this.loading = false;
    }
  }

  // 拨打电话
  goToTelPage(tel: string) {
    if (!tel) return;

    // 移除可能的非数字字符
    const cleanTel = tel.replace(/\D/g, '');
    if (cleanTel) {
      window.location.href = `tel://${cleanTel}`;
    }
  }

  // 点击搜周边按钮
  onSurround() {
    this.$emit('surround');
  }
}
</script>

<style lang="less" scoped>
.resource-info {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding: 20px 15px;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  max-height: 33%;
  overflow-y: auto;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .title {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .to-around {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      padding: 6px 10px;
      border-radius: 20px;
      font-size: 14px;
      color: #333;

      .title-icon {
        width: 14px;
        height: 18px;
        margin-right: 4px;
      }

      .van-icon {
        margin-left: 2px;
      }

      &:active {
        background-color: #e0e0e0;
      }
    }
  }

  .detail-box {
    line-height: 24px;

    .address {
      margin-bottom: 8px;
      color: #333;
      font-size: 15px;
    }

    .coordinates {
      margin-bottom: 12px;
      color: #666;
      font-size: 14px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px 0;

      .loading-text {
        margin-top: 10px;
        color: #969799;
        font-size: 14px;
      }
    }

    .empty-data {
      padding: 20px 0;
    }

    .resource-fields {
      margin-top: 10px;

      .field-item {
        display: flex;
        margin-bottom: 10px;
        font-size: 14px;

        .field-label {
          color: #666;
          min-width: 90px;
          flex-shrink: 0;
        }

        .field-value {
          flex: 1;
          color: #333;

          .phone-icon {
            margin-left: 8px;
            vertical-align: -2px;
          }
        }
      }
    }
  }
}
</style>
