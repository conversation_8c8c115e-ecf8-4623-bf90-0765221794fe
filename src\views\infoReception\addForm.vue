<template>
  <div class="list">
    <Header title="新增突发事件"></Header>
    <div class="list-tab">
      <van-form @submit="onSubmit" class="list-card">
        <van-cell-group inset v-for="(item, index) in detailsData" :key="index">
          <template v-for="ele in item.list">
            <van-field
              v-if="ele.inputType === 'textarea'"
              v-model="details[ele.prop]"
              :label="ele.name"
              rows="4"
              type="textarea"
              :maxlength="ele.maxlength || ''"
              :placeholder="ele.placeholder || '请输入内容'"
              show-word-limit
            >
              <template #left-icon>
                <van-icon :name="ele.icon" :color="ele.color" />
              </template>
            </van-field>
            <van-field
              v-else-if="ele.inputType === 'popup'"
              v-model="details[ele.prop]"
              :label="ele.name"
              :name="ele.typeCode ? ele.typeCode : ele.prop"
              is-link
              readonly
              :placeholder="ele.placeholder || '请输入内容'"
              :rules="rules[ele.prop] || rules[ele.typeCode] || []"
              @click="
                showPicker = true;
                popupType = ele.popupType;
                calendarKey = ele.prop;
              "
            >
              <template #left-icon>
                <van-icon :name="ele.icon" :color="ele.color" />
              </template>
            </van-field>
            <van-field
              v-else
              v-model="details[ele.prop]"
              :label="ele.name"
              :name="ele.typeCode ? ele.typeCode : ele.prop"
              :placeholder="ele.placeholder || '请输入内容'"
              clearable
              :rules="rules[ele.prop] || rules[ele.typeCode] || []"
            >
              <template #left-icon>
                <van-icon :name="ele.icon" :color="ele.color" />
              </template>
              <template #input v-if="ele.type === 'file'">
                <van-uploader
                  v-model="details.attachment"
                  :max-size="100 * 1024 * 1024"
                  :max-count="5"
                />
              </template>
            </van-field>
          </template>
        </van-cell-group>
        <div class="submit-btn">
          <van-button block type="primary" native-type="submit">
            确认{{ btnTitle }}
          </van-button>
        </div>
      </van-form>
      <Popup
        :type="popupType"
        :showPicker="showPicker"
        :keys="calendarKey"
        @onConfirm="onConfirm"
        @onCancel="
          showPicker = false;
          popupType = '';
        "
      ></Popup>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch,Prop } from "vue-property-decorator";
import Header from "@/views/common/header.vue";
import { downloadReport } from "@/utils/validate";
import { Cascader, Notify } from "vant";
import Popup from "@/components/popup.vue";
// 校验函数返回 true 表示校验通过，false 表示不通过
let validator = (val) => val.length <= 50;
@Component({
  components: {
    Header,
    Popup,
  },
})
export default class addForm extends Vue {
  public detailsData: any = [
    {
      name: "baseInfo",
      list: [
        {
          name: "信息标题",
          prop: "title",
          color: "#c8e3ee",
          icon: "coupon-o",
          placeholder: "填写事发时间、事发地点及要素信息后，自动生成信息标题",
          maxlength: "50",
        },
        {
          name: "信息摘要",
          prop: "infoAbstract",
          color: "#eed49b",
          icon: "orders-o",
          inputType: "textarea",
          placeholder: "填写事发时间、事发地点及要素信息后，自动生成信息摘要",
          maxlength: "1000",
        },
        {
          name: "处理情况",
          prop: "dealProcess",
          color: "#e4bfa0",
          icon: "newspaper-o",
          inputType: "textarea",
          placeholder: "填写事件处理情况",
          maxlength: "1000",
        },
      ],
    },
    {
      name: "originInfo",
      list: [
        {
          name: "接报时间",
          prop: "receiveTime",
          value: "",
          color: "#b5dbe8",
          icon: "clock-o",
          type: "popup",
          inputType: "popup",
          placeholder: "请选择接报时间",
          popName: "showCalendar",
          popupType: "datetime",
        },
        {
          name: "信息来源",
          prop: "infoSources",
          value: "",
          color: "#c8e3ee",
          icon: "peer-pay",
          maxlength: "30",
        },
      ],
    },
    {
      name: "conInfo",
      list: [
        {
          name: "事发时间",
          prop: "occurTime",
          color: "#eed49b",
          icon: "underway-o",
          type: "popup",
          inputType: "popup",
          placeholder: "请选择时间",
          popName: "showCalendar",
          popupType: "datetime",
        },
        {
          name: "行政区划",
          prop: "districtName",
          color: "#eed49b",
          icon: "location-o",
          type: "popup",
          inputType: "popup",
          placeholder: "请选择行政区划",
          popName: "showAreaAddress",
          popupType: "cascader",
        },
        {
          name: "事发地点",
          prop: "cuDistrict",
          color: "#e4bfa0",
          icon: "location-o",
          maxlength: "100",
        },
        {
          name: "事件类型",
          prop: "eventTypeName",
          color: "#b5dbe8",
          icon: "font-o",
          type: "popup",
          placeholder: "请选择事件类型",
          popName: "showTypePicker",
          typeCode: "eventTypeCode",
          inputType: "popup",
          popupType: "typePicker",
        },
        {
          name: "事件级别",
          prop: "eventLevelName",
          color: "#c8e3ee",
          icon: "bar-chart-o",
          type: "popup",
          placeholder: "请选择事件级别",
          popName: "showPicker",
          typeCode: "eventLevelCode",
          inputType: "popup",
          popupType: "levelPicker",
        },
      ],
    },
    {
      name: "peopleInfo",
      list: [
        {
          name: "承办人",
          prop: "editorName",
          color: "#c8e3ee",
          icon: "manager-o",
          type: "person",
        },
        {
          name: "承办人电话",
          prop: "editorTel",
          color: "#eed49b",
          icon: "phone-o",
        },
        {
          name: "签发人",
          prop: "issuerName",
          color: "#b5dbe8",
          icon: "user-o",
        },
      ],
    },
    {
      name: "otherInfo",
      list: [
        {
          name: "附件",
          prop: "attachments",
          type: "file",
          color: "#eed49b",
          icon: "photo-o",
        },
      ],
    },
  ];
  public rules: any = {
    title: [
      { required: true, message: "请输入50字以内标题" },
      { validator, message: "请输入50字以内标题" },
    ],
    infoAbstract: [{ required: true, message: "请输入1000字以内信息摘要" }],
    dealProcess: [{ required: true, message: "请输入1000字以内处理情况" }],
    receiveTime: [{ required: true, message: "请选择接报时间" }],
    infoSources: [{ required: true, message: "请输入30字以内信息来源" }],
    cuDistrict: [{ required: true, message: "请输入100字以内的地址信息" }],
    occurTime: [{ required: true, message: "请选择事发时间" }],
    eventTypeCode: [{ required: true, message: "请选择事件类型" }],
    eventLevelCode: [{ required: true, message: "请选择事件级别" }],
    editorId: [
      { required: true },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: "请输入正确的姓名" },
    ],
    editorTel: [
      { required: true },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/, message: "请输入正确的电话号码" },
    ],
    issuerName: [
      { required: true },
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: "请输入正确的姓名" },
    ],
  };
  // 是否显示面板
  public showPopupPicker: any = false;
  public popupType: any = "";
 

  public showCalendar = false;
  public showPicker = false;
  public showTypePicker = false;
  public showAreaAddress = false;
  // 选项面板
  public eventAreaList = [];

  public type: any = ""; // 页面类型，add-新增，detail-详情
  public infoType: any = ""; // 页面信息类型
  public details: any = {
    attachment: [],
  }; // 接报信息字段

  public calendarKey = "";
  // public maxDate = new Date();

  public eventTypeList: any = []; // 事件类型

  private role: any;

  public btnTitle = "";
  public formatterCalendar = (type, val) => {
    switch (type) {
      case "year":
        return `${val}年`;
      case "month":
        return `${val}月`;
      case "day":
        return `${val}日`;
      case "hour":
        return `${val}时`;
      case "minute":
        return `${val}分`;
      default:
        return val;
    }
  };
  async mounted() {
    console.log("信息接报----》", this.$route.query);
    this.role = JSON.parse(localStorage.getItem("role"));
    this.btnTitle = this.role.tenantId.split(".").length === 2 ? "录入" : "上报";
    // this.details.reporter = '497e923741c846b38b3fd8f13f7006dd'; // 报送人用户ID
    this.details.editorId = this.role.personId; // 报送人用户ID
    // this.details.reporter = this.role.name; // 报送人用户ID
    this.details.districtCode = this.role.districtCode; // 行政区划代码
    this.$set(this.details, "editorTel", this.role.cellphone);
    this.$set(this.details, "editorName", this.role.name);
    this.$set(this.details, "readerName", this.role.name);
  }

  initEventType() {}

  // 新增/保存信息接报详情
  saveInfo(file = false) {
    console.log(this.details);
    let params = Object.assign({}, this.details);
    // 格式化日期传参
    params.occurTime = params.occurTime
      ? this["$moment"](params.occurTime).format("YYYY-MM-DD HH:mm:ss")
      : this["$moment"](new Date()).format("YYYY-MM-DD HH:mm:ss");
    params.receiveTime = params.receiveTime
      ? this["$moment"](params.receiveTime).format("YYYY-MM-DD HH:mm:ss")
      : this["$moment"](new Date()).format("YYYY-MM-DD HH:mm:ss");
    // params.reportTime = params.reportTime
    //   ? this["$moment"](params.reportTime).format("YYYY-MM-DDTHH:mm:ss")
    //   : this["$moment"](new Date()).format("YYYY-MM-DDTHH:mm:ss");
    params.reportDistCode = this.role.districtCode;
    params.dealStateCode = this.role.tenantId.split(".").length === 2 ? "04" : "14"; // 处置状态,保存时传04，上报时传14
    this["$api"].InfoRequest.saveEvent(params, (res) => {
      if (res.data.status == 200) {
        this.details.receiveId = res.data.data;
        Notify({ type: "success", message: res.data.msg || "上报成功" });
        this.$router.push("/infoReception");
        this.afterRead(res.data.data);
      } else {
        Notify({ type: "danger", message: res.data.msg || "请求失败" });
      }
    });
  }

  onConfirm(val) {
    console.log(
      "onConfirm------->" + val,
      "calendarKey------->" + this.calendarKey,
      "popupType------->" + this.popupType
    );
    this.showPicker = false;
    if (this.popupType === "datetime") {
      this.details[this.calendarKey] = this["$moment"](val).format("YYYY-MM-DD HH:mm:ss");
    } else if (this.popupType === "cascader") {
      this.details.districtName = val.map((option) => option.fullName).join(" ");
    } else if (this.popupType === "typePicker") {
      let valObj = JSON.parse(val);
      this.details.eventTypeName = valObj.val[valObj.val.length - 1];
      this.details.eventTypeCode =valObj.eventTypeList[valObj.index[0]].children[
        valObj.index[1]
      ].id;
    } else if (this.popupType === "levelPicker") {
      let valObj = JSON.parse(val);
      this.details.eventLevelCode = valObj.val.code;
      this.details.eventLevelName = valObj.val.text;
    }
    // this.popupType = "";
  }
  // 确认上报按钮
  onSubmit() {
    console.log("确认上报按钮+++++++++", this.details);
    this.saveInfo();
  }

  formateDate(time) {
    // 兼容之前的时间格式
    if (time instanceof Object) {
      return this["$moment"](time.time).format("YYYY-MM-DD HH:mm:ss");
    } else {
      return this["$moment"](time).format("YYYY-MM-DD HH:mm:ss");
    }
  }

  //上传
  async afterRead(receiveId) {
    let _this = this;
    const formData = new FormData();
    this.details.attachment.map((ele) => {
      formData.append("files", ele.file);
    });
    formData.append("receiveId ", receiveId);
    this["$api"].InfoRequest.getAttach(formData, function (res) {
      console.log("getAttach-上传完成--->", res);
    });
  }
}
</script>
<style lang="less" scoped>
@url: "~@/assets/images/";
.list {
  // display: flex;
  // flex-direction: column;
  width: 100%;
  height: 100%;
  // overflow: hidden;
  &-card {
    margin-top: 5px;
  }
  &-casualties {
    display: flex;
    flex-wrap: wrap;
    color: #323233;
    align-items: center;
    .custom-title {
      width: 50%;
      text-indent: 10px;
      height: 30px;
      line-height: 30px;
      display: flex;
      &:nth-child(2n + 1) span {
        color: #c8e3ee;
      }
      &:nth-child(2n) span {
        color: #e4bfa0;
      }
      &:nth-child(2n + 2) span {
        color: #af5255;
      }
      &:nth-child(4n) span {
        color: #323233;
      }
      div {
        display: flex;
        justify-content: space-between;
        width: 130px;
      }
      span {
        // display: inline-block;
        // width: 100%;
        font-size: 3.6vw;
        // text-align: right;
        font-weight: 600;
      }
    }
  }
  &-tab {
    width: 100%;
    height: calc(100% - 100px);
    overflow: auto;
    /deep/.van-form {
      // height: 100%;
      .van-cell-group {
        // flex: 1;
        display: flex;
        flex-direction: column;
        // height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        // height: 100%;
        .van-cell {
          padding: 12px 0;
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
  .attach_area {
    display: flex;
    align-items: center;
    float: left;
    width: calc(100% - 3rem);
    margin-bottom: 10px;
  }
  .submit-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
  }
}
</style>
