<template>
  <div class="list">
    <Header title="分组管理" backPath="/">
      <template slot="oper">
        <div class="msg">
          <van-icon name="plus" @click="show2 = true" />
        </div>
      </template>
    </Header>
    <div class="list-main">
      <!-- @before-close="beforeClose" -->
      <div class="list-main-cont" style="max-height: 725px">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" :offset="20" @load="getList">
            <div v-for="(item, ind) in list" :key="item.id">
              <van-swipe-cell class="van-ellipsis">
                <template #default>
                  <van-cell @click="showGroupList(item)">
                    <template #icon>
                      <van-icon name="cluster-o" size="24" color="#01a9e8" class="clustero" />
                    </template>
                    <template #title>
                      <div class="list-content">
                        <span class="content">{{ item.groupName }}</span>
                      </div>
                    </template>
                  </van-cell>
                </template>
                <template #right>
                  <van-button square text="编辑" @click.stop="edit(item, ind)" type="primary" class="edit-button" />
                  <van-button square text="删除" @click.stop="deleteGroup(item, ind)" type="danger" class="delete-button" />
                </template>
              </van-swipe-cell>
            </div>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <van-popup
      v-model="show2"
      position="bottom"
      :close-on-click-overlay="false"
      @click-overlay="show2 = false"
      @click-close-icon="show2 = false"
      height="46%"
    >
      <van-form>
        <!-- <div class="center">新增分组</div> -->
        <van-cell align="center"> 新增分组 </van-cell>
        <van-field
          required
          v-model="groupName"
          name="分组名称:"
          label="分组名称:"
          placeholder="分组名称:"
          :rules="[{ required: true, message: '请填写分组名称' }]"
        />
        <van-field
          required
          v-model="sort"
          name="序号:"
          label="序号:"
          placeholder="序号:"
          :rules="[{ required: true, message: '请填写序号' }]"
        />
      </van-form>
      <div style="margin: 16px">
        <van-button round block type="info" @click="onSubmit">提交</van-button>
      </div>
    </van-popup>
    <van-action-sheet v-model="show" style="height: 86%">
      <van-pull-refresh v-model="refreshing2" @refresh="onRefresh2">
        <van-list v-model:loading="loading2" :finished="finished2" finished-text="没有更多了" :offset="20" @load="getMenberList">
          <div class="addressDetail-content" v-for="(item, index) in groupMenberList" :key="item.userId">
            <van-swipe-cell ref="swipeCell" :before-close="beforeClose">
              <div class="item">
                <span class="label el-icon-user-solid"></span>
                <div class="cont">
                  <p>
                    {{ item.personName }}
                    <van-icon color="rgb(25,103,242)" name="phone" v-if="item.telNumber" />
                    {{ item.telNumber }}
                  </p>
                  <p>{{ item.personJob == null || item.personJob == 'null' ? '' : item.personJob }}</p>
                </div>
              </div>
              <template #right>
                <van-button square text="删除" @click.stop="deleteItem(item, index)" type="danger" class="delete-button" />
              </template>
            </van-swipe-cell>
          </div>
        </van-list>
      </van-pull-refresh>
    </van-action-sheet>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
import { Notify, Dialog } from 'vant';

@Component({
  components: {
    Header
  }
})
export default class GroupManage extends Vue {
  list: any = [];
  total: any = 0;
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;
  refreshing2: boolean = false;
  loading2: boolean = false;
  finished2: boolean = false;
  show: boolean = false;
  show2: boolean = false;
  isEdit: boolean = false;
  groupName = '';
  sort = '';
  searchObj: any = {
    nowPage: 1,
    pageSize: 10,
    orgCode: '53010341',
    type: '1'
  };

  personObj = {
    nowPage: 1,
    pageSize: 10
  };

  groupMenberList = [];
  public onRefresh() {
    this.searchObj.nowPage = 1;
    this.list = [];
    // if (this.finished) return;
    this.getList();
  }

  public edit(item, index) {
    this.show2 = true;
    this.isEdit = true;
    this.groupName = item.groupName;
    this.selectId = item.groupId;
    this.sort = item.sort;
  }

  public deleteGroup(item) {
    Dialog.confirm({
      message: '确定删除吗？'
    }).then(() => {
      taskRequest.deleteGroup(
        {
          groupId: item.groupId
        },
        (res) => {
          if (res.data.status === 200) {
            // this.groupMenberList.splice(index, 1);
            this.getList();
          } else {
            Notify(res.data.msg);
          }
        }
      );
    });
  }

  public beforeClose({ position, instance }) {
    switch (position) {
      case 'right':
        Dialog.confirm({
          message: '确定删除吗？'
        }).then(() => {
          instance.close();
        });
        break;
    }
  }

  public deleteItem(item, index) {
    Dialog.confirm({
      message: '确定删除吗？'
    }).then(() => {
      taskRequest.deleteMenber(
        {
          groupId: this.selectId,
          groupType: '1',
          id: item.personId
        },
        (res) => {
          if (res.data.status === 200) {
            // this.groupMenberList.splice(index, 1);
            Notify('删除成功');
            this.personObj.nowPage = 1;
            this.getMenberList();
            console.log(this.$refs.swipeCell as any);
            // (this.$refs.swipeCell as any).forEach(cell => cell.reset());
          } else {
            Notify(res.data.msg);
          }
        }
      );
    });
  }
  public onSubmit() {
    taskRequest.addMenberList(
      {
        groupId: this.isEdit ? this.selectId : '',
        groupName: this.groupName,
        groupType: '1',
        orgCode: '53010341',
        sort: this.sort
      },
      (res) => {
        if (res.data.status === 200) {
          this.show2 = false;
          this.getList();
          this.sort = '';
          this.groupName = '';
          this.isEdit = false;
          this.selectId = '';
        }
      }
    );
  }
  public onRefresh2() {
    this.personObj.nowPage = 1;
    this.groupMenberList = [];
    // if (this.finished) return;
    this.getMenberList();
  }
  private selectId = '';

  showGroupList(item) {
    this.groupMenberList = [];
    this.personObj.nowPage = 1;
    this.selectId = item.groupId;
    this.getMenberList();
  }

  getMenberList() {
    let that = this;
    taskRequest.getGroupMenberList(
      {
        groupId: this.selectId,
        nowPage: that.personObj.nowPage,
        pageSize: 8
      },
      (res) => {
        if (res.data.status == 200) {
          const data = res.data.data;
          if (!this.show) {
            if (that.personObj.nowPage === 1 && !data.list?.length) {
              Notify('暂无数据');
              return;
            }
            this.show = true;
          }

          if (that.personObj.nowPage === 1) {
            that.groupMenberList = data.list;
          } else {
            that.groupMenberList = [...that.groupMenberList, ...data.list];
          }
          that.total = data.total;
          that.loading2 = false;
          that.refreshing2 = false;
          if (that.groupMenberList.length >= data.total) {
            that.finished2 = true;
          } else {
            that.finished2 = false;
          }
          that.personObj.nowPage++;
        } else {
          that.finished2 = true;
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      }
    );
  }

  private getList() {
    let that = this;
    taskRequest.getGroupManage(this.searchObj, (res: any) => {
      if (res.data.status == '200') {
        this.list = res.data.data;
        that.loading = false;
        that.refreshing = false;
        that.finished = true;
      } else {
        this.$toast(res.data.msg);
      }
    });
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.msg {
  position: absolute;
  right: 20px;
  top: 8px;
  font-size: 24px;
  color: white;
  z-index: 99;
}
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);

      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  /deep/.van-tabs__line {
    background-color: #1967f2;
  }
  .list-content {
    display: flex;
    flex-direction: column;
    .time {
      display: inline-block;
      color: #9f9f9f;
      // span{
      //     &:nth-child(1){
      //       margin-right: 20px;
      //       display: inline-block;
      //     }
      // }
    }
  }
}
.addressDetail-content {
  margin: 10px;
  padding: 5px 10px;
  background: #fff;
  border-radius: 4px;
}
.item {
  display: flex;
  align-items: center;
  font-size: 16px;
  span {
    display: inline-block;
  }
  .label {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 34px;
    width: 50px;
    margin-right: 20px;
    color: #666;
    text-align-last: justify;
    background-color: #99c0e7;
    border-radius: 5px;
    color: #fff;
    height: 50px;
  }
  .cont {
    color: #1c1d1d;
    p {
      margin: 5px 0;
    }
    > p:last-child {
      color: #9ea2a1;
      font-size: 15px;
    }
  }
  &:last-child {
    margin-bottom: 0;
  }
}
// /deep/ .van-cell {
//   padding: 20px 10px;
// }
.clustero {
  margin-right: 10px;
}
/deep/ .van-swipe-cell__right {
  right: -2px;
}
.edit-button {
  margin-right: 4px;
}
</style>
