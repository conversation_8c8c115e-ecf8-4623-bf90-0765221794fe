<template>
  <div class="list">
    <Header :title="queryObj.title"></Header>
    <div class="list-main">
      <div class="list-main-search">
        <van-search v-model="searchObj.eventTitle" placeholder="事件标题关键字搜索" @search="onSearch" />
      </div>
      <div class="list-main-cont" style="height: calc(100% - 100px)">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" offset="100" @load="onLoad">
            <van-cell v-for="(item, index) in list" :key="index" style="margin-top: 16px" @click="handleToDetail(item)">
              <template #title>
                <div class="list_content">
                  <div>{{ item.name }}</div>
                </div>
              </template>
              <template #label>
                <div class="list_content">
                  <div>地址：{{ item.address }}</div>
                </div>

                <template v-if="queryObj.layer == 'hazardInfo'">
                  <div class="list_content">
                    <div>距事发地：{{ item.distance }}公里</div>
                  </div></template
                >
                <template v-else-if="queryObj.layer == 'danger'"> </template>
                <template v-else-if="queryObj.layer == 'materialItem'">
                  <div class="list_content">
                    <div>负责人：{{ item.distance }}公里</div>
                  </div></template
                >
                <template v-else-if="queryObj.layer == 'shelterItem'">
                  <div class="list_content">
                    <div>负责人：{{ item.distance }}公里</div>
                  </div></template
                >
                <template v-else-if="queryObj.layer == 'rescuecompany'">
                  <div class="list_content">
                    <div>负责人：{{ item.distance }}公里</div>
                  </div></template
                >
              </template>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
    <!-- 查看详情 -->
    <van-popup v-model="detailHandle.showInfo" v-if="detailHandle.showInfo" position="right" :style="{ height: '100%', width: '100%' }">
      <HazardDetail :type="queryObj.type" :requestObj="detailInfo" @close="closeInfo" />
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Notify } from 'vant';
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
const dataJson = require('../infoReception/data.json');
import HazardDetail from './detail.vue';
@Component({
  components: {
    Header,
    HazardDetail
  }
})
export default class event extends Vue {
  searchObj: any = {
    eventTitle: '',
    nowPage: 1,
    pageSize: 10
  };
  private title: string = '';
  type: string = '';
  queryObj: any = {};
  list: any = [];
  total: any = 0;
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;
  detailHandle: any = { showInfo: false };
  detailInfo: any = {};
  created() {
    this.queryObj = this.$route.query;
    console.log(this.queryObj);
    // this.getList()
    this.list = [
      {
        orgid: '1c2c43e1-e2f5-11eb-8279-55046b0d2d5a',
        id: '1c2c43e1-e2f5-11eb-8279-55046b0d2d5a',
        orgname: '武汉市新洲区汪集中心卫生院',
        name: '武汉市新洲区汪集中心卫生院',
        address: '湖北省武汉市新洲区汪集街汪盛路185号',
        longitude: 114.707798,
        latitude: 30.778635,
        geomType: 'Point',
        distance: '1.5'
      }
    ];
  }
  getList() {
    const that = this;
    that.finished = true;
    return;
    if (this.list.length && this.list.length === this.total) {
      return;
    }
    apiServer.eventList(this.searchObj, function (res) {
      if (res.status === 200) {
        const data = res.data.data;
        if (that.searchObj.nowPage === 1) {
          that.list = data.list;
        } else {
          that.list = [...that.list, ...data.list];
        }
        that.total = data.total;
        that.loading = false;
        that.refreshing = false;
        if (that.list.length >= data.total) {
          that.finished = true;
        } else {
          that.finished = false;
        }
        that.searchObj.nowPage++;
      } else {
        that.finished = true;
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    });
  }
  onSearch() {
    this.searchObj.nowPage = 1;
    this.getList();
  }
  // 跳转详情页面
  handleToDetail(item) {
    // this.$router.push({
    //   path: `/event/detail`,
    //   query: {
    //     eventtype: item.type
    //   }
    // });
    this.detailInfo = item;
    this.detailHandle.showInfo = true;
  }
  closeInfo() {
    let _this = this;
    _this.detailHandle.showInfo = false;
    this.detailInfo = {};
    return;
    _this['$gsmdp'].initGoback(function () {
      // console.log("调用返回")
      try {
        _this['$gsmdp'].goBack(function () {});
      } catch (error) {
        // console.log("close error")
      }
    });
  }
  // 下拉刷新
  public onRefresh() {
    this.searchObj.nowPage = 1;
    this.list = [];
    // if (this.finished) return;
    this.getList();
  }
  // 上拉加载刷新
  public onLoad() {
    if (this.finished) return;
    this.getList();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    // position: absolute;
    // z-index: 1;
    // left: 0;
    // top: 46px;
    // height: calc(100vh - 46px);
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    // overflow: auto;
    &-search {
      height: 56px;
    }
    &-cont {
      // flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #03a9f4;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  .list_content {
    font-size: 14px;
    width: 100%;
    line-height: 24px;
  }
}
</style>
