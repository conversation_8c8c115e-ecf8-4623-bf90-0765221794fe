<template>
  <div class="affiche">
    <Header title="通知公告"></Header>
    <div class="affiche-content">
      <div class="affiche-content-today">
        <span class="time-range">今天</span>
        <ul class="list">
          <li>
            <span class="time">2022-04-16 08:30</span>
            <span class="content">东湖高新区第二小学西操场设立临时转移安置点，目前可容纳500人。</span>
            <div class="contact">
              <van-image
                width="10px"
                height="12px"
                fit="contain"
                :src="require(`@/assets/images/quickResponse/ico-phone.png`)"
              />
              <span class="title">联系人</span>
              <span class="name">张凤霞</span>
              <span class="phone">13148766777</span>
            </div>
          </li>
        </ul>
      </div>
      <div class="affiche-content-yesterday">
        <span class="time-range">昨天</span>
        <ul class="list">
          <li>
            <span class="time">2022-04-16 08:30</span>
            <span class="content">东湖高新区第二小学西操场设立临时转移安置点，目前可容纳500人。</span>
            <div class="contact">
              <van-image
                width="10px"
                height="12px"
                fit="contain"
                :src="require(`@/assets/images/quickResponse/ico-phone.png`)"
              />
              <span class="title">联系人</span>
              <span class="name">张凤霞</span>
              <span class="phone">13148766777</span>
            </div>
          </li>
          <li>
            <span class="time">2022-04-16 08:30</span>
            <span class="content">东湖高新区第二小学西操场设立临时转移安置点，目前可容纳500人。</span>
            <div class="contact">
              <van-image
                width="10px"
                height="12px"
                fit="contain"
                :src="require(`@/assets/images/quickResponse/ico-phone.png`)"
              />
              <span class="title">联系人</span>
              <span class="name">张凤霞</span>
              <span class="phone">13148766777</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
  }
})
export default class Affiche extends Vue {
  
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.affiche {
  background: #f5f6f6;
  span {
    display: inline-block;
  }
  &-content {
    height: calc(100% - 50px);
    overflow: auto;
    padding: 0 11px;
    .time-range {
      display: inline-block;
      width: 100%;
      // padding: 10px 0;
      padding-top: 10px;
      text-align: center;
      color: #b2b4b5;
    }
    .list {
      li {
        display: flex;
        flex-direction: column;
        padding: 10px;
        background: #fff;
        border-radius: 2px;
        margin-top: 10px;
        .time {
          display: flex;
          align-items: center;
          margin-bottom: 9px;
          color: #a6a8a9;
          &::before {
            content: '';
            display: inline-block;
            width: 38px;
            height: 20px;
            margin-left: -13px;
            margin-right: 7px;
            background: url("@{url}/quickResponse/ico-message.png") center no-repeat;
            background-size: cover;
          }
        }
        .content {
          margin-bottom: 13px;
          color: #1c1d1d;
          font-weight: bold;
          line-height: 28px;
        }
        .contact {
          display: flex;
          align-items: center;
          border-top: 1px solid #eae8eb;
          padding-top: 11px;
          color: #1c1d1d;
          .title {
            margin-left: 6px;
            margin-right: 10px;
          }
          .name {
            margin-right: 14px;
          }
        }
      }
    }
  }
}
</style>