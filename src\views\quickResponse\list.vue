<template>
  <div class="list">
    <Header title="快速响应" backPath="/"></Header>
    <div class="list-search">
      <van-search v-model="listParams.keywords" placeholder="关键字搜索" />
      <div class="search-btn" @click="handleSearch">查询</div>
    </div>
    <div class="list-content">
      <template v-if="lists.length > 0">
        <div class="list-content-item" v-for="(item, index) in lists" :key="index" @click="handleItem(item)">
          <div class="list-content-item-name">
            <span class="name">{{ item.eventTitle }}</span>
            <span class="latestReplyDate">{{ item.latestReplyDate }}</span>
          </div>
          <div class="list-content-item-bottom">
            <span class="latestReplyText">{{ item.latestReplyText }}</span>
            <!-- <van-badge v-if="item.unreadCount && item.unreadCount > 0" dot /> -->
            <van-badge v-if="item.unreadCount && item.unreadCount > 0" :content="item.unreadCount" max="100" />
          </div>
        </div>
      </template>
      <van-empty v-else description="暂无数据" />
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
@Component({
  components: {
    Header
  }
})
export default class list extends Vue {
  private showPicker: boolean = false;
  private columns = ['杭州', '宁波', '温州', '嘉兴', '湖州'];
  lists: any = [];
  listParams: any = {
    nowPage: 1,
    pageSize: 20,
    teamId: '',
    keywords: ''
  };
  handleItem(item) {
    sessionStorage.setItem('eventInfo', JSON.stringify(item));
    this.$store.commit('TEAMTYPECODE', item.eventTypeCode);
    this.$router.push({
      path: `/quicks/${item.eventId}`,
      query: {
        districtCode: item.districtCode,
        eventTypeCode: item.eventTypeCode
      }
    });
    let param = {
      eventId: item.eventId,
      replyFrom: 'gov',
      teamId: item.teamId
    };
    // 清除未读消息
    taskRequest.clearUnread(param, function (res) {
      if (res.data.status == 200) {
        console.log('+++++clearUnread+++', res.data);
      }
    });
    // if (item.dataType === '1') {
    //   this.$router.push({
    //     path: `/quicks/${item.emTask.eventId}`,
    //     // path: `/quickResponse/${item.emTask.eventId}`,
    //     query: {
    //       title: item.emTask.taskTitle
    //     }
    //   });
    // } else {
    //   this.$router.push({
    //     path: `/teamSignIn`,
    //     query: {
    //       taskId: item.taskId,
    //       teamId: item.teamId,
    //       eventId: item.emTask.eventId,
    //       statusCd: item.emTask.statusCd
    //     }
    //   });
    // }
  }
  private onSelect(val) {
    console.log('onSelect--------->', val);
  }
  handleSearch() {
    this.getData();
  }
  private getData() {
    let _this = this;
    let param = {
      nowPage: this.listParams.nowPage,
      pageSize: this.listParams.pageSize,
      teamId: this.listParams.teamId,
      keywords: this.listParams.keywords
    };
    taskRequest.getTeamEventList(param, function (res) {
      if (res.data.status == 200) {
        _this.lists = res.data.data.list;
      }
    });
  }
  private getQueryString() {
    var url = window.location.href.split('=')[1];
    return url;
  }
  hideNavBar() {
    if (this['$dd'].env.platform != 'notInDingTalk') {
      this['$dd'].biz.navigation.hideBar({
        hidden: true // true：隐藏，false：显示
      });
    }
  }
  created() {
    this.listParams.teamId = localStorage.getItem('teamId');
    this.getData();
    this.hideNavBar();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  span {
    display: inline-block;
  }
  &-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 13px;
    background: #fff;
    /deep/.van-search {
      flex: 1;
      padding: 0;
      margin-right: 11px;
      height: 30px;
      .van-search__content {
        height: 100%;
      }
      .van-cell {
        color: #b5b5b5;
        background: #f4f7f8;
      }
    }
    /deep/.el-select {
      flex: 1;
      background: #f4f7f8;
      .el-input__inner {
        height: 30px;
        border: none;
        color: #b5b5b5;
        outline: none;
      }
    }
    .search-btn {
      border: 1px solid #c9ccd7;
      border-radius: 2px;
      padding: 0 5px;
      margin-left: 10px;
      height: 30px;
      line-height: 30px;
      color: #333;
    }
  }
  &-content {
    height: calc(100% - 90px);
    overflow: auto;
    padding: 8px;
    &-item {
      margin-bottom: 5px;
      background: url('@{url}/quickResponse/ico-light.png') 90% top no-repeat;
      background-size: 17%;
      background-color: #fff;
      padding: 17px 12px;
      border-radius: 4px;
      &-name,
      &-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        span:first-child {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .latestReplyText {
          color: #666;
        }
      }
      &-name {
        span:last-child {
          color: #666;
        }
        // &-type {
        //   width: 35px;
        //   height: 18px;
        //   margin-right: 8px;
        //   text-align: center;
        //   font-size: 12px;
        //   color: #fff;
        //   background-size: contain !important;
        //   background: url('@{url}/quickResponse/tag-event.png') center no-repeat;
        //   &.tag-10, &.tag_0, &.tag_2 {
        //     background: url('@{url}/quickResponse/tag-task.png') center no-repeat;
        //   }
        //   &.tag-50, &.tag_1 {
        //     background: url('@{url}/quickResponse/tag-event.png') center no-repeat;
        //   }
        // }
        .name {
          font-size: 16px;
          color: #1c1d1d;
        }
      }
      &-bottom {
        &-time {
          margin-right: 11px;
          color: #666;
          font-size: 15px;
        }
        &-status {
          display: flex;
          align-items: center;
          padding: 2px 6px;
          color: #1a67f2;
          font-size: 12px;
          border: 1px solid #1a67f2;
          border-radius: 2px;
          /deep/.van-image {
            margin-right: 5px;
          }
        }
      }
    }
  }
}
</style>
