<template>
  <div class="authpage">
    <van-dialog v-model="visibleLoginCode" title="输入登录码" confirm-button-color="#326eff" :showConfirmButton="false">
      <van-form ref="formRef" @submit="submit">
        <van-cell-group style="margin: 30px 20px">
          <van-field v-model="loginCode" name="loginCode" placeholder="请输入本人手机后四位" :rules="rules.loginCode" />
        </van-cell-group>
        <div style="margin: 16px">
          <van-button round block type="primary" native-type="submit"> 确定 </van-button>
        </div>
      </van-form>
    </van-dialog>
    <!-- 浏览器机制  不允许用户初始化 自己加载地理位置 需要用户手动触发按钮后获取位置详情 
    确定规则后，在决定完善提示语，“需访问地理位置，请先打开定位功能”
    Only request geolocation information in response to a user gesture. -->
    <van-dialog
      v-model="locationVisible"
      title="需访问地理位置，请确认已打开定位。"
      confirm-button-color="#326eff"
      :showConfirmButton="false"
    >
      <div class="button-text">
        <van-button size="small" type="primary" @click="getUserLocation"> 是 </van-button>
        <van-button plain size="small" type="info" @click="declineLocation"> 否 </van-button>
      </div>
    </van-dialog>

    <van-dialog v-model="visible" allow-html confirm-button-color="#326eff" :showConfirmButton="false">
      <p class="html" v-html="title"></p>
      <div class="button-text">
        <van-button size="small" type="primary" @click="confirm(11)"> 是 </van-button>
        <van-button plain size="small" type="info" @click="confirm(10)"> 否 </van-button>
      </div>
    </van-dialog>
  </div>
</template>

<script lang="ts">
import axios from 'axios';
import { Component, Vue, Watch } from 'vue-property-decorator';
import taskRequest from '@/api/webcross/task';
import { Notify } from 'vant';
let egis = require('egis-2d');
import gisConfig from '@/components/baseMap/gisConfig.json';

@Component({
  components: {}
})
export default class authPage extends Vue {
  visible: boolean = false;
  visibleLoginCode: boolean = false; // 输入登陆码弹窗
  authCode: any = ''; // 任务授权码
  loginCode: any = null; // 任务登录码
  cellPhoneCode = '';
  address = '';
  authData: any = {}; // 授权码获取到的信息
  locationVisible = true;
  rules: any = {
    loginCode: [
      { required: true, message: '请输入登录码' },
      { pattern: /^\d{4}$/, message: '请输入4位数字' }
    ]
  };
  declineLocation() {
    this.locationVisible = false;
    alert('您点击了拒绝，请刷新页面重新获取地理位置权限');
  }

  async getUserLocation() {
    var ua = window.navigator.userAgent.toLowerCase();
    console.log(ua,'ua');
    if (this['wxUtils'].isWeiXin()) {
      await this['wxUtils']
        .getLocation()
        .then((res) => {
          let { latitude, longitude } = res;
          this.getLocationCode(`${longitude},${latitude}`);
        })
        .catch((ex) => {
          console.log(ex, 'eeeee');
        });
    } else {
      this.getLocation();
    }
  }

  locationInfo: any = {
    getLocSuccess: false, // 获取当前位置是否成功
    address: '',
    locInfo: {
      longitude: '',
      latitude: '',
      address: ''
    } as any // 当前位置
  };
  title = '';
  async getLocationCode(location) {
    //构造逆地理编码服务对象
    this.locationVisible = false;
    let WRGSService = new egis.ews.RestWRGSService({
      url: gisConfig.internetEgisConfig.url, //服务
      clientId: gisConfig.internetEgisConfig.clientId, //用户id
      clientSecret: gisConfig.internetEgisConfig.clientSecret, //用户密码
      authType: gisConfig.internetEgisConfig.authType,
      tokenUrl: gisConfig.internetEgisConfig.tokenUrl
    });
    let WRGSInput = new egis.ews.WRGSInput({
      // location: '116.4091658,39.9580264'
      location: location
    });
    let WRGSInputCode: any = WRGSService.regeocode(WRGSInput);
    WRGSInputCode.then((data) => {
      // locationInfo = data;
      let locInfo: any = {};
      locInfo.longitude = data.location.x;
      locInfo.latitude = data.location.y;
      locInfo.address = data.formatted_address;
      this.address = data.formatted_address;
      window.localStorage.setItem('locInfo', JSON.stringify(locInfo));
      // let locInfo = JSON.parse(window.localStorage.getItem("locInfo")) || {};
      this.locationInfo.locInfo = locInfo;
      this.authCode = this.$route.query.authCode || ''; // 获取任务授权码
      // if (localStorage.getItem('cellPhoneCode')) {
      //   this.parseAuthcode(localStorage.getItem('cellPhoneCode'));
      // } else {
      //   this.visibleLoginCode = true;
      // }
         this.parseAuthcode('');

      console.log('逆编码获取地理位置----->locInfo', locInfo);
    });
  }
  async showPosition(position) {
    console.log('获取经纬度成功-------》', position);
    let location_lon = position.coords.longitude;
    let location_lat = position.coords.latitude;
    this.locationInfo.locInfo.longitude = location_lon;
    this.locationInfo.locInfo.latitude = location_lat;
    console.log('当前位置----------》' + position.coords.address, '经度：' + location_lon, '纬度：' + location_lat);
    // await gisAbility.getLocationCode(`${location_lon},${location_lat}`);
    await this.getLocationCode(`${location_lon},${location_lat}`);
    // let locInfo = JSON.parse(window.localStorage.getItem("locInfo")) || {};
    // this.locationInfo.locInfo = locInfo;
  }
  getLocation() {
    if (navigator.geolocation) {
      console.log('获取经纬度');
      navigator.geolocation.getCurrentPosition(this.showPosition, this.showError, { timeout: 5000 });
    }
  }
  showError(error) {
    console.log('获取经纬度失败-------》', error);
    switch (error.code) {
      case error.TIMEOUT:
        alert('请求超时！请再试一次!');
        break;
      case error.POSITION_UNAVAILABLE:
        alert('我们找不到你的位置 Sorry!  必须https才能访问定位');
        break;
      case error.PERMISSION_DENIED:
        alert('请允许地理位置访问！');
        break;
      case error.UNKNOWN_ERROR:
        alert('发生了未知错误！');
        break;
      default:
        alert('获取地理位置放生了未知错误');
        break;
    }
  }
  private currentLoginCode= '';

  private onFailed(errorInfo: any) {}

  private confirm(status) {
    taskRequest.modifyTaskStatus(
      {
        authCode: this.authCode,
        confirmStatus: status,
        latitude: this.locationInfo.locInfo.latitude,
        longitude: this.locationInfo.locInfo.longitude,
        address: this.locationInfo.locInfo.address
      },
      (res) => {
        if (status == '10') {
          // 选择否
          Notify({ type: 'danger', message: '你没有权限进行操作' });
        } else {
          // // this.visibleLoginCode = true;
          // let cellPhoneCode= localStorage.getItem('cellPhoneCode')
          this.parseAuthcode(this.currentLoginCode)
        }
      }
    );
  }
  handleTitle(title){
        //  解析文本
        let levelCount = -1;
        levelCount = title.search('级响应');
        let levelAlbNum = title.slice(levelCount - 1, levelCount);
        let levelClass = 0;
        switch (levelAlbNum) {
          case 'Ⅰ':
            levelClass = 1;
            break;
          case 'Ⅱ':
            levelClass = 2;
            break;
          case 'Ⅲ':
            levelClass = 3;
            break;
          case 'Ⅳ':
            levelClass = 4;
            break;
        }
        let levelText = `<span class="level${levelClass}">${title.slice(levelCount - 1, levelCount + 3)}</span>`;
        let unqinoLeft = title.indexOf('》');
        let unqinoRight = title.indexOf('《');
        let unqinoText = `<span class="level${levelClass}">${title.slice(unqinoRight, unqinoLeft + 1)}</span>`;
        if (unqinoLeft > -1 && unqinoRight > -1 && levelCount > -1) {
          this.title =
            title.slice(0, unqinoRight) +
            unqinoText +
            title.slice(unqinoLeft + 1, levelCount - 1) +
            levelText +
            title.slice(levelCount + 3, -1);
        } else {
          this.title = title;
        }
  }

  submit() {
    // this.parseAuthcode();
    (this.$refs.formRef as any)
      .validate()
      .then(async (result) => {
        this.parseAuthcode(this.loginCode);
        // localStorage.setItem('cellPhoneCode', this.loginCode);
      })
      .catch((err) => {
        return false;
      });
  }
  mounted() {
    console.log("进来authPage")
  }
  // 如果有授权码，解析授权码
  private async parseAuthcode(loginCode) {
    console.log('授权相关操作', this.locationInfo.locInfo, this.address);
    console.log(this.address);
    console.log(this.locationInfo);
    let params: any = {
      authCode: this.authCode,
      latitude: this.locationInfo.locInfo.latitude,
      longitude: this.locationInfo.locInfo.longitude,
      address: this.locationInfo.locInfo.address
    };
    if (loginCode) {
      params.loginCode = loginCode;
    }
    taskRequest.parseAuthcode(params, (res) => {
      console.log(res,'ressss');
      const eventId = res.data.data?.eventId;
      const relMobileSuffix= res.data.data?.relMobileSuffix;
      if (res.data.status === 200) {
        console.log(res, '授权相关操作');
        this.authData = res.data.data || {};
        localStorage.setItem('token', res.data.data.token);
        localStorage.setItem('authData', JSON.stringify(this.authData));
        localStorage.setItem('teamId', JSON.stringify(res.data.data.teamId));
        localStorage.setItem(eventId+'-'+ relMobileSuffix,'1');
        this.$router.push(`/quicks/${this.authData.eventId}`);
        axios.defaults.headers.common['token'] = res.data.data.token;
      } else {
        if (res.data.status == 605) {
             if(localStorage.getItem(eventId+'-'+ relMobileSuffix)){
                  // 匹配到 直接调用
                  this.visibleLoginCode = false;
                   this.currentLoginCode = relMobileSuffix;
                  this.confirm(11);
                  return;
            }
              this.visible = true;
              this.visibleLoginCode = false;
              this.currentLoginCode = relMobileSuffix;
              let title = res.data.msg;
              if (title) {
                  this.handleTitle(title)
              }
        }else if(res.data.status == 606){
             this.visible = true;
              this.visibleLoginCode = false;
              this.currentLoginCode = relMobileSuffix;
              let title = res.data.msg;
              if (title) {
                  this.handleTitle(title)
              }
        }
        else if (res.data.status == 602 || res.data.status == 604) {
          //  验证码错误或者超时
          if(localStorage.getItem(eventId+'-'+ relMobileSuffix)){
              this.parseAuthcode(relMobileSuffix);
              return;
          }
          if (!this.visibleLoginCode) {
            this.visibleLoginCode = true;
          };
          if(params.loginCode){
                Notify({ type: 'danger', message: res.data.msg });
          }
        } else {
          if(eventId){
              if(localStorage.getItem(eventId+'-'+ relMobileSuffix)){
              this.parseAuthcode(relMobileSuffix)
              } else {
                this.visibleLoginCode = true;
              }
          }
          this.visible = false;
          Notify({ type: 'danger', message: res.data.msg });
        }
      }
    });
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.authpage {
  width: 100%;
  height: 100%;
  background: #fff;
  .button-text {
    text-align: center;
    margin: 10px 0;
    .van-button {
      margin-right: 10px;
    }
  }
}

/deep/ .van-dialog__header {
  margin: 0 10px !important;
}
.html {
  padding: 0 10px;
  text-align: justify;
}
</style>
