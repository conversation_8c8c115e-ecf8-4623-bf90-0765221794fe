<template>
  <van-popup
    v-model:show="showFeedbackPop"
    :close-on-click-overlay="false"
    position="bottom"
    @click-overlay="closePop"
    @click-close-icon="closePop"
    :style="{ height: feedbackType === 'resources' ? '30%' : '40%' }"
  >
    <div class="main">
      <div class="header">
        <van-icon name="cross" size="25" @click="closePop" />
        {{ popTitle.title }}
        <div class="sureBtn" @click="handleSure">
          {{ feedbackType === "overTask" ? "确认完成任务" : "确认" }}
        </div>
      </div>
      <div class="content">
        <van-field
          v-model="feedbackObj.replyText"
          rows="3"
          :autosize="{ maxHeight: 100, minHeight: 50 }"
          type="textarea"
          :placeholder="popTitle.placeholder"
        />
        <van-uploader
          v-if="feedbackType !== '2'"
          v-model="feedbackObj.attachmentList"
          :max-size="100 * 1024 * 1024"
          :max-count="5"
          accept="image/*,video/*"
          :after-read="afterRead"
          multiple
        />
      </div>
      <!-- <div class="footer">
        <div class="sureBtn" @click="handleSure">{{feedbackType === 'overTask' ? '确认完成任务' : '确认'}}</div>
      </div> -->
    </div>
  </van-popup>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import { Notify } from "vant";
import taskRequest from "@/api/webcross/task";
import apiServer from "@/api/request-service";

@Component
export default class popFeedback extends Vue {
  @Prop() showFeedbackPop!: boolean;
  @Prop() feedbackType!: string;
  @Prop() teamInfo!: any;
  @Prop() locationInfo!: any;
  @Prop() curFeedbackTypeInfo!: any; // 当前选中反馈类型
  feedbackObj: any = {
    replyText: "",
    attachmentList: [],
  };

  get popTitle() {
    let popDefault = {
      title: "",
      placeholder: "",
    };
    switch (this.feedbackType) {
      case "2":
        popDefault.title = "抵达现场情况登记";
        popDefault.placeholder = "请将您单位带了多少人多少物资抵达现场的情况进行登记";
        break;
      case "3":
        popDefault.title = "任务协调申请";
        popDefault.placeholder =
          "任务进行过程中困难需要省应急管理厅进行协调的事项请进行登记";
        break;
      case "4":
        popDefault.title = "反馈现场情况";
        popDefault.placeholder = "请您将想反馈的现场情况进行反馈";
        break;
      case "5":
        popDefault.title = "物资装备申请";
        popDefault.placeholder = "如果遇到物资装备不够的情况，请把情况进行登记";
        break;
      case "overTask":
        popDefault.title = "任务完成反馈";
        popDefault.placeholder = "请将任务完成情况进行反馈";
        break;
      // default:
      //   popDefault.title = this.curFeedbackTypeInfo.replyTypeName;
      //   popDefault.placeholder = '请输入'+this.curFeedbackTypeInfo.replyTypeName;
      //   break;
    }

    return popDefault;
  }
  mounted() {
    this.feedbackObj.replyText = "";
    this.feedbackObj.attachmentList = [];
  }
  afterRead(file) {
    // 此时可以自行将文件上传至服务器
    console.log("afterRead----------->", file);
    let _this = this;
    file.status = "uploading";
    file.message = "上传中...";
    const formData = new FormData();
    formData.append("file", file.file);
    apiServer.uploadFile(formData, function (_resultObj) {
      if (_resultObj.data.status == 200) {
        file.status = "done";
        file.message = "上传完成";
        file.attachmentList = _resultObj.data;
        // _this.feedbackObj.attachmentList.push(_resultObj);
        console.log("shangchuan ------->", _this.feedbackObj, _resultObj);
      }
    });
  }
  closePop() {
    console.log("++++++++++++++");
    this.$emit("closeFeedback");
  }
  async handleSure() {
    debugger
    let locInfo = JSON.parse(window.localStorage.getItem("locInfo")) || {};
    if (Object.keys(locInfo).length > 0) {
      this.locationInfo.locInfo = locInfo;
    }
    // if (!this.locationInfo.locInfo.latitude || !this.locationInfo.locInfo.longitude) {
    //   Notify({ type: 'danger', message: '获取当前位置失败，请检查定位权限是否打开' });
    //   return false;
    // }
    let params: any = {
      latitude: this.locationInfo.locInfo.latitude,
      longitude: this.locationInfo.locInfo.longitude,
      replyAddress: this.locationInfo.locInfo.address,
      replyText: this.feedbackObj.replyText,
      replyFrom: "team",
      eventId: this.$route.params.id
    };
    let files: any = [];
    this.feedbackObj.attachmentList.forEach((el) => {
      files.push(el.attachmentList);
    });
    params.attachmentList = files;
    if (this.feedbackType === "overTask") {  //overTask:确认完成任务
      this.teamInfo.latitude = this.locationInfo.locInfo.latitude;
      this.teamInfo.longitude = this.locationInfo.locInfo.longitude;
      this.teamInfo.replyAddress = this.locationInfo.locInfo.address;
      this.teamInfo.taskId = this.teamInfo.teamTask.taskId;
      this.teamInfo.teamTaskId = this.teamInfo.teamTask.teamTaskId;
      this.teamInfo.teamId = this.teamInfo.teamTask.teamId;
      // this.teamInfo.teamName = this.teamInfo.teamName;
      console.log(this.teamInfo,'this.teamInfo')
      Object.assign(params, this.teamInfo);
      params.replyTypeCode = "1";
      params.replyText =  this.feedbackObj.replyText
      params.attachmentList = files
      console.log(params,'this.teamInfo====>object')
      taskRequest.finishTask(params, (res) => {
        console.log("finishTask------->", res);
        if (res.data.status === 200) {
          Notify({ type: "success", message: res.data.msg || "结束成功" });
          this.$emit("handleSureFeedback",res.data.status);
          this["$socketApi"].onsend({
            eventId: this.$route.params.id,
            requestFrom: "app",
          });
        } else {
          Notify({ type: "danger", message: res.data.msg || "结束失败" });
        }
      });
    } else {
      console.log("++++++++++++teaminfo--->", this.teamInfo);
      params.eventId = this.$route.params.id;
      params.replyTypeCode = this.feedbackType;
      params.teamId = this.teamInfo.teamId;
      taskRequest.reply(params, (res) => {
        if (res.data.status === 200) {
          this.feedbackObj.replyText = "";
          this.feedbackObj.attachmentList = [];
          this.$emit("handleSureFeedback");
          this["$socketApi"].onsend({
            eventId: this.$route.params.id,
            requestFrom: "app",
          });
        } else {
          Notify({ type: "danger", message: res.data.msg || "消息发送失败" });
        }
      });
    }
  }
}
</script>

<style lang="less" scoped>
@url: "~@/assets/images/";
.main {
  .header {
    position: relative;
    height: 54px;
    line-height: 54px;
    text-align: center;
    font-size: 18px;
    border-bottom: 1px solid #ccc;
    /deep/.van-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .content {
    padding: 10px 20px;
    /deep/.van-field {
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    /deep/.van-uploader {
      margin-top: 15px;
    }
  }
  /deep/.footer {
    height: 80px !important;
    border: none !important;
    .sureBtn {
      min-width: 100px;
      padding: 0 10px;
      text-align: center;
      background: #326fff;
      color: #fff;
      border-radius: 4px;
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      float: right;
      margin-top: 20px;
      margin-right: 20px;
    }
  }
  .sureBtn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    color: #326fff;
    font-size: 16px;
  }
}
</style>
