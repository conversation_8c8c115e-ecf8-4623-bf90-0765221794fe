{"name": "@eapp/emis-app-h5-2.0-euip", "version": "0.1.1", "private": false, "scripts": {"serve": " vue-cli-service serve ", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "install-sass": "npm install node-sass@4.10.0 --SASS_BINARY_SITE=http://***********:10185/repository/content/sites/gs-assets/node/sass/", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@antv/f2": "^3.4.1", "@form-create/element-ui": "^1.0.12", "@gsafety/cad-glog": "^2.0.3", "@gsafety/cad-gutil": "^2.0.1", "@gsafety/rx-eventbus": "^2.0.3", "@gsafety/vue-httpclient": "^2.0.0", "@types/echarts": "^4.4.1", "@types/es6-promise": "^3.3.0", "@types/js-cookie": "^2.2.0", "@types/lodash": "^4.14.117", "@types/standard-error": "^1.1.0", "@vue/babel-preset-app": "^5.0.4", "axios": "^0.18.0", "axios-observable": "^1.0.6", "core-js": "^3.22.0", "crypto-js": "^4.2.0", "cube-ui": "~1.12.15", "dingtalk-jsapi": "^2.14.0", "docx-preview": "^0.3.5", "echarts": "^4.4.0", "egis-2d": "^1.0.50", "element-ui": "^2.12.0", "js-audio-recorder": "^1.0.7", "js-cookie": "^2.2.0", "jszip": "^3.10.1", "lamejs": "1.2.0", "lodash": "^4.17.11", "mint-ui": "^2.2.13", "moment": "^2.29.4", "mpvue-calendar": "^2.3.7", "muse-ui": "^3.0.2", "picker-extend": "^2.1.0", "rxjs": "^5.4.2", "rxjs-compat": "^6.3.3", "sm3": "^1.0.3", "standard-error": "^1.1.0", "ts-md5": "^1.2.4", "vant": "^2.2.16", "vconsole": "^3.14.6", "vue": "^2.6.11", "vue-class-component": "^6.0.0", "vue-i18n": "^8.11.2", "vue-pickers": "^2.1.1", "vue-property-decorator": "^7.0.0", "vue-qr": "^4.0.6", "vue-router": "^3.0.1", "vuex": "^3.0.1", "vuex-class": "^0.3.1", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@types/chai": "^4.1.0", "@types/mocha": "^5.2.4", "@vue/cli-plugin-babel": "^3.1.0", "@vue/cli-plugin-typescript": "^3.1.0", "@vue/cli-plugin-unit-mocha": "^3.1.0", "@vue/cli-service": "^3.1.0", "@vue/test-utils": "^1.0.0-beta.20", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.0", "chai": "^4.1.2", "gulp": "^3.9.1", "gulp-concat": "^2.6.1", "gulp-merge-json": "^1.3.1", "gulp-minify-css": "^1.2.4", "gulp-plumber": "^1.2.0", "gulp-sass": "^4.0.2", "gulp-sonar": "^3.0.1", "less": "^3.11.1", "less-loader": "^5.0.0", "minimist": "^1.2.0", "mobile-select": "^1.1.2", "node-sass": "^4.12.0", "picker-extend": "^2.1.0", "postcss-px-to-viewport": "^0.0.3", "sass-loader": "^7.0.1", "sass-resources-loader": "^2.0.3", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "typescript": "^3.0.0", "validate-commit-msg": "^2.14.0", "vue-cli-plugin-cube-ui": "^0.2.5", "vue-template-compiler": "^2.6.11"}, "config": {"validate-commit-msg": {"types": ["feat", "themes", "fix", "docs", "style", "refactor", "perf", "test", "build", "ci", "chore", "revert"], "warnOnFail": false, "maxSubjectLength": 100, "helpMessage": "does not match '<type>: <subject>' !"}}, "husky": {"hooks": {"pre-commit": "npm run lint", "pre-push": "npm run build", "commit-msg": "validate-commit-msg"}}, "transformModules": {"cube-ui": {"transform": "cube-ui/src/modules/${member}", "kebabCase": true}}}