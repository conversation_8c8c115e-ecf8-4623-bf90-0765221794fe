<template>
  <div class="backlog">
    <!-- <Header title="待办事项" backPath="/home"></Header> -->
    <div class="common-header">
      <van-nav-bar title="待办事项" right-text="全部已读" @click-right="readAll" />
    </div>
    <div class="list">
      <div class="list-main">
        <div class="list-main-cont">
          <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
            <van-list
              v-model="loading"
              :finished="finished"
              finished-text="没有更多了"
              :offset="20"
              @load="getList"
            >
              <van-cell
                v-for="(item, index) in list"
                :key="index"
                class="van-ellipsis"
                @click="handleToDetail(item)"
              >
                <template #title>
                  <div class="list_item">
                    <div class="list_content">
                      <van-icon
                        name="todo-list"
                        size="24"
                        color="#01a9e8"
                        style="margin-right: 5px; vertical-align: middle"
                      />{{ item.msgContent }}
                    </div>
                    <div class="list_top">
                      <div class="">{{ item.msgTitle }}</div>
                      <div class="btnPart">
                        <span class="time_show">{{ item.publishTime }}</span>
                      </div>
                    </div>
                  </div>
                </template>
              </van-cell>
            </van-list>
          </van-pull-refresh>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import Header from "@/views/common/header.vue";
import { Notify, Dialog } from "vant";
@Component({
  components: {
    Header,
  },
})
export default class backlog extends Vue {
  private pageInfo: any = {
    nowPage: 1,
    pageSize: 9999,
    total: null,
  };
  private list: any = [];
  refreshing: boolean = false;
  loading: boolean = false;
  finished: boolean = false;
  requestId: any = "";

  mounted() {
    this.getList();
  }

  private getList() {
    const params = {
      // loginName: JSON.parse(window.localStorage.getItem('role')).loginName,
      // loginName:  '13807175689', // 测试账号
      loginName: JSON.parse(window.localStorage.getItem("role")).cellphone,
      pageNo: this.pageInfo.nowPage,
      pageSize: this.pageInfo.pageSize,
    };
    this["$api"].InfoRequest.getBacklogList(params, (res) => {
      if (res.data.status === 200) {
        let historyList = JSON.parse(res.data.data);
        if (!historyList[0]) return;
        historyList[0]?.list.forEach((ele) => {
          ele.publishTime = this["$moment"](ele.pushTime.time).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        });
        this.list = historyList[0].list;
        this.finished = true;
        console.log("=====list>", this.list);
      } else {
        this.list = [];
        this.finished = true;
      }
    });
  }

  handleToDetail(item) {
    const objInfo = this.getToPage(item.msgTitle);
    let queryInfo = {
      type: "detail",
      infotype: objInfo.type || "event",
    };
    const name = objInfo.idName ? objInfo.idName : "id";
    queryInfo[name] = item.majorId;
    this.$router.push({
      path: objInfo.url,
      query: queryInfo,
    });
    this.readFn(item);
  }

  getToPage(name) {
    let url = "";
    let idName = "";
    let type = "";
    switch (name) {
      case "信息处理":
      case "突发事件信息":
        url = "/infoReception/add";
        break;
      case "值班表":
        url = "/duty";
        break;
      case "通知公告":
        url = "/announcementDeatil";
        type = "107";
        break;
      case "领导批示":
        url = "/leaderDetail";
        type = "leader";
        break;
      case "值班日志":
        url = "/duty";
        break;
      case "气象预警":
        url = "/warnDetailInfo";
        idName = "alertInfoId";
        break;
    }
    return { url, idName, type };
  }

  onRefresh() {
    // 清空列表数据
    this.finished = false;
    this.list = [];
    // 重新加载数据 将 loading 设置为 true，表示处于加载状态
    this.loading = true;
    this.refreshing = true;
    this.pageInfo.nowPage = 1;
    this.getList();
  }
  // 全部已读
  readAll() {
    Dialog.confirm({
      message: "确定要清空待办事项的所有未读吗？",
    })
      .then(() => {
        this.readFn(null);
      })
      .catch(() => {
        // on cancel
      });
  }
  readFn(item: any) {
    let params = {
      msgId: item && item.id ? item.id : "", // 消息主键 ，单个标记为已读时必传 可不传
      userId: JSON.parse(window.localStorage.getItem("role")).personId, // 阅读消息的用户ID
      modelTypeid: "", // 消息所属模块ID
      majorId: "", // 消息所属业务ID
    };
    this["$api"].InfoRequest.realMsg(params, (res) => {
      if (res.data.status === 200) {
        if (!item) {
          this.getList();
        }
      } else {
        Notify({ type: "danger", message: res.data.msg || "请求失败" });
      }
    });
  }
}
</script>

<style lang="less" scoped>
.backlog {
  background: #f5f6f6;
  span {
    display: inline-block;
  }
  .common-header /deep/.van-nav-bar__text {
    color: #fff !important;
  }
}
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  .van-tabs__line {
    background-color: #1967f2;
  }
  .van-ellipsis {
    margin-bottom: 10px;
  }
  :deep(.van-cell__title) {
    width: 100%;
  }
  .list_item {
    width: 100%;
    background: white;
    .text_ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .list_top {
      display: flex;
      justify-content: space-between;
      width: 100%;
      height: 50%;
      position: relative;
      .btnPart {
        padding-left: 3%;
        .action {
          max-width: 40%;
          background: transparent;
          color: #e45050;
          border: 1px solid #e45050;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
          margin-left: 5px;
        }
        .total {
          max-width: 40%;
          background: transparent;
          color: #00a0e9;
          border: 1px solid #00a0e9;
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 12px;
        }
        .time_show {
          -webkit-border-radius: 6px;
          -moz-border-radius: 6px;
          border-radius: 6px;
          padding: 1px 0px;
          float: right;
        }
      }
    }
  }
  .list_content {
    width: 100%;
    margin-bottom: 5px;
    font-size: 15px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
</style>
