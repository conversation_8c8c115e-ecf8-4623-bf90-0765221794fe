import Vue from 'vue'

//import Cube from 'cube-ui' //引入Cube-UI
//import '@/assets/styles/reset.css';
//import echarts from 'echarts'; // 引入echarts
// import ElementUI from 'element-ui'; //ElementUI
// import 'element-ui/lib/theme-chalk/index.css'
// Vue.use(ElementUI)

//museUi全部引入
// import MuseUI from 'muse-ui'; //引入museUi
// import 'muse-ui/dist/muse-ui.css';
// Vue.use(MuseUI);
//museUi按需引入
import { LoadMore } from 'muse-ui';
import 'muse-ui/dist/muse-ui.css';
Vue.use(LoadMore);

//动态表单相关
//import formCreate, {maker} from './common/formcreate/vantui'
// import formCreate, {maker} from '@form-create/element-ui'
// Vue.use(formCreate)
// Vue.prototype.$maker=maker;

//vant组件
//全部引入
// import Vant from 'vant';
// import 'vant/lib/index.css';
// Vue.use(Vant);
import 'vant/lib/icon/local.css';
//按需引入
import { NavBar,CellGroup,Switch,Slider,Stepper,Dialog,Field,Toast,Icon,Search,Tab, Tabs ,CollapseItem ,Collapse,
     Form , Popup,Grid, GridItem ,Image as VanImage ,Cascader, Button,Picker,DatetimePicker,RadioGroup, Radio ,Col, Row,Checkbox, CheckboxGroup,ImagePreview,Empty,Uploader, PullRefresh, List, Cell, ActionSheet, Notify, Calendar, Loading, Badge, SwipeCell,Progress, Popover, TreeSelect, Tag, Overlay  } from 'vant';

Vue.use(NavBar);
Vue.use(Dialog);
Vue.use(Field);
Vue.use(Stepper);
Vue.use(Slider);
Vue.use(Toast);
Vue.use(Icon);
Vue.use(Search);
Vue.use(Tab);
Vue.use(Tabs);
Vue.use(Popup)
Vue.use(Grid);
Vue.use(GridItem);
Vue.use(VanImage);
Vue.use(Form)
Vue.use(Button)
Vue.use(Picker)
Vue.use(DatetimePicker)
Vue.use(RadioGroup)
Vue.use(Radio)
Vue.use(Col)
Vue.use(Row)
Vue.use(CheckboxGroup);
Vue.use(Checkbox)
Vue.use(Switch);
Vue.use(CellGroup);
Vue.use(ImagePreview);
Vue.use(Empty);
Vue.use(Uploader);
Vue.use(PullRefresh);
Vue.use(List);
Vue.use(Cell);
Vue.use(ActionSheet);
Vue.use(Notify);
Vue.use(Calendar);
Vue.use(Loading);
Vue.use(Badge);
Vue.use(SwipeCell);
Vue.use(Cascader);
Vue.use(Progress )
Vue.use(Popover )
Vue.use(TreeSelect)
Vue.use(Collapse)
Vue.use(CollapseItem )
Vue.use(Tag)
Vue.use(Overlay)
// Vue.component('van-field',Field);

//级联选择组件
import menuLinkage from "@/views/common/menuLinkage/menuLinkage.vue"
Vue.component('menuLinkage',menuLinkage);
//formCreate.component('field', Field );



