<template>
  <van-popup
    v-model="showPicker"
    position="bottom"
    :close-on-click-overlay="false"
    @click-overlay="onCancel"
    @click="onCancel"
  >
    <van-datetime-picker
      v-if="type === 'datetime'"
      type="datetime"
      v-model="pickerObj[keys]"
      :formatter="formatterCalendar"
      @confirm="onConfirm"
      @cancel="onCancel"
    />
    <van-picker
      v-else-if="type === 'typePicker'"
      :columns="eventTypeList"
      @confirm="onConfirmType"
      @cancel="onCancel"
      value-key="label"
      show-toolbar
    />
    <van-picker
      v-else-if="type === 'levelPicker'"
      :columns="levelList"
      @confirm="onConfirmLevel"
      @cancel="onCancel"
      show-toolbar
    />
    <van-cascader
      v-else-if="type === 'cascader'"
      v-model="pickerObj.districtCode"
      :field-names="fieldNames"
      title="请选择所在地区"
      :options="eventAreaList"
      @close="onCancel"
      @finish="onConfirmPickerArea"
    />
  </van-popup>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import apiServer from "@/api/request-service";
@Component({
  components: {},
})
export default class popup extends Vue {
  @Prop() showPicker!: boolean;
  @Prop() type!: string;
  @Prop() keys!: string;
  public pickerObj: any = {
    districtCode: "",
    receiveTime: new Date(),
    occurTime: new Date(),
  };
  public eventTypeList: any = []; // 事件类型
  public eventAreaList = []; // 行政区划
  public levelList: any = [
    { text: "特别重大", code: "1" },
    { text: "重大", code: "2" },
    { text: "较大", code: "3" },
    { text: "一般", code: "4" },
  ];
  public fieldNames = {
    text: "fullName",
    value: "districtCode",
    children: "children",
  };
  mounted() {
    this.getEventTypeList(); // 获取事件类型列表
    this.getAreaList();
  }
  public formatterCalendar = (type, val) => {
    switch (type) {
      case "year":
        return `${val}年`;
      case "month":
        return `${val}月`;
      case "day":
        return `${val}日`;
      case "hour":
        return `${val}时`;
      case "minute":
        return `${val}分`;
      default:
        return val;
    }
  };
  getAreaList() {
    this["$api"].InfoRequest.getDistrictList({}, (res) => {
      if (res.data.status === 200) {
        this.eventAreaList = [JSON.parse(res.data.data)];
        this.dealAreaData(this.eventAreaList);
      }
    });
  }
  dealAreaData(data) {
    data.forEach((ele) => {
      if (ele.children.length) {
        this.dealAreaData(ele.children);
      } else {
        ele.children = null;
      }
    });
  }

  // 获取事件类型列表
  private getEventTypeList() {
    this["$api"].InfoRequest.getEventTypeList({}, (res) => {
      if (res.data.status === 200) {
        this.eventTypeList = res.data.data.treeData;
      }
    });
  }
  // 选择行政区划
  onConfirmPickerArea({ selectedOptions }) {
    console.log("选择行政区划--->", selectedOptions);
    this.$emit("onConfirm", selectedOptions);
  }
  // 选择事件类型
  public onConfirmType(val, index) {
    // console.log("onConfirmPicker-------->",this.eventTypeList);
    this.$emit("onConfirm", JSON.stringify({ val: val, index: index,eventTypeList:this.eventTypeList }));
  }
  // 选择事件级别
  public onConfirmLevel(val, index) {
    console.log("onConfirmPicker-------->", val, index);
    this.$emit("onConfirm", JSON.stringify({ val: val, index: index }));
  }
  public onConfirm(val) {
    this.$emit("onConfirm", val);
  }
  public onCancel() {
    this.$emit("onCancel");
  }
}
</script>

<style lang="less" scoped></style>
