<template>
        <div class="mainApp">
                 <van-loading type="spinner" v-if="loading" class="loading" vertical 
                    >登陆中...</van-loading >
        </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import axios from "axios";
import apiServer from "@/api/request-service";
const CryptoJS = require("crypto-js");
import gisConfig from '@/components/baseMap/gisConfig.json';


@Component({})
export default class MainApp extends Vue {
    private loading = true;
    private indentification_login_name= '';
    private  getTitleName() {
        const that = this;
        return new Promise((resolve,reject)=>{
                apiServer.titleName({ keys: ["system_identification_login_name"] }, function (res) {
                let indentification_login_name =
                    "system_identification_login_name" + res.data.data[0].configValue ||
                    "gsafety@emis2020";
                that.indentification_login_name = indentification_login_name.substring(
                    indentification_login_name.length - 32,
                    indentification_login_name.length
                );
                resolve(that.indentification_login_name)
        });
        })
 
  }
  async created() {
    try{
       let res  = await this.getTitleName();
      
       console.log(res,'res');
        this.submit(res);
    }catch(e){
        console.log(e);
    }
  }
    // 登录
  public submit(indentification_login_name) {
    const that = this;
     let path = this.$route.query.path;
    this.loading = true;
    const params = {
      username:gisConfig.loginParams.name,
      password: this.aesEncrypt(gisConfig.loginParams.password, this.indentification_login_name),
    };
    apiServer.userLogin(params, function (res) {
      that.loading = false;
      console.log("++++++++++++++login", res);
      if (res.data.status === 200) {
        localStorage.setItem("token", res.data.data.token);
        localStorage.setItem("role", JSON.stringify(res.data.data.role));
        localStorage.setItem("teamId", res.data.data.teamId);
        axios.defaults.headers.common["token"] = res.data.data.token;
        if(!path|| path =='mainApp'){
             that.$router.push("/home");
        } else {
           that.$router.push(`/${path}`);
        }
       
      } else {
        that.$toast(res.data.msg);
      }
    });
    return;
  }
  /**
   * 密码加密方法
   */
  aesEncrypt(message, key, iv?) {
    let encryptKey = CryptoJS.enc.Utf8.parse(key);
    let cipher = CryptoJS.AES.encrypt(message, encryptKey, {
      iv: !!iv ? CryptoJS.enc.Hex.parse(iv) : null,
      mode: !!iv ? CryptoJS.mode.CBC : CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return cipher.ciphertext.toString(CryptoJS.enc.Base64);
  }
}
</script>

<style lang="less" scoped>
 
.mainApp{
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
}
</style>