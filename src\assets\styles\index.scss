// 皮肤样式
@import './themes/variables.scss';
// 样式初始化
@import './base/base.scss';
// 封装样式
@import './helpers/mixin.scss';
// 自定义样式
// @import './themes/themes.scss';
// 公共样式
 @import './common/common.scss';
//elementui---重置样式
@import "./resetElementUiCss/resetElementui.scss";

.level1{
  color: red !important;
}
.level2{
  color: darkorange !important;
}
.level3{
  color: darkgoldenrod !important;
}
.level4{
  color: blue !important;
}

.van-image-preview__image img {
  // width: initial;
  width: 100%;
  margin-left: 50%;
  transform: translateX(-50%);
}
.van-button--primary {
  background-color: #326eff !important;
  border: 1px solid #326eff !important;
}