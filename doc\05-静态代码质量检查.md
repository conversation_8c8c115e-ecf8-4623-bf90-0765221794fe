# 静态代码质量检查  
> 客户端用到editconfig、prettier、tslint来辅助代码质量检查，推送到服务端主要依赖sonar服务    

## 静态代码质量Git提交Hook      
主要依赖`husky`三方包辅助处理
```  js 
  "husky": {
    "hooks": {
      "pre-commit": "npm run lint",
      "pre-push": "npm run build",
      "commit-msg": "validate-commit-msg"
    }
  },  
```

## EditorConfig    
EditorConfig帮助开发人员在不同的编辑器和IDE之间定义和维护一直的编码样式，根据文件类型遵循统一定义的样式。  
``` js    
# Editor configuration, see http://editorconfig.org
root = true

[*]
charset = utf-8
indent_style = space
indent_size = 2
end_of_line = lf

[*.{md,markdown}]
max_line_length = off
insert_final_newline = true
trim_trailing_whitespace = false

[*.js]
insert_final_newline = false
trim_trailing_whitespace = true

[*.json]
insert_final_newline = true
trim_trailing_whitespace = false

[*.{java,cs}]
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = 140

[*.gradle]
indent_style = space
indent_size = 4
insert_final_newline = true
trim_trailing_whitespace = true

[*.yml]
insert_final_newline = true
trim_trailing_whitespace = true

[*.ts]
insert_final_newline = true
trim_trailing_whitespace = true
```  

## Prettier  
Prettier是一种代码格式化工具，能够解析代码，使代码根据设定的规则来重新打印出格式规范的代码。  
```  js
{
"singleQuote": true,
"semi": true,
"printWidth": 140,
"tabWidth": 2,
"trailingComma": "none",
"bracketSpacing": true
}  
```  

## TSLint  
TSLint是一种可扩展的静态分析工具，可检查TypeScript代码的可读性，可维护性和功能性错误。它在现代编辑器和构建系统中得到广泛支持，如下是cad客户端中的lint规则，对客户端配置和格式化程序进行自定义。  
```  js  
{
    "tslint.enable": true,
    "tslint.autoFixOnSave": true,
    "rulesDirectory": [
        "node_modules/codelyzer"
    ],
    "rules": {
        "class-name": true,
        "comment-format": [
        true,
        "check-space"
        ],
        "curly": true,
        "eofline": true,
        "forin": true,
        "indent": [
            true,
            "spaces"
            ],
        "label-position": true,
        "max-line-length": [
            true,
            140
            ],
        "member-access": false,
        "member-ordering": [
        true,
        "static-before-instance",
        "variables-before-functions"
        ],
        "no-arg": true,
        "no-bitwise": true,
        "no-console": [
            true,
            "debug",
            "info",
            "time",
            "timeEnd",
            "trace"
        ],
        "no-construct": true,
        "no-debugger": true,
        "no-duplicate-variable": true,
        "no-empty": false,
        "no-eval": true,
        "no-shadowed-variable": true,
        "no-string-literal": false,
        "no-switch-case-fall-through": true,
        "no-trailing-whitespace": true,
        "no-unused-expression": true,
        "no-use-before-declare": false,
        "no-var-keyword": true,
        "object-literal-sort-keys": false,
        "one-line": [
            true,
            "check-open-brace",
            "check-catch",
            "check-else",
            "check-whitespace"
        ],
        "quotemark": [
            true,
            "single"
         ],
        "radix": true,
        "semicolon": [
            true,
            "always"
        ],
        "triple-equals": [
            true,
            "allow-null-check"
        ],
    "typedef-whitespace": [
        true,
        {
        "call-signature": "nospace",
        "index-signature": "nospace",
        "parameter": "nospace",
        "property-declaration": "nospace",
        "variable-declaration": "nospace"
        }
    ],
    "variable-name": false,
    "whitespace": [
        true,
        "check-branch",
        "check-decl",
        "check-operator",
        "check-separator",
        "check-type"
    ],
    "directive-selector": [
        false,
        "attribute",
        "mf",
        "camelCase"
    ],
    "component-selector": [
        false,
        "element",
        "mf",
        "kebab-case"
    ],
    "use-input-property-decorator": true,
    "use-output-property-decorator": true,
    "use-host-property-decorator": true,
    "no-input-rename": true,
    "no-output-rename": true,
    "use-life-cycle-interface": true,
    "use-pipe-transform-interface": true,
    "component-class-suffix": true,
    "directive-class-suffix": true,
    "pipe-naming": [
        true,
        "camelCase"
        ]
    }
}  
```  

## sonar集成   
``` js  
gulp.task('sonar', () => {
  const option = {
    sonar: {
      host: {
        url: 'http://************:9000'
      },
      login: 'devappuser',
      password: 'devapp',
      projectKey: 'violet-seed',
      projectName: 'violet-seed',
      projectVersion: '1.0.0',
      sources: 'src',
     // test: 'tests',
      language: 'ts',
      sourceEncoding: 'UTF-8',
      ts: {
        tslintpath: 'node_modules/tslint/bin/tslint',
        tslintconfigpath: 'tslint.json',
        lcov: {
          reportpath: 'coverage/lcov.info'
        }
      },
      exec: {
        maxBuffer: 1024 * 1024
      }
    }
  }  
```

