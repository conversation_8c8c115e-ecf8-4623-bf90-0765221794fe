<template>
  <div class="div_big">
    <van-nav-bar :title="requestObj.org_name" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
     <!-- <van-icon name="ellipsis" size="25" />  -->
     
  </template>
   </van-nav-bar>
    <!-- 主体 -->
  <div class="org_main_box">
    <ul class="duty_org_select_list">
          <div class="div_parent" v-for="(item,index) in orgList" :key="index">
            <span v-if="item.type=='1'"  class="type_class_title">当前用户所在机构</span>
            <span v-if="item.type=='2'"  class="type_class_title">区市其他机构</span>
            <div class="class_li" v-for="(itemch,indexch) in item.dutyOrgList" :key="'ch'+indexch">
              <div v-if="indexch!=0" class="div_line"></div>
              <li @click="selectedOrg(itemch)" >
                <span>
                  <span>
                    <img v-if="item.type=='1'" class="class_type" src="../../assets/images/icons/orgsj.png" alt="" srcset=""> 
                    <img v-if="item.type=='2'"  class="class_type" src="../../assets/images/icons/orgqj.png" alt="" srcset=""> 
                    <img v-if="item.type=='3'" class="class_type" src="../../assets/images/icons/orghg.png" alt="" srcset=""> 
                  </span>
                {{itemch.orgName}} 
                <span class="selectSuccess" v-if="requestObj.org_code&&requestObj.org_code==itemch.orgCode">
                  <img src="../../assets/images/icons/duihao.png" alt="" srcset="">
                </span>
                <span class="selectSuccess" >
                  <img src="../../assets/images/icons/duihao.png" alt="" srcset="">
                </span>
                </span>
              </li>
            </div>
            
          </div>
          <!-- <div class="div_parent">
             <span class="type_class_title">区市值班</span>
            <li>
              <span><span><img class="class_type" src="../../assets/images/icons/orgqj.png" alt="" srcset=""> </span>烟台市地震监测中心台</span>
            </li>
          </div> -->
          
          <!-- <div class="div_line"></div> -->
          
          
          
        </ul>
  </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue ,Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
@Component({
  name: 'dutyOrgSelect',
  components: { 
  }
})
export default class dynamic extends Vue {
  @Prop(Object) private requestObj :Object;
  private value = '';
  private orgList=[];

  GoBack(){
    let _this=this;
    _this.$emit("close")
  }
  selectedOrg(_item){
    this.$emit("close",{"orgcode":_item.orgCode,"orgname":_item.orgname})
  }
  private created() {
    let _this = this;
    _this.$enJsBack();
    console.log("select orgcode",_this.requestObj)
    console.log("dddd"+_this.$store.state.userInfo.orgcode)
    //先添加本级机构
    let cityObj={
      type:"1",
      dutyOrgList:[
        {
          orgCode:_this.$store.state.userInfo.orgcode,
          orgName:_this.$store.state.userInfo.orgName
        }
      ]
    }
    _this.orgList.push(cityObj)
    apiServer.findOrgChildrenById({orgCode:_this.$store.state.userInfo.orgcode},function (res) {
      console.log("res===>",res)
      if(res.data){
        let list=res.data.data;
        list=list.map((v)=>{
          return {

          }
        })
        let districtObj={
          type:"2",
          dutyOrgList:list
        }
        _this.orgList.push(districtObj)
        console.log("list---",list)
        //_this.orgList=;
            //_this.dutyTitle=res.data.data[0].dutyOrgList[0]['orgName'];
        //console.log("orgList===>"+JSON.stringify(_this.orgList))
      }
    })
  }
  mounted(){
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      console.log("调用返回")
      _this.GoBack()
    })

  }
}
</script>
<style scoped lang="scss">
.div_big {
  height: 100%;
  [class*=van-hairline]::after{
        border: none;
  }
  .org_main_box{
    width: 100%;
    height: calc( 100% - 46px );
    background: #f4f7f8;
    overflow: auto;
    .duty_org_select_list{
      width: 100%;
      .div_parent{
        >span{
          display: block;
          line-height: 45px;
          width: 90%;
          font-size: 18px;
          padding-left: 5%;
          color: #777777;
        }
        >.class_li{
          .div_line{
            width: 90%;
            margin-left: 5%;
            height: 1px;
            border-top: #c9c9c9 solid 1px;
          }
          >li{
            width: 100%;
            height: 6vh;
            line-height: 6vh;
            background: white;
            border-radius: 5px;
            position: relative;
            font-size: 16px;
            >span{
              display: inline-block;
              height: 6vh;
              img{
                height: 4vh;
                vertical-align: middle;
              }
            }
            .class_type{
              height: 3vh;
              vertical-align: middle;
              padding-right: 10px;
              margin-top: -5px;
            }
            
            >span:nth-of-type(1){
              width: 100%;
              padding-left: 5%;
            }
            .selectSuccess{
              display: inline-block;
              width: 6vh;
              height: 6vh;
              position: absolute;
              right: 10px;
            }
          }
        }
        
      }
     
      
      

    }
  }
}
</style>
