<template>
  <div class="fileshow_big">
    <!-- invoke common List -->
    <div class="file_title_div" v-if="showObject && showObject.showText">
           附件：
        </div>
    <div class="info_big">
      
      <div class="info_content">
        
        <div class="img_list">
          <span v-for="(item,index) in imgList" @click="openImg(index)" :key="index">
            <img  :src="item.urlOuterNet" alt="" srcset="">
          </span>
        </div>
        <!-- <p>
          <img v-for="(item,index) in imgList" :src="item.urlOuterNet" alt="" :key="index">
        </p> -->
        <p v-if="showObject && showObject.orgName">{{showObject && showObject.orgName}}</p>
      </div>
      <div class="attachmentList" v-if="fileList.length>0">
       
        
        <div class="attach_area">
          <li v-for="(item,index) in fileList" :key="index" @click="openFile(item)">
            <span><img src="../../assets/images/ppt.png" alt="" srcset=""></span>
            <span>{{item.name}}</span>
          </li>
        </div>
        
      </div>
    </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue , Prop, Watch} from 'vue-property-decorator';
import { ImagePreview } from 'vant';
//const baseServe = window['g'].BASEIP;
@Component({
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
  }
})
export default class fileShow extends Vue {
  @Prop(Object) private showObject :Object;
  imgList: any =[];
  fileList:any =[];

  @Watch('showObject',{deep: true, immediate: true})
  private initData() {
    this.initFileItem();
  }

  initFileItem(){
    let _this=this;
    let attachmentList=_this.showObject && _this.showObject['attachmentList'];
    if(attachmentList&&attachmentList.length>0){
      attachmentList.forEach((item,index) => {
        if(_this.$whetherImg(item.name)){
          let ipconfig="";
          if(window['g'].IP.indexOf("/gapi")>=0){
            ipconfig = window['g'].IP.split("/gapi")[0];
          }else{
            ipconfig = window['g'].IP;
          }
          ipconfig = process.env.NODE_ENV === 'development' ? 'http://************:8990/' : ipconfig;      
          item.urlOuterNet=item.path?ipconfig+item.path+'':ipconfig+item.urlOuterNet+'';
          _this.imgList.push(item);

        }else{
          _this.fileList.push(item);
        }
        console.log(_this.fileList, '==========================_this.fileList')
      });
      console.log(_this.imgList, _this.fileList, '========================>_this.fileList')
    }
  }
  openImg(_index){
    let _this=this;
    console.log("open img index===>"+_index);
    console.log(_this.imgList)
    let imgsArr=[];
    _this.imgList.forEach((item,index)=>{
      console.log(item)
      imgsArr.push(item.urlOuterNet)
    })
    ImagePreview({
      images: imgsArr,
      startPosition: _index,
      onClose() {
        // do something
      },
    });
    
  }
  openfile_old(_item){
    let _this=this;
    console.log(_item)
    const toast = _this.$toast.loading({
          duration: 0, // 持续展示 toast
          forbidClick: true,
          message: '正在下载',
        });
    
    let timeout= setTimeout(() => {
      let displaynone = document.querySelector(".van-toast")['style'].display;
      if(displaynone!='none'){
         _this.$toast.clear();
        _this.$toast("请求超时")
      }
    }, 30000);
    let token=localStorage.getItem("token")
    console.log(_item.urlOuterNe)
    let dowloadUrl= window['g'].IP+"/gemp-app/api/gemp/app/basement/attachment/download/v1"
    let paramStr="?fileId="+_item.attachId+"&token="+token;
    _this['$gsmdp'].downloadFilePost({
      url: dowloadUrl,
      //url:"https://outexp-beta.cdn.qq.com/outbeta/2020/06/18/comgsafetyfoshan_3.1.0_eec3c153-f056-58bd-b88e-3b71b4c37f5c.apk",
      header:{
        token:token,
        fileId:_item.attachId
      },
      request:"post",
      filePath: '/storage/emulated/0/com.gsafety.main/file/'+_item.name,
      success: function(res) {
        alert(JSON.stringify(res))
        const filePath = res.tempFilePath
        alert("下载成功打开"+filePath)
        _this['$gsmdp'].openFile({
          filePath: filePath,
          success: function(res) {
            //alert(JSON.stringify(res))
            console.log('打开文档成功')
          }
        })
      },
      taskRunning(result){
        //_this.$toast("start==>"+taskres)
        console.log(JSON.stringify(result))
        toast.message="正在下载"+result.progress+"%"
        console.log(JSON.stringify(result))
      }
      ,taskComplete(result) {
        _this.$toast.clear();
        clearTimeout(timeout)
        //alert('Download taskComplete: ' + JSON.stringify(result));
         const filePath = result.tempFilePath
        _this['$gsmdp'].openFile({ 
          filePath: filePath,
          success: function(res) {
            //alert(JSON.stringify(res))
            console.log('打开文档成功')
          }
        })
      }
    })
  }
  openFile(_item){
    let _this=this;
    console.log(_item)
    //alert(JSON.stringify(_item))
    const toast = _this.$toast.loading({
          duration: 0, // 持续展示 toast
          forbidClick: true,
          message: '正在下载',
        });
    let timeout= setTimeout(() => {
      let displaynone = document.querySelector(".van-toast")['style'].display;
      if(displaynone!='none'){
         _this.$toast.clear();
        _this.$toast("请求超时")
      }
    }, 30000);
    let token=localStorage.getItem("token")
    console.log(_item.urlOuterNet)   
    let dowloadUrl= window['g'].IP+"/gemp-user/api/attachment/download/v1"
    console.log(dowloadUrl,'下载地址')
    let paramStr="?fileId="+_item.attachId+"&token="+token;
    // let paramStr="?fileId=afb2bff186d74dbfacdd060526b01267&token="+token;
    
    console.log(paramStr,'参数')
    _this['$gsmdp'].downloadFilePost({
      url: dowloadUrl,
      fileName:_item.name,
      params:{
        token:token,
        fileId:_item.attachId
      }
      ,taskComplete(result) {
        console.log("----"+JSON.stringify(result))
        _this.$toast.clear();
        clearTimeout(timeout)
         const filePath = result.filePath
         let fileType=_this.$findFileType(filePath);
         switch (fileType) {
          case "1":
            _this['$gsmdp'].previewVideo({
                videoPath: filePath,
                success(result) {
                  console.log('success' + result);
                },
                fail(err) {
                  console.log(err);
                }
              });
             break;
          case "2":
            _this['$gsmdp'].previewAudio({
              filePath:filePath,
              success: function (res) {
                console.log('打开音频成功');
              }
            });
            break;
          case "3":
            _this['$gsmdp'].openFile({
              filePath: filePath,
              success: function(res) {
                 console.log('打开文档成功')
              }
            })
            break;
           default:
             break;
         }
         //判断文件下载格式
      }
    })
  }
  created() {
   // let _this=this;
    
  }
  mounted(){
    // this.initFileItem();
  }
}
</script>
<style scoped lang="scss">
.fileshow_big{
  width: 100%;
  background: #fff;
  .file_title_div{
      width: 100%;
      font-size: 4.2vw;
      height: 12vw;
      line-height: 12vw;
      border-top: 1px solid #c7c7c7;
      padding-left: 5%;
    }
  .info_big{
    width: 90%;
    margin:0 5%;
    background: white;
    border-radius: 15px;
    font-size: 15px;
    
    .img_list{
      width: 100%;
      // padding-left: 5%;
      span{
        display: inline-block;
        width: 30%;
        margin: 1.4vw;
        img{
          width: 26vw;
          height: 26vw;
          border-radius: 10px;
        }
      }
    }
    .info_content{
      width: 100%;
      //padding: 0px 2%;
      border-top: 1px solid #f2f2f2;
      li{
        padding-top: 10px;
        font-size: 13px;
        text-align: center;
        color: #4277bf;
      }
      p:nth-of-type(1){
        text-align: center;
        img{
          width: 80%;
        }
      }
      p:nth-of-type(2){
        float: right;
        font-size: 15px;
        line-height: 20px;
      }
    }
    .attachmentList{
      line-height: 2.5rem;
      >div{
        float: left;
        width: 3rem;
      }
      .attach_area{
        float: left;
        width: calc( 100% - 3rem ) ;
      }
      li{
        float: left;
        width: 100%;
        height: 48px;
        // padding-left: 5%;
        color: #4277bf;
        span{
          display: inline-block;
          height: 100%;
        }
        span:nth-of-type(1){
          width: 10%;
          float: left;
        }
        span:nth-of-type(2){
          width: 90%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        img{
          height: 25px;
          vertical-align: middle;
          margin-right: 6px;
          margin-top: -3px;
        }
      }
    }
  }
}

</style>
