<template>
  <div class="base-map-container">
    <!-- 地图容器 -->
    <div :id="mapId || 'g2map'" class="base-map"></div>

    <!-- 图层树控制面板 -->
    <div class="layer-tree-panel" :class="{ 'panel-expanded': showLayerTree }">
      <!-- 图层树按钮 -->
      <div class="layer-tree-toggle" @click="toggleLayerTree">
        <i class="iconfont icon-layers"></i>
        <span class="toggle-text">图层</span>
      </div>

      <!-- 图层树内容 -->
      <div class="layer-tree-content" v-show="showLayerTree">
        <!-- 底图切换 -->
        <div class="layer-group">
          <div class="group-title">
            <i class="iconfont icon-map"></i>
            <span>底图</span>
          </div>
          <div class="base-map-options">
            <div class="base-map-option" :class="{ active: currentBaseMap === 'vector' }" @click="switchBaseMap('vector')">
              <div class="option-preview vector-preview"></div>
              <span class="option-label">矢量图</span>
            </div>
            <div class="base-map-option" :class="{ active: currentBaseMap === 'satellite' }" @click="switchBaseMap('satellite')">
              <div class="option-preview satellite-preview"></div>
              <span class="option-label">影像图</span>
            </div>
          </div>
        </div>

        <!-- 功能图层 -->
        <div class="layer-group">
          <div class="group-title">
            <i class="iconfont icon-layer"></i>
            <span>功能图层</span>
          </div>
          <div class="layer-list">
            <!-- 路况图层 -->
            <div class="layer-item">
              <div class="layer-info">
                <i class="layer-icon traffic-icon"></i>
                <span class="layer-name">路况信息</span>
              </div>
              <van-switch v-model="layers.traffic" size="20px" @change="toggleLayer('traffic', $event)" />
            </div>

            <!-- 水系图层 -->
            <div class="layer-item">
              <div class="layer-info">
                <i class="layer-icon water-icon"></i>
                <span class="layer-name">水系分布</span>
              </div>
              <van-switch v-model="layers.water" size="20px" @change="toggleLayer('water', $event)" />
            </div>

            <!-- 天气图层 -->
            <div class="layer-item">
              <div class="layer-info">
                <i class="layer-icon weather-icon"></i>
                <span class="layer-name">天气信息</span>
              </div>
              <van-switch v-model="layers.weather" size="20px" @change="toggleLayer('weather', $event)" />
            </div>
          </div>
        </div>

        <!-- 图层透明度控制 -->
        <div class="layer-group" v-if="hasActiveOverlays">
          <div class="group-title">
            <i class="iconfont icon-opacity"></i>
            <span>透明度</span>
          </div>
          <div class="opacity-controls">
            <div class="opacity-item" v-if="layers.traffic">
              <span class="opacity-label">路况</span>
              <van-slider v-model="opacity.traffic" :min="0" :max="100" @change="changeOpacity('traffic', $event)" />
              <span class="opacity-value">{{ opacity.traffic }}%</span>
            </div>
            <div class="opacity-item" v-if="layers.water">
              <span class="opacity-label">水系</span>
              <van-slider v-model="opacity.water" :min="0" :max="100" @change="changeOpacity('water', $event)" />
              <span class="opacity-value">{{ opacity.water }}%</span>
            </div>
            <div class="opacity-item" v-if="layers.weather">
              <span class="opacity-label">天气</span>
              <van-slider v-model="opacity.weather" :min="0" :max="100" @change="changeOpacity('weather', $event)" />
              <span class="opacity-value">{{ opacity.weather }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { Switch, Slider } from 'vant';
import gisAbility from '@/utils/gis/gisAbility';
import { EnvironmentContext } from '@/utils/gis/environmentContext';

// 注册 Vant 组件
Vue.use(Switch);
Vue.use(Slider);

@Component({
  components: {
    'van-switch': Switch,
    'van-slider': Slider
  }
})
export default class baseMap extends Vue {
  @Prop() mapId: String;

  // 图层树控制
  private showLayerTree: boolean = false;

  // 当前底图类型
  private currentBaseMap: string = 'vector'; // 'vector' | 'satellite'

  // 图层状态
  private layers = {
    traffic: false, // 路况图层
    water: false, // 水系图层
    weather: false // 天气图层
  };

  // 图层透明度
  private opacity = {
    traffic: 80, // 路况图层透明度
    water: 70, // 水系图层透明度
    weather: 60 // 天气图层透明度
  };

  // 图层实例存储
  private layerInstances = {
    vectorBase: null, // 矢量底图
    satelliteBase: null, // 影像底图
    traffic: null, // 路况图层
    water: null, // 水系图层
    weather: null // 天气图层
  };

  mounted() {
    this.initMap();
  }

  // 计算属性：是否有激活的叠加图层
  get hasActiveOverlays(): boolean {
    return this.layers.traffic || this.layers.water || this.layers.weather;
  }

  // 初始化地图
  private initMap() {
    gisAbility.initEgisMap(this.mapId); // 初始化egisMap对象
    gisAbility.createMapTileLayer(); // 创建TileLayer图层实例，加载天地图WMTS服务数据
    this.initLayers(); // 初始化所有图层
  }

  // 初始化所有图层
  private initLayers() {
    // 这里将在后续实现具体的图层初始化逻辑
    console.log('初始化图层...');
  }

  // 切换图层树显示/隐藏
  private toggleLayerTree() {
    this.showLayerTree = !this.showLayerTree;
  }

  // 切换底图
  private switchBaseMap(type: string) {
    if (this.currentBaseMap === type) return;

    this.currentBaseMap = type;
    console.log(`切换底图到: ${type}`);

    // 这里将在后续实现具体的底图切换逻辑
    if (type === 'vector') {
      this.showVectorMap();
    } else if (type === 'satellite') {
      this.showSatelliteMap();
    }
  }

  // 显示矢量地图
  private showVectorMap() {
    console.log('显示矢量地图');
    // 具体实现将在后续添加
  }

  // 显示影像地图
  private showSatelliteMap() {
    console.log('显示影像地图');
    // 具体实现将在后续添加
  }

  // 切换图层显示/隐藏
  private toggleLayer(layerType: string, visible: boolean) {
    console.log(`切换图层 ${layerType}: ${visible ? '显示' : '隐藏'}`);

    switch (layerType) {
      case 'traffic':
        this.toggleTrafficLayer(visible);
        break;
      case 'water':
        this.toggleWaterLayer(visible);
        break;
      case 'weather':
        this.toggleWeatherLayer(visible);
        break;
    }
  }

  // 切换路况图层
  private toggleTrafficLayer(visible: boolean) {
    console.log(`路况图层: ${visible ? '显示' : '隐藏'}`);
    // 具体实现将在后续添加
  }

  // 切换水系图层
  private toggleWaterLayer(visible: boolean) {
    console.log(`水系图层: ${visible ? '显示' : '隐藏'}`);
    // 具体实现将在后续添加
  }

  // 切换天气图层
  private toggleWeatherLayer(visible: boolean) {
    console.log(`天气图层: ${visible ? '显示' : '隐藏'}`);
    // 具体实现将在后续添加
  }

  // 改变图层透明度
  private changeOpacity(layerType: string, value: number) {
    console.log(`设置 ${layerType} 图层透明度: ${value}%`);

    // 更新透明度值
    this.opacity[layerType] = value;

    // 应用透明度到对应图层
    switch (layerType) {
      case 'traffic':
        this.setTrafficOpacity(value);
        break;
      case 'water':
        this.setWaterOpacity(value);
        break;
      case 'weather':
        this.setWeatherOpacity(value);
        break;
    }
  }

  // 设置路况图层透明度
  private setTrafficOpacity(opacity: number) {
    console.log(`设置路况图层透明度: ${opacity}%`);
    // 具体实现将在后续添加
  }

  // 设置水系图层透明度
  private setWaterOpacity(opacity: number) {
    console.log(`设置水系图层透明度: ${opacity}%`);
    // 具体实现将在后续添加
  }

  // 设置天气图层透明度
  private setWeatherOpacity(opacity: number) {
    console.log(`设置天气图层透明度: ${opacity}%`);
    // 具体实现将在后续添加
  }
}
</script>
<style scoped lang="less">
.base-map-container {
  position: relative;
  width: 100%;
  height: 100%;

  .base-map {
    width: 100%;
    height: 100%;
  }

  // 图层树控制面板
  .layer-tree-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    // 图层树按钮
    .layer-tree-toggle {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.1);
      }

      .iconfont {
        font-size: 18px;
        color: #409eff;
        margin-right: 8px;
      }

      .toggle-text {
        font-size: 14px;
        color: #333;
        font-weight: 500;
      }
    }

    // 展开状态
    &.panel-expanded {
      .layer-tree-toggle {
        border-bottom: 1px solid #eee;
        border-radius: 8px 8px 0 0;
      }
    }

    // 图层树内容
    .layer-tree-content {
      width: 280px;
      max-height: 400px;
      overflow-y: auto;

      // 图层组
      .layer-group {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        // 组标题
        .group-title {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .iconfont {
            font-size: 16px;
            color: #666;
            margin-right: 8px;
          }

          span {
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }
        }

        // 底图选项
        .base-map-options {
          display: flex;
          gap: 12px;

          .base-map-option {
            flex: 1;
            cursor: pointer;
            border: 2px solid #e6e6e6;
            border-radius: 6px;
            padding: 8px;
            transition: all 0.2s ease;

            &:hover {
              border-color: #409eff;
            }

            &.active {
              border-color: #409eff;
              background: rgba(64, 158, 255, 0.05);
            }

            .option-preview {
              width: 100%;
              height: 60px;
              border-radius: 4px;
              margin-bottom: 8px;
              background-size: cover;
              background-position: center;
            }

            .vector-preview {
              background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
              background-image: repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 10px,
                  rgba(255, 255, 255, 0.1) 10px,
                  rgba(255, 255, 255, 0.1) 20px
                ),
                repeating-linear-gradient(-45deg, transparent, transparent 10px, rgba(0, 0, 0, 0.05) 10px, rgba(0, 0, 0, 0.05) 20px);
            }

            .satellite-preview {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              background-image: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
            }

            .option-label {
              display: block;
              text-align: center;
              font-size: 12px;
              color: #666;
              font-weight: 500;
            }
          }
        }

        // 图层列表
        .layer-list {
          .layer-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;

            &:last-child {
              border-bottom: none;
            }

            .layer-info {
              display: flex;
              align-items: center;

              .layer-icon {
                width: 20px;
                height: 20px;
                margin-right: 12px;
                border-radius: 4px;

                &.traffic-icon {
                  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                }

                &.water-icon {
                  background: linear-gradient(135deg, #74b9ff, #0984e3);
                }

                &.weather-icon {
                  background: linear-gradient(135deg, #fdcb6e, #e17055);
                }
              }

              .layer-name {
                font-size: 14px;
                color: #333;
                font-weight: 500;
              }
            }
          }
        }

        // 透明度控制
        .opacity-controls {
          .opacity-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }

            .opacity-label {
              width: 40px;
              font-size: 12px;
              color: #666;
              margin-right: 12px;
            }

            .van-slider {
              flex: 1;
              margin: 0 12px;
            }

            .opacity-value {
              width: 35px;
              font-size: 12px;
              color: #666;
              text-align: right;
            }
          }
        }
      }
    }
  }
}

// 自定义滚动条
.layer-tree-content::-webkit-scrollbar {
  width: 4px;
}

.layer-tree-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.layer-tree-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;

  &:hover {
    background: #a8a8a8;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .layer-tree-panel {
    top: 10px;
    right: 10px;

    .layer-tree-content {
      width: 260px;
      max-height: 350px;
    }

    .layer-group {
      padding: 12px;
    }
  }
}
</style>
