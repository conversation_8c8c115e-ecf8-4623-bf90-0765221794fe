<template>
  <div :id="mapId || 'g2map'" class="base-map"></div>
</template>
<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import gisAbility from '@/utils/gis/gisAbility';
import { EnvironmentContext } from '@/utils/gis/environmentContext';

// import gisConfig from './gis.config.json';
let egis = require('egis-2d');
@Component({
  components: {}
})
export default class baseMap extends Vue {
  @Prop() mapId: String;
  // @Prop() longitude: any;
  // @Prop() latitude: any;
  private elementLayer: any = null;
  private WRGSService: any = null;
  mounted() {
    this.initMap();
  }
  // 初始化地图
  private initMap(triggerCallback: boolean = true) {
    gisAbility.initEgisMap(this.mapId); // 初始化egisMap对象
    gisAbility.createMapTileLayer(); // 创建TileLayer图层实例，加载天地图WMTS服务数据
    // this.addPointToMap();
  }
}
// const emit = defineEmits(['mapEventCallback']);
// const props = defineProps({
//   isEdit: Boolean,
//   locInfo: Object,
//   type: String, // 地图事件类型，签到签退才传，其他不传
//   longitude: Number,
//   latitude: Number,
//   getLocSuccess: Boolean, // 获取当前定位信息是否成功
//   mapId: String
// })
// const VUE:any = getCurrentInstance().appContext.config.globalProperties;
// let elementLayer = ref(null);
// let WRGSService = ref(null);

// watch(() => props.getLocSuccess, () => {
//   console.log('locInfo------->', props.locInfo);
//   // 判断获取当前定位信息是否成功 成功时，展示定位图标； 未成功时，不展示
//   if (!props.getLocSuccess) {
//     return false;
//   }
//   console.log('addPointToMap---->', props.locInfo);
//   addPointToMap();
// }, {deep: true})

// onMounted(() => {
//   initMap();
// })
// /** 初始化地图 */
// const initMap = (triggerCallback: boolean = true) => {
//   gisAbility.initEgisMap(); // 初始化egisMap对象
//   gisAbility.createMapTileLayer(); // 创建TileLayer图层实例，加载天地图WMTS服务数据
//   if (props.isEdit) {
//     eventSubscribe();
//   }
//   if (props.longitude && props.latitude) {
//     addPointToMap();
//   }
// }

// /** 画点 */
// const addPointToMap = () => {
//   console.log('++++++++++++++++++++++++++', elementLayer.value)
//   if (elementLayer.value) {
//     EnvironmentContext.egisMap.removeLayer(elementLayer.value);
//   }
//   elementLayer.value = new egis.carto.ElementLayer({
//     // id: 'ElementLayer',//图层id
//     // name: '元素图层', //图层名称
//     map: EnvironmentContext.egisMap
//   });
//   console.log('+++++elementLayer+++++++++++++++++++++', elementLayer.value)
//   EnvironmentContext.egisMap.addLayer(elementLayer.value);
//   var point = new egis.sfs.Point({
//     x: props.locInfo === undefined ? props.longitude : props.locInfo.longitude,
//     y: props.locInfo === undefined ? props.latitude : props.locInfo.latitude,
//     spatialReference: 4326
//   }); //创建一个点对象
//   //创建简单标记符号
//   var simpleMarkerSymbol = new egis.sfs.SimpleMarkerSymbol({
//     fillColor: new egis.sfs.Color({r:255, g: 0, b:0, a: 255}),
//     size: 5,
//     style: "circle"
//   });
//   //创建图片符号
//   var pictureSymbol = new egis.sfs.PictureMarkerSymbol({
//     source: 'data:image/png;base64,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',
//     width: 32,
//     height: 38,
//     offsetX: 16,
//     offsetY: 38,
//     opacity: 1,
//     rotation: 0
//   });
//   //创建元素
//   var element = new egis.sfs.Element({
//     geometry: point,
//     // symbol: simpleMarkerSymbol
//     symbol: pictureSymbol
//   });
//   (elementLayer.value as any).add(element);//把创建的元素添加到元素图层中
//   EnvironmentContext.egisMap.setCenter(point);
// }

// /** 事件订阅 */
// const eventSubscribe = () => {
//   // 地图点击事件
//   EnvironmentContext.egisMap.on('click', (button: any, shift: any, screenX: any, screenY: any, mapX: any, mapY: any, handle: any) => {
//     console.log('coordinate--->', mapX, mapY)
//     let coordinate = [mapX, mapY];
//     mouseClickFn(coordinate);
//   })
// }

// const mouseClickFn = (coordinate: any) => {
//   // 构造 逆地理编码 服务对象
//   if (!WRGSService.value) {
//     WRGSService.value = new egis.ews.RestWRGSService({
//       url: gisConfig.internetEgisConfig.url,
//       clientId: gisConfig.internetEgisConfig.clientId,
//       clientSecret: gisConfig.internetEgisConfig.clientSecret,
//       authType: gisConfig.internetEgisConfig.authType,
//       tokenUrl: gisConfig.internetEgisConfig.tokenUrl,
//     });
//   }
//   // 构造 逆向地理编码 输入参数对象
//   const WRGSInput = new egis.ews.WRGSInput({
//     location: coordinate.join(','),
//     ext_poi: true, // 是否搜索返回附近poi数据，为true则返回
//     ext_road: true, //是否返回附近道路数据，为true则返回
//   });
//   // 调用逆向地理编码服务的regeocode接口
//   let promise = (WRGSService.value as any).regeocode(WRGSInput);
//   promise.then(
//     (result: any) => {
//       if (elementLayer.value) { // 手动定位时，如果自动获取定位图层存在，清除图层
//         EnvironmentContext.egisMap.removeLayer(elementLayer.value);
//       }
//       // result为服务返回结果数据
//       console.log('result------------->', result);
//       if (result.address_component.country !== '中国') {
//         VUE.$toast('目前的选择不在中国境内,请重新在地图上选择！');
//         return;
//       }
//       (props.locInfo as any).longitude = coordinate[0];
//       (props.locInfo as any).latitude = coordinate[1];
//       (props.locInfo as any).address = result.formatted_address;
//       // 将点添加到地图上
//       addPointToMap();
//       emit('mapEventCallback', result);
//     },
//     (err:any) => {
//       VUE.$toast('获取地理位置失败，请重新选择！');
//       return;
//     },
//   );
// }
</script>
<style scoped lang="less">
.base-map {
  width: 100%;
  height: 100%;
}
</style>
