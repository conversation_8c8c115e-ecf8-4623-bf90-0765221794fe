<template>
  <div class="list">
    <Header title="事件详情"></Header>
    <div class="list-tab">
      <van-form class="list-card">
        <van-cell-group inset v-for="(item, index) in detailsData" :key="index">
          <van-cell
            v-for="ele in item.list"
            :key="ele.prop"
            :value="ele.type !== 'label' ? ele.value : null"
            :label="ele.type === 'label' ? ele.value : null"
          >
            <template #title>
              <van-icon :name="ele.icon" :color="ele.color" style="margin-right: 10px" />
              <span>{{ ele.name }}</span>
            </template>
            <template #right-icon v-if="ele.type === 'phone' && ele.value">
              <van-icon name="phone-circle" class="phone-icon" color="#13b2a1" @click="goToTelPage(ele.value)" />
            </template>
            <template #label v-if="ele.prop.includes('List')">
              <div class="list-casualties" v-if="ele.prop === 'typeItemVOList'">
                <div v-for="(obj, idx) in ele.label" :key="idx" class="custom-title">
                  <div>
                    {{ obj.name }} <span>{{ details[obj.prop] || 0 }}</span>
                  </div>
                  {{ '人' }}
                </div>
              </div>
              <div class="list-casualties" v-else-if="ele.prop.includes('List') && ele.label">
                <div class="attach_area" v-for="(item, index) in ele.label.attachmentList" :key="index" @click="downLoad(item, ele.type)">
                  <span><img src="../../assets/images/ppt.png" alt="" srcset="" /></span>
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </template>
          </van-cell>
        </van-cell-group>
      </van-form>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';
import { downloadReport } from '@/utils/validate';
@Component({
  components: {
    Header
  }
})
export default class add extends Vue {
  private detailsData: any = [
    {
      name: 'baseInfo',
      list: [
        {
          name: '事件标题',
          prop: 'eventTitle',
          value: '',
          color: '#c8e3ee',
          icon: 'coupon-o',
          type: 'label'
        },
        {
          name: '信息摘要',
          prop: 'infoAbstract',
          value: '',
          color: '#c8e3ee',
          icon: 'coupon-o',
          type: 'label'
        },
        {
          name: '详情描述',
          prop: 'infoDescription',
          value: '暂无信息',
          color: '#eed49b',
          icon: 'orders-o',
          type: 'label'
        }
      ]
    },
    {
      name: 'originInfo',
      list: [
        {
          name: '更新时间',
          prop: 'updateTimeStr',
          value: '',
          color: '#b5dbe8',
          icon: 'clock-o'
        },
        {
          name: '数据来源单位',
          prop: 'orgName',
          value: '',
          color: '#c8e3ee',
          icon: 'peer-pay'
        }
      ]
    },
    {
      name: 'conInfo',
      list: [
        {
          name: '事发时间',
          prop: 'occurTimeStr',
          value: '',
          color: '#eed49b',
          icon: 'underway-o'
        },
        {
          name: '事发地点',
          prop: 'infoAddress',
          value: '',
          color: '#e4bfa0',
          icon: 'location-o'
        },
        {
          name: '事件类型',
          prop: 'eventTypeName',
          value: '',
          color: '#b5dbe8',
          icon: 'font-o'
        },
        {
          name: '事件级别',
          prop: 'eventLevelName',
          value: '',
          color: '#c8e3ee',
          icon: 'bar-chart-o'
        },
        {
          name: '人员伤亡情况信息',
          prop: 'typeItemVOList',
          value: '',
          color: 'red',
          icon: 'friends-o',
          label: [
            {
              name: '死亡人数',
              color: '#c8e3ee',
              prop: 'deathNum'
            },
            {
              name: '失踪人数',
              color: '#eed49b',
              prop: 'lossNum'
            },
            {
              name: '轻伤',
              color: '#e4bfa0',
              prop: 'minorInjureNum'
            },
            {
              name: '重伤',
              color: '#b5dbe8',
              prop: 'seriousInjureNum'
            },
            {
              name: '受威胁人数',
              color: '#ce997b',
              prop: 'threatenedNum'
            },
            {
              name: '转移人数',
              color: '#323233',
              prop: 'transferedNum'
            },
            {
              name: '受伤人数',
              color: '#af5255',
              prop: 'woundNum'
            },
            {
              name: '被困人数',
              color: '#cdb87a',
              prop: 'trappedNum'
            }
          ]
        }
      ]
    },
    {
      name: 'otherInfo',
      list: [
        {
          name: '附件',
          prop: 'attachList',
          value: '',
          type: 'xxjb',
          color: '#eed49b',
          icon: 'photo-o'
        }
      ]
    }
  ];
  private id: any = ''; // 接报信息id
  public details: any = {}; // 接报信息字段

  private role: any;
  private recordList: any = [];
  async mounted() {
    console.log('信息接报----》', this.$route.query);
    this.id = this.$route.query.id;
    await this.getDetail();
  }

  // 获取信息接报详情
  async getDetail() {
    if (this.id) {
      await this['$api'].InfoRequest.getEventById({ id: this.id }, (res) => {
        console.log('getEventDetail-------->>>>>', res.data.data);
        if (res.data.status === 200) {
          this.details = res.data.data;
          this.details.occurTimeStr = this.formateDate(this.details.occurTime);
          this.details.updateTime = this.formateDate(this.details.updateTime);

          this.detailsData.forEach((item: any) => {
            item.list.forEach((ele: any) => {
              if (ele.prop === 'typeItemVOList') {
                ele.value = null;
              } else if (ele.prop === 'attachList') {
                ele.value = null;
                this.details[ele.prop].forEach((myItem) => {
                  myItem.name = myItem.journalName || myItem.accessoryname;
                });
                ele.label = {
                  attachmentList: this.details[ele.prop],
                  showText: false
                };
              } else {
                ele.value = this.details[ele.prop];
              }
            });
          });
        } else {
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      });
    }
  }

  formateDate(time) {
    // 兼容之前的时间格式
    if (time instanceof Object) {
      return this['$moment'](time.time).format('YYYY-MM-DD HH:mm:ss');
    } else {
      return this['$moment'](time).format('YYYY-MM-DD HH:mm:ss');
    }
  }

  //下载
  downLoad(item, type) {
    const params: any = {
      fileId: item.receiveattachid || item.journalId,
      fileName: item.name,
      fileType: type
    };
    this['$api'].InfoRequest.downLoad(params, (res) => {
      downloadReport(res);
    });
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`;
  }
}
</script>
<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-card {
    margin-top: 5px;
  }
  &-casualties {
    display: flex;
    flex-wrap: wrap;
    color: #323233;
    align-items: center;
    .custom-title {
      width: 50%;
      text-indent: 10px;
      height: 30px;
      line-height: 30px;
      display: flex;
      &:nth-child(2n + 1) span {
        color: #c8e3ee;
      }
      &:nth-child(2n) span {
        color: #e4bfa0;
      }
      &:nth-child(2n + 2) span {
        color: #af5255;
      }
      &:nth-child(4n) span {
        color: #323233;
      }
      div {
        display: flex;
        justify-content: space-between;
        width: 130px;
      }
      span {
        // display: inline-block;
        // width: 100%;
        font-size: 3.6vw;
        // text-align: right;
        font-weight: 600;
      }
    }
  }
  &-tab {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: auto;
    /deep/.van-form {
      //   display: flex;
      //   flex-direction: column;
      height: 100%;
      .van-cell-group {
        // flex: 1;
        // display: flex;
        // flex-direction: column;
        // height: calc(100% - 75px);
        // overflow: auto;
        margin: 12px 10px;
        padding: 0 10px;
        // height: 100%;
        .van-cell {
          padding: 12px 0;
        }
      }
      .van-button--primary {
        background-color: #326eff;
        border: 1px solid #326eff;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
      }
      .submit-btn {
        background: #fff;
      }
    }
  }
  .attach_area {
    display: flex;
    align-items: center;
    float: left;
    width: calc(100% - 3rem);
    margin-bottom: 10px;
  }
  .phone-icon {
    margin: 5px 0 0 5px;
  }
}
</style>
