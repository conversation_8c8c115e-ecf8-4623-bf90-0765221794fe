<template>
  <div class="div_big">
    <van-nav-bar title="预警信息" left-text="返回" left-arrow @click-left="GoBack" @click-right="showPopups">
      <template #right v-if="currentType == '0'">
       新增
      </template>
    </van-nav-bar>
    <commonList
      ref="slider"
      :loadObj="{ name: 'emergencyItem' }"
      :tabArr="tabData"
      :searchObj="{ whetherHave: true, placeholder: '请输入搜索关键词' }"
      :sliderType="sliderType"
      @changeType="changeType"
    ></commonList>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import commonList from '../common/commonList.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    commonList
  }
})
export default class planManageList extends Vue {
  private sliderType = {
    planType: [],
    eventType: []
  };
 currentType = '0';
  changeType(idx, name) {
    let _this = this;
    _this.currentType = idx;
    // _this.tabData.forEach((item, index) => {
    //   if (name == item.label) {
    //     item.children.forEach((element) => {
    //       item.checked = false;
    //     });
    //     _this.sliderType.planType = item.children;
    //   }
    // });
    // _this.getEventType()
  }
  showPopups() {
    // this.$refs.slider['showPopup']();
    this.$router.push({path: '/emergency/add'})
  }
  tabData = [
    { code: '10000', name: '已发布', status: '1', way: '1', },
    { code: '20000', name: '收件箱', status: '2', way: '2' },
    { code: '30000', name: '草稿箱', status: '3', way: '3' },
    { code: '40000', name: '已解除', status: '2', way: '1' }
  ];
  selected: any = 0;
  refreshing: any = false;
  loading: any = false;
  showDetail: any = false;
  pageIndex: any = 1;
  pageSize: any = 10;
  keyword: any = '';
  open: any = false;
  position: any = 'left';
  queryMap: any = {};

  onClickLeft() {
    console.log('返回点击');
  }
  clickMore() {
    console.log('打开菜单');
    this.open = true;
  }
  handleObj(item) {
    let _this = this;
    console.log(item);
    if (!item.uploadMore) {
      _this.refreshing = false;
      _this.loading = false;
    }
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.pageIndex = 1;
    let pageObj = { pageIndex: 1, pageSize: _this.pageSize };
    _this.$refs.itemRef['queryRequest'](pageObj);
  }
  load() {
    const _this = this;
    _this.pageIndex++;
    _this.loading = true;
    let pageObj = { pageIndex: _this.pageIndex, pageSize: _this.pageSize };
    _this.$refs.itemRef['queryRequest'](pageObj);
  }
  GoBack() {
    this.$router.push('/');
  }
  closeDetail() {
    this.showDetail = false;
  }
  private planTypes = [];
  // initTab() {
  //   let _this = this;
  //   let param = {};
  //   apiServer.findPlanManageMenu(
  //     param,
  //     function (res) {
  //       let arr = res.data.data;
  //       arr.forEach((item, index) => {
  //         item.name = item.label;
  //         item.code = item.id;
  //       });
  //       _this.tabData = arr;
  //       _this.tabData[0].children.forEach((element) => {
  //         element.checked = false;
  //       });
  //       _this.sliderType.planType = _this.tabData[0].children;
  //     },
  //     true
  //   );
  // }
  getEventType() {
    let _this = this;
    let param = {};
    apiServer.findEventType(
      param,
      function (res) {
        let arr = res.data.data.treeData;
        arr.forEach((element) => {
          element.checked = false;
        });
        _this.sliderType.eventType = arr;
      },
      true
    );
  }
  created() {
    let _this = this;
    // _this.initTab();
    // _this.getEventType()
    _this.$enJsBack();
  }

  mounted() {
    let _this = this;
    _this['$gsmdp'].initGoback(function () {
      console.log('调用返回');
      _this.GoBack();
    });
    //  this.$gsmdp.initGoback(function(){
    //   _this.GoBack()
    // })
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div {
    width: 100%;
  }
}
.content_list {
  height: calc(100% - 46px);
  overflow: auto;
  background: #f1efef;
}
.showMenu {
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
  color: white;
}
</style>
