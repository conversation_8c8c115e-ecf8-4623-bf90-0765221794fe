<template>
  <div class="fileUpload">
    <van-row gutter="20" >
      <van-col span="6">
        <div class="btns btn-camera">
          <van-icon name="photo" size="50" color="#909399" />
          <input type="file" accept="image/*"  @change="handleUploadFile" capture="camera" />
          <span class="btn-label">拍照</span>
        </div>
      </van-col>
      <van-col span="6" v-if='!isWweixin'>
        <div class="btns btn-camera">
          <template  >
                <van-icon name="photograph" size="50" color="#909399" />
                <input type="file"   accept="video/*"  @change="handleUploadFile($event)" capture="camcorder" />
                <span class="btn-label" @click="camreFn">拍摄</span>
          </template>
        </div>
      </van-col>
      <van-col span="6" v-if='isWweixin'>
        <div class="btns btn-camera" @click='captureVideo'>
          <template  >
                <van-icon name="photograph" size="50" color="#909399" />
                <span class="btn-label" @click="camreFn">摄像</span>
          </template>
        </div>
      </van-col>
      <!-- <van-col span="6">
        <div class="btns btn-camera">
          <van-icon name="photograph" size="50" color="#909399" />
          <input type="file" accept="video/*"  @change="handleUploadFile" capture="camera" />
          <span class="btn-label">拍视频</span>
        </div>
      </van-col> -->
      <van-col span="6">
        <div class="btns btn-file">
          <van-icon name="column" size="50" color="#909399" />
          <input type="file" accept=".doc,.docx,application/pdf,application/msword" @change="handleUploadFile" />
          <span class="btn-label">文件</span>
        </div>
      </van-col>
       <van-col span="6">
        <div class="btns btn-file" @click='sendLocation'>
          <van-icon name="location" size="50" color="#909399" />
          <span class="btn-label">位置</span>
        </div>
      </van-col>
    </van-row>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import apiServer from '@/api/request-service';
import { Notify } from 'vant';
@Component({
  components: {}
})
export default class fileUpload extends Vue {
  public attachmentList: any = [];
  private isSupprotH5 = false;
  private showVideo = false;
  @Prop({}) locationInfo;
  private camreFn(){
    this.showVideo = true;
  }
  private get isWweixin(){
    return this['wxUtils'].isWeiXin();
  }

  private sendLocation(){
    if(!this.locationInfo.locInfo.longitude||!this.locationInfo.locInfo.latitude){
        Notify({type:'danger',message:'获取当前位置失败，请稍后再试'});
    } 
    this.$emit('sendLocation')
  }


  public handleUploadFile(e: any) {
    this.attachmentList = [];
    const _this = this;
    console.log(e,'e');
    console.log(e.target)
    const file = e.target.files[0];
    // const reader = new FileReader();
    // const aBlob = new Blob([file], { type: 'video/mp4' }); // 指定转换成blob的类型
    const type = file.type;
    const formData:any = new FormData();
    file.status = 'start';
    file.timeStammp = new Date().getTime();
    var urls = null;
       console.error(file,'file');
        console.log(type, 'type');
        console.log(file.size,'size');
        console.log(file.type,'type');
        console.log(file.type,'type');
    if ((window as any).createObjectURL != undefined) {
      //basic
      urls = (window as any).createObjectURL(file);
    } else if (window.URL != undefined) {
      //mozilla(firefox)兼容火狐
      urls = window.URL.createObjectURL(file);
    } else if (window.webkitURL != undefined) {
      //webkit or chrome
      urls = window.webkitURL.createObjectURL(file);
    }
    formData.append('file', file);
    console.log(formData,'formData')
    console.log(urls);
    let attachmentList = [];
    // if (type.indexOf('video') > -1) {
    // 视频消息单独处理
    attachmentList.push({
      url: urls,
      name: type ==''? file.name+'.mp4': file.name,
      status: 'pending',
      timeStammp: file.timeStammp,
      percentage: 0,
      path: urls
    });
    _this.$emit('submitFile', attachmentList, true);
    apiServer.uploadFile(formData, function (_resultObj) {
      attachmentList = [];
      if (_resultObj.status == 200) {
        file.status = 'done';
        file.message = '上传完成';
        file.attachmentList = _resultObj.data;
        attachmentList.push({
          ...file.attachmentList,
          timeStammp: file.timeStammp
        });
        _this.$emit('submitFile', attachmentList);
      }
    });
    // }
    //  else {
    //   apiServer.uploadFile(formData, function (_resultObj) {
    //     if (_resultObj.status == 200) {
    //       file.status = 'done';
    //       file.message = '上传完成';
    //       file.attachmentList = _resultObj.data;
    //       _this.attachmentList.push(file.attachmentList);
    //       _this.$emit('submitFile', _this.attachmentList);
    //     }
    //   });
    // }
    //   apiServer.uploadFile(formData, function (_resultObj) {
    //   if (_resultObj.status == 200) {
    //     file.status = 'done';
    //     file.message = '上传完成';
    //     file.attachmentList = _resultObj.data;
    //     console.log(_resultObj,'aaa')
    //     _this.attachmentList.push(file.attachmentList);
    //     _this.$emit('submitFile', _this.attachmentList);
    //   }
    // });
  }

  private captureVideo(){
    this['wxUtils'].choiseVideo()
  }

  private mounted(){
    this.messsageBus.on('submitFile',(data)=>{
        if(data.notUpload){
          this.$emit('submitFile', data.attachmentList, true);
        }else{
           this.$emit('submitFile', data.attachmentList);
        }
    })
  }
}
</script>

<style lang="less" scoped>
.fileUpload {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  /deep/.van-row {
    width: 100%;
  }
  .btns {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    height: 71px;
    .btn-label {
      display: inline-block;
      position: absolute;
      bottom: 0;
      z-index: 2;
      height: 21px;
      &.btn-labels{
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: red;
      }
    }
    input {
      width: 100%;
      height: 100%;
      position: absolute;
      z-index: 10;
      opacity: 0;
    }
  }
}
#video{
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  background: rgba(0,0,0,0.7);
  height: 100vh;
 
}

 .videobtn{
    position: absolute;
    left: 0;
    bottom: 20px;
    width: 100%;
    color: white;
    text-align: center;
    display: flex;
    justify-content: space-around;
    padding: 0 30px;
    font-size: 20px;
    z-index: 999;
    p{
      margin: 5px 0 !important;
    }
  }
</style>
