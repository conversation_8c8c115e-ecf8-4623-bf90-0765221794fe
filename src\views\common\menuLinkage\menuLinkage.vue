<template>
    <div class="div_big" >
      <div class="city_big">
        <div class="div_title" v-if="isMultiple">
          <span @click="handleBtn(0)">取消</span>
          <span>{{title}}选择</span>
          <span @click="handleBtn(1)">确定</span>
        </div>
        <div class="div_tabs">
          <div class="div_tab" @click="clickTab(index)" :class="{active:item.active}" v-for="(item,index) in tabs" :key="index">
            {{item.name}}
            <span :class="{active:item.active}"></span>
          </div>
        </div>
        <div class="div_content" :class="{isMultiple:isMultiple}" ref="divContent">
          <li v-for="(item,index) in listArr"  :key="index">
            <span class="select_moudle" >
              <van-checkbox v-if="isMultiple" v-model="item.checked" @click="changeCheckBox(item)"></van-checkbox>
              <img v-else src="@/assets/images/icons/icon_org.svg" alt="" srcset="">
            </span>
            <!-- <i class="arrow-down" v-if="item.children !== null"></i> -->
            <span @click="handleData(item,0)">{{item.label}}</span>
            <span @click="handleData(item)" v-if="!item.leaf">
              <img src="@/assets/images/icons/icon_next.svg" alt="" srcset=""><font>展开</font>
            </span>
          </li>
        </div>
      </div>
    </div>
</template>
<script>
// import { queryCityData } from '@/assets/js/service'
import { menuLinkage } from './menuLinkage.js'
export default {
  mixins:[menuLinkage],
};
</script>
<style lang="less" scoped>
@import './menuLinkage';
</style>
