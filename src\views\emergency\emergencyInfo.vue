<template>
  <div class="info_div">
    <van-nav-bar title="预警信息详情" left-text="返回" left-arrow @click-left="GoBack" @click-right="goTransfer">
      <template #right v-if="detailInfo.way == '2'">
        <!-- <van-icon name="ellipsis" size="25" /> -->
        转办
      </template>
    </van-nav-bar>
    <MeetBack @documentBack="documentBack"></MeetBack>
    <div class="info_content">
      <ul>
        <li class="info_cell">
          <span>预警标题</span>
          <span>{{ detailInfo.title }}</span>
        </li>
        <li class="info_cell">
          <span>预警类型:</span>
          <span>{{ detailInfo.typeName }}</span>
        </li>
        <li class="info_cell">
          <span>预警级别：</span>
          <span>{{ detailInfo.levelName }}</span>
        </li>
        <li class="info_cell">
          <span>预警时间：</span>
          <span>{{ detailInfo.startTime }}-{{ detailInfo.endTime }}</span>
        </li>
        <li class="info_cell">
          <span>预警范围：</span>
          <span class="arr-text"
            ><span v-for="(item, index) in detailInfo.districts" :key="item">{{ item }}<span v-if="index > 0">,</span> </span></span
          >
        </li>
      </ul>
      <div class="text_area_div">
        <div class="text_area_title">预警描述</div>
        <div class="text_area_centent">
          {{ detailInfo.warningInfo == '' || detailInfo.warningInfo == null ? '无事故简述' : detailInfo.warningInfo }}
        </div>
      </div>

      <div class="text_area_div">
        <div class="text_area_title">防御指南</div>
        <div class="text_area_centent">
          {{ detailInfo.warningGuide == '' || detailInfo.warningGuide == null ? '无预防措施' : detailInfo.warningGuide }}
        </div>
      </div>
      <ul style="margin-top: 0.5rem">
        <li class="info_cell">
          <span>发布单位：</span>
          <span>{{ detailInfo.issueOrg }}</span>
        </li>
        <li class="info_cell">
          <span>发布人：</span>
          <span>{{ detailInfo.issuePerson }}</span>
        </li>
      </ul>
      <div class="text_area_div" v-if="requestType == 'addTask'">
        <div class="text_area_title">
          接收单位
          <van-icon class="icon_add" name="add-o" @click="handleAdd" size="20px" />
        </div>
        <div class="text_area_centent">
          <van-tag
            closeable
            v-for="item in receiptOrgList"
            class="tag-style"
            :key="item.code"
            size="medium"
            type="primary"
            @close="closeDist(item)"
          >
            {{ item.text }}
          </van-tag>
        </div>
      </div>
      <div class="text_area_div" v-if="requestType == 'addTask'">
        <div class="text_area_title">
          接收人(分组选择)
          <van-icon class="icon_add" name="add-o" @click="handleGroupAdd" size="20px" />
        </div>
        <div class="text_area_centent">
          <van-tag
            closeable
            v-for="item in receiptOrgList"
            class="tag-style"
            :key="item.code"
            size="medium"
            type="primary"
            @close="closeDist(item)"
          >
            {{ item.text }}
          </van-tag>
        </div>
      </div>
    </div>
    <!-- <footerSend></footerSend> -->
    <!-- 接受企业 -->
    <van-popup
      v-model:show="unitShow"
      position="bottom"
      round
      :style="{ height: '50%' }"
      @click-overlay="unitShow = false"
      @click-close-icon="unitShow = false"
    >
      <div class="main">
        <div class="header">
          选中接收单位
          <div class="sureBtn" @click="unitShow = false">确定</div>
        </div>
        <van-checkbox-group v-model="receiptOrgList">
          <van-cell-group>
            <van-cell v-for="(item, index) in orgList" clickable :key="item.code" :title="`${item.text}`" @click="toggle(index)">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxes" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
    <!-- 选择人员 -->
    <van-popup
      v-model="showGroup"
      :close-on-click-overlay="false"
      position="bottom"
      @click-overlay="showGroup = false"
      @click-close-icon="showGroup = false"
      :style="{ height: '60%' }"
    >
      <div class="main">
        <div class="header">选择人员</div>
        <!-- 分组选择 -->
        <div class="title"></div>
        <div class="group-list">
          <van-checkbox-group v-model="checkedGroup" @change="handleGroupChange" direction="horizontal">
            <van-checkbox :name="item.code" v-for="(item, index) in groupList" :key="index" shape="square">{{ item.text }}</van-checkbox>
          </van-checkbox-group>
        </div>
        <div class="title">人员列表</div>
        <van-checkbox-group v-model="groupUser">
          <van-cell-group>
            <van-cell v-for="(item, index) in groupUserList" clickable :key="item.code" :title="`${item.text}`" @click="toggle(index)">
              <template #right-icon>
                <van-checkbox :name="item" ref="checkboxes" />
              </template>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import linkmail from '../../assets/server/mailList_index.js';
import FilePreview from '@/components/filePreview.vue';
import MeetBack from '@/components/meet-back/meet-back';
// import footerSend from './components/footerSend.vue';
import taskRequest from '@/api/webcross/task';
@Component({
  components: {
    FilePreview,
    MeetBack
    // footerSend
  }
})
export default class integratedItem extends Vue {
  @Prop(String) private requestId: string;
  @Prop(Object) private requestObj: any;
  @Prop(String) private requestType: string;
  detailInfo: any = {};
  showObject: any = {};
  unitShow: boolean = false; // 接受单位
  orgList: any = [];
  receiptOrgList: any = [];
  toggle(index) {
    this.$refs.checkboxes[index].toggle();
  }
  closeDist(item) {
    this.receiptOrgList.splice(this.receiptOrgList.indexOf(item), 1);
  }
  handleAdd() {
    this.unitShow = true;
  }
  showGroup: boolean = false;
  handleGroupAdd() {
    this.showGroup = true;
    this.getGroupList();
  }
  groupList: any = []; // 组别列表
  checkedGroup: any = [];
  // 分组
  getGroupList() {
    const params = { type: '1', orgCode: '53020102', access: '1' };
    taskRequest.getGroupManage(params, (res: any) => {
      this.groupList = res.data.data.map((item: any) => {
        return {
          code: item.groupId,
          text: item.groupName
        };
      });
    });
  }
  async handleGroupChange(val) {
    let apiList = val.map((item: any) => this.getgroupUserList(item));
    await Promise.all([...apiList]).then((res) => {
      console.log(res);
      let list = [...res];
      console.log(res, list);
    });
  }
  groupUser: any = [];
  groupUserList: any = [];
  getgroupUserList(groupId) {
    let _this = this;
    const params = { groupId: groupId, nowPage: 1, pageSize: 1000 };
    new Promise((resolve, reject) => {
      taskRequest.getGroupMenberList(params, (res: any) => {
        let data = res.data.data.list.map((item: any) => {
          return {
            code: item.personId,
            text: item.personName
          };
        });
        resolve(data);
        // this.groupUserList = res.data.data
      });
    });
  }
  infoRequest() {
    let _this = this;
    let formdata = new FormData();
    formdata.append('id', _this.requestId);
    const params = {
      id: _this.requestId
    };
    apiServer.findWarningById(params, function (res) {
      let dataHandleInfo = res.data.data; //dataHandle.caseInfo(res);
      _this.detailInfo = dataHandleInfo;
    });
  }
  getOrgData() {
    let _this = this;
    const params = {
      parentCode: JSON.parse(window.localStorage.getItem('role')).districtCode
    };
    apiServer.getCurrentUserTreeList(params, function (res) {
      _this.orgList = res.data.data.map((item) => {
        return { text: item.districtName, code: item.districtCode };
      });
    });
  }
  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
  // 转办
  goTransfer() {}
  documentBack() {
    this.GoBack();
  }
  //获取组织机构
  private getAllOrg() {
    let that = this;
    let parma = {
      orgCode: '53020102',
      updateTime: '',
      userId: ''
    };
    return new Promise(function (resolve, reject) {
      linkmail
        .GetLinkmanList(parma)
        .then(function (response) {
          if (response.data.data) {
            console.log(response.data.data);
            that.orgList = that.filterArray(response.data.data.list, '0');
          } else {
            (that as any).$toast({ message: '请求数据异常!', position: 'bottom', duration: 3000 });
          }
          resolve();
        })
        .catch(function (error) {
          (that as any).$toast({ message: '请求数据异常!', position: 'bottom', duration: 3000 });
          reject(error);
        });
    });
  }

  private filterArray(data, id) {
    //组织机构的递归
    var fa = function (parentid) {
      var _array = [];
      for (var i = 0; i < data.length; i++) {
        var n = data[i];
        if (n.parentcode === parentid) {
          n.children = fa(n.emorgid);
          _array.push(n);
        }
      }
      return _array;
    };
    return fa(id);
  }
  created() {
    let _this = this;
    //_this['$apiServer'].setToken();
    _this.getOrgData();
    _this.infoRequest();
    // _this.getAllOrg();
    // setTimeout(function() {

    // }, 500);
  }
  mounted() {
    let _this = this;
    _this['$gsmdp'].initGoback(function () {
      console.log('调用返回');
      _this.GoBack();
    });
  }
}
</script>
<style scoped lang="scss">
.info_div {
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #f4f7f8;
  .info_content {
    width: 100%;
    height: calc(100% - 106px);
    padding-bottom: 20px;
    overflow-y: auto;
    ul {
      width: 100%;
      background: #fff;
    }
    li {
      width: 100%;
      padding: 0px 3%;
      p {
        margin: 0;
        line-height: 3rem;
        font-size: 17px;
        font-weight: bold;
      }
    }
    .info_cell {
      line-height: 2.5rem;
      color: #333333;
      overflow: hidden;
      display: flex;
      span {
        display: inline-block;
        float: left;
      }
      span:nth-of-type(1) {
        width: 4.6rem;
        color: #000000;
      }
      span:nth-of-type(2) {
        width: calc(100% - 5rem);
      }
      .arr-text {
        display: flex;
        span {
          width: auto;
        }
      }
    }
    .text_area_div {
      width: 100%;
      .text_area_title {
        width: 100%;
        height: 2.5rem;
        line-height: 2.5rem;
        padding: 0px 3%;
        color: c0c0c0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .text_area_centent {
        width: 100%;
        background: #fff;
        line-height: 2.5rem;
        padding: 0px 3%;
        .tag-style {
          margin-right: 10px;
        }
      }
    }
    .info_div_attachment {
      width: 100%;
      background: #fff;
      padding: 0px 3%;
      span {
        display: inline-block;
        float: left;
        line-height: 2.5rem;
        img {
          width: 1.5rem;
          vertical-align: middle;
          float: left;
          margin-top: 0.3rem;
        }
      }
      span:nth-of-type(1) {
        width: 3rem;
      }
      span:nth-of-type(2) {
        width: calc(100% - 3rem);
      }
    }
  }
}
.main {
  .header {
    position: relative;
    height: 54px;
    line-height: 54px;
    text-align: center;
    font-size: 18px;
    border-bottom: 1px solid #ccc;
    /deep/.van-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .title {
    padding: 5px 10px;
  }
  .group-list {
    padding: 5px 10px;
  }
  .userList {
    padding: 5px 10px;
  }
}
</style>
<style lang="less">
.info_div {
  .replyFile .file-container {
    width: 100%;
    height: 100%;
  }
}
</style>
