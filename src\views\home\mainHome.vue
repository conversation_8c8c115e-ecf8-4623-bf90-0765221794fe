<template>
  <div class="home">
    <div class="home-top">
      <div class="title">
        <span>全力做好应急抢险救援</span>
        <span>确保人民生命安全放在第一位落到实处</span>
        <span>人民至上 生命至上</span>
      </div>
    </div>
    <div class="home-bottom">
      <ul>
        <template v-for="(item, index) in menuList">
          <li @click="handleToMenu(item)" v-if="item.show" :key="index">
            <van-image width="48px" height="48px" fit="contain" :src="item.icon" />
            <span class="name">{{ item.name }}</span>
          </li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import infoRequest from '@/api/webcross/infoRequest';
import nativeSchema from '@/utils/tool-native-schema';

import axios from 'axios';
import { Notify } from 'vant';
@Component
export default class MainHome extends Vue {
  menuList: any = [
    { name: '事件管理', path: '/event', icon: require(`@/assets/images/home/<USER>
    {
      name: '协同会商',
      path: '',
      icon: require(`@/assets/images/home/<USER>
      show: true
    },
    {
      name: '预案管理',
      path: '/planManageList',
      icon: require(`@/assets/images/icons/widget_upload_pic.png`),
      show: true
    },

    {
      name: '应急叫应',
      path: '/emergencyResponse',
      icon: require(`@/assets/images/home/<USER>
      show: true
    },
    {
      name: '案例列表',
      path: '/caseList',
      icon: require(`@/assets/images/icons/tel_btn.png`),
      show: true
    },
    {
      name: '知识库',
      path: '/navigatePage',
      icon: require(`@/assets/images/icons/back_picture.png`),
      show: true
    },
    {
      name: '一键搜',
      path: '/search',
      icon: require(`@/assets/images/icons/fix_ico.png`),
      show: true
    },
    {
      name: '综合查询',
      path: '/integratedQueryhList', //integratedQueryhList
      icon: require(`@/assets/images/icons/phoneBtn.png`),
      show: true
    },
    {
      name: '指挥调度',
      path: '/list',
      icon: require(`@/assets/images/home/<USER>
      show: true
    }
    // {
    //   name: '预警信息',
    //   path: '/warnInfo',
    //   icon: require(`@/assets/images/home/<USER>
    //   show: true
    // },
    // {
    //   name: "信息接报",
    //   path: "/infoReception",
    //   icon: require(`@/assets/images/home/<USER>
    //   show: true,
    // },
    // {
    //   name: "领导批示",
    //   path: "/leadership",
    //   icon: require(`@/assets/images/icons/phoneBtn.png`),
    //   show: true,
    // },
    // {
    //   name: "通知公告",
    //   path: "/announcement",
    //   icon: require(`@/assets/images/icons/fix_ico.png`),
    //   show: true,
    // },

    //  {
    //   name: "分组管理",
    //   path: "/GroupManage",  //integratedQueryhList
    //   icon: require(`@/assets/images/icons/audio_dispatch.png`),
    //   show: true,
    // },
    // {
    //   name: "消息通知",
    //   path: "",
    //   icon: require(`@/assets/images/home/<USER>
    //   show: true,
    // },
    // {
    //   name: "值班排班",
    //   path: "/duty",
    //   icon: require(`@/assets/images/home/<USER>
    //   show: true,
    // },
    // {
    //   name: "现场扫码",
    //   path: "",
    //   icon: require(`@/assets/images/home/<USER>
    //   show: true,
    // },
    // {
    //   name: '呈报上报',
    //   path: '/reportto',
    //   icon: require(`@/assets/images/icons/widget_upload_file.png`),
    //   show: true
    // },
  ];
  handleToMenu(item) {
    if (item.name == '协同会商') {
      if (this['wxUtils'].isWeiXin()) {
        this['wxUtils'].openAppNew();
      } else {
        nativeSchema.loadSchema({
          // schema头协议,
          protocal: 'vsxinrhtx://',
          // 发起唤醒请求后，会等待loadWaiting时间，超时则跳转到failUrl，默认3000ms
          loadWaiting: 3000,
          // 唤起失败时的跳转链接，默认跳转到应用商店下载页
          failUrl: 'https://223.75.52.194:6089/u/phone130.apk'
        });
      }
      return;
    }
    if (item.path && item.path !== '') {
      this.$router.push(item.path);
    } else {
      console.log('即将跳转---------->', item.name);
    }
  }

  mounted() {
    // 政务微信免登录相关功能
    let isLogout = window.localStorage.getItem('isLogout') || '';
    let locationInfo = window.location.search || window.location.hash || '';
    let hashArrObj: any = {};
    if (locationInfo) {
      let hashArr = locationInfo.split('?').length > 1 ? locationInfo.split('?')[1].split('&') : [];
      hashArr.forEach((item) => {
        let itemArr = item.split('=');
        hashArrObj[itemArr[0]] = itemArr[1];
      });
      console.log('hashArrNew---->', hashArrObj);
    }
    if (isLogout === '1') {
      this.$router.push('/login'); // 主动退出登录返回登录页
    } else {
      let token = localStorage.getItem('token') || '';
      if (hashArrObj.code && !token) {
        infoRequest.govAppLogin({ code: hashArrObj.code }, (res) => {
          console.log('政务微信登录+++++++++++++>>>', res);
          if (res.status === 'error' && !localStorage.getItem('token')) {
            Notify({ type: 'danger', message: res.data.msg || '获取用户信息失败' });
            this.$router.push('/login');
          } else {
            if (res.data.status === 200) {
              localStorage.setItem('token', res.data.data.token);
              localStorage.setItem('role', JSON.stringify(res.data.data.role));
              localStorage.setItem('teamId', res.data.data.teamId);
              axios.defaults.headers.common['token'] = res.data.data.token;
            } else {
              Notify({ type: 'danger', message: res.data.msg || '获取用户信息失败' });
            }
          }
        });
      }
    }
    // let teamId = localStorage.getItem('teamId');
    // if (teamId) {
    //   this.menuList.forEach(el => {
    //     if (el.name === '快速响应') {
    //       el.show = true;
    //     }
    //   });
    // }
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/home';
.home {
  position: relative;
  background: #fff;
  height: calc(100vh - 80px) !important;
  overflow: auto;
  ul {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center;
    height: 100%;
    li {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 25%;
      text-align: center;
    }
  }
  &-top {
    width: 100%;
    height: 55%;
    background: url('@{url}/bg-top.png') center no-repeat;
    background-size: cover;
    color: #fff;
    .title {
      width: 100%;
      padding-top: 30%;
      text-align: center;
      span {
        display: block;
        margin-top: 5px;
        font-size: 16px;
        &:last-child {
          margin-top: 10px;
          font-size: 24px;
          font-weight: bold;
        }
      }
    }
  }
  &-bottom {
    position: absolute;
    top: 38%;
    width: 100%;
    height: 61%;
    padding: 0 20px;
    background: url('@{url}/bg-bottom.png') no-repeat left top;
    background-size: cover;
    padding-bottom: 30px;
    ul {
      height: fit-content;
      margin-top: 22%;
      li {
        width: 25%;
        margin-bottom: 25px;
        span {
          padding-top: 10px;
          color: #1c1d1d;
        }
      }
    }
  }
}
</style>
