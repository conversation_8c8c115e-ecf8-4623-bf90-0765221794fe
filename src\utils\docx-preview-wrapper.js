/**
 * docx-preview 包装器
 * 用于解决模块导入问题
 */

// 直接导入依赖，确保它们被打包
import * as docxPreviewLib from 'docx-preview';
import * as JSZipLib from 'jszip';

// 设置全局 JSZip (docx-preview 需要)
window.JSZip = JSZipLib;

// 确保 docxPreview 可用
const docxPreview = docxPreviewLib;

/**
 * 渲染 Word 文档
 * @param {Blob} blob - 文档的二进制数据
 * @param {HTMLElement} container - 用于渲染文档的容器元素
 * @returns {Promise<void>}
 */
export async function renderWordDocument(blob, container) {
  try {
    await docxPreview.renderAsync(blob, container);
    return true;
  } catch (error) {
    console.error('Error rendering document:', error);
    const div = document.createElement('div');
    div.style.padding = '20px';
    div.style.color = 'red';
    div.textContent = '文档渲染失败，可能是不支持的格式或文件已损坏。';
    container.appendChild(div);
    return false;
  }
}

export default {
  renderAsync: renderWordDocument
};
