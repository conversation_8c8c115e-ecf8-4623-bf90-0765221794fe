/**
 * docx-preview 包装器
 * 用于解决模块导入问题
 */

// 尝试获取全局变量或导入模块
let docxPreview;
let JSZip;

// 首先尝试使用全局变量（由 webpack.ProvidePlugin 提供）
if (typeof window.docxPreview !== 'undefined') {
  docxPreview = window.docxPreview;
  console.log('Using global docxPreview');
} else {
  // 如果全局变量不可用，尝试导入模块
  try {
    docxPreview = require('docx-preview');
    console.log('Using required docxPreview');
  } catch (error) {
    console.error('Failed to import docx-preview:', error);
    // 提供一个后备实现
    docxPreview = {
      renderAsync: async (blob, container) => {
        console.error('docx-preview not available, using fallback implementation');
        const div = document.createElement('div');
        div.style.padding = '20px';
        div.style.color = 'red';
        div.textContent = '无法加载文档预览组件，请检查网络连接或联系管理员。';
        container.appendChild(div);
        return Promise.resolve();
      }
    };
  }
}

// 同样处理 JSZip
if (typeof window.JSZip !== 'undefined') {
  JSZip = window.JSZip;
  console.log('Using global JSZip');
} else {
  try {
    JSZip = require('jszip');
    console.log('Using required JSZip');
  } catch (error) {
    console.error('Failed to import jszip:', error);
  }
}

// 确保 JSZip 在全局范围内可用（docx-preview 需要）
if (JSZip && typeof window.JSZip === 'undefined') {
  window.JSZip = JSZip;
}

/**
 * 渲染 Word 文档
 * @param {Blob} blob - 文档的二进制数据
 * @param {HTMLElement} container - 用于渲染文档的容器元素
 * @returns {Promise<void>}
 */
export async function renderWordDocument(blob, container) {
  try {
    // 确保 docxPreview.renderAsync 存在
    if (typeof docxPreview.renderAsync === 'function') {
      await docxPreview.renderAsync(blob, container);
      return true;
    } else {
      throw new Error('renderAsync method not found');
    }
  } catch (error) {
    console.error('Error rendering document:', error);
    const div = document.createElement('div');
    div.style.padding = '20px';
    div.style.color = 'red';
    div.textContent = '文档渲染失败，可能是不支持的格式或文件已损坏。';
    container.appendChild(div);
    return false;
  }
}

// 导出 renderAsync 方法
export const renderAsync = renderWordDocument;

// 默认导出
export default {
  renderAsync: renderWordDocument
};
