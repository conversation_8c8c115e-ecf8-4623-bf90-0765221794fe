/**
 * docx-preview 包装器
 * 用于解决模块导入问题
 */

// 尝试导入 docx-preview
let docxPreview;
try {
  docxPreview = require('docx-preview');
} catch (error) {
  console.error('Failed to import docx-preview:', error);
  // 提供一个后备实现
  docxPreview = {
    renderAsync: async (blob, container) => {
      console.error('docx-preview not available, using fallback implementation');
      const div = document.createElement('div');
      div.style.padding = '20px';
      div.style.color = 'red';
      div.textContent = '无法加载文档预览组件，请检查网络连接或联系管理员。';
      container.appendChild(div);
      return Promise.resolve();
    }
  };
}

// 尝试导入 jszip
let JSZip;
try {
  JSZip = require('jszip');
  // 设置全局 JSZip
  window.JSZip = JSZip;
} catch (error) {
  console.error('Failed to import jszip:', error);
}

/**
 * 渲染 Word 文档
 * @param {Blob} blob - 文档的二进制数据
 * @param {HTMLElement} container - 用于渲染文档的容器元素
 * @returns {Promise<void>}
 */
export async function renderWordDocument(blob, container) {
  try {
    await docxPreview.renderAsync(blob, container);
    return true;
  } catch (error) {
    console.error('Error rendering document:', error);
    const div = document.createElement('div');
    div.style.padding = '20px';
    div.style.color = 'red';
    div.textContent = '文档渲染失败，可能是不支持的格式或文件已损坏。';
    container.appendChild(div);
    return false;
  }
}

export default {
  renderAsync: renderWordDocument
};
