body {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}
div{
  white-space:normal; 
  word-break:break-all;
}

i,
b {
    font-style: normal;
}

html {
    box-sizing: border-box;
}

html,
body,
#app {
    height: 100%;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

.clearfix {
    &:after {
        visibility: hidden;
        display: block;
        font-size: 0;
        content: " ";
        clear: both;
        height: 0;
    }
}

//vue router transition css
.fade-enter-active,
.fade-leave-active {
    transition: all .2s ease
}

.fade-enter,
.fade-leave-active {
    opacity: 0;
}

//main-container全局样式
.app-main {
    min-height: 100%
}

.app-container {
    padding: 20px;
}

.svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.arraw_show {
    right: 1px;
    margin-right: 15px;
    float: right;
    color: blue;
    font-size: 1.7rem;
}
input {
  border: none;
  border-style:none
}
.enterpriseDataDetail .el-cascader-panel{
    width: 100%;
  }
.enterpriseDataDetail .el-cascader-menu{
    max-width: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.enterpriseDataDetail .el-cascader-menu__wrap{
    max-width: 40%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}