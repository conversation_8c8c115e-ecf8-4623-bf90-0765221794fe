<template>
  <div class="div_big">
    <van-nav-bar title="任务调度" left-text="     "   left-arrow  @click-left="GoBack" >
    <template #right>
    <!-- <van-icon name="ellipsis" size="25" /> -->
  </template>
   </van-nav-bar>
    <div class="tab_class">
      <!-- <tab>
          <tab-item v-for="(item,index) in tabData" :key="index" :selected="index==0" @on-item-click="onItemClick(index)" style="font-size: 15px">{{item.name}}</tab-item>
      </tab> -->
    </div>
    <!-- <div class="search_div">
      <van-search v-model="keyword" placeholder="请输入搜索关键词"  />
    </div> -->
    <div class="content_list" >
     <ul class="content_ul" ref="container" >
        <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
          <dispatchListItem ref="itemRef" @handleObj="handleObj" />
        </mu-load-more>
     </ul>
      <!-- <popup :hide-on-blur=false  v-model="showDetail" width="100%" position="right">
        <homeDetail v-if="showDetail"  :queryMap="queryMap" @close="closeDetail"></homeDetail>
      </popup> -->
    </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import dispatchListItem from './dispatchListItem.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    dispatchListItem
  }
})
export default class Home extends Vue {
  tabData = [{"code":"0","name":"全部"},{"code":"1","name":"队伍"},{"code":"2","name":"事件"},{"code":"2","name":"任务"},{"code":"2","name":"通知"}];
  selected: any = 0;
  refreshing: any = false; 
  loading: any = false;
  showDetail: any = false;
  pageIndex: any = 1;
  pageSize: any = 10;
  keyword:any ="";
  open:any = false;
  position:any = 'left';
  queryMap: any ={};
  onClickLeft(){
    console.log("返回点击")

  }
  clickMore(){
    console.log("打开菜单")
    this.open=true;
  }
  handleObj(item){
    let _this=this;
    console.log(item)
    if(!item.uploadMore){
      _this.refreshing=false;
      _this.loading=false;
    }
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.pageIndex=1;
    let pageObj={pageIndex:1,pageSize:_this.pageSize}
    _this.$refs.itemRef['queryRequest'](pageObj)
  }
  load() {
    const _this = this;
    _this.pageIndex++;
    _this.loading = true;
    let pageObj={pageIndex:_this.pageIndex,pageSize:_this.pageSize}
    _this.$refs.itemRef['queryRequest'](pageObj)
  }
  GoBack(){
    this.$router.go(-1);
    // console.log("fffa")
    // try {
    //     this['$gsmdp'].goBack({
    //       success(res) {
    //         alert(JSON.stringify(res))
    //       }
    //     })
    // } catch (error) {
    //     console.log("close error")
    // }
  }
  closeDetail(){
      this.showDetail=false;
  };
  created() {
    let _this=this;
    
  }
  mounted(){
     window['GoBack']=this.GoBack;//安卓调用GoBack（手机back键返回问题）
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div{
    width: 100%;
  }
}
.content_list{
  height: calc( 100% - 46px );
   padding: 0 15px;
   overflow: auto;
  background: #f1efef;
}
.showMenu{
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
      color: white;
      
}
</style>