<template>
  <div class="list">
    <Header title="通知公告" backPath="/"></Header>
    <div class="list-main">
      <div class="list-tab">
        <van-tabs v-model="curInfoType" @change="onClickTab">
          <van-tab :title="item.name" :name="item.type" v-for="item in infoType" :key="item.type"></van-tab>
        </van-tabs>
      </div>
      <div class="list-main-search">
        <van-search v-model="keyword" placeholder="关键字搜索" @search="onSearch" />
      </div>
      <div class="list-main-cont" style="max-height: 725px;">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            :offset="20"
            @load="getList"
          >
            <van-cell v-for="item in list" :key="item.id" :title="item.caption" class="van-ellipsis" @click="handleToDetail(item)">
              <template #icon>
                <van-icon name="todo-list" size="24" color="#01a9e8" style="marginRight: 10px"/>
              </template>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </div>
      <!-- <div class="list-main-add" @click="handleAddInfo">上报信息</div> -->
    </div>
    <van-action-sheet
      v-model="showInfoType"
      :actions="infoType"
      cancel-text="取消"
      close-on-click-action
      @select="onSelect"
      @cancel="onCancelReception"
    />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class announcement extends Vue {
  keyword: any = '';
  list: any = [];
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;
  showInfoType: boolean = false;
  addType: any = {}; // 新增信息接报类型
  infoType: any = [
    {name: '通知', type: '107'},
    {name: '公告', type: '69'},
    {name: '通报', type: '70'},
    {name: '部级通知公告', type: '1'},
  ]
  private paging: any = {
    total: null,
    nowPage: 1,
    pageSize: 15
  }
  curInfoType: any = '107'; // 当前选中tab
  mounted() {
  }
  getList(first=false, isSearch= false) {
    let params = {
      orgCode: JSON.parse(window.localStorage.getItem('role')).orgCode,
      docTypeCode: this.curInfoType,
      keyword: this.keyword,
      nowPage: this.paging.nowPage,
      pageSize: this.paging.pageSize,
      qType: '3',
    }
    if (this.list.length > 0 && this.list.length === this.paging.total && !first) {
      return
    }
    setTimeout(() => {
      this.loading = true;
      this['$api'].InfoRequest.getEventList(params, res => {
        let data = JSON.parse(res.data.data);
        this.paging.total = data.total;
        if (res.data.status === 200) {
          if(this.paging.nowPage === 1) {
            this.list = []
          }
          if (isSearch) {
            this.list = [...JSON.parse(res.data.data).list];
          } else {
            this.list = [...this.list, ...JSON.parse(res.data.data).list];
          }
          this.paging.total = data.total;
          this.loading = false;
          this.refreshing = false;
          this.finished = false;
          if(this.list.length >= data.total) {
            this.finished = true;
          }
          this.paging.nowPage++;
        } else {
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      })
    }, 500)
    
  }
  onClickTab(val) {
    this.curInfoType = val;
    this.paging.nowPage = 1;
    this.keyword = '';
    this.getList(true)
  }
  // 点击添加上报信息
  handleAddInfo() {
    this.showInfoType = true;
  }
  onCancelReception() {
    this.showInfoType = false;
  }
  // 点击新增信息
  onSelect(item) {
    this.addType = item;
    const path = this.curInfoType === 'event' ? `/infoReception/add` : '/infoReception/ordinaryAdd'
    this.$router.push({
      path: path,
      query: {
        type: 'add',
        infotype: item.type
      }
    })
  }
  onSearch(value) {
    this.paging.nowPage = 1;
    this.getList(true, true);
  }
  // 跳转详情页面
  handleToDetail(item) {
    const path = '/announcementDeatil';
    this.$router.push({
      path: path,
      query: {
        type: 'detail',
        infotype: this.curInfoType,
        id: item.id || item.ordinaryId || item.receiveid
      }
    })
  }
  private timer: any = null;
  onRefresh() {
    // 清空列表数据
    this.finished = false;
    this.list = [];
    // 重新加载数据 将 loading 设置为 true，表示处于加载状态
    this.loading = true;
    this.refreshing = true;
    this.paging.nowPage = 1;
    this.getList();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  /deep/.van-tabs__line {
    background-color: #1967f2;
  }
}
</style>