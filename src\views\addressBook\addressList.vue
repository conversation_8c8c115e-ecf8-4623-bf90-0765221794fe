<template>
  <div class="addressList">
    <Header :title="$route.query.title"></Header>
    <div class="addressList-card">
      <div class="cardImg"></div>
      <div class="cardText">{{currenInfo.orgName}}</div>
    </div>
    <div class="addressList-cardInfo">
      <van-cell-group inset>
        <van-cell inset title="部门电话" :value="currenInfo.dutyTel">
          <template #right-icon v-if="currenInfo.dutyTel">
            <van-icon name="phone-circle" class="phone-icon" color="#13b2a1" @click="goToTelPage(currenInfo.dutyTel)" />
          </template>
        </van-cell>
        <van-cell inset title="部门传真号" :value="currenInfo.fax"></van-cell>
        <van-cell inset title="上级部门" :value="parentLabel"></van-cell>
      </van-cell-group>
    </div>
    <div class="addressList-content">
      <div class="addressList-content-search">
        <van-search v-model="keyword" placeholder="可按机构名字查询" @search="onSearch" />
      </div>
      <div class="addressList-content-list">
        <span class="title">下级部门</span>
        <ul>
          <li v-for="item in orgList" :key="item.id" @click="handleList(item)">
            <span>{{item.label}}</span>
            <van-icon name="arrow" />
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Inject, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header
  }
})
export default class addressList extends Vue {
  private keyword: any = '';
  private orgList: any = [];
  private orgAllList: any = [];
  private currenInfo: any = {};
  private parentLabel: any = '';

  private getTreeByParentOrgCode(orgCode) { 
    //查询用户子级
    apiServer.findOrgChildrenById({orgCode, keyword: this.keyword}, (res) => {
      if (res.status === 200) {
        this.orgList = res.data.data;
        // if(Object.prototype.toString.call(res.data.data) === '[object Object]') {
        //   this.orgList = [res.data.data];
        // }
        this.orgAllList = res.data.data;
        this.getUserInfo(this.$route.params.id)
      }
    })
  }

  private getUserInfo(orgCode) {
    apiServer.getUserInfo({orgCode}, (res) => {
      if(res.status === 200) {
        this.currenInfo = res.data.data;
      }
    })
  }

  private handleList(item) {
    if (item.leaf) {
      this.$router.push({path: `/addressBook/detail/${item.id}`, query: {title: item.label}})
    } else { // 有子节点
      this.currenInfo = item;
      this.parentLabel = item.parentLabel;
      this.getTreeByParentOrgCode(item.id);
      // this.$router.push({
      //   path: `/addressBook/list/${item.id}`,
      //   query: {
      //     title: item.label
      //   }
      // })
    }
  }

  private onSearch(value) {
    this.keyword = value;
    this.orgList[0].children = this.orgAllList.filter(ele => ele.label.includes(value))
    // this.getTreeByParentOrgCode();
  }

  mounted() {
    // this.currenInfo.label = this.$route.query.title;
    // this.currenInfo.parentLabel = this.$route.query.parentLabel;
    this.parentLabel = this.$route.query.parentLabel;
    this.getTreeByParentOrgCode(this.$route.params.id);
  }

  goToTelPage(tel) {
    window.location.href = `tel://${tel}`
  }
}
</script>

<style lang="less" scoped>
.addressList {
  background: #f5f6f6;
  position: relative;
  span {
    display: inline-block;
  }
  .phone-icon {
    margin: 5px 0 0 5px;
  }
  &-card {
    height: 200px;
    width: 100%;
    background: #3577e9;
    display: flex;
    .cardImg {
      width: 80px;
      height: 80px;
      background: url('../../assets/images/home/<USER>');
      background-size: 100% 100%;
      margin: 30px 20px 0 50px;
    }
    .cardText {
      color: #fff;
      font-size: 18px;
      margin-top: 60px;
    }
  }
  &-cardInfo {
    width: calc(100vw - 20px);
    margin: 10px;
    background: #fff;
    position: absolute;
    top: 160px;
    border-radius: 10px;
  }
  &-content {
    margin: 0 10px;
    width: calc(100vw - 20px);
    position: absolute;
    top: 320px;
    height: calc(100% - 380px);
    &-search {
      margin-bottom: 5px;
    }
    &-list {
      overflow: auto;
      height: 100%;
      .title {
        padding: 5px 10px;
        color: #b2b4b5;
      }
      ul {
        padding-left: 10px;
        background: #fff;
        li {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 36px;
          padding-right: 10px;
          border-bottom: 1px solid #eae8eb;
          color: #1c1d1d;
          /deep/.van-icon {
            color: #dddde0;
          }
        }
      }
    }
  }
}
</style>