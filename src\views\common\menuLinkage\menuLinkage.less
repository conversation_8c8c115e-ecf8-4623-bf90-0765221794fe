.div_big {
    widows: 100%;
    height: 100%;
}

.city_big {
    width      : 100%;
    height     : 100%;
    background : #fff;
    padding-top: 1rem;

    .div_title {
        width      : 100%;
        position   : relative;
        height     : 10vw;
        text-align : center;
        line-height: 10vw;
        font-size  : 4vw;
        font-weight: bold;

        span {
            display: inline-block;
            float  : left;
            height : 100%;
        }

        span:nth-of-type(1) {
            width: 20%;
            color: #666666;
        }

        span:nth-of-type(2) {
            width: 60%;
        }

        span:nth-of-type(3) {
            color: #00CC00;
            width: 20%;
        }

    }

    .div_tabs {
        width  : 100%;
        height : 30px;
        padding: 0px 5%;

        .div_tab {
            height     : 30px;
            min-width  : 10%;
            line-height: 30px;
            float      : left;
            position   : relative;
            padding    : 1px 8px;

            .active {
                display: block;
                color  : #0071B8;
            }

            span {
                width     : 80%;
                height    : 1px;
                display   : none;
                background: #0071B8;
                position  : absolute;
                bottom    : 1px;
                animation : mymove 2s infinite;
            }
        }
    }

    .isMultiple {
        height: calc(100% - 12vw - 10vw) !important;
    }

    .div_content {
        width   : 100%;
        height  : calc(100% - 12vw);
        overflow: auto;
        padding : 1.33333vw 5%;

        li {
            width        : 100%;
            height       : 10vw;
            line-height  : 10vw;
            padding-left : 5%;
            border-bottom: 1px solid #e5e5e5;
            list-style   : none;

            span:nth-of-type(1) {
                display : inline-block;
                width   : 8%;
                height  : 100%;
                float   : left;
                position: relative;

                .van-checkbox {
                    transform: translate(0%, 35%);
                }
            }

            span:nth-of-type(2) {
                display      : inline-block;
                width        : 72%;
                height       : 100%;
                float        : left;
                padding-left : .5rem;
                font-size    : .75rem;
                overflow     : hidden;
                text-overflow: ellipsis;
                white-space  : nowrap;
            }

            span:nth-of-type(3) {
                display: inline-block;
                width  : 20%;
                height : 100%;
                float  : left;
            }

            font {
                margin-left: .2rem;
                font-size  : .75rem;
                color      : #7592e2;
            }

            img {
                height        : 1.3rem;
                vertical-align: middle;
            }

            .arrow-down {
                display       : inline-block;
                vertical-align: middle;
                margin-right  : 10px;
                border-top    : 6px solid #979797;
                border-right  : 6px solid transparent;
                border-left   : 6px solid transparent;
                content       : "";
            }
        }
    }

}