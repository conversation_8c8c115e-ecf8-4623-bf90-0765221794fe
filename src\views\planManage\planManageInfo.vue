<template>
  <div class="div_big" style="height: 100%; overflow: hidden" @touchstart="moveClick" @touchmove="moveEv" @touchend="moveEnd">
    <van-nav-bar title="预案内容" left-text="返回" @click-left="GoBack">
      <template #right>
        <van-icon name="orders-o" size="25" @click="openList" />
      </template>
    </van-nav-bar>
    <div class="showcontent">
      <div class="contents">
        <!-- main content -->
        <div class="allcontent">
          <p>{{ infoObj.planName }}</p>
          <!--侧滑菜单内容-->
          <div v-for="(item, index) in infolist" :key="index">
            <!--头部信息摘要-->
            <div class="base_head">
              <div class="base_head_fi"></div>
              <div class="base_head_sec">
                <div class="headicon"></div>
              </div>
              <div
                :id="'anchor-' + index"
                class="base_head_content van-ellipsis"
                :style="{ fontSize: 20 - getLength(item, 1) + 'px', opacity: 100 - getLength(item, 4) + '%' }"
              >
                {{ item.sectionIndex }}{{ item.nodeName }}
              </div>
            </div>
            <!--摘要对应的详细信息-->
            <div class="info_show" v-html="item.content"></div>
          </div>
        </div>
      </div>
    </div>
    <van-popup
      v-model="drawerVisibility"
      position="left"
      :style="{ height: 'calc( 100% - 46px )', position: 'absolute', bottom: '0', transform: 'translate3d(0,-46.3%,0)', width: '60%' }"
    >
      <div class="sidebig">
        <div class="flow_row">
          <div class="plancatalog">预案目录</div>
          <div class="plancontent">
            <div class="catalogevery" v-for="(item, index) in infolist" :key="index">
              <li>
                <div class="bigc"><div class="minc"></div></div>
                <div @click="goAnchor('#anchor-' + index)" class="licontent">{{ item.sectionIndex }}{{ item.nodeName }}</div>
              </li>
              <div class="longline" v-if="infolist.length != index + 1"></div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import _ from 'lodash';
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import apiServer from '../../api/request-service';

@Component({
  components: {}
})
export default class planManageItem extends Vue {
  @Prop(String) private requestId: string;
  @Prop(Object) private requestObj: any;
  eventList: any = [];
  keywords: any = '';
  swcount: number = 0;
  pageXI: number = 0;
  pageYI: number = 0;
  infoObj: any = {};
  infolist: any = [];
  drawerVisibility: boolean = false;
  closeInfo() {
    let _this = this;
    _this['$gsmdp'].initGoback(function () {
      console.log('调用返回');
      try {
        _this.GoBack();
      } catch (error) {
        console.log('close error');
      }
    });
  }
  mounted() {
    let _this = this;
    _this.queryRequest();
  }
  openList() {
    this.drawerVisibility = !this.drawerVisibility;
  }
  goAnchor(selector) {
    this.drawerVisibility = false;
    var anchor = this.$el.querySelector(selector);
    //console.log(document.getElementsByClassName("vux-drawer-body")[0].scrollTop)
    //document.getElementsByClassName("vux-drawer-body")[0].scrollTo(document.getElementsByClassName("vux-drawer-body")[0].scrollLeft, 1000)
    //document.documentElement.scrollTop = anchor.offsetTop
    //setTimeout(function(){
    document.body.scrollTop = anchor.offsetTop;
    document.getElementsByClassName('contents')[0].scrollTop = anchor.offsetTop;
    console.log('赋值完成');
    //},900)
  }
  compare(property) {
    return function (a, b) {
      var value1 = a[property];
      var value2 = b[property];
      return value1 - value2;
    };
  }

  queryRequest() {
    let _this = this;
    let param = {
      planId: _this.requestId
    };
    apiServer.findStructurInfo(param, function (res) {
      console.log('res------', res);
      if (res.data && res.data.status == 200) {
        let infoObj = res.data.data;
        _this.infoObj = infoObj;
        let list = _this.listHandle(infoObj.emergencyPlanNodeDTOList || []);
        _this.infolist = _this.treeToArray(list);
        console.log('content--->', infoObj);
      }
    });
  }

  /**数组转树结构start */
  listHandle(list: Array<any>) {
    let newList = this.generateOptions(
      list,
      (params: any, index?: number) => {
        return {
          nodeId: params.nodeId, // 定位锚点id
          nodeName: params.nodeName,
          sectionIndex: `${this.toChinesNum(index + 1)}、`,
          content: params.content,
          sort: params.sort, // 排序
          childrenList: []
        };
      },
      (childParams: any, sectionIndex: string) => {
        return {
          nodeId: childParams.nodeId, // 定位锚点id
          parentId: childParams.parentId,
          nodeName: childParams.nodeName,
          sectionIndex: sectionIndex,
          content: childParams.content,
          sort: childParams.sort, // 排序
          childrenList: []
        };
      }
    );
    return newList;
  }

  //生成Cascader级联数据
  private generateOptions(params, fnFirst, fnOther) {
    let result = [];
    params.forEach((param: any) => {
      if (param.parentId == 0) {
        //判断是否为顶层节点
        let parent: any = fnFirst(param, result.length); // 回调一级节点结构
        parent.childrenList = this.getchilds(param.nodeId, params, fnOther, result.length + 1); //获取子节点
        result.push(parent);
      }
    });
    return result;
  }
  // 递归处理子节点结构
  private getchilds(nodeId, array, fnOther, sectionIndex) {
    let childs = new Array();
    //循环获取子节点
    array.forEach((arr: any) => {
      if (arr.parentId == nodeId) {
        childs.push(fnOther(arr, `${sectionIndex}.${arr.sort}`)); // 回调二级及以上节点结构
      }
    });
    //获取子节点的子节点
    childs.forEach((child: any) => {
      let childscopy = this.getchilds(child.nodeId, array, fnOther, `${child.sectionIndex.slice(0, -2)}.${child.sort}`); //递归获取子节点
      if (childscopy.length > 0) {
        child.childrenList = childscopy;
      }
    });
    return childs;
  }

  // 数字转中文
  private toChinesNum(num) {
    let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    let unit = ['', '十', '百', '千', '万'];
    num = parseInt(num);
    let getWan = (temp) => {
      let strArr = temp.toString().split('').reverse();
      let newNum = '';
      for (var i = 0; i < strArr.length; i++) {
        newNum =
          (i == 0 && strArr[i] == 0
            ? ''
            : i > 0 && strArr[i] == 0 && strArr[i - 1] == 0
            ? ''
            : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i])) + newNum;
      }
      // 处理'一十二'为'十二'
      if (newNum.slice(0, 2) === '一十') {
        newNum = newNum.substr(1);
      }
      return newNum;
    };
    let overWan = Math.floor(num / 10000);
    let noWan: any = num % 10000;
    if (noWan.toString().length < 4) {
      noWan = '0' + noWan;
    }
    return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num);
  }
  /**数组转树结构end */

  // 树结构转换为数组
  treeToArray(list) {
    let ary = [],
      treeTrans = new Array(),
      current = null,
      cNode = null;
    // 默认从树的第一个节点开始遍历
    treeTrans.push({ node: list, index: 0 });
    while (treeTrans.length > 0) {
      // 获取当前第一个栈帧的节点
      (current = treeTrans[treeTrans.length - 1]), (cNode = current.node[current.index]);
      if (cNode == null) {
        // 如果超出了节点长度时弹出当前遍历栈
        treeTrans.pop();
        continue;
      }

      // 将当前节点的值放入数组
      ary.push(cNode);
      // 当前节点往后移
      current.index++;

      // 如果当前节点有子元素则入栈，下次循环遍历子元素
      if (cNode.childrenList && cNode.childrenList.length) {
        treeTrans.push({ node: cNode.childrenList, index: 0 });
        continue;
      }
    }
    return ary;
  }

  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
  moveEv(e) {
    this.swcount++;
    if (this.swcount > 5) {
      console.log('进入侧滑');
      let pageX = e.changedTouches[0].pageX;
      /*if($(".vux-drawer-active").length==0){
					if(pageX<this.pageXI){
							console.log("进入右滑")
							this.openside();
		  				this.swcount=0;
					}
				}else{
					if(pageX>this.pageXI){
						  console.log("进入左滑");
						  this.openside();
		  				this.swcount=0;
					}
				}*/
    }
  }
  moveEnd(e) {
    let pageX = e.changedTouches[0].pageX;
    console.log(pageX + '===>' + this.pageXI);
    /*左右滑*/
    if (pageX > this.pageXI + 55) {
      if (this.infolist.length > 0) {
        this.drawerVisibility = true;
      } else {
        console.log('无目录显示');
      }
      this.swcount = 0;
    } else if (pageX - 55 < this.pageXI) {
      this.drawerVisibility = false;
      this.swcount = 0;
    }
  }
  moveClick(e) {
    this.swcount = 0;
    console.log('moveClick==>' + e.changedTouches[0].pageX);
    this.pageXI = e.changedTouches[0].pageX;
  }
  

  private getLength(item: any, num: number) {
    // return item.sectionIndex.replace(/\./g, '').length * num
    return item.sectionIndex.split('.').length * num;
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;

  .showcontent {
    width: 100%;
    height: calc(100% - 46px);
    overflow: hidden;
  }
  .contents {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .allcontent p {
    width: 100%;
    font-size: 0.8rem;
    box-sizing: border-box;
    padding: 0.1rem 0.8rem;
  }
  .base_head {
    width: 100%;
    height: 60px;
    border-top: 1px solid #d4d4d4;
  }
  .base_head > div {
    float: left;
  }
  .base_head .base_head_fi {
    width: 5%;
    height: 100%;
  }
  .base_head .base_head_sec {
    width: 5%;
    height: 100%;
    border-bottom: 1px solid #d4d4d4;
  }
  .base_head .base_head_sec div {
    width: 30%;
    height: 26px;
    background: #67af7d;
    margin-top: 17px;
    border-radius: 7px;
  }
  .base_head .base_head_content {
    width: 90%;
    height: 100%;
    border-bottom: 1px solid #d4d4d4;
    line-height: 60px;
    font-size: 18px;
  }
  .clean {
    clear: both;
    margin-bottom: 10px;
  }
  .info_show {
    width: 80%;
    margin-left: 10%;
    word-break: break-all;
    word-wrap: break-word;
    img {
      width: 100%;
    }
  }
  .info_show li {
    width: 100%;
    height: 30px;
    margin-top: 2%;
    margin-bottom: 2%;
    margin-left: 10%;
    line-height: 30px;
    font-size: 15px;
  }
  .info_show li div {
    float: left;
  }

  .mui-bar.mui-bar-nav {
    position: relative;
    height: 0.9rem;
    font-size: 0.26rem;
    background-color: #4675ff;
    padding-right: 0.2rem;
    padding-left: 0.2rem;
  }

  .mui-bar-nav.mui-bar .lefto {
    position: absolute;
    top: 50%;
    margin-top: -0.3rem;
    z-index: 9999;
    width: 0.18rem;
    height: 0.6rem;
    left: 0.2rem;
  }
  .mui-bar-nav.mui-bar .righto {
    position: absolute;
    top: 50%;
    margin-top: -0.25rem;
    z-index: 9999;
    width: 0.5rem;
    height: 0.5rem;
    right: 0.2rem;
  }
  .mui-bar-nav.mui-bar .righdivto {
    position: absolute;
    top: 50%;
    margin-top: -0.25rem;
    z-index: 9999;
    width: 33px;
    height: 0.5rem;
    right: 31px;
  }
  .goBacks {
    height: 100%;
    margin-left: -0.2rem;
    width: 0.6rem;
    position: relative;
  }

  .sidebig {
    width: 100%;
    height: 100%;
    background: white;
  }
  .flow_row {
    width: 80%;
    height: 100%;
    margin: 0% 10%;
  }
  .flow_row .plancatalog {
    width: 100%;
    height: 14%;
    float: left;
    color: #000000;
    text-align: center;
    font-weight: bold;
    padding-top: 14%;
  }
  .flow_row .plancontent {
    width: 100%;
    height: 80%;
    float: left;
    overflow-y: auto;
  }
  .plancontent .bigc {
    width: 14px;
    height: 14px;
    background: #4675ff;
    border-radius: 18px;
    margin: 0rem 0.26rem;
    float: left;
  }
  .plancontent .minc {
    width: 12px;
    height: 12px;
    background: white;
    margin: 1px;
    border-radius: 10px;
  }
  .longline {
    width: 1px;
    height: 1.7rem;
    background: #4675ff;
    position: absolute;
    top: 0.8rem;
    left: 0.6rem;
  }
  .licontent {
    width: 80%;
    height: 100%;
    line-height: 0.4rem;
    padding-top: 3px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .catalogevery {
    width: 100%;
    position: relative;
  }
  .plancontent li {
    height: 2rem;
    list-style: none;
  }
}
</style>
