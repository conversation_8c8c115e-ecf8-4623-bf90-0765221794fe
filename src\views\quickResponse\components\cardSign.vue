<template>
  <!-- 签到 -->
  <div class="teamSignIn-item signin">
    <div class="teamSignIn-user">省应急厅</div>
    <div class="teamSignIn-info">
      <p>省应急厅</p>
      <div class="teamSignIn-sign">
        <div class="sign-box">
          <i></i>
          <van-button type="primary" @click="teamSign">抵达签到</van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
@Component({
  components: {
  }
})
export default class cardSign extends Vue {
  public teamSign() {
    this.$emit('teamSign');
  }
}
</script>

<style lang="less" scoped>
</style>