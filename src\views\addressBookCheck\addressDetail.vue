<template>
  <div class="addressDetail">
    <Header :title="$route.query.title || '通讯录'"></Header>
    <div class="addressDetail-container" v-if="personList && personList.length > 0">
      <div class="addressDetail-content" v-for="(item,index) in personList" :key="item.userId">
        <div class="item" @click="goToPage(item)">
          <div class="lefItem">
            <span class="label el-icon-user-solid"></span>
            <div class="cont">
              <p>{{ item.personName }}</p>
              <p>{{ item.personJob == null || item.personJob == 'null' ? '' : item.personJob }}</p>
            </div>
          </div>
          <div class="sperCheck">
            <van-checkbox shape="square" checked-color="#07c160" v-model="item.checked" @click="changeCheckBox(item)"></van-checkbox>
            <!-- {{radioArray[index]}}
            <van-radio-group v-model="radioArray[index]">
              <van-radio name="1"></van-radio>
            </van-radio-group> -->
          </div>
        </div>

        <!-- <div class="item">
          <span class="label">联系方式</span>
          <span class="cont">{{item.telNumber}}</span>
          <span style="marginLeft: 10px"><van-icon name="phone-circle" color="#13b2a1"/></span>
        </div>
        <div class="item">
          <span class="label">单位名称</span>
          <span class="cont">{{item.orgFullName}}</span>
        </div> -->
      </div>      
    </div>
    <van-empty description="暂无人员数据" v-else />
    <div class="btnQuer" @click="handleAddInfo">确认</div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
import { Notify } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class addressDetail extends Vue {
  private personList: any = [];

  private radioArray:any=[]

  private handleAddInfo () {
    // debugger
    this.$store.commit('PERSONDETAIL', []);
    let newAry=this.personList.filter(el=>el.checked)
    if(newAry.length > 1 || newAry.length == 0){
       Notify({ type: "danger", message: "请勾选一个审批人" });
       return
    }
    console.log(newAry,"选中的数据")
    this.$store.commit('PERSONDETAIL', newAry);
    this.$router.push({path: '/reportAddForm'})

    //  Notify({ type: "danger", message: res.data.msg || "请求失败" });

  }
  private getSubPersonFn() {
    let params = {
      nowPage: 1,
      pageSize: 999,
      orgCode: this.$route.params.id
    };
    apiServer.getSubPersons(params, (res) => {
      if (res.status === 200) {
        this.personList = res.data.data.list;
      }
    });
  }
  private changeCheckBox(item) {
    debugger;
  }
  mounted() {
    this.getSubPersonFn();
  }
  goToPage(item) {
    // debugger
    // this.$store.commit('ADDRESSDETAIL', item);
    // this.$router.push({path: '/addressBook/addressInfo'})
  }
}
</script>

<style lang="less" scoped>
.btnQuer {
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #1967f2;
  color: #fff;
  letter-spacing: 3px;
  font-weight: bold;
  // position: absolute;
  // bottom:0;
}
.addressDetail {
  display: flex;
  flex-direction: column;
  background: #f5f6f6;
  &-container {
    flex: 1;
    overflow: auto;
    // height: calc( 100% - 200px );
  }
  &-content {
    margin: 10px;
    padding: 5px 10px;
    background: #fff;
    border-radius: 4px;
    .lefItem {
      display: flex;
      align-items: center;
      width: calc(100% - 50px);
    }
    .sperCheck {
      width: 50px;
      display: flex;
      justify-content: flex-end;
    }
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      font-size: 16px;
      justify-content: space-between;
      align-items: center;
      span {
        display: inline-block;
      }
      .label {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 34px;
        width: 50px;
        margin-right: 20px;
        color: #666;
        text-align-last: justify;
        background-color: #99c0e7;
        border-radius: 5px;
        color: #fff;
        height: 50px;
      }
      .cont {
        color: #1c1d1d;
        width: calc(100% - 50px);
        p {
          margin: 5px 0;
        }
        > p:last-child {
          color: #9ea2a1;
          font-size: 15px;
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>