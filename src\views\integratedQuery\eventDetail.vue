<template>
  <div class="div_big">
    <van-nav-bar title="事件详情" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
    <!-- <van-icon name="ellipsis" size="25" /> -->
  </template>
   </van-nav-bar>
   <!-- invoke common List -->
  <div class="info_big">
    <p>
      <span>
        <img src="../../assets/images/icons/1.png" alt="" srcset=""> 
      </span>  
      <span>{{detailInfo.eventTypeName}}</span>
    </p>
    <div class="info_head">
        <p>{{detailInfo.eventTitle}}</p>
        <!-- <li>
          <span>所属行政区划：</span>
          <span>{{detailInfo.districtName}}</span>
        </li>
        <li>
          <span>队伍位置：</span>
          <span>{{detailInfo.address}}</span>
        </li> -->
    </div>
    <div class="info_content">
      <li>
        <span>发布时间：</span>
        <span>{{detailInfo.occurTime}}</span>
      </li>
      <p>
       {{detailInfo.infoDescription==null?"无描述":detailInfo.infoDescription}}
      </p>
    </div>
    <p>附件：</p>
    <file-preview direction="left" :fileList="detailInfo.attachList"></file-preview>
  </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import FilePreview from '@/components/filePreview.vue';
@Component({
  components: {
    FilePreview
  }
})
export default class rescueTeamDetail extends Vue {
  @Prop(String) private requestId :string;
  position:any = 'left';
  queryMap: any ={};
  detailInfo: any = {};
  showObject: any ={};
  onClickLeft(){
    console.log("返回点击")

  }
  requestItem(){
    let _this=this;
    let param={"eventId":_this.requestId}
    apiServer.findDetailQuery(param,function(res){
      if(res.status==200){
        _this.detailInfo=res.data.data;
        _this.showObject.orgName="";
      }
    },"2")
  }
  GoBack(){
    this.$emit("close")
  }
  created() {
    let _this=this;
    
  }
  mounted(){
    this.requestItem();
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      _this.GoBack()
    })
  }
}
</script>
<style scoped lang="scss">
.div_big{
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  .info_big{
    width: 90%;
    height: calc( 100% - 46px - 6% );
    margin: 5%;
    background: white;
    border-radius: 15px;
    font-size: 15px;
    >p{
      padding-top: 10px;
      line-height: 25px;
      img{
          height: 25px;
          vertical-align: middle;
          margin-right: 1.33333vw;
          margin-top: -5px;
      }
      span:nth-of-type(1){
        display: inline-block;
        height: 25px;;
      }
      span:nth-of-type(2){
        display: inline-block;
        font-weight: bold;
        color: gray;
        padding-top: 5px;
        line-height: 25px;
      }
    }
    .info_head{
      width: 100%;
      padding: 0% 5% 5% 5%;
      text-align: center;
      >li{
        width: 100%;
        text-align: left;
      }
    }
    .info_content{
      width: 100%;
      padding: 0px 5%;
      border-top: 1px solid #f2f2f2;
      li{
        padding-top: 10px;
        font-size: 13px;
        text-align: center;
        color: #4277bf;
      }
    }
    .attachmentList{
      li{
        width: 100%;
        height: 30px;
        padding-left: 5%;
        color: #4277bf;
        span{
          display: inline-block;
          height: 100%;
        }
        span:nth-of-type(1){
          width: 10%;
          float: left;
        }
        span:nth-of-type(2){
          width: 90%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        img{
          height: 25px;
          vertical-align: middle;
          margin-right: 6px;
          margin-top: -3px;
        }
      }
    }
  }
}

</style>