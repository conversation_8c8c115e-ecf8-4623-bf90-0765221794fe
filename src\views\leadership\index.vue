<template>
  <div class="list">
    <Header title="领导批示" backPath="/home"></Header>
    <div class="list-main">
      <van-tabs v-model="curInfoType" @change="onClickTab">
        <van-tab :title="item.name" :name="item.type" v-for="item in infoType" :key="item.type"></van-tab>
      </van-tabs>
      <div class="list-main-search">
        <van-search v-model="keyword" placeholder="关键字搜索" @search="onSearch" />
      </div>
      <div class="list-main-cont">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            :offset="20"
            @load="getList"
          >
            <van-cell v-for="item, index in list" :key="index" :title="item.title" class="van-ellipsis" @click="handleToDetail(item)">
              <template #icon>
                <van-icon name="todo-list" size="24" color="#01a9e8" style="marginRight: 10px"/>
              </template>
            </van-cell>
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';

@Component({
  components: {
    Header
  }
})

export default class leadership extends Vue {
  private curInfoType: any = ''; // 当前tabs
  private infoType: any = [ // tabs
    {
      name: '领导批示列表',
      type: 'leader',
    },
    {
      name: '接收办理反馈',
      type: 'feedback'
    },
    {
      name: '部领导批示',
      type: 'dleader'
    }
  ];

  private keyword: any = ''; //关键字搜索
  refreshing: boolean = false;
  loading: boolean = false;
  finished: boolean = false;
  list: any = [];
  showInfoType: boolean = false;
  addType: any = {}; // 新增信息接报类型
  private apptype:any = '2';
  private requestType:any = 'getEventList';
  private paging: any = {
    total: null,
    nowPage: 1,
    pageSize: 15,
  }

  private onClickTab(data) {
    this.curInfoType = data;
    if (data === 'leader') {
      this.apptype = '2';
      this.requestType = 'getEventList';
    } else if (data === 'dleader') {
      this.apptype = '1';
      this.requestType = 'getEventList';
    } else {
      this.requestType = 'getLimleadappdealList';
    }
    this.paging.nowPage = 1;
    this.keyword = '';
    this.getList(true);
  }

  private onRefresh() {
    // 清空列表数据
    this.finished = false;
    this.list = [];
    // 重新加载数据 将 loading 设置为 true，表示处于加载状态
    this.loading = true;
    this.refreshing = true;
    this.paging.nowPage = 1;
    this.getList();
  }

  // 跳转详情页面
  handleToDetail(item) {
    
    const path = '/leaderDetail';
    console.log(item,'=============item')
    this.$router.push({
      path: path,
      query: {
        type: 'detail',
        infotype: this.curInfoType,
        id: item.id
      }
    })
  }

  onSearch(value) {
    console.log(value)
    this.paging.nowPage = 1;
    this.getList(true);
  }

  // 获取列表数据
  getList(first=false) {
    let params: any = {
      orgCode: JSON.parse(window.localStorage.getItem('role')).orgCode,
      qType: '4',
      keyword: this.keyword,
      nowPage: this.paging.nowPage,
      pageSize: this.paging.pageSize,
      apptype: this.apptype,
    }
    if (this.requestType === 'getLimleadappdealList') {
      params = {
        orgCode: JSON.parse(localStorage.getItem('role')).orgCode,
        keyword: this.keyword,
        nowPage: this.paging.nowPage,
        pageSize: this.paging.pageSize,
      }
    }
    if (this.list.length > 0 && this.list.length === this.paging.total && !first) {
      return
    }
    setTimeout(() => {
      this.loading = true;
      this['$api'].InfoRequest[this.requestType](params, res => {
        let data = JSON.parse(res.data.data);
        this.paging.total = data.total;
        if (res.data.status === 200) {
          if(this.paging.nowPage === 1) {
            this.list = []
          }
          const arr = [...JSON.parse(res.data.data).list];
          arr.forEach((itm: any) => {
            itm.title = itm.title ? itm.title : itm.docTitle;
          })
          if (first) {
            this.list = arr;
          } else {
            this.list = [...this.list, ...arr];
          }
          this.paging.total = data.total;
          this.loading = false;
          this.refreshing = false;
          this.finished = false;
          if(this.list.length >= data.total) {
            this.finished = true;
          }
          this.paging.nowPage++;
        } else {
          Notify({ type: 'danger', message: res.data.msg || '请求失败' });
        }
      })
    }, 500)
    
  }
}

</script>

<style lang="less" scoped>
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    &-search {
      height: 56px;
    }
    &-cont {
      flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      // position: absolute;
      // bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #1967f2;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
  /deep/.van-tabs__line {
    background-color: #1967f2;
  }
}
</style>