<template>
  <div class="replyFile" v-if="fileList && fileList.length > 0">
   
    <template v-for="(file,index) in fileList" >
      <template  v-if="/(.*)\.(mp4|avi|MOV|mov|rmvb|rm|3gp)$/.test(file.name)">
            <video
                width="100%"
                type="video/mp4"
                :src="file.path"
                muted="muted"
                preload="preload"
                controls="controls"
                style="min-width: 50vw; max-height: 300px; background: #ccc"
              ></video>
          <transition mode="out-in" >
                <van-progress v-if='!file.attachId && file.percentage!="100"' :percentage="file.percentage"  stroke-width="8"  />
          </transition>
      </template>
      <!-- <audio v-else-if="file.path.endsWith('.mp3')" :src="file.path" controls id="playMp3"></audio> -->
     
      <div
        :key="index"
        v-else-if="/(.*)\.(mp3)$/.test(file.name)"
        class="dialog-record"
        id="dialogRecord"
      >
        <div name="dialogRecord" id="record" :class="direction">
          <div class="dialog">
            <div
              class="dialog-container"
              :style="{
                width: 10 + (50 / 60) * (file.duration || 1) + 'vw',
                maxWidth: '60vw',
              }"
              @click="handlePlay($event, file)"
            >
              <div class="dialog-time" v-if="direction === 'right'">
                {{ file.duration ? file.duration + '"' : "" }}
              </div>
              <div :class="['audio-animation', 'audio-animation_' + file.attachId]">
                <div id="two"></div>
                <div id="three"></div>
                <div id="four"></div>
              </div>
              <div class="dialog-time" v-if="direction === 'left'">
                {{ file.duration ? file.duration + '"' : "" }}
              </div>
            </div>
            <!-- <audio class="audio" :ref="'audioRef_'+file.attachId">
                <source :src="file.path"/>
              </audio> -->
            <!-- <audio v-show="false" :src="file.path" controls id="playMp3" :ref="'audioRef_'+file.attachId"></audio> -->
            <div v-show="false" :ref="'audioRef_' + file.attachId"></div>
            <div class="focus" tabindex="0"></div>
          </div>
        </div>
        <transition mode="out-in" >
                <van-progress v-if='!file.attachId && file.percentage!="100"' :percentage="file.percentage"  stroke-width="8"  />
          </transition>
      </div>
      <template  v-else-if="/(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/.test(file.name)">
           <img
            :src="file.path"
            @click="hanldPreview(file)"
          />
         <transition mode="out-in" >
                <van-progress v-if='!file.attachId && file.percentage!="100"' :percentage="file.percentage"  stroke-width="8"  />
          </transition>
      </template>
     <template  v-else >
        <div @click="handlePreviewFile(file)" class="file-container">
        <span style="margin-right: 8px" class="file-name" v-if="direction === 'right'">{{
          file.name
        }}</span>
        <span>
          <img v-if="/(.*)\.(ppt|pptx)$/.test(file.name)" :src="imgUrl.pptPng" alt="" />
          <img
            v-else-if="/(.*)\.(doc|docx)$/.test(file.name)"
            :src="imgUrl.wordPng"
            alt=""
          />
          <img
            v-else-if="/(.*)\.(xlsx|xls)$/.test(file.name)"
            :src="imgUrl.excelPng"
            alt=""
          />
          <img v-else-if="/(.*)\.(txt)$/.test(file.name)" :src="imgUrl.txtPng" alt="" />
          <img v-else-if="/(.*)\.(pdf)$/.test(file.name)" :src="imgUrl.pdfPng" alt="" />
          <img v-else :src="imgUrl.filePng" alt="" />
        </span>
        <span style="margin-left: 8px" class="file-name" v-if="direction === 'left'">{{
          file.name
        }}</span>
      </div>
       <transition mode="out-in" >
                <van-progress v-if='!file.attachId && file.percentage!="100"' :percentage="file.percentage"  stroke-width="8"  />
          </transition>
     </template>
      
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import { Notify, ImagePreview, Toast } from "vant";
import event from "@/views/event/index.vue";
import taskRequest from "@/api/webcross/task";
import { Progress } from 'vant';
import { downloadReport } from "@/utils/validate";
@Component({
  components: {},
})
export default class filePreview extends Vue {
  @Prop() fileList: any;
  @Prop() direction: any;
  @Prop() percentage:any;
  public imgUrl: any = {
    wordPng: require("@/assets/images/word.png"),
    pdfPng: require("@/assets/images/PDF.png"),
    pptPng: require("@/assets/images/ppt.png"),
    excelPng: require("@/assets/images/excel.png"),
    txtPng: require("@/assets/images/txt.png"),
    filePng: require("@/assets/images/file.png"),
  };
  // 预览图片
  hanldPreview(item) {
    ImagePreview([item.path]);
  }
  // 预览文件
  handlePreviewFile(item) {
    if (item.pathOuterNet || item.urlOuterNet) {
      window.open(item.pathOuterNet || item.urlOuterNet);
      return false;
    }
    let fileId = item.response ? item.response.attachId : item.attachId;
    taskRequest.download({ fileId }, (res) => {
      console.log("download++++++++++>>>", res);
      this.downloadFile(res);
    });
  }
  downloadFile(obj) {
    if (obj == null) return false;
    // 兼容ie的文件下载方法
    let flag =
      window.navigator.userAgent.indexOf("Trident") > -1 &&
      window.navigator.userAgent.indexOf("rv:11.0") > -1;
    if (flag) {
      (window.navigator as any).msSaveBlob(obj.blobStream, obj.filename);
      try {
      } catch (e) {
        console.log(e);
      }
    } else {
      // 谷歌的下载方法
      let downloadElement = document.createElement("a");
      downloadElement.href = obj.url;
      downloadElement.target = "_self";
      downloadElement.download = obj.filename;
      document.body.appendChild(downloadElement);
      downloadElement.click();
      document.body.removeChild(downloadElement);
      window.URL.revokeObjectURL(obj.url); // 释放掉blob对象
    }
  }
  handlePlay(e, file: any) {
    // 播放
    // const appDiv: any = this.$refs['audioRef_'+file.attachId][0];
    // const sound = new Audio(file.path);
    // // const sound = new Audio('http://172.18.8.167:8990/'+file.path);
    // appDiv.addEventListener("click", () => {
    //   sound.play()
    // });

    // let player: any = this.$refs['audioRef_'+file.attachId][0];
    // const sound = new Audio('http://172.18.8.167:8990/'+file.path);
    const sound = new Audio(file.path);
    if (sound.paused) {
      /*如果已经暂停*/
      sound.play(); /*播放*/
      let audioArr = Array.prototype.slice.call(
        (window.document as any).getElementsByClassName("audio-animation")
      );
      audioArr.forEach((el) => {
        el.classList.remove("audioPlay");
      });
      (window.document as any)
        .getElementsByClassName("audio-animation_" + file.attachId)[0]
        .classList.add("audioPlay");
    } else {
      (window.document as any)
        .getElementsByClassName("audio-animation_" + file.attachId)[0]
        .classList.remove("audioPlay");
    }
    // 录音播放结束停止动画
    sound.addEventListener(
      "ended",
      function () {
        (window.document as any)
          .getElementsByClassName("audio-animation_" + file.attachId)[0]
          .classList.remove("audioPlay");
      },
      false
    );
  }
}
</script>

<style lang="less" scoped>
.replyFile {
  width: 100%;
  margin-top: 10px;
  margin-right: 8px;
  text-align: end;
  img {
    width: 100%;
  }
  audio {
    width: 100%;
    min-width: 50vw;
  }
  .file-container {
    display: flex;
    width: 60vw;
    height: 60px;
    padding: 10px;
    background: #fff;
    border-radius: 4px;
    .file-name {
      flex: 1;
      text-align: left;
      font-size: 13px;
      color: #666;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    img {
      width: 40px !important;
    }
  }
}
.dialog {
  width: 60vw;
}
.dialog-record .dialog span.text {
  max-width: 83%;
  height: auto;
  background: #1967f2;
  padding-left: 3.5%;
  padding-right: 3.5%;
  padding-top: 8px;
  padding-bottom: 8px;
  font-size: 1.3rem;
  color: #fff;
  line-height: 25px;
  border-radius: 5px;
  margin-left: 7px;
  display: inline-block;
  margin-right: 3%;
}
.dialog-record .dialog .focus {
  height: 5px;
  outline-style: none;
}
/*播放语音时的动画*/
@keyframes yuying {
  0% {
    height: 0%;
  }
  20% {
    height: 50%;
  }
  50% {
    height: 90%;
  }
  80% {
    height: 50%;
  }
  100% {
    height: 0%;
  }
}

.dialog-container {
  width: 40px;
  height: 40px;
  border: 1px solid #1967f2;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
}
.dialog-container .audio-animation {
  display: flex;
  align-items: center;
  width: 20px;
  height: 20px;
  > div {
    border-style: solid;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-radius: 50%;
    border-width: 2px;
    box-sizing: border-box;
    vertical-align: middle;
    display: inline-block;
  }
}
.right {
  .dialog-container {
    justify-content: flex-end;
  }
  .audio-animation {
    > div {
      border-right-color: transparent;
      margin-right: -10px;
    }
  }
  #two {
    width: 20px;
    height: 20px;
    margin-right: -16px;
  }
  #three {
    width: 15px;
    height: 15px;
  }
  #four {
    width: 10px;
    height: 10px;
  }
}
.left {
  .dialog-container {
    justify-content: flex-start;
  }
  .audio-animation {
    margin-left: 10px;
    > div {
      border-left-color: transparent;
      margin-left: -10px;
    }
  }
  #two {
    width: 10px;
    height: 10px;
  }
  #three {
    width: 15px;
    height: 15px;
  }
  #four {
    width: 20px;
    height: 20px;
    margin-left: -16px;
  }
}
.audioPlay #two {
  animation: yuying 0.6s infinite 0.3s;
  -webkit-animation: yuying 0.6s infinite 0.3s;
}
.audioPlay #three {
  animation: yuying 0.6s infinite 0.45s;
  -webkit-animation: yuying 0.6s infinite 0.45s;
}
.audioPlay #four {
  animation: yuying 0.6s infinite 0.6s;
  -webkit-animation: yuying 0.6s infinite 0.6s;
}
</style>
