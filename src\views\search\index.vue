<template>
  <div class="searchDetail">
    <div class="search_div">
      <van-search :maxlength="20" placeholder="请输入搜索关键字" v-model="keywords" show-action @search="onSearch">
        <template #left>
          <van-icon name="arrow-left" size="20" @click="onCancel" style="margin-right: 10px" />
        </template>
        <template #action>
          <div @click="onSearch">搜索</div>
        </template>
      </van-search>
    </div>
    <!-- <div class="menu-box">
      <ul>
        <template v-for="(item, index) in menuList">
          <li @click="handleToMenu(item)" v-if="item.show" :class="{ active: searchType == item.type }" :key="index">
            <van-image width="48px" height="48px" fit="contain" :src="item.icon" />
            <span class="name">{{ item.name }}</span>
          </li>
        </template>
      </ul>
    </div> -->

    <!-- 地图组件 -->
    <div class="map-container">
      <baseMap ref="baseMapRef" :mapId="`map_${new Date().getTime()}`"></baseMap>

      <!-- 地图控件 -->
      <div class="map-controls">
        <div class="control-btn" @click="zoomIn">
          <van-icon name="plus" />
        </div>
        <div class="control-btn" @click="zoomOut">
          <van-icon name="minus" />
        </div>
        <div class="control-btn" @click="locateMe">
          <van-icon name="aim" />
        </div>
      </div>
    </div>

    <!-- 搜索结果底部弹出层 -->
    <van-popup
      v-model="showResults"
      position="bottom"
      :style="{ height: `${popupHeight}%` }"
      round
      closeable
      @close="onPopupClose"
      :overlay="false"
      class="result-popup"
    >
      <!-- 拖动手柄 -->
      <div class="drag-handle" @touchstart="onTouchStart" @touchmove="onTouchMove" @touchend="onTouchEnd">
        <div class="handle-line"></div>
      </div>

      <!-- 结果头部 -->
      <div class="result-header">
        <div class="result-count">找到 {{ replyList.length }} 个结果</div>
      </div>

      <!-- 过滤选项 -->
      <div class="filter-options">
        <div
          v-for="(item, index) in menuList"
          :key="index"
          class="filter-item"
          :class="{ active: searchType === item.type }"
          @click="handleFilterChange(item)"
        >
          {{ item.name }}
        </div>
      </div>

      <!-- 搜索结果列表 -->
      <div class="searchResult-content" id="assPCon" ref="myScrollbar">
        <template v-if="replyList.length && loadOver">
          <div v-for="(item, index) in replyList" :key="index">
            <div class="searchResult-item" @click="handleItem(item)">
              <div class="item-main">
                <div class="item-title">{{ item.eventTitle }}</div>
                <div class="item-address">{{ item.infoAddress || '暂无地址信息' }}</div>
                <div class="item-tags">
                  <!-- 显示结果类型标签 -->
                  <span class="tag" :class="'tag-' + item.resultType">{{ item.typeName }}</span>
                  <!-- 如果有事件类型，也显示 -->
                  <span class="tag" v-if="item.eventTypeName">{{ item.eventTypeName }}</span>
                </div>
              </div>
              <div class="item-actions">
                <div class="action-btn" @click.stop="toMap(item)">
                  <div class="location-icon"></div>
                  <div class="action-text">到这里</div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <!-- <template v-if="!replyList.length && loadOver">
          <van-empty description="暂无数据" />
        </template> -->
      </div>
    </van-popup>

    <!-- 搜索结果信息按钮 (当弹出层关闭时显示) -->
    <div class="result-info-btn" v-if="replyList.length > 0 && !showResults" @click="showResults = true">
      <div class="info-text">找到 {{ replyList.length }} 个结果</div>
      <van-icon name="arrow-up" />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import taskRequest from '@/api/webcross/task';
import serveApi from '@/api/request-service';
import baseMap from '@/components/baseMap/baseMap.vue';
import { Toast, Notify } from 'vant';
import { EnvironmentContext } from '@/utils/gis/environmentContext';
import layerUtils from '@/utils/gis/layerUtils';
import egisDataServer from '@/api/service/EgisDataServer';
import gisAbility from '@/utils/gis/gisAbility';
import zwwxSDK from '@/utils/zwwx';
@Component({
  components: {
    baseMap
  }
})
export default class MainDetail extends Vue {
  private keywords = '';
  private replyList = [];
  private eventId: any = '';
  private loadOver = false;
  private teamId: any = '';
  private searchType: any = '';
  private showResults = false;
  // 应急资源图层
  private resourceLayers: any = null;
  // 弹出层高度百分比：33, 50, 100
  private popupHeight: number = 33;

  // 触摸相关变量
  private touchStartY: number = 0;
  private touchStartHeight: number = 0;

  // 地图和定位相关
  private locationInfo = {
    latitude: 30.6402, // 武汉市默认坐标
    longitude: 114.3889,
    address: '当前位置'
  };
  private districtCode = JSON.parse(localStorage.getItem('role')).districtCode;
  // 地图组件引用
  private baseMapRef: any = null;

  menuList: any = [
    {
      name: '事件管理',
      path: '/quicks/',
      icon: require(`@/assets/images/home/<USER>
      show: true,
      type: 'event'
    },
    {
      name: '应急资源',
      icon: require(`@/assets/images/home/<USER>
      show: true,
      type: 'emergency'
    },
    {
      name: '地名地址',
      icon: require(`@/assets/images/icons/widget_upload_pic.png`),
      show: true,
      type: 'address'
    }
  ];

  handleToMenu(item: any) {
    this.searchType = item.type;
    return;
  }

  /**
   * 处理过滤选项变更
   */
  handleFilterChange(item: any) {
    // 如果选择的是当前类型，不做任何操作
    if (this.searchType === item.type) {
      this.searchType = '';
    } else {
      // 更新搜索类型
      this.searchType = item.type;
    }

    // 如果有关键词，重新搜索
    if (this.keywords) {
      this.onSearch();
    }
  }

  private async onSearch() {
    let self = this;
    if (!self.keywords) return;

    // 显示加载提示
    Toast.loading({
      message: '搜索中...',
      forbidClick: true,
      duration: 0
    });

    // 如果没有选择类型或类型为空，调用所有三个接口并整合结果
    if (!this.searchType) {
      this.searchAllTypes();
    }
    // 否则，根据选择的类型调用相应的接口
    else if (this.searchType === 'event') {
      this.searchEvents();
    } else if (this.searchType === 'emergency') {
      this.searchTeams();
    } else if (this.searchType === 'address') {
      this.searchAddresses();
    }
  }

  /**
   * 搜索事件管理
   */
  private searchEvents() {
    // 基础查询参数
    const params: any = {
      districtCode: '',
      eventId: '',
      eventLevelCode: '',
      eventTitle: this.keywords,
      eventTypeCode: '',
      eventTypeCodeList: [],
      listOrder: { prop: '', sort: '' },
      nowPage: 1,
      occurTime: '',
      occurTimeEnd: '',
      occurTimeStart: '',
      pageSize: 10,
      updateTime: ''
    };

    serveApi.eventList(params, (res: any) => {
      if (res.status === 200 && res.data && res.data.data && res.data.data.list) {
        // 处理搜索结果
        const processedResults = this.processSearchResults(res.data.data.list, 'event', '事件管理');
        // 显示搜索结果
        this.showSearchResults(processedResults);
      } else {
        this.showSearchResults([]);
      }
    });
  }

  /**
   * 搜索应急资源
   */
  private async searchTeams() {
    try {
      // 创建所有图层的请求
      let allPromise = [];
      this.resourceLayers.forEach((element: any) => {
        const result = egisDataServer
          .getLayerList({
            code: element.key,
            filter: {
              districtCodes: this.districtCode,
              keyword: this.keywords
            }
          })
          .then((data) => {
            // 为每个结果添加所属图层的key和label
            return {
              data: data || [],
              layerKey: element.key,
              layerLabel: element.label
            };
          });
        allPromise.push(result);
      });

      // 等待所有请求完成
      const results = await Promise.all(allPromise);

      // 整合所有图层的结果，保留每个图层的类型信息
      let allTeamItems = [];
      results.forEach((result) => {
        if (result && result.data && result.data.length > 0) {
          // 为每个结果项添加所属图层信息
          const processedData = result.data.map((item) => ({
            ...item,
            layerKey: result.layerKey,
            layerLabel: result.layerLabel
          }));
          allTeamItems = [...allTeamItems, ...processedData];
        }
      });

      // 处理搜索结果
      const processedResults = this.processSearchResults(allTeamItems, 'emergency', '应急资源');
      // 显示搜索结果
      this.showSearchResults(processedResults);
    } catch (error) {
      console.error('搜索应急资源失败:', error);
      Toast.clear();
      Toast.fail('搜索应急资源失败');
      this.showSearchResults([]);
    }
  }

  /**
   * 搜索地名地址
   */
  private async searchAddresses() {
    try {
      const res: any = await gisAbility.getByAddress(this.keywords, [this.locationInfo.longitude, this.locationInfo.latitude]);
      console.log('地名地址搜索结果:', res);

      // 检查返回结果的格式，适配不同的数据结构
      let addressItems = res.pois || [];

      // 处理搜索结果
      const processedResults = this.processSearchResults(addressItems, 'address', '地名地址');
      // 显示搜索结果
      this.showSearchResults(processedResults);
    } catch (error) {
      console.error('搜索地名地址失败:', error);
      Toast.clear();
      Toast.fail('搜索地名地址失败');
      this.showSearchResults([]);
    }
  }

  /**
   * 搜索所有类型并整合结果
   */
  private async searchAllTypes() {
    // 用于存储所有搜索结果
    let allResults: any[] = [];
    let completedRequests = 0;
    const totalRequests = 3; // 三个接口

    // 基础查询参数
    const params: any = {
      districtCode: '',
      eventId: '',
      eventLevelCode: '',
      eventTitle: this.keywords,
      eventTypeCode: '',
      eventTypeCodeList: [],
      listOrder: { prop: '', sort: '' },
      nowPage: 1,
      occurTime: '',
      occurTimeEnd: '',
      occurTimeStart: '',
      pageSize: 10,
      updateTime: ''
    };

    // 1. 事件管理搜索
    serveApi.eventList(params, (res: any) => {
      completedRequests++;

      if (res.status === 200 && res.data && res.data.data && res.data.data.list) {
        // 处理搜索结果
        const eventResults = this.processSearchResults(res.data.data.list, 'event', '事件管理');
        allResults = [...allResults, ...eventResults];
      }

      this.handleAllSearchCompletion(completedRequests, totalRequests, allResults);
    });

    // 2. 应急资源搜索
    try {
      let teamPromises: Promise<any>[] = [];
      this.resourceLayers.forEach((element: any) => {
        const result = egisDataServer
          .getLayerList({
            code: element.key,
            filter: {
              districtCodes: this.districtCode,
              keyword: this.keywords
            }
          })
          .then((data) => {
            // 为每个结果添加所属图层的key和label
            return {
              data: data || [],
              layerKey: element.key,
              layerLabel: element.label
            };
          });
        teamPromises.push(result);
      });

      // 使用Promise.all等待所有应急资源请求完成
      const results = await Promise.all(teamPromises);
      completedRequests++;

      // 整合所有图层的结果，保留每个图层的类型信息
      let allTeamItems: any[] = [];
      results.forEach((result) => {
        if (result && result.data && result.data.length > 0) {
          // 为每个结果项添加所属图层信息
          const processedData = result.data.map((item) => ({
            ...item,
            layerKey: result.layerKey,
            layerLabel: result.layerLabel
          }));
          allTeamItems = [...allTeamItems, ...processedData];
        }
      });

      // 处理搜索结果
      const teamResults = this.processSearchResults(allTeamItems, 'emergency', '应急资源');
      allResults = [...allResults, ...teamResults];
      console.log(allResults);
    } catch (error) {
      console.error('搜索应急资源失败:', error);
      completedRequests++;
    } finally {
      this.handleAllSearchCompletion(completedRequests, totalRequests, allResults);
    }

    // 3. 地名地址搜索
    try {
      const res: any = await gisAbility.getByAddress(this.keywords, [this.locationInfo.longitude, this.locationInfo.latitude]);
      console.log('地名地址搜索结果 (searchAllTypes):', res);
      completedRequests++;

      // 检查返回结果的格式，适配不同的数据结构
      let addressItems = res.pois || [];

      // 处理搜索结果
      const addressResults = this.processSearchResults(addressItems, 'address', '地名地址');
      allResults = [...allResults, ...addressResults];
    } catch (error) {
      console.error('搜索地名地址失败:', error);
      completedRequests++;
    } finally {
      this.handleAllSearchCompletion(completedRequests, totalRequests, allResults);
    }
  }

  /**
   * 处理搜索结果
   * @param results 搜索结果数组
   * @param resultType 结果类型 ('event'|'emergency'|'address')
   * @param typeName 类型名称 ('事件管理'|'应急资源'|'地名地址')
   * @returns 处理后的结果数组
   */
  private processSearchResults(results: any[], resultType: string, typeName: string): any[] {
    if (!results || !Array.isArray(results) || results.length === 0) {
      return [];
    }

    return results.map((item: any, index: number) => {
      // 根据不同类型处理经纬度
      let longitude = item.longitude;
      let latitude = item.latitude;

      if (resultType === 'address') {
        // 处理地址类型的经纬度
        if (!longitude && item.location && item.location.lng) {
          longitude = item.location.lng;
        }
        if (!latitude && item.location && item.location.lat) {
          latitude = item.location.lat;
        }

        if (!longitude && item.detail_info && item.detail_info.navi_location && item.detail_info.navi_location.lng) {
          longitude = item.detail_info.navi_location.lng;
        }
        if (!latitude && item.detail_info && item.detail_info.navi_location && item.detail_info.navi_location.lat) {
          latitude = item.detail_info.navi_location.lat;
        }
      }

      // 通用字段处理
      longitude = longitude || item.lon || item.jd || 114.3889; // 默认使用武汉市坐标
      latitude = latitude || item.lat || item.wd || 30.6402;

      // 处理资源类型
      let itemResultType = resultType;
      let itemTypeName = typeName;

      // 如果是应急资源，使用各自图层的类型信息
      if (resultType === 'emergency' && item.layerKey) {
        itemResultType = item.layerKey; // 使用图层key作为更具体的类型
        itemTypeName = item.layerLabel || '应急资源'; // 使用图层label作为类型名称
      }

      // 构建通用结果对象
      const result: any = {
        ...item,
        resultType: itemResultType,
        typeName: itemTypeName,
        eventTitle: item.eventTitle || item.name || item.title || '未命名',
        infoAddress: item.infoAddress || item.address || '暂无地址信息',
        longitude: longitude,
        latitude: latitude
      };

      return result;
    });
  }

  /**
   * 显示搜索结果
   * @param results 处理后的搜索结果
   */
  private showSearchResults(results: any[]) {
    this.loadOver = true;
    this.replyList = results;

    // 显示搜索结果列表
    this.showResults = true;

    // 默认显示为33%高度
    this.popupHeight = 33;

    // 清除之前的标记
    this.$nextTick(() => {
      // 如果有搜索结果，将结果显示在地图上
      if (this.replyList.length > 0) {
        this.showResultsOnMap();
      }
    });

    // 清除加载提示
    Toast.clear();
  }

  /**
   * 处理所有搜索完成后的操作
   */
  private handleAllSearchCompletion(completed: number, total: number, results: any[]) {
    // 当所有请求都完成时
    if (completed === total) {
      this.showSearchResults(results);
    }
  }

  /**
   * 在地图上显示搜索结果
   */
  private showResultsOnMap() {
    // 确保地图已初始化
    if (!EnvironmentContext.egisMap) {
      console.warn('地图尚未初始化，无法显示搜索结果');
      return;
    }

    try {
      // 使用 layerUtils 在地图上显示点位
      const pointList = this.replyList.map((item) => {
        // 处理各种可能的经纬度格式
        let longitude = item.longitude;
        let latitude = item.latitude;

        // 如果是百度地图返回的数据结构
        if (!longitude && item.location && item.location.lng) {
          longitude = item.location.lng;
        }
        if (!latitude && item.location && item.location.lat) {
          latitude = item.location.lat;
        }

        // 如果有导航位置
        if (!longitude && item.detail_info && item.detail_info.navi_location && item.detail_info.navi_location.lng) {
          longitude = item.detail_info.navi_location.lng;
        }
        if (!latitude && item.detail_info && item.detail_info.navi_location && item.detail_info.navi_location.lat) {
          latitude = item.detail_info.navi_location.lat;
        }

        // 其他可能的字段名
        longitude = longitude || item.lon || item.jd || 114.3889; // 默认使用武汉市坐标
        latitude = latitude || item.lat || item.wd || 30.6402;

        return {
          longitude: longitude,
          latitude: latitude,
          name: item.eventTitle || item.name || '未命名位置',
          address: item.infoAddress || item.address || '暂无地址信息',
          id: item.eventId || item.uid || Date.now(),
          type: item.resultType || this.searchType,
          // 添加原始数据，以便在点击标记时显示详情
          originalData: item
        };
      });

      // 如果有结果，显示在地图上
      if (pointList.length > 0) {
        layerUtils.showPointList(pointList, {
          setCenter: true // 设置地图中心点为第一个结果的位置
        });
      }
    } catch (error) {
      console.error('在地图上显示搜索结果失败:', error);
    }
  }

  handleItem(item: any) {
    // 使用 item.resultType 而不是 this.searchType，因为在混合搜索结果中，每个项目有自己的类型
    const itemType = item.resultType || this.searchType;

    if (itemType === 'event') {
      // 事件管理类型 - 使用原有的eventId
      sessionStorage.setItem('eventInfo', JSON.stringify(item));
      this.$store.commit('TEAMTYPECODE', item.eventTypeCode);
      // this.$router.push({
      //   path: `/quicks/${item.eventId}`,
      //   query: {
      //     districtCode: item.districtCode,
      //     eventTypeCode: item.eventTypeCode
      //   }
      // });
    } else if (itemType === 'emergency' || this.resourceLayers.some((layer) => layer.key === itemType)) {
      // 应急资源类型 - 使用生成的eventId
      // this.$router.push({
      //   path: '/result',
      //   query: {
      //     eventId: item.eventId, // 这是我们在processSearchResults中生成的ID
      //     type: itemType,
      //     layerKey: item.layerKey // 添加图层key信息
      //   }
      // });
    } else if (itemType === 'address') {
      // 地名地址类型 - 使用生成的eventId
      // this.$router.push({
      //   path: '/result',
      //   query: {
      //     eventId: item.eventId, // 这是我们在processSearchResults中生成的ID
      //     type: itemType,
      //     // 添加额外的地址信息
      //     name: item.name || item.eventTitle,
      //     address: item.address || item.infoAddress,
      //     longitude: item.longitude,
      //     latitude: item.latitude
      //   }
      // });
    }
  }

  toMap(item: any) {
    // 使用 item.resultType 而不是 this.searchType，因为在混合搜索结果中，每个项目有自己的类型
    const itemType = item.resultType || this.searchType;

    // 构建导航参数
    const query: any = {};
    if (itemType === 'event') {
      query.eventId = item.eventId;
      query.type = itemType;
    }
    // 如果是应急资源，添加图层信息
    if (itemType === 'emergency' || this.resourceLayers.some((layer) => layer.key === itemType)) {
      query.layerKey = item.layerKey;
      query.id = item.id;
    }

    // 对于地址类型，添加额外的位置信息
    if (itemType === 'address') {
      query.name = item.name || item.eventTitle;
      query.address = item.address || item.infoAddress;
      query.longitude = item.longitude;
      query.latitude = item.latitude;
      query.type = itemType;
    }

    this.$router.push({
      path: '/result',
      query: query
    });
  }

  private onCancel() {
    this.$router.go(-1);
  }

  // 放大地图
  private zoomIn() {
    if (!EnvironmentContext.egisMap) return;
    EnvironmentContext.egisMap.zoomIn();
  }

  // 缩小地图
  private zoomOut() {
    if (!EnvironmentContext.egisMap) return;
    EnvironmentContext.egisMap.zoomOut();
  }

  // 定位到当前位置
  private locateMe() {
    if (zwwxSDK.isWeiXin()) {
      this.getZwwxLocation();
    } else {
      // 获取用户当前位置
      this.getCurrentLocation();
    }
  }

  /**
   * 处理弹出层关闭事件
   */
  private onPopupClose() {
    // 弹出层关闭时，重置高度为默认值
    this.popupHeight = 33;
  }

  /**
   * 处理触摸开始事件
   */
  private onTouchStart(event: TouchEvent) {
    this.touchStartY = event.touches[0].clientY;
    this.touchStartHeight = this.popupHeight;
  }

  /**
   * 处理触摸移动事件
   */
  private onTouchMove(event: TouchEvent) {
    // 防止页面滚动
    event.preventDefault();

    const currentY = event.touches[0].clientY;
    const deltaY = this.touchStartY - currentY;

    // 计算搜索框高度占屏幕的百分比
    const searchBoxHeight = document.querySelector('.search_div')?.clientHeight || 50;
    const windowHeight = window.innerHeight;
    const searchBoxPercent = (searchBoxHeight / windowHeight) * 100;

    // 最大高度为100% - 搜索框高度
    const maxHeight = 100 - searchBoxPercent;

    // 计算新的高度百分比
    // 向上滑动，deltaY为正，高度增加
    // 向下滑动，deltaY为负，高度减少
    const heightChange = (deltaY / windowHeight) * 100;
    let newHeight = this.touchStartHeight + heightChange;

    // 限制高度在合理范围内
    newHeight = Math.max(20, Math.min(maxHeight, newHeight));

    // 更新弹出层高度
    this.popupHeight = newHeight;
  }

  /**
   * 处理触摸结束事件
   */
  private onTouchEnd() {
    // 计算搜索框高度占屏幕的百分比
    const searchBoxHeight = document.querySelector('.search_div')?.clientHeight || 50;
    const windowHeight = window.innerHeight;
    const searchBoxPercent = (searchBoxHeight / windowHeight) * 100;

    // 最大高度为100% - 搜索框高度
    const maxHeight = 100 - searchBoxPercent;

    // 根据当前高度，自动吸附到最接近的预设高度
    if (this.popupHeight < 25) {
      // 如果高度小于25%，关闭弹出层
      this.showResults = false;
    } else if (this.popupHeight < 40) {
      // 33%高度
      this.popupHeight = 33;
    } else if (this.popupHeight < 75) {
      // 50%高度
      this.popupHeight = 50;
    } else {
      // 最大高度（100% - 搜索框高度）
      this.popupHeight = maxHeight;
    }
  }

  mounted() {
    this.eventId = this.$route.query.eventId;
    this.teamId = this.$route.query.teamId;
    this.loadOver = true;
    this.locateMe();

    const data = require(`./allLayers.json`);
    this.resourceLayers = [...data.allLayers[2].children, ...data.allLayers[3].children];
  }

  /**
   * 获取用户当前位置
   */
  private getCurrentLocation() {
    if (navigator.geolocation) {
      Toast.loading({
        message: '定位中...',
        forbidClick: true,
        duration: 0
      });
      navigator.geolocation.getCurrentPosition(
        this.locationCb,
        (error) => {
          // 获取位置失败
          console.error('获取位置失败:', error);
          Toast.fail('获取位置失败，使用默认位置');
          Toast.clear();
        },
        {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0
        }
      );
    } else {
      Toast.fail('您的浏览器不支持地理定位');
    }
  }

  private getZwwxLocation() {
    this['$zwwx'].getLocation().then(this.locationCb);
  }

  private locationCb = (position) => {
    // 成功获取位置
    this.locationInfo.latitude = position.coords.latitude;
    this.locationInfo.longitude = position.coords.longitude;

    Toast.success('定位成功');
    Toast.clear();

    // 更新地图中心点
    this.$nextTick(() => {
      layerUtils.addPointToMap([this.locationInfo.longitude, this.locationInfo.latitude]);
    });
  };
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.searchDetail {
  height: 100%;
  background: white;
  position: relative;

  .van-search__action {
    color: #1a67f2;
  }

  /* 结果弹出层样式 */
  .result-popup {
    overflow: hidden;

    /* 拖动手柄 */
    .drag-handle {
      width: 100%;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      touch-action: none; /* 防止触摸事件被浏览器处理 */

      .handle-line {
        width: 40px;
        height: 4px;
        background-color: #ddd;
        border-radius: 2px;
      }
    }

    /* 结果头部 */
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 16px 10px;
      border-bottom: 0;

      .result-count {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    /* 过滤选项 */
    .filter-options {
      display: flex;
      padding: 0 10px 10px;
      overflow-x: auto;
      white-space: nowrap;
      border-bottom: 1px solid #f0f0f0;

      &::-webkit-scrollbar {
        display: none;
      }

      .filter-item {
        padding: 6px 12px;
        margin-right: 8px;
        font-size: 13px;
        color: #666;
        background-color: #f5f5f5;
        border-radius: 16px;
        flex-shrink: 0;

        &:last-child {
          margin-right: 0;
        }

        &.active {
          color: #fff;
          background-color: #1a67f2;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  /* 结果信息按钮 */
  .result-info-btn {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    padding: 10px 20px;
    border-radius: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    z-index: 100;

    .info-text {
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-right: 8px;
    }

    .van-icon {
      color: #1a67f2;
    }

    &:active {
      background-color: #f5f5f5;
    }
  }

  /* 搜索结果内容 */
  .searchResult-content {
    padding: 0;
    flex: 1;
    overflow-y: auto;
    background-color: #fff;
    height: calc(100% - 100px);
  }

  .searchResult-item {
    padding: 16px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;

    &:active {
      background-color: #f9f9f9;
    }

    &::before {
      content: '';
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #1a67f2;
      display: none; /* 默认隐藏，可以根据需要显示 */
    }

    .item-main {
      flex: 1;
      overflow: hidden;
      padding-right: 10px;
    }

    .item-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .item-address {
      font-size: 13px;
      color: #999;
      margin-bottom: 6px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        background: url(~@/assets/images/icons/locationGray.png) no-repeat center center;
        background-size: contain;
        margin-right: 4px;
        opacity: 0.6;
      }
    }

    .item-tags {
      display: flex;
      flex-wrap: wrap;

      .tag {
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 10px;
        margin-right: 5px;
        margin-bottom: 2px;

        /* 默认样式 */
        color: #ff6e30;
        background-color: rgba(255, 110, 48, 0.1);

        /* 事件管理标签样式 */
        &.tag-event {
          color: #ff3b30;
          background-color: rgba(255, 59, 48, 0.1);
        }

        /* 应急资源标签样式 */
        &.tag-team {
          color: #ff9500;
          background-color: rgba(255, 149, 0, 0.1);
        }

        /* 地名地址标签样式 */
        &.tag-address {
          color: #34c759;
          background-color: rgba(52, 199, 89, 0.1);
        }
      }
    }

    .item-actions {
      display: flex;
      flex-direction: column;
      align-items: center;
      min-width: 60px;

      .action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 5px;

        &:active {
          opacity: 0.7;
        }

        .location-icon {
          width: 22px;
          height: 22px;
          background: url(~@/assets/images/icons/locationGray.png) no-repeat center center;
          background-size: 100% 100%;
          margin-bottom: 4px;
        }

        .action-text {
          font-size: 12px;
          color: #1a67f2;
        }
      }
    }
  }

  /* 搜索结果信息 */
  .search-result-info {
    position: absolute;
    bottom: 30px;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;

    .result-count {
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
      padding: 8px 15px;
      border-radius: 20px;
      font-size: 14px;
      margin-bottom: 12px;
      text-align: center;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    }

    .result-list-btn {
      background-color: #ff6e30;
      color: #fff;
      padding: 12px 24px;
      border-radius: 24px;
      font-size: 15px;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(255, 110, 48, 0.4);

      &:active {
        background-color: #e65d20;
      }

      .van-icon {
        margin-right: 6px;
        font-size: 18px;
      }

      span {
        margin-left: 2px;
      }
    }
  }

  .menu-box {
    padding: 6px 10px;
    ul {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-items: center;
      height: 100%;
      li {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        text-align: center;
        span {
          padding-top: 10px;
          color: #1c1d1d;
        }

        &.active {
          span {
            color: #1a67f2;
            font-weight: bold;
          }
        }
      }
    }
  }

  /* 地图容器样式 */
  .map-container {
    position: absolute;
    top: 50px;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 15px;
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), transparent);
      z-index: 2;
    }

    /* 地图控件 */
    .map-controls {
      position: absolute;
      right: 15px;
      bottom: 120px;
      z-index: 10;
      display: flex;
      flex-direction: column;

      .control-btn {
        width: 40px;
        height: 40px;
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

        &:active {
          background-color: #f5f5f5;
        }

        .van-icon {
          color: #333;
          font-size: 20px;
        }
      }
    }
  }

  /* 当前位置标记样式 */
  .current-location-marker {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    pointer-events: none;

    .pulse-circle {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(26, 103, 242, 0.2);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      animation: pulse 2s infinite;
    }

    .center-point {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #1a67f2;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      border: 2px solid white;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    }
  }

  /* 地图控件 */
  .map-controls {
    position: absolute;
    right: 15px;
    bottom: 100px;
    z-index: 10;
    display: flex;
    flex-direction: column;

    .control-btn {
      width: 40px;
      height: 40px;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);

      &:active {
        background-color: #f5f5f5;
      }

      .van-icon {
        color: #333;
        font-size: 20px;
      }
    }
  }

  /* 返回搜索按钮 - 不再需要，由底部容器的关闭按钮替代 */
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  70% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
}
</style>
