<template>
  <div class="div_big">
    <van-nav-bar title="救援队伍详情" left-text="返回"   left-arrow  @click-left="GoBack" >
    <template #right>
    <!-- <van-icon name="ellipsis" size="25" /> -->
  </template>
   </van-nav-bar>
   <!-- invoke common List -->
  <div class="info_big">
    <div class="info_head">
        <p>{{detailInfo.teamName}}</p>
        <li>
          <span>所属行政区划：</span>
          <span>{{detailInfo.districtName}}</span>
        </li>
        <li>
          <span>队伍位置：</span>
          <span>{{detailInfo.address}}</span>
        </li>
        <li>
          <span>队伍名称：</span>
          <span>烟台市蓝天救援队</span>
        </li>
        <span></span>
    </div>
    <div class="info_content">
      <p>
       {{detailInfo.notes==null?"无描述":detailInfo.notes}}
      </p>
    </div>
  </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue , Prop} from 'vue-property-decorator';
import apiServer from '../../api/request-service';
@Component({
  components: {
  }
})
export default class rescueTeamDetail extends Vue {
  @Prop(String) private requestId :string;
  position:any = 'left';
  queryMap: any ={};
  detailInfo: any = {};
  onClickLeft(){
    console.log("返回点击")

  }
  requestItem(){
    let _this=this;
    let param={"teamId":_this.requestId}
    console.log(JSON.stringify(param))
    apiServer.findDetailQuery(param,function(res){
      console.log(res)
      if(res.status==200){
        _this.detailInfo=res.data.data;
        console.log(_this.detailInfo)
      }
    },"1")
  }
  GoBack(){
    this.$emit("close")
  }
  created() {
    let _this=this;
    
  }
  mounted(){
    this.requestItem();
    let _this=this;
    _this['$gsmdp'].initGoback(function(){
      _this.GoBack()
    })
  }
}
</script>
<style scoped lang="scss">
.div_big{
  width: 100%;
  height: 100%;
  background: #f2f2f2;
  .info_big{
    width: 90%;
    height: calc( 100% - 46px - 6% );
    margin: 5%;
    background: white;
    border-radius: 15px;
    font-size: 15px;
    .info_head{
      width: 100%;
      padding: 5%;
      text-align: center;
      >li{
        width: 100%;
        text-align: left;
      }
    }
    .info_content{
      width: 100%;
      padding: 0px 5%;
      border-top: 1px solid #f2f2f2;
    }
  }
}

</style>