<template>
  <div class="info_div">
    <van-nav-bar :title="requestObj.name" left-text="返回" left-arrow @click-left="GoBack"> </van-nav-bar>
    <MeetBack @documentBack="documentBack"></MeetBack>
    <div class="info_content">
      <ul>
        <li class="info_cell" v-for="item in labelConfig" :key="item.label">
          <span>{{ item.label }}</span>
          <span>{{ detailInfo[item.name] }}</span>
        </li>
      </ul>
     <div v-if="type == 'materialItem'">
      <h3>物资列表</h3>
      <van-list>
        <van-cell v-for="(item, index) in materialList" :key="index" >
        <template #title>
          <div class="content-item">
            <div class="index">{{ index + 1 }}</div>
            <div class="name">{{ item.name }}</div>
            <div class="value">{{ item.value }}{{ item.unit }}</div>
          </div>
          </template>
        </van-cell>
      </van-list>
     </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import apiServer from '../../api/request-service';
import MeetBack from '@/components/meet-back/meet-back';
import fieldConfig from '@/utils/fieldConfig';
@Component({
  components: {
    MeetBack
  }
})
export default class integratedItem extends Vue {
  @Prop(String) private requestId: string;
  @Prop(Object) private requestObj: any;
  @Prop(String) private type: string;
  detailInfo: any = {};
  showObject: any = {};
  labelConfig: any = [];
  materialList: any = [
    {
      name: '牙具盒',
      value: 10,
      MATERIALTYPEname: null,
      unit: '个'
    },
    {
      name: '睡袋',
      value: 63,
      MATERIALTYPEname: null,
      unit: '个'
    },
    {
      name: '洗漱用品',
      value: 193,
      MATERIALTYPEname: null,
      unit: '个'
    }
  ];
  meterialObject: any = {
    objectid: 17,
    repertoryid: 'YCK1032063534',
    id: 'YCK1032063534',
    _id: 'YCK1032063534',
    repertoryname: '武汉亚一商超A休闲食品仓4号库',
    name: '武汉亚一商超A休闲食品仓4号库',
    repertorytypecode: '社会',
    REPERTORYTYPENAME: '社会',
    REPERTORYTYPECODE: '社会',
    levelcode: '社会',
    LEVELNAME: '社会',
    districtcode: '新洲区',
    address: '湖北省武汉市新洲区阳逻经济开发区京东大道亚一园区',
    longitude: 114.576578,
    latitude: 30.74829,
    area: null,
    capacity: null,
    restock: null,
    tranforce: null,
    dutytel: null,
    concateper: '刘瑶',
    leader: '刘瑶',
    concateofftel: '13116090750',
    concatemobtel: null,
    phone: null,
    chargedept: '京东物流',
    CHARGEDEPT: '京东物流',
    chargedepttel: null,
    createuser: null,
    createtime: null,
    updatetime: null,
    orgcode: null,
    deleteflag: null,
    resguaraper: null,
    resguaratel: null,
    orgname: null,
    fax: null,
    wkt: null,
    sourcedept: null,
    geosourcedept: null,
    geoupdatetime: null,
    geomType: 'Point',
    towncode: null,
    jcsj: null,
    zzwhhglrysl: null,
    tbr: null,
    tjfzr: null
  };
  handleAdd() {}
  infoRequest() {
    let _this = this;
    let formdata = new FormData();
    formdata.append('id', _this.requestId);
    const params = {
      id: _this.requestId
    };
    apiServer.findWarningById(params, function (res) {
      let dataHandleInfo = res.data.data; //dataHandle.caseInfo(res);
      _this.detailInfo = dataHandleInfo;
    });
  }

  GoBack() {
    let _this = this;
    try {
      _this.$emit('close');
    } catch (error) {
      console.log('close error');
    }
  }
  documentBack() {
    this.GoBack();
  }
  created() {
    let _this = this;
    _this.labelConfig = fieldConfig[_this.type];
    if (this.type == 'materialItem') _this.detailInfo = _this.meterialObject;
    //_this['$apiServer'].setToken();
    //   _this.infoRequest();
  }
  mounted() {
    let _this = this;
    _this['$gsmdp'].initGoback(function () {
      console.log('调用返回');
      _this.GoBack();
    });
  }
}
</script>
<style scoped lang="scss">
.info_div {
  width: 100%;
  height: 100%;
  font-family: '微软雅黑';
  background: #f4f7f8;
  .info_content {
    width: 100%;
    height: calc(100% - 46px);
    overflow-y: auto;
    ul {
      width: 100%;
      background: #fff;
    }
    li {
      width: 100%;
      padding: 0px 3%;
      p {
        margin: 0;
        line-height: 3rem;
        font-size: 17px;
        font-weight: bold;
      }
    }
    .info_cell {
      line-height: 2.5rem;
      color: #333333;
      overflow: hidden;
      display: flex;
      span {
        display: inline-block;
        float: left;
      }
      span:nth-of-type(1) {
        width: 4.6rem;
        color: #000000;
      }
      span:nth-of-type(2) {
        width: calc(100% - 5rem);
      }
      .arr-text {
        display: flex;
        span {
          width: auto;
        }
      }
    }
    .text_area_div {
      width: 100%;
      .text_area_title {
        width: 100%;
        height: 2.5rem;
        line-height: 2.5rem;
        padding: 0px 3%;
        color: c0c0c0;
      }
      .text_area_centent {
        width: 100%;
        background: #fff;
        line-height: 2.5rem;
        padding: 0px 3%;
      }
    }
    .info_div_attachment {
      width: 100%;
      background: #fff;
      padding: 0px 3%;
      span {
        display: inline-block;
        float: left;
        line-height: 2.5rem;
        img {
          width: 1.5rem;
          vertical-align: middle;
          float: left;
          margin-top: 0.3rem;
        }
      }
      span:nth-of-type(1) {
        width: 3rem;
      }
      span:nth-of-type(2) {
        width: calc(100% - 3rem);
      }
    }
  }
}
.content-item{
  display: flex;
  align-items: center;
  .index{
    width: 50px;
  }
  .name{
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .value {
    // width: 100px;
  }
}
</style>
<style lang="less">
.info_div {
  .replyFile .file-container {
    width: 100%;
    height: 100%;
  }
}
</style>
