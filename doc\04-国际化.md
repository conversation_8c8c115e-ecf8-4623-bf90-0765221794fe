# 国际化处理方式  

## 一、国际化处理约定  
1. 国际化词条文件遵从统一的命名约定  
```  
src  
├── lang                                    国际化词条，多语言文件
│   ├── en-US.json                          -英文词条
│   ├── es-EC.json                          -西语词条
│   ├── index.ts                           -i18n处理
│   └── zh-CN.json                          -中文词条  
```

2. `vue`内模块化词条文件    
`view`模块内每个组件的词条文件，与`vue`文件目录同级  
```  
  │   ├── views                            
  │   │   ├── about
  │   │   │   ├── about.scss               组件样式文件，建议独立
  │   │   │   ├── about.vue                组件vue文件，建议template与ts可以并存于此文件。也可依据实际情况将ts分离
  │   │   │   └── about-zh-CN.json                  国际化词条文件，研发时。 构建时会通过gulp统一进行合并到lang目录   

  ```   

3. 词条的key, 参考AG01的做法统一为大驼峰
4. 词条划分, 可以按组件划分(结构清晰, 但会有冗余词条), 或者按系统划分(减少冗余词条, 但使用不方便, 不便于查找维护)
5. 词条文件的命名, 建议严格规范命名, zh.json  en.json 不是标准的国际化文件命名, 
命名参考: 
组件：messages-zh-CN.json  messages-en-US.json , 例如：router的router-zh-CN.json、incident-wait的incident-wait-zh-CN.json    
如`alarm-person-zh-CN.json`:  
```  js
{
  "AlarmPerson": {
    "AppealInfo": "报警人信息",
    "AppealPhone": "报警号码",
    "Age": "年龄",
    "AppealPeople": "报警人",
    "AppealAddress": "来电地址",
    "MALE": "男",
    "FEMALE": "女",
    "Unknow": "不明",
    "Under15": "15岁以下",
    "From16To25": "16至25岁",
    "From26To35": "26至35岁",
  }
}  
```  

## 二、vue template模板绑定中写法  
``` html  
<template>
  <div class="alarm-person">
    <h2 class="form-title">{{$t('AlarmPerson.AppealInfo')}}</h2>
    ......  
```


## 三、`ts`代码中使用国际化  
```  js
import i18n from '@/lang';   
i18n.t(`KeyWorld`);
or
i18n.t(`TimeSheet.${strResult}`);  
```  


