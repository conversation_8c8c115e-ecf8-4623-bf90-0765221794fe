/**
 * 日志组件初始化类
 */

import { initLogger, LogOpts, InjectClsLog, InjectLog, logFactory, Logger } from '@gsafety/cad-glog';

export const initLog = () => {
  const logOpts: LogOpts = {
    logToElectron: true, // 是否发送日志到Electron主进程
    logLevel: -1, // 日志等级，对应jsnlog等级
    systemName: 'cad-log-test', // TODO edit 记录系统名称
    electronEventName: 'renderlog-electron-event' // TODO edit 发送到electron-main主进程的事件名称
  };
  initLogger(logOpts);
};

const gblog = logFactory.getLogger('violet-seed-logger');
export { logFactory, gblog, Logger, InjectClsLog, InjectLog };
