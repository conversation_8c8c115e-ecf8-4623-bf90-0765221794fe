import { expect } from 'chai';

import { stringFormatArr, stringFormat } from '@gsafety/cad-gutil/dist/stringformat';

describe('stringformatArr function test', () => {
  it('should return guid', () => {
    const result = stringFormatArr('http://1.1.1.1/{name}/jijeh/{id}', ['username', '123']);
    expect(result).to.equal('http://1.1.1.1/username/jijeh/123');
  });
});

describe('stringFormat function test', () => {
  it('should return guid', () => {
    const result = stringFormat('http://1.1.1.1/{name}/jijeh/{id}', 'username1', '111');
    expect(result).to.equal('http://1.1.1.1/username1/jijeh/111');
  });
});
