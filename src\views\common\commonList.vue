<template>
  <div class="my_list">
    <div v-if="tabArr && tabArr.length > 0">
      <van-tabs v-model="active" color="#1f4ae3" title-active-color="#1f4ae3" @change="changeTab">
        <van-tab v-for="(item, index) in tabArr" :title="item.name" :key="index">
          <div class="search_div" v-if="searchObj.whetherHave">
            <van-search v-model="keyword" @search="onSearch" @cancel="onCancel" input-align="center" :placeholder="searchObj.placeholder" :maxlength="20" />
          </div>
          <div class="content_list" :style="{ height: autoHeight }" @touchstart="moveClick" @touchmove="moveEv" @touchend="moveEnd">
            <ul class="content_ul" ref="container" @click="changeShow">
              <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
                <!-- <integratedItem ref="itemsRef" :requestFlag="index.toString()" @handleObj="handleObj" /> -->
                <component
                  :is="loadObj.name"
                  ref="itemsRef"
                  :locationInfo='locationInfoData'
                  :requestObj="item"
                  :showFlag="show && sliderType"
                  :requestFlag="index.toString()"
                  @handleObj="handleObj"
                ></component>
              </mu-load-more>
            </ul>
          </div>
        </van-tab>
      </van-tabs>
    </div>
    <div v-else>
      <!-- calc( 100% - 46px - 58px - 53px) -->
      <div class="search_div" v-if="searchObj.whetherHave">
        <van-search v-model="keyword" @search="onSearch" @cancel="onCancel" :placeholder="searchObj.placeholder" :maxlength="20" />
      </div>
      <div class="content_list" :style="{ height: autoHeight }">
        <ul class="content_ul" ref="container">
          <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
            <!-- <integratedItem ref="itemsRef"  @handleObj="handleObj" /> -->
            <component   :locationInfo='locationInfoData' :is="loadObj.name" ref="itemsRef" :requestObj="null" :requestFlag="null" @handleObj="handleObj"></component>
          </mu-load-more>
        </ul>
      </div>
    </div>

    <van-popup v-model="show" v-if="show && sliderType" position="right" :style="{ height: '94%', width: '50%' }" :overlay="false">
      <div class="slider_div">
        <h4><img src="../../assets/images/planManage/planType.png" />预案类型</h4>
        <div v-for="item in sliderType.planType" :key="item.id" class="planType_div">
          <p @click="changePlantParam(item)" :class="item.checked === true ? 'planBg' : ''">
            {{ item.label }}
          </p>
        </div>
        <h4><img src="../../assets/images/planManage/eventType.png" />事件类型</h4>
        <div v-for="item in sliderType.eventType" :key="item.id" class="eventType_div">
          <p @click="changeEventParam(item)" :class="item.checked === true ? 'eventBg' : ''">
            {{ item.label }}
          </p>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import integratedItem from '../integratedQuery/integratedItem.vue';
import planItem from '../knowledgeBase/planItem.vue';
import caseItem from '../knowledgeBase/caseItem.vue';
import lawItem from '../knowledgeBase/lawItem.vue';
import knowledgeItem from '../knowledgeBase/knowledgeItem.vue';
import baseKnowledgeItem from '../knowledgeBase/baseKnowledgeItem.vue';
import teachingVideoItem from '../knowledgeBase/teachingVideoItem.vue';
import specificationsItem from '../knowledgeBase/specificationsItem.vue';

import taskItem from '../taskManage/taskListItem.vue';
import planManageItem from '../planManage/planManageItem.vue';
import listItem from '../taskManage/listItem.vue'
import emergencyItem from '../emergency/emergencyItem.vue';
import deliveryItem from '../power/deliveryItem.vue';
@Component({
  components: {
    integratedItem,
    planItem,
    caseItem,
    lawItem,
    knowledgeItem,
    baseKnowledgeItem,
    teachingVideoItem,
    specificationsItem,
    taskItem,
    planManageItem,
    listItem,
    emergencyItem,
    deliveryItem
  }
})
export default class commonList extends Vue {
  @Prop(Array) private tabArr: Array<any>;
  @Prop(Object) private searchObj: object;
  @Prop(Object) private loadObj: object;
  @Prop(Object) private sliderType?: object;
  @Prop(Object) private locationInfo?:object
  private locationInfoData:any={};
  @Watch('keyword')
  onChangeValue(newVal: string, oldVal: string) {
    let _this = this;
    _this.keyword = _this.$changeVal(newVal)
    console.log(newVal, this.active);
    if (_this.tabArr.length > 0) {
      _this.tabArr[_this.active].currentSearch = newVal;
    }
  }
  changeShow() {
    if (this.show) {
      this.show = false;
      return;
    }
  }
  @Watch('params', { deep: true })
  onChangeType(newVal: string, oldVal: string) {
    let _this = this;
    console.log('search==>' + _this.keyword + '===>' + _this.active);
    let toIndex = 0;
    let items: any = _this.$refs.itemsRef;
    if (_this.active >= items.length) {
      toIndex = items.length - 1;
    } else {
      toIndex = _this.active;
    }
    let itemsRef = {};
    if (_this.tabArr.length > 0) {
      itemsRef = _this.$refs.itemsRef[toIndex];
    } else {
      itemsRef = _this.$refs.itemsRef;
    }
    itemsRef['searchSet'](_this.keyword);
    let pageObj = {
      pageIndex: 1,
      pageSize: _this.pageSize,
      planTypeCode: _this.params.planTypeCode,
      eventTypeCode: _this.params.eventTypeCode
    };
    itemsRef['queryRequest'](pageObj);
  }
  refreshing: any = false;
  loading: any = false;
  currItem: any = 'integratedItem';
  keyword: any = '';
  autoHeight: any = ''; //动态列表高度
  active: any = 0;
  pageIndex: any = 1;
  pageSize: any = 10;

  private params = {
    eventTypeCode: '',
    planTypeCode: ''
  };
  private swcount = 0;
  private pageXI = 0;
  private pageYI = 0;
  private planTypeCodes = []; //选中的预案类型
  private eventTypeCodes = []; //选中的事件类型
  changePlantParam(item) {
    let _this = this;
    //true的话相当于要取消
    let checkedFlag=item.checked;
    if (item.checked) {
      _this.planTypeCodes.splice(this.planTypeCodes.indexOf(item), 1);
    } else {
      _this.planTypeCodes.splice(0, 1);
      _this.planTypeCodes.push(item.id);
    }
    _this.sliderType['planType'].map((v)=>{
      v.checked = false;
      return v;
    })
    if(!checkedFlag){
      _this.sliderType['planType'].forEach(element => {
        if (element.id === item.id) {
          item.checked = !item.checked;
        }
      });
    }
    this.$forceUpdate();
    this.params.planTypeCode = this.planTypeCodes.join(',');
  }

  changeEventParam(item) {
    let _this = this;
    //true的话相当于要取消
    if (item.checked) {
      _this.eventTypeCodes.splice(this.eventTypeCodes.indexOf(item), 1);
    } else {
      _this.eventTypeCodes.push(item.id);
    }
    _this.sliderType['eventType'].forEach(element => {
      if (element.id === item.id) {
        item.checked = !item.checked;
      }
    });
    this.$forceUpdate();
    this.params.eventTypeCode = this.eventTypeCodes.join(',');
  }
  moveEv(e) {
    this.swcount++;
    if (this.swcount > 5) {
      let pageY = e.changedTouches[0].pageY;
      //console.log("yvalue=>"+(pageY-this.pageYI)*4)
      let pageX = e.changedTouches[0].pageX;
    }
  }
  private show: any = false;
  showPopup() {
    this.show = !this.show;
  }
  moveEnd(e) {
    let _this = this;
    let pageX = e.changedTouches[0].pageX;
    // console.log(pageX + '===>' + this.pageXI);
    /*左右滑*/
    if (pageX + 100 < this.pageXI) {
      console.log('进入右滑');
      //this.NextMonth();

      this.show = true;
      this.swcount = 0;
    } else if (pageX > this.pageXI + 100) {
      console.log('进入左滑');
      this.show = false;
      //this.PreMonth();
      this.swcount = 0;
    }
  }

  moveClick(e) {
    this.swcount = 0;
    console.log('moveClick==>' + e.changedTouches[0].pageX);
    this.pageXI = e.changedTouches[0].pageX;
    this.pageYI = e.changedTouches[0].pageY;
  }

  created() {
    let _this = this;
    var name = 'myItem';
return
    var myComponent = () => import('../integratedQuery/integratedItem.vue');
    var route = {
      name: name,
      component: myComponent
    };
    _this.calcAutoHeight();
  }
  @Watch('locationInfo',{
    deep:true
  })
  private handleData(val){
    this.locationInfoData = val;
  }
  mounted() {
    this.sliderType;
    window['GoBack'] = this.GoBack; //安卓调用GoBack（手机back键返回问题）
  }
  changeTab(_index, itemName) {
    let _this = this;
    _this.keyword = '';
    let tabObj = _this.tabArr[_index];
    //切换tab页面清空分页数据与分页数据
    if (tabObj.currentIndex) {
      _this.pageIndex = tabObj.currentIndex;
    }
    if (tabObj.currentSearch) {
      // _this.keyword = tabObj.currentSearch;
    }

    //侧滑栏数据更新
    // this.sliderType={

    // };
    this.$emit('changeType', _index, itemName);
    this.params = {
      eventTypeCode: '',
      planTypeCode: ''
    };
    this.eventTypeCodes = [];
    this.planTypeCodes = [];
    //隐藏策划栏
    this.show = false;
    //_this.$refs.itemsRef[_index].initDataRequest(tabObj)
  }
  //calc list height（module tab add padding is 58px module search is  53px ）
  calcAutoHeight() {
    let _this = this;
    let aheight = '100% - 46px ';
    if (_this.tabArr.length > 0) {
      aheight += '- 58px ';
    }
    if (_this.searchObj['whetherHave']) {
      aheight += ' - 53px ';
    }
    _this.autoHeight = 'calc(' + aheight + ')';
  }
  onSearch() {
    let _this = this;
    console.log('search==>' + _this.keyword + '===>' + _this.active);
    let toIndex = 0;
    if(_this.keyword){
      _this.keyword=_this.keyword.trim();
    }
    let items: any = _this.$refs.itemsRef;
    if (_this.active >= items.length) {
      toIndex = items.length - 1;
    } else {
      toIndex = _this.active;
    }
    let itemsRef = {};
    if (_this.tabArr.length > 0) {
      itemsRef = _this.$refs.itemsRef[toIndex];
    } else {
      itemsRef = _this.$refs.itemsRef;
    }
    itemsRef['searchSet'](_this.keyword);
    let pageObj = { pageIndex: 1, pageSize: _this.pageSize };
    itemsRef['queryRequest'](pageObj);
  }
  onCancel() {
    console.log('cancel search');
  }
  handleRight(_url) {
    this.$emit('handleRight', _url);
  }
  GoBack() {
    let _this = this;
    console.log('go back');
    window['gsmdp'].showToast({ title: 'Go back' });

    //_this.$isAndroid()
  }
  private shuzu;
  handleObj(item) {
    let _this = this;
    console.log(item);
    if (!item.uploadMore) {
      _this.refreshing = false;
      _this.loading = false;
    }
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.pageIndex = 1;
    console.log(_this.active);
    if (_this.tabArr.length > 0) {
      _this.tabArr[_this.active].currentIndex = 1;
    }
    let toIndex = 0;
    let items: any = _this.$refs.itemsRef;
    if (_this.active >= items.length) {
      toIndex = items.length - 1;
    } else {
      toIndex = _this.active;
    }
    let pageObj = { pageIndex: 1, pageSize: _this.pageSize };
    console.log(_this.$refs.itemsRef);
    if (_this.tabArr.length > 0) {
      _this.$refs.itemsRef[toIndex]['queryRequest'](pageObj);
    } else {
      _this.$refs.itemsRef['queryRequest'](pageObj);
    }
  }
  load() {
    const _this = this;
    _this.pageIndex++;
    if (_this.tabArr.length > 0) {
      _this.tabArr[_this.active].currentIndex = _this.pageIndex;
    }
    let toIndex = 0;
    let items: any = _this.$refs.itemsRef;
    if (_this.active >= items.length) {
      toIndex = items.length - 1;
    } else {
      toIndex = _this.active;
    }
    _this.loading = true;
    let pageObj = { pageIndex: _this.pageIndex, pageSize: _this.pageSize };
    if (_this.tabArr.length > 0) {
      _this.$refs.itemsRef[toIndex]['queryRequest'](pageObj);
    } else {
      _this.$refs.itemsRef['queryRequest'](pageObj);
    }
  }
}
</script>
<style scoped lang="scss">
.planBg {
  background: #e8f1f5;
  color: #3e5dd2 !important;
}
.eventBg {
  background: #e8f1f5;
}

.my_list {
  width: 100%;
  height: 100%;
  .van-popup--right {
    top: 45px;
    //   right: 0;
    // -webkit-transform: translate3d(0, -50%, 0);
    // transform: translate3d(0, -50%, 0);
  }
  //   .van-overlay {
  //  background: rgba(0, 0, 0, 0.1)
  // }

  .search_div {
    width: 100%;
  }
  > div {
    height: 100%;
  }
}

.content_list {
  height: calc(100% - 46px);
  overflow: auto;
  background: #f1efef;
  .content_ul {
    height: 100%;
  }
}
.showMenu {
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
  color: white;
}
.slider_div {
  padding: 0 10px;
  h4 {
    font-size: 14px;
    img {
      height: 25px;
      width: 25px;
      vertical-align: middle;
      margin-right: 10px;
    }
  }

  .planType_div {
    p {
      color: #808080;
      height: 30px;
      line-height: 30px;
      padding-left: 20px;
    }
  }
  .eventType_div {
    p {
      height: 30px;
      line-height: 30px;
      padding-left: 20px;
    }
  }
}
</style>
