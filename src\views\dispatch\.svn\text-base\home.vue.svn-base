<template>
  <div class="div_big">
    <x-header :left-options="{backText: '',preventGoBack: true}" @on-click-back='goBack'  style=" z-index: 10;background:#1FB1FE;">舆情管理
    </x-header>
    <span class="showMenu">
          <mu-menu cover placement="bottom-end" :open.sync="open">
            <mu-button icon>
              <mu-icon value="more_vert"></mu-icon>
            </mu-button>
            <mu-list slot="content">
              <mu-list-item button  @click.native="comeMenu(0)">
                <mu-list-item-title >事件库</mu-list-item-title>
              </mu-list-item>
              <mu-list-item button @click.native="comeMenu(1)">
                <mu-list-item-title>我的关注</mu-list-item-title>
              </mu-list-item>
              <mu-list-item button @click.native="comeMenu(2)">
                <mu-list-item-title>自定义提醒</mu-list-item-title>
              </mu-list-item>
              <mu-list-item button @click.native="comeMenu(3)">
                <mu-list-item-title>关闭菜单</mu-list-item-title>
              </mu-list-item>
            </mu-list>
          </mu-menu>
      </span>
    <div class="tab_class">
      <tab>
          <tab-item v-for="(item,index) in tabData" :key="index" :selected="index==0" @on-item-click="onItemClick(index)" style="font-size: 15px">{{item.name}}</tab-item>
      </tab>
    </div>
    <div class="content_list" >
     <ul class="content_ul" ref="container" v-if="selected==0">
        <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
        <li @click="comeInDetail(index,1)" v-for="(item,index) in remindList" :key="index">
          <div class="list_title centent_margin">{{item.title}}</div>
          <div class="list_center centent_margin">
            <span class="time_left">{{item.formatDate}}</span>
            <span class="flag_right_red"  v-if="item.importanceType=='1'">&nbsp;&nbsp;重大网络舆论突发事件&nbsp;&nbsp;</span>
              <span class="flag_right_ye"  v-if="item.importanceType=='2'">&nbsp;&nbsp;潜在网络舆论突发事件&nbsp;&nbsp;</span>
          </div>
          <div class="list_content centent_margin">{{item.content}}</div>
        </li>
        <div v-if="remindList&&remindList.length==0" style="width:100%;text-align:center;padding-top: 10%;">
          <img src="../../assets/imgs/ramind.png" style="width:292px" />
          <!--<img src="../../assets/imgs/notdata.png" />-->
        </div>
        </mu-load-more>
     </ul>
     <ul class="content_ul" ref="container" v-if="selected==1">
        <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
        <li @click="comeInDetail(index,0)" v-for="(item,index) in eventList" :key="index">
          <div class="list_title centent_margin">{{item.title}}</div>
          <div class="list_center centent_margin">
            <span class="time_left">{{item.formatDate}}</span>
             <span class="flag_right_red"  v-if="item.importanceType=='1'">&nbsp;&nbsp;重大网络舆论突发事件&nbsp;&nbsp;</span>
              <span class="flag_right_ye"  v-if="item.importanceType=='2'">&nbsp;&nbsp;潜在网络舆论突发事件&nbsp;&nbsp;</span>
          </div>
          <div class="list_content centent_margin">{{item.content}}</div>
        </li>
        <div v-if="eventList&&eventList.length==0" style="width:100%;text-align:center;padding-top: 10%;">
          
          <img src="../../assets/imgs/notdata.png" />
        </div>

        </mu-load-more>
     </ul>

     
      <popup :hide-on-blur=false  v-model="showDetail" width="100%" position="right">
        <homeDetail v-if="showDetail"  :queryMap="queryMap" @close="closeDetail"></homeDetail>
      </popup>
    </div>
    
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { XHeader,Tab, TabItem , Popup } from 'vux';
import homeDetail from './homeDetail.vue';
@Component({
  components: {
    XHeader,
    Tab,
    TabItem,
    Popup,
    homeDetail
  }
})
export default class Home extends Vue {
  tabData = [{"code":"0","name":"事件提醒"},{"code":"0","name":"全部"}];
  selected: any = 0;
  eventList: any = [];
  remindList: any = [];
  refreshing: any = false; 
  loading: any = false;
  showDetail: any = false;
  pageIndex: any = 1;
  pageSize: any = 10;
  docked:any = false;
  open:any = false;
  position:any = 'left';
  queryMap: any ={};
  comeMenu(flag){
    console.log("进入")
    this.open=false;
    if(flag==0){
      this.$router.push({path: "/eventSearch", query: {"id":"0"}});//事件库
    }else if(flag==1){
       this.$router.push({path: "/myLike", query: {"id":"1"}});//我的关注
    }else if(flag==2){
       this.$router.push({path: "/eventRemind", query: {"id":"2"}});//事件提醒
    }else if(flag==3){
      
    }
  }
    onItemClick(_index) {
      console.log(_index)
      this.selected=_index;
      if(this.selected==0){
          this.eventList=[];
      }else if(this.selected==1){
          this.remindList=[];
      }
      this.refresh()
    }
    clickMore(){
      console.log("打开菜单")
      this.open=true;
    }
    refresh() {
      let _this = this;
      _this.refreshing = true;
      //debugger;
      //_this.$refs.container.scrollTop = 0;
      _this.pageIndex=1;
      _this.queryRequest("");
    }
    load() {
      const _this = this;
      _this.pageIndex++;
      _this.queryRequest("1");
      _this.loading = true;
      // setTimeout(() => {
      //   _this.loading = false;
      // }, 2000);
    }
    goBack(){
      try {
         window['android'].destory();
      } catch (error) {
         console.log("close error")
      }
    }
    comeInDetail(_index,type) {
      let _this=this;
      let obj={};
      if(type==0){
        obj=_this.eventList[_index]
      }else{
         obj=_this.remindList[_index]
      }
      console.log("进入详情")
      console.log(obj)
      _this.queryMap.id=obj['id'];
      _this.queryMap.name=obj['title'];
      console.log(_this.queryMap)
      _this.showDetail=true;
    }
    closeDetail(){
       this.showDetail=false;
    };
    queryRequest(page_flag){
      let _this=this;
      var p1 = new Promise(function (resolve, reject) {
        //_this.loading = true;
        try {
          // const params = new URLSearchParams();
          // params.append('days', '30');
          // params.append("curPage",_this.pageIndex);
          // params.append("pageSize",_this.pageSize);  
          let param,url;
          if(_this.selected==1){
             param={"curPage": _this.pageIndex,"days": 30,"pageSize": _this.pageSize} 
             url='api/event/recent/eventinfo/list/v1';
          }else{
             param={"curPage": _this.pageIndex,"pageSize": _this.pageSize} 
             url='api/event/subscribed/eventinfo/list/v1';
          }
          let res =_this['$apiServer'].commonRequest(url,param);
          console.log(res)
          resolve(res)
        } catch (error) {
          reject("500");
        }
      });
      Promise.all([p1]).then(function (results) {
        _this.loading=false;
        _this.refreshing=false;
        let resultsObj=results[0];
        if(resultsObj==null||resultsObj['status']!=200){
          console.log("处理异常");
        }else{
          if(page_flag){
             resultsObj['data'].list.forEach(function(item,index){
                  let currDate=new Date(item.occurTime)
                  item.formatDate=currDate.getFullYear()+"-"+_this.setZero(currDate.getMonth()+1)+"-"+_this.setZero(currDate.getDate());
               })
            if(_this.selected==1){
              resultsObj['data'].list.forEach(function(item,index){
                _this.eventList.push(item)
              })
            }else{
              resultsObj['data'].list.forEach(function(item,index){
                _this.remindList.push(item)
              })
            }
            
          }else{
              resultsObj['data'].list.forEach(function(item,index){
                  let currDate=new Date(item.occurTime)
                  item.formatDate=currDate.getFullYear()+"-"+_this.setZero(currDate.getMonth()+1)+"-"+_this.setZero(currDate.getDate());
               })
             if(_this.selected==1){
               _this.eventList=resultsObj['data'].list;
            }else{
              _this.remindList=resultsObj['data'].list;
            }
          }
         
        }
      }).catch(function (status) {
        
         
      });
    }
    setZero(num){
      num=num+"";
      if(num&&num.length==1){
        return "0"+num;
      }else{
        return num;
      }
    }
    created() {
      let _this=this;
      _this['$apiServer'].setToken();
      console.log("init run")
      setTimeout(function() {
         _this.queryRequest("");
      }, 500);
     
    }
    

}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%
}
.content_list{
  height: calc( 100% - 44px - 45px );
 
}
.content_ul{
  height: 100%;
  overflow: auto;
}
.centent_margin{
  width: 98%;
  margin-left: 1%;
}
.content_ul li{
  width: 94%;
  height: 115px;
  margin: 10px 3%;
  position: relative;
  box-shadow: #c1c1c1 0vw 1vw 2vw 0vw;
}
.content_ul li .flag_red{
    width: 7px;
    height: 100%;
    background: red;
    position: absolute;
    top: 1px;
    right: -3%;
}
.content_ul li .flag_ye{
    width: 7px;
    height: 100%;
    background: #ffb911;
    position: absolute;
    top: 1px;
    right: -3%;
}

.content_ul .list_title{
    font-size: 17px;
    font-weight: bold;
    line-height: 30px;
    color:#3b70e5b8;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.content_ul .list_center{
  font-size: 13px;
  color: gray;
  
}
.content_ul .list_center .time_left{
  width: 25%;
  background: red($color: #000000);
  display: inline-block;
  line-height: 25px;
}
.content_ul .list_center .flag_right_red{
  border: 2px solid red;
  display: inline-block;
  line-height: 20px;
  border-radius: 25px;
  margin: 0px 3px;
  float: right;
}
.content_ul .list_center .flag_right_ye{
  border: 2px solid rgb(223, 161, 27);
  display: inline-block;
  line-height: 20px;
  border-radius: 25px;
  margin: 0px 3px;
  float: right;
}
.content_ul .list_content{
    font-size: 15px;
    line-height: 30px;
    text-indent: 2em;//首行缩进
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-family: "黑体";
    clear: both;
}
.showMenu{
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
      color: white;
}
</style>