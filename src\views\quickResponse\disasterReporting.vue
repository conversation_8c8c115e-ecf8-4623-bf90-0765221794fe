<template>
  <div class="disasterReporting">
    <Header title="灾情上报"></Header>
    <div class="disasterReporting_content">
      <h2>上报历史</h2>
      <div class="timeline_block">
        <el-timeline>
          <el-timeline-item v-for="(activity, index) in activities"
                            :key="index"
                            :color="activity.color"
                            :size="activity.size"
                            :timestamp="activity.content"
                            placement="top">
            <div class="timeline_info">
              <div class="timeline_title">{{activity.timestamp}}</div>
              <div>
                <van-image width="100"
                           height="100"
                           src="https://cdn.jsdelivr.net/npm/@vant/assets/cat.jpeg" />
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <div style="margin: 16px;position: fixed;bottom: 0;width: calc(100% - 32px);">
      <van-button block
                  type="primary"
                  native-type="submit"
                  @click="submit">
        灾情上报
      </van-button>
    </div>
    <van-popup v-model="show"
               closeable
               position="bottom"
               :style="{ height: '75%' }">
      <van-form @submit="onSubmit">
        <van-cell-group inset>
          <van-field v-model="formObj.content"
                     type="textarea"
                     rows="4"
                     autosize
                     name="content"
                     label="上报内容"
                     :placeholder="'请输入详细信息'"
                     :rules="[{ required: true, message: '请输入详细信息' }]" />
          <!-- <van-field
            name="附件"
            label="附件"
          /> -->
          <div class="text">附件</div>
          <van-uploader v-model="fileList"
                        multiple
                        style="margin-top:15px" />
        </van-cell-group>
        <div style="margin: 16px;width: calc(100% - 32px);">
          <van-button block
                      type="primary"
                      native-type="submit">
            确认提交
          </van-button>
        </div>
      </van-form>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { Uploader } from 'vant';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
    [Uploader.name]: Uploader
  }
})
export default class disasterReporting extends Vue {
  private show = false;
  private submit() {
    this.show = true;
  }
  private submitForm() {}
  private formObj: any = {
    content: ''
  };
  private pattern: any = /\d/;
  private onSubmit() {}
  private activities: any = [
    {
      content: '2022-04-16 19:32:22',
      timestamp: '西沙路东侧一百米处发生次生爆炸',
      size: 'large',
      color: '#87c64e'
    },
    {
      content: '2022-04-15 14:14:53',
      timestamp: '距爆炸四公里处人员进行疏散',
      size: 'large',
      color: '#f39800'
    }
  ];
  private fileList: any = [{ url: 'https://cdn.jsdelivr.net/npm/@vant/assets/leaf.jpeg' }, { url: 'https://cloud-image', isImage: true }];
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.disasterReporting {
  background: #fff;
  .disasterReporting_content {
    padding: 2px 10px 13px 10px;
    h2 {
      font-size: 17px;
      color: #333;
      padding-bottom: 15px;
      border-bottom: 1px solid #e4e4e4;
    }
    .timeline_block {
      padding-top: 10px;
      .timeline_info {
        .timeline_title {
          font-size: 14px;
          color: #1c1d1d;
          margin: 8px 0;
        }
      }
    }
  }
  .van-button--primary {
    background-color: #326eff;
    border: 1px solid #326eff;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
  }
}
/deep/.van-form {
  .van-cell-group {
    padding: 0 10px;
    .text {
      padding: 16px 0;
      border-bottom: 1px solid #cecece;
      font-size: 17px;
      color: #1c1d1d;
    }
    .van-cell {
      padding: 12px 0;
      border-bottom: 1px solid #cecece;
      &:nth-child(2) {
        font-size: 15px;
      }
      &:nth-child(1) {
        font-size: 17px;
        color: #1c1d1d;
        flex-direction: column;
        height: 348px;
        border: none;
        .van-cell__title {
          width: 100%;
          padding-bottom: 12px;
          border-bottom: 1px solid #cecece;
        }
        .van-cell__value {
          padding-top: 12px;
        }
      }
    }
  }
  .van-button--primary {
    background-color: #326eff;
    border: 1px solid #326eff;
    border-radius: 4px;
    font-size: 16px;
    font-weight: bold;
  }
  textarea.van-field__control {
    height: 250px !important;
    min-height: 250px;
    max-height: 250px !important;
  }
}
</style>
