import { Dialog } from 'vant';
import { gsmdp } from './common/gsmdp/gsmdp'; // 事件处置 左上角 返回的功能处理

// 获取当前位置信息
export function getLocInfo(t: any) {
  console.log('++++++++++++++++获取当前位置信息***********')
  t.locInfo.tip = '正在获取位置信息';
  t.getLocSuccess = false;
  new Promise((resolve, reject) => {
    gsmdp.getLocation({
      data: {
        type: 'gcj02',
        altitude: 'true',
        openSetting: 'false'
      },
      success(res:any) {
        console.log('获取当前位置信息***********success---->', res);
        resolve(res);
      },
      fail(err:any) {
        console.log('获取当前位置信息***********fail---->', err);
        const data = JSON.parse(gsmdp.isGPSOnSync());
        if (data.result && !data.result.isOpen) {
          t.locInfo = { err: 'gps', tip: '定位失败，请打开定位服务并点击重新定位！' };
          t.getLocSuccess = false;
        } else {
          t.locInfo = { err: 'network', tip: '位置信息获取失败，请检查网络！' };
          t.getLocSuccess = false;
        }
      }
    });
  }).then((loc: any) => {
    return new Promise((resolveLoc, rejectLoc) => {
      gsmdp.getLocationInfo({
        data: {
          longitude: loc.longitude,
          latitude: loc.latitude,
          address: ''
        },
        success(res:any) {
          console.log('获取当前位置信息222getLocationInfo***********success---->', res);
          resolveLoc(res);
        },
        fail(err:any) {
          console.log('获取当前位置信息222getLocationInfo***********fail---->', err);
          t.locInfo = { tip: '位置信息获取失败，点击重试' };
          t.getLocSuccess = false;
        }
      });
    }).then((locInfo: any) => {
      console.log('++++++++++++>>>>>位置信息', locInfo )
      if (locInfo.longitude !== '' && locInfo.latitude !== '' && locInfo.address !== '') {
        t.locInfo = locInfo;
        t.getLocSuccess = true;
      }
    });
  });
}

export function openGps(t: any) {
  const data = JSON.parse(gsmdp.isGPSOnSync());
  if (data.result && !data.result.isOpen) {
    Dialog.confirm({
      title: '提示',
      message: '是否打开定位服务？'
    })
      .then(() => {
        gsmdp.openGPSSettingSync();
      })
      .catch((err: any) => {
        console.log(err, '+err');
      });
  }
  return data.result && data.result.isOpen;
}
