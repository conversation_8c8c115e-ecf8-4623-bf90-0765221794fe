<!-- 一键查询信息详情 -->
<template>
  <div class="search-info">
    <div class="title-box">
      <div class="title">{{ resultInfo.eventTitle }}</div>
      <!-- <div class="to-around" @click="onCancel">
        <img class="title-icon" src="@/assets/images/icons/locationGray.png" alt="" />
        搜周边
        <van-icon name="arrow" size="20" />
      </div> -->
    </div>
    <div class="detail-box">
      <div class="address">{{ resultInfo.infoAddress }}</div>
      <div class="coordinates" v-if="resultInfo.longitude && resultInfo.latitude">
        经度: {{ resultInfo.longitude }} 纬度: {{ resultInfo.latitude }}
      </div>
      <div class="team-box" v-if="type == 'team'">
        <div class="team-item">负责人：{{ teamInfo.teamName }}{{ teamInfo.responsibleMobile }}</div>
        <div class="team-item">值班电话：{{ teamInfo.relName }} {{ teamInfo.relMobile }}</div>
        <div class="team-item">队伍人数：{{ teamInfo.peopleNum || 0 }}人</div>
        <div class="team-item">队伍类型：{{ teamInfo.teamType }}</div>
        <div class="team-item">主管单位：{{ teamInfo.groupCodeName }}</div>
      </div>
      <div class="team-box" v-if="type == 'event'">
        <div class="team-item">事发时间: {{ resultInfo.occurTime }}</div>
        <div class="team-item">事件类型：{{ resultInfo.eventTypeName }}</div>
        <div class="team-item">
          人员伤亡情况： 死亡{{ resultInfo.deathNum || 0 }}人，重伤{{ resultInfo.seriousInjureNum || 0 }}人，轻伤{{
            resultInfo.minorInjureNum || 0
          }}人，受伤{{ resultInfo.deathNum || 0 }}人，失踪{{ resultInfo.lossNum || 0 }}人，受困{{ resultInfo.trappedNum || 0 }}人。
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
@Component({
  components: {}
})
export default class searchInfo extends Vue {
  @Prop(Object) private resultInfo: any;
  @Prop(String) private type: any;
  teamInfo: any = {
    teamName: '辰安',
    teamFrom: null,
    teamType: null,
    seqNum: 1,
    relName: '刘超文',
    relMobile: '18602720630',
    peopleNum: null,
    responsibleName: null,
    responsibleMobile: null,
    teamTask: null,
    nowTaskCount: 0,
    sendPeopleNum: 0,
    groupCode: '20',
    groupCodeName: '救援力量'
  };
  eventInfo: any = {};

  // 点击搜周边按钮
  onCancel() {
    this.$emit('surround');
  }

  created() {}
}
</script>
<style lang="less" scoped>
.search-info {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  padding: 20px 10px;
  border-top-left-radius: 15px;
  border-top-right-radius: 15px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  max-height: 30%;
  min-height: 25%;
  overflow-y: auto;

  .title-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .title {
      flex: 1;
      font-size: 18px;
      font-weight: 600;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .to-around {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      padding: 6px 10px;
      border-radius: 20px;
      font-size: 14px;
      color: #333;

      .title-icon {
        width: 14px;
        height: 18px;
        margin-right: 4px;
      }

      .van-icon {
        margin-left: 2px;
      }

      &:active {
        background-color: #e0e0e0;
      }
    }
  }

  .detail-box {
    line-height: 24px;

    .address {
      margin-bottom: 8px;
      color: #333;
      font-size: 15px;
    }

    .coordinates {
      margin-bottom: 10px;
      color: #666;
      font-size: 14px;
    }

    .team-box {
      margin-top: 10px;

      .team-item {
        margin-bottom: 6px;
        font-size: 14px;
        color: #666;
      }
    }
  }
}
</style>
