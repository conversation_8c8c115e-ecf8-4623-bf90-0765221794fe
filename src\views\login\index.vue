<template>
  <div class="login">
    <div class="header">
      <img src="../../assets/images/logo.png" alt="" />
    </div>
    <!-- 登陸表單 -->
    <van-form validate-first @submit="submit" class="login-content">
      <van-field
        class="inputBox"
        v-model="loginInfo.username"
        name="username"
        :left-icon="require('../../assets/images/ico_user.png')"
        placeholder="请输入用户名"
        :rules="[{ required: true, message: '' }]"
      />
      <!-- 通过 validator 进行函数校验 -->
      <van-field
        class="inputBox"
        v-model="loginInfo.password"
        name="password"
        :type="paswdType"
        :left-icon="require('../../assets/images/ico_pass.png')"
        @click-right-icon="showPassword"
        right-icon="eye"
        placeholder="请输入密码"
        :rules="[{ required: true, message: '' }]"
      />
      <!-- 提交按钮 -->
      <div class="submitBtn">
        <van-button round block native-type="submit" class="bottomBtn"> 登录 </van-button>
      </div>
    </van-form>
    <van-loading type="spinner" v-if="loading" class="loading" vertical>登陆中...</van-loading>
  </div>
</template>

<script lang="ts">
import axios from 'axios';
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
const CryptoJS = require('crypto-js');
@Component({
  components: {
    Header
  }
})
export default class login extends Vue {
  loading = false;
  paswdType = 'password';
  loginInfo = {
    username: '',
    password: ''
  };

  indentification_login_name = '';

  created() {
    this.getTitleName();
  }
  mounted() {
    localStorage.removeItem('isLogout');
  }
  getTitleName() {
    const that = this;
    apiServer.titleName({ keys: ['system_identification_login_name'] }, function (res) {
      let indentification_login_name = 'system_identification_login_name' + res.data.data[0].configValue || 'gsafety@emis2020';
      that.indentification_login_name = indentification_login_name.substring(
        indentification_login_name.length - 32,
        indentification_login_name.length
      );
    });
  }
  showPassword() {
    if (this.paswdType === '') {
      this.paswdType = 'password';
    } else {
      this.paswdType = '';
    }
  }
  // 登录
  public submit() {
    const that = this;
    this.loading = true;
    const params = {
      username: this.loginInfo.username,
      password: this.aesEncrypt(this.loginInfo.password, this.indentification_login_name)
    };
    apiServer.userLogin(params, function (res) {
      that.loading = false;
      console.log('++++++++++++++login', res);
      if (res.data.status === 200) {
        localStorage.setItem('token', res.data.data.token);
        localStorage.setItem('role', JSON.stringify(res.data.data.role));
        localStorage.setItem('teamId', res.data.data.teamId);
        axios.defaults.headers.common['token'] = res.data.data.token;
        that.$router.push('/home');
        // let loginObj: any = JSON.stringify(res.data.data.role);
        // Object.defineProperties(loginObj, {
        //   'userid': {
        //     value: loginObj.userId
        //   },
        //   'orgcode': {
        //     value: loginObj.orgCode
        //   },
        //   'token': {
        //     value: JSON.stringify(res.data.data.token)
        //   }
        // });
        // this.$store.commit('SET_USER_INFO', loginObj);
        // window.location.reload();
      } else {
        that.$toast(res.data.msg);
      }
    });
    return;
  }
  /**
   * 密码加密方法
   */
  aesEncrypt(message, key, iv?) {
    let encryptKey = CryptoJS.enc.Utf8.parse(key);
    let cipher = CryptoJS.AES.encrypt(message, encryptKey, {
      iv: !!iv ? CryptoJS.enc.Hex.parse(iv) : null,
      mode: !!iv ? CryptoJS.mode.CBC : CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    });
    return cipher.ciphertext.toString(CryptoJS.enc.Base64);
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.login {
  position: relative;
  display: flex;
  // flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  // background: #fff;
  color: #fff;
  background: url('@{url}login_bg.png') no-repeat 0 0;
  background-size: 100% 100%;
  .header {
    position: absolute;
    top: calc(50% - 250px);
    z-index: 100;
    font-weight: bold;
    font-size: 24px;
    text-align: center;
    img {
      width: 55%;
    }
  }
  .login-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    height: 350px;
    margin: 0 20px;
    padding: 20px;
    background: url('@{url}account_bg.png') no-repeat center / 100% 100%;
    .inputBox {
      margin: 0 0 20px 0;
      border-bottom: 1px solid #f8f8f8;
      // box-shadow: 0 2px 12px 0 rgb(243, 243, 243, 0.8);
    }
    /deep/.van-field__left-icon {
      display: flex;
      align-items: center;
      margin-right: 10px;
    }
  }
  .bottomBtn {
    margin-top: 10px;
    // background:-webkit-linear-gradient(left,#4a7dff,#00A3F0);
    background: #4a7dff;
    color: #fff;
    font-size: 16px;
  }
  .loading {
    display: flex;
    justify-content: center;
    position: absolute;
    top: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.7);
  }
}
</style>
