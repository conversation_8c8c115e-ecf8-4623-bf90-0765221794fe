export default {
  // 尾矿库
  mine: [
    // {
    //   label: "名称",

    //   name: "name"
    // },
    {
      label: '地址',

      name: 'address'
    },
    {
      name: 'yxzkmc',
      label: '运行状况'
    },
    // {
    //   label: "运营状况",

    //   name: "RUNSTATUSCODE"
    // },
    {
      name: 'sshydlmc',
      label: '所属行业大类'
    },
    {
      name: 'sshyxlmc',
      label: '所属行业小类'
    },
    {
      name: 'tyshxydm',
      label: '统一社会信用代码'
    },
    {
      name: 'sfwzk',
      label: '是否为无主库'
    },
    {
      label: '主要负责人',

      name: 'wkkfzr'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'wkkfzrbgsdh'
    },
    {
      name: 'ptgrzrs',
      label: '普通工人总数',
      unit: '人'
    },
    {
      name: 'zcaqgcsrs',
      label: '注册安全工程师人数',
      unit: '人'
    },
    {
      name: 'tzzyrs',
      label: '特种作业人数',
      unit: '人'
    },
    {
      name: 'zzaqglrs',
      label: '专职安全管理人数',
      unit: '人'
    },
    {
      name: 'zzaqglwkkxgzyrs',
      label: '专职安全管理尾矿库相关专业人数',
      unit: '人'
    },
    {
      name: 'fzrrs',
      label: '负责人人数',
      unit: '人'
    },
    {
      name: 'gcktdw',
      label: '工程勘探单位'
    },
    {
      name: 'aqypjdw',
      label: '安全预评价单位'
    },
    {
      name: 'aqsssjdw',
      label: '安全设施设计单位'
    },
    {
      name: 'sgdwmc',
      label: '施工单位名称'
    },
    {
      name: 'gcjldwmc',
      label: '工程监理单位名称'
    },
    {
      name: 'aqyspjdw',
      label: '安全验收评价单位'
    },
    // {
    //   label: "地方政府包保责任人姓名",

    //   name: "COUNTYRESPPER"
    // },
    // {
    //   label: "职务",

    //   name: "countyrespperduty"
    // },
    // {
    //   label: "安全度",

    //   name: "wkkaqdcode"
    // },
    // {
    //   label: "尾矿库等别",

    //   name: "wkkdbcode"
    // },
    // {
    //   label: "是否头顶库",

    //   name: "isoverlibrary"
    // },
    // {
    //   label: "下游1000米受影响人数或设施",

    //   name: "AFFECTNUM"
    // },
    // {
    //   label: "矿种",

    //   name: "MINERALSPE"
    // },

    {
      label: '设计服务年限',
      unit: '年',
      name: 'designservlife'
    },
    {
      label: '已服务年限',
      unit: '年',
      name: 'servlife'
    },
    // {
    //   label: "是否经过扩容",

    //   name: "isdilatation"
    // },
    // {
    //   label: "是否安装在线监测",

    //   name: "sfazzxjcxt"
    // },
    // {
    //   label: "设计坝高",
    //   unit: "米",
    //   name: "designdamht"
    // },
    // {
    //   label: "现状坝高",
    //   unit: "米",
    //   name: "mqdjbgd"
    // },
    // {
    //   label: "设计库容",
    //   unit: "立方米",
    //   name: "designcapa"
    // },
    // {
    //   label: "现状库容",
    //   unit: "立方米",
    //   name: "xzqkr"
    // },
    // {
    //   label: "是否有安全生产许可证",

    //   name: "SFAQSCXKZ"
    // },
    // {
    //   label: "安全许可证有效期",

    //   name: "AQSCXKZJZRQ"
    // },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  yjch: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '类别',

      name: 'type'
    },
    {
      label: '负责人',

      name: 'resp_per'
    },
    {
      label: '联系电话',
      type: 'Tel',
      name: 'resp_per_tel'
    },
    {
      label: '地址',

      name: 'adress'
    },
    {
      label: '测绘装备',

      name: 'equip'
    }
  ],
  common: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '联系人',

      name: 'respper'
    },
    {
      label: '电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '行政区划',

      name: 'districtcode'
    }
  ],
  // 学校
  School: [
    {
      label: '地址',
      name: 'address'
    },
    {
      label: '学校类型',
      name: 'defobjtypecode'
    },
    // {
    //   label: "行政区划",
    //   name: "districtcode"
    // },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '学生人数',
      unit: '人',
      name: 'studentnum'
    },
    {
      label: '教职工人数',
      unit: '人',
      name: 'facultynum'
    },
    {
      name: 'zzxyrs',
      label: '专职校医人数',
      unit: '人'
    },
    {
      name: 'aqbwrysl',
      label: '安保人员数量',
      unit: '人'
    },
    {
      name: 'jss',
      label: '教室数',
      unit: '间'
    },
    {
      name: 'jsyfjzmj',
      label: '教室建筑面积',
      unit: '平方米'
    },
    {
      name: 'xxbsm',
      label: '学校（机构）标识码'
    },
    {
      name: 'xxjgjbz',
      label: '学校（机构）举办者'
    },
    {
      name: 'zdmj',
      label: '占地面积',
      unit: '平方米'
    },
    {
      name: 'wyyssbts',
      label: '万元以上设备',
      unit: '台'
    },
    {
      name: 'sfhwwbhdwhlsjz',
      label: '是否含文物保护单位或历史建筑'
    },
    {
      name: 'tygjzmj',
      label: '体育馆建筑面积',
      unit: '平方米'
    },
    {
      name: 'xsssgyjzmj',
      label: '学生宿舍建筑面积',
      unit: '平方米'
    },

    {
      name: 'sfyxyywssyws',
      label: '是否有校医院/卫生室/医务室'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 医院
  Hospital: [
    {
      label: '地址',
      name: 'address'
    },
    {
      label: '医院类型',
      name: 'orgtypecode'
    },
    {
      label: '医院等级',
      name: 'orggradecode'
    },
    {
      label: '统一社会信用代码',
      name: 'creditcode'
    },
    // {
    //   label: "行政区划",
    //   name: "districtcode"
    // },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    // {
    //   label: "联系人",
    //   name: "contactper"
    // },
    // {
    //   label: "联系人电话",
    //   type: "Tel",
    //   name: "contactmtel"
    // },
    {
      label: '主要特色',
      name: 'mainfeature'
    },
    {
      label: '年诊疗人次',
      unit: '人次',
      name: 'clinicunm'
    },
    {
      label: '年入院人次',
      unit: '人次',
      name: 'inpatientnum'
    },
    {
      label: '科室数量',
      unit: '个',
      name: 'departmentnum'
    },
    {
      label: '病床数',
      unit: '张',
      name: 'bednum'
    },
    {
      label: '医生数',
      unit: '人',
      name: 'doctornum'
    },
    {
      label: '护士数',
      unit: '人',
      name: 'nursenum'
    },
    {
      label: '急救车辆数量',
      unit: '辆',
      name: 'ambulancenum'
    },

    {
      label: '主要医疗装备',
      name: 'equipdesc'
    },
    {
      label: '医院建院年份',
      name: 'buildtime'
    },
    {
      label: '抗震设防烈度',
      name: 'aseisinten'
    },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    // {
    //   label: "主管单位",
    //   name: "chargedept"
    // },
    // {
    //   label: "主管单位电话",
    //   type: "Tel",
    //   name: "cdepttel"
    // },
    {
      label: '应急通信方式',
      name: 'commtype'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 居民区
  resident: [
    {
      label: '地址',
      name: 'address'
    },
    {
      label: '行政区划',
      name: 'districtcode'
    },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      label: '联系人',
      name: 'respper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    // {
    //   label: "联系人",
    //   name: "contactper"
    // },
    // {
    //   label: "联系人电话",
    //   type: "Tel",
    //   name: "contactmtel"
    // },
    {
      name: 'zhs',
      label: '总户数',
      unit: '户'
    },
    {
      name: 'nmczrksl',
      label: '常住人口',
      unit: '人'
    },
    {
      name: 'czryrs',
      label: '残障人员',
      unit: '人'
    },
    {
      name: 'zhxxyrs',
      label: '灾害信息员',
      unit: '人'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  Airport: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   label: "行政区划",
    //   name: "districtcode"
    // },
    {
      label: '值班电话',
      type: 'Tel',
      name: 'dutytel'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  Station: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   label: "行政区划",
    //   name: "districtcode"
    // },
    {
      label: '值班电话',
      type: 'Tel',
      name: 'dutytel'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      label: '车站类型',
      name: 'stationtypecode'
    },
    {
      label: '车站等级',
      name: 'stationlevelcode'
    },
    {
      label: '候车面积',
      unit: '㎡',
      name: 'waitroomarea'
    },
    {
      label: '广场面积',
      unit: '㎡',
      name: 'plazaarea'
    },
    {
      label: '站台数',
      unit: '个',
      name: 'platformnum'
    },
    {
      label: '应急设施',
      name: 'emergestab'
    },
    {
      label: '旅客吞吐量',
      unit: '人/次',
      name: 'passthruput'
    },
    {
      label: '货邮吞吐量',
      unit: '万吨',
      name: 'cargothruput'
    },
    {
      label: '投入使用时间',
      name: 'inusedate'
    },
    {
      label: '设计使用年限',
      name: 'useyearnum'
    },
    {
      label: '可容纳人数',
      unit: '人',
      name: 'maxpersonnum'
    },
    {
      label: '监测方式',
      name: 'monitmode'
    },
    {
      label: '防护措施',
      name: 'defencestep'
    },
    {
      label: '所属铁路局',
      name: 'bureauname'
    },
    {
      label: '线路数',
      unit: '条',
      name: 'railnum'
    },
    {
      label: '站房面积',
      unit: '㎡',
      name: 'stationarea'
    },
    {
      label: '总面积',
      unit: '㎡',
      name: 'totalarea'
    },
    {
      label: '铁路线路名称',
      name: 'railname'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  coalMine: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   label: "行政区划",
    //   name: "county"
    // },
    {
      label: '煤矿类型',
      name: 'coaltypecode'
    },
    {
      label: '法定代表人',
      name: 'legal_name'
    },
    {
      label: '法人电话',
      type: 'Tel',
      name: 'legal_te'
    },
    {
      label: '负责人',
      name: 'principal'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'principal_cell'
    },
    {
      label: '允许开采深度',
      unit: '米',
      name: 'approved_mine_depth'
    },
    {
      label: '投产日期',
      name: 'product_date'
    },
    {
      label: '设计生产能力',
      unit: '万吨/年',
      name: 'design_output'
    },
    {
      label: '核定生产能力',
      unit: '万吨/年',
      name: 'proved_output'
    },
    {
      label: '开拓方式',
      name: 'minestyle'
    },
    {
      label: '运输方式',
      name: 'transmitstyle'
    },
    {
      label: '供电方式',
      name: 'owerstyle'
    },
    {
      label: '通风方式',
      name: 'ventilatestyle'
    },
    {
      label: '水文地质类型',
      name: 'hydrogeological'
    },
    {
      label: '煤层自然倾向性',
      name: 'mine_fire'
    },
    {
      label: '煤尘爆炸性',
      name: 'grime_explosive'
    },
    {
      label: '平均涌水量',
      unit: 'm3/h',
      name: 'mine_waterburst'
    },
    {
      label: '最大涌水量',
      unit: 'm3/h',
      name: 'mine_waterburst_max'
    },
    {
      label: '开采工作面数量',
      unit: '个',
      name: 'workfacenum'
    },
    {
      label: '开采掘进面数量',
      unit: '个',
      name: 'minefacenum'
    },

    {
      label: '是否有冲击地压',
      name: 'rockburst'
    },
    {
      label: '瓦斯等级',
      name: 'ws_grade'
    },
    // {
    //   label: "创建时间",
    //   name: "createdate"
    // },
    {
      label: '职工人数',
      unit: '人',
      name: 'worernum'
    },
    // {
    //   label: "上级企业编码",
    //   name: "parentname"
    // },
    // {
    //   label: "所属监管机构编码",
    //   name: "deptclass"
    // },
    // {
    //   label: "标准化级别",
    //   name: "standardclass"
    // },
    // {
    //   label: "统一社会信用代码",
    //   name: "creditcode"
    // },
    // {
    //   label: "政府监管部门",
    //   name: "corp_type"
    // },
    // {
    //   label: "矿井井型",
    //   name: "mine_class"
    // },
    // {
    //   label: "允许开采上下标高",
    //   unit: "米",
    //   name: "a_yxkcsxxbg"
    // },
    // {
    //   label: "建设时间",
    //   name: "a_jssj1"
    // },
    // {
    //   label: "开采类型",
    //   name: "mine_minetype"
    // },
    // {
    //   label: "主要灾害类型",
    //   name: "mine_geohazardtype"
    // },
    // {
    //   label: "矿井地质类型",
    //   name: "a_kjdzlx"
    // },
    // {
    //   label: "煤层自然倾向性说明",
    //   name: "a_mine_firedescrip"
    // },
    // {
    //   label: "煤层爆炸性说明",
    //   name: "a_grime_explosivedescrip"
    // },
    // {
    //   label: "设计服务年限",
    //   name: "service_years"
    // },
    {
      name: 'ssqymc',
      label: '煤矿所属企业'
    },
    {
      name: 'mccc',
      label: '煤层赋存'
    },
    {
      name: 'mzjmz',
      label: '煤种及煤质'
    },
    {
      name: 'hssfyqcxq',
      label: '洪水设防要求（重现期）',
      unit: '年'
    },
    {
      name: 'hssfxzcxq',
      label: '洪水设防现状（重现期）',
      unit: '年'
    },
    {
      name: 'gdzcjz',
      label: '固定资产净值',
      unit: '万元'
    },
    {
      name: 'dbgl',
      label: '顶板管理'
    },
    {
      name: 'yjbntssl',
      label: '永久避难硐室数量',
      unit: '间'
    },
    {
      name: 'glryrs',
      label: '管理人员人数',
      unit: '人'
    },
    {
      name: 'zyjsryrs',
      label: '专业技术人员人数',
      unit: '人'
    },
    {
      name: 'anglryrs',
      label: '安全管理人员人数',
      unit: '人'
    },
    {
      name: 'dbzdzgrs',
      label: '单班最大在岗人数',
      unit: '人'
    },
    {
      name: 'ylryrs',
      label: '医疗人员人数',
      unit: '人'
    },
    {
      name: 'zgcs',
      label: '总工程师'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 文物保护单位
  Culturalrelicunit: [
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '单位类型',

      name: 'relictypecode'
    },
    // {
    //   label: "行政区划",

    //   name: "districtcode"
    // },
    {
      label: '联系人',

      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      label: '占地面积',

      name: 'area'
    },
    {
      name: 'maxpersonnum',
      label: '游人容量',
      unit: '人'
    },
    {
      label: '文物时代',

      name: 'relicage'
    },
    {
      label: '主要文物介绍',

      name: 'relicdescrip'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 新闻广播机构
  Newscast2: [
    {
      label: '地址',
      name: 'address'
    },
    {
      name: 'defobjtypecode',
      label: '类型'
    },
    // {
    //   label: "行政区划",

    //   name: "districtcode"
    // },
    {
      label: '联系人',

      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'mainequip',
      label: '主要设备'
    },
    {
      name: 'coverarea',
      label: '覆盖范围'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 科研机构
  Resins: [
    {
      label: '地址',
      name: 'address'
    },
    {
      name: 'defobjtypecode',
      label: '类型'
    },
    // {
    //   label: "行政区划",

    //   name: "districtcode"
    // },
    {
      label: '联系人',

      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'supportdept',
      label: '依托单位'
    },
    {
      name: 'supsubject',
      label: '所属学科'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 隧道
  Tunnel: [
    // {
    //   name: "tunnelid", label: "隧道编号"
    // },
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'tunnellength',
      label: '隧道长度',
      unit: '米'
    },
    {
      name: 'tunnelwidth',
      label: '隧道宽度',
      unit: '米'
    },
    {
      name: 'tunnelheight',
      label: '隧道净高',
      unit: '米'
    },
    {
      name: 'lanedesc',
      label: '车道情况'
    },
    // {
    //   name: "aseisinten", label: "抗震设防烈度"
    // },
    {
      name: 'road',
      label: '所在公路（铁路）'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',

      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 桥梁
  Bridge: [
    // {
    //   label: "名称",

    //   name: "name"
    // },
    {
      label: '地址',

      name: 'address'
    },
    {
      name: 'bridgelength',
      label: '跨径总长',
      unit: '米'
    },
    {
      name: 'bridgewidth',
      label: '桥梁宽度',
      unit: '米'
    },
    {
      name: 'limitload',
      label: '限载重量',
      unit: '吨'
    },
    {
      name: 'lanedesc',
      label: '车道情况'
    },
    {
      name: 'road',
      label: '所在公路（铁路）'
    },
    {
      label: '负责人',

      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',

      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 加油加气站
  Gasstation: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "districtcode",
    //   label: "行政区划"
    // },
    {
      name: 'respper',
      label: '负责人'
    },
    {
      name: 'respmtel',
      type: 'Tel',
      label: '负责人电话'
    },
    {
      name: 'sfwyhgyq',
      label: '是否位于化工园区'
    },
    {
      name: 'djhf',
      label: '等级划分'
    },
    {
      name: 'aqscbzhdj',
      label: '安全生产标准化等级'
    },
    {
      name: 'cglx',
      label: '储罐类型'
    },
    {
      name: 'oiltanknum',
      unit: '个',
      label: '油罐数量'
    },
    {
      name: 'storagevol',
      unit: '立方米',
      label: '储存总容量'
    },
    {
      name: 'scalecode',
      label: '加油站规模'
    },
    {
      name: 'oils',
      label: '经营油气种类'
    },
    {
      name: 'inuserdate',
      label: '投入使用时间'
    },
    {
      name: 'facilities',
      label: '应急设施情况'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 非煤矿山
  metalnonmetal: [
    {
      label: '地址',
      name: 'ksdzmc'
    },
    // {
    //   label: "行政区划",
    //   name: "county"
    // },
    // {
    //   label: "所采矿种",
    //   name: "sckz"
    // },
    // {
    //   label: "设计生产规模",
    //   unit: "万吨/年",
    //   name: "desproscale"
    // },
    // {
    //   label: "现有尾矿库",
    //   name: "nowtailpond"
    // },
    // {
    //   label: "主管企业性质",
    //   name: "chargenature"
    // },
    {
      label: '主要负责人',
      name: 'ksfzr'
    },
    {
      label: '主要负责人电话',
      type: 'Tel',
      name: 'ksfzryddh'
    },
    {
      label: '安全负责人',
      name: 'aqfzr'
    },
    {
      label: '安全负责人电话',
      type: 'Tel',
      name: 'aqfzryddh'
    },
    {
      label: '矿区面积',
      unit: '平方米',
      name: 'kqmj'
    },
    {
      label: '设计服务年限',
      name: 'sjfwnx'
    },
    {
      label: '特种作业人数',
      unit: '人',
      name: 'tzzyrysl'
    },
    {
      label: '普通员工总人数',
      unit: '人',
      name: 'ptygzsl'
    },
    {
      label: '所属行业',
      name: 'sshy'
    },
    {
      label: '采矿许可证编号',
      name: 'ckxkz'
    },
    {
      label: '采矿许可证发证机关',
      name: 'ckxkzfzjg'
    },
    {
      label: '采矿许可证启始日期',
      name: 'ckxkzqsrq'
    },
    {
      label: '采矿许可证截止日期',
      name: 'ckxkzjzrq'
    },
    {
      label: '安全许可证编号',
      name: 'aqscxkz'
    },
    {
      label: '安全生产许可证发证机关',
      name: 'aqscxkzfzjg'
    },
    {
      label: '安全生产许可证启始日期',
      name: 'aqscxkzqsrq'
    },
    {
      label: '安全生产许可证截止日期',
      name: 'aqscxkzjzrq'
    },
    {
      label: '设计生产矿石量',
      unit: '万吨/年',
      name: 'sjscksll'
    },
    {
      label: '设计生产金属量',
      unit: '万吨/年',
      name: 'sjscjsl'
    },
    {
      label: '安全出口数量',
      unit: '个',
      name: 'floorexitnum'
    },
    {
      label: '井下单班最多作业人数',
      unit: '人',
      name: 'maxpernum'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 露天矿山
  ALL_META01: [
    {
      label: '地址',
      name: 'ksdzmc'
    },
    {
      name: 'kcfs',
      label: '开采方式'
    },
    // {
    //   label: "行政区划",
    //   name: "county"
    // },
    {
      label: '所采矿种',
      name: 'kczykzmc'
    },
    {
      name: 'scgmmc',
      label: '生产规模名称'
    },
    {
      label: '主要负责人',
      name: 'ksfzr'
    },
    {
      label: '联系方式',
      type: 'Tel',
      name: 'ksfzryddh'
    },
    {
      label: '安全负责人',
      name: 'aqfzr'
    },
    {
      label: '联系方式',
      type: 'Tel',
      name: 'aqfzryddh'
    },
    {
      label: '矿区面积',
      unit: '平方千米',
      name: 'kqmj'
    },
    {
      label: '设计服务年限',
      name: 'sjfwnx',
      unit: '年'
    },
    {
      label: '特种作业人数',
      name: 'tzzyrysl',
      unit: '人'
    },
    {
      label: '普通员工总人数',
      name: 'ptygzsl',
      unit: '人'
    },
    {
      label: '采矿许可证编号',
      name: 'ckxkz'
    },
    {
      label: '采矿许可证发证机关',
      name: 'ckxkzfzjg'
    },
    {
      label: '采矿许可证启始日期',
      name: 'ckxkzqsrq'
    },
    {
      label: '采矿许可证截止日期',
      name: 'ckxkzjzrq'
    },
    {
      label: '安全许可证编号',
      name: 'aqscxkz'
    },
    {
      label: '安全生产许可证发证机关',
      name: 'aqscxkzfzjg'
    },
    {
      label: '安全生产许可证启始日期',
      name: 'aqscxkzqsrq'
    },
    {
      label: '安全生产许可证截止日期',
      name: 'aqscxkzjzrq'
    },
    // {
    //   label: "设计生产矿石量",
    //   unit: "吨/年",
    //   name: "sjscksll"
    // },
    // {
    //   label: "设计生产金属量",
    //   unit: "万吨/年",
    //   name: "sjscjsl"
    // },
    // {
    //   label: "安全出口数量",
    //   unit: "个",
    //   name: "floorexitnum"
    // },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 地下矿山
  ALL_META02: [
    {
      label: '地址',
      name: 'ksdzmc'
    },
    {
      name: 'kcfs',
      label: '开采方式'
    },
    // {
    //   label: "行政区划",
    //   name: "county"
    // },
    {
      label: '所采矿种',
      name: 'kczykzmc'
    },
    {
      name: 'scgmmc',
      label: '生产规模名称'
    },
    {
      label: '主要负责人',
      name: 'ksfzr'
    },
    {
      label: '联系方式',
      type: 'Tel',
      name: 'ksfzryddh'
    },
    {
      label: '安全负责人',
      name: 'aqfzr'
    },
    {
      label: '联系方式',
      type: 'Tel',
      name: 'aqfzryddh'
    },
    {
      label: '矿区面积',
      unit: '平方千米',
      name: 'kqmj'
    },
    {
      label: '设计服务年限',
      name: 'sjfwnx',
      unit: '年'
    },
    {
      label: '特种作业人数',
      name: 'tzzyrysl',
      unit: '人'
    },
    {
      label: '普通员工总人数',
      name: 'ptygzsl',
      unit: '人'
    },
    {
      label: '采矿许可证编号',
      name: 'ckxkz'
    },
    {
      label: '采矿许可证发证机关',
      name: 'ckxkzfzjg'
    },
    {
      label: '采矿许可证启始日期',
      name: 'ckxkzqsrq'
    },
    {
      label: '采矿许可证截止日期',
      name: 'ckxkzjzrq'
    },
    {
      label: '安全许可证编号',
      name: 'aqscxkz'
    },
    {
      label: '安全生产许可证发证机关',
      name: 'aqscxkzfzjg'
    },
    {
      label: '安全生产许可证启始日期',
      name: 'aqscxkzqsrq'
    },
    {
      label: '安全生产许可证截止日期',
      name: 'aqscxkzjzrq'
    },
    // {
    //   label: "设计生产矿石量",
    //   unit: "吨/年",
    //   name: "sjscksll"
    // },
    // {
    //   label: "设计生产金属量",
    //   unit: "万吨/年",
    //   name: "sjscjsl"
    // },
    // {
    //   label: "安全出口数量",
    //   unit: "个",
    //   name: "floorexitnum"
    // },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  Reservoir: [
    {
      label: '地址',
      name: 'address'
    },
    {
      label: '所在河流',
      name: 'river'
    },
    {
      label: '水库等级',
      name: 'levelcode'
    },
    {
      name: 'damtype',
      label: '坝型'
    },
    {
      name: 'damelevation',
      unit: '米',
      label: '坝顶高程'
    },
    {
      name: 'maxdamheight',
      unit: '米',
      label: '最大坝高'
    },
    {
      name: 'damwidth',
      unit: '米',
      label: '坝顶宽度'
    },
    {
      name: 'damlength',
      unit: '米',
      label: '坝顶长度'
    },
    // {
    //   name: "weirwidth",
    //   unit: "米",
    //   label: "溢洪道堰顶宽"
    // },
    // {
    //   name: "weielevation",
    //   unit: "米",
    //   label: "溢洪道堰顶高程"
    // },
    // {
    //   name: "maxdischarge",
    //   unit: "米",
    //   label: "最大泄流量"
    // },

    {
      label: '库区面积',
      unit: '平方千米',
      name: 'area'
    },
    {
      label: '集雨面积',
      unit: '平方千米',
      name: 'rainarea'
    },
    {
      label: '正常水位',
      unit: '米',
      name: 'norwaterlevel'
    },
    {
      label: '设计水位',
      unit: '米',
      name: 'deswaterlevel'
    },
    {
      label: '校核水位',
      unit: '米',
      name: 'chkwaterlevel'
    },
    {
      label: '防洪限制水位',
      unit: '米',
      name: 'fldctrlwaterlevel'
    },
    {
      label: '死水位',
      unit: '米',
      name: 'deadwaterlevel'
    },
    {
      label: '历史最高水位',
      unit: '米',
      name: 'maxwaterlevel'
    },
    {
      label: '历史最高水位时间',
      name: 'maxwaterleveldate'
    },
    {
      label: '总库容',
      unit: '万立方米',
      name: 'totalstopacity'
    },
    {
      label: '正常库容',
      unit: '万立方米',
      name: 'normalstopacity'
    },
    {
      label: '设计库容',
      unit: '万立方米',
      name: 'designsstopacity'
    },
    {
      label: '防洪库容',
      unit: '万立方米',
      name: 'floodstopacity'
    },
    {
      label: '防限库容',
      unit: '万立方米',
      name: 'limitstopacity'
    },
    {
      label: '死库容',
      unit: '万立方米',
      name: 'deadstopacity'
    },
    {
      label: '调节库容',
      unit: '万立方米',
      name: 'regulatstopacity'
    },
    {
      label: '灌溉库容',
      unit: '万立方米',
      name: 'irrigstopacity'
    },
    {
      label: '灌溉面积',
      unit: '平方千米',
      name: 'irrigarea'
    },
    {
      label: '受影响人员数',
      unit: '人',
      name: 'affectednum'
    },
    {
      name: 'adminrespper',
      label: '行政责任人'
    },
    {
      name: 'adminrespperjob',
      label: '行政责任人职务'
    },
    {
      name: 'adminresppertel',
      type: 'Tel',
      label: '行政责任人联系电话'
    },
    {
      name: 'techrespper',
      label: '技术责任人'
    },
    {
      name: 'techrespperjob',
      label: '技术责任人职务'
    },
    {
      name: 'techresppertel',
      type: 'Tel',
      label: '技术责任人联系电话'
    },
    {
      name: 'patrolrespper',
      label: '巡查责任人'
    },
    {
      name: 'patrolrespperjob',
      label: '巡查责任人职务'
    },
    {
      name: 'patrolresppertel',
      type: 'Tel',
      label: '巡查责任人联系电话'
    },
    {
      label: '主管单位',
      name: 'chargedept'
    },
    {
      label: '主管单位电话',
      type: 'Tel',
      name: 'chargedepttel'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  Shelter: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   name: "districtcode",
    //   label: "行政区划"
    // },
    {
      name: 'sheltertypecode',
      label: '避难场所类型'
    },
    {
      name: 'levelcode',
      label: '级别'
    },
    {
      label: '可容纳人数',
      name: 'maxpersonnum',
      unit: '人'
    },
    {
      name: 'abacusarea',
      unit: '平方米',
      label: '篷宿区面积'
    },
    {
      label: '值班电话',
      type: 'Tel',
      name: 'dutytel'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'cdepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'aseisinten',
      label: '抗震设防烈度'
    },
    {
      name: 'opentime',
      label: '开放时间'
    },
    {
      name: 'inusedate',
      label: '投入使用年份'
    },
    {
      name: 'useyearnum',
      label: '设计使用年限'
    },
    {
      name: 'facilitystate',
      label: '配套设施情况'
    },
    {
      name: 'materialstate',
      label: '应急物资储备情况'
    },
    {
      name: 'commtype',
      label: '应急通信方式'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: '更新时间'
    }
  ],
  // 物资
  materialItem: [
    {
      label: '储备库类型',
      name: 'REPERTORYTYPENAME'
    },
    {
      label: '行政区划：',
      name: 'districtcode'
    },
    {
      label: '级别：',
      name: 'LEVELNAME'
    },
    {
      label: '地址：',
      name: 'address'
    },
    {
      label: '负责人：',
      name: 'concateper'
    },
    {
      label: '值班电话：',
      type: 'Tel',
      name: 'dutytel'
    },
    {
      label: '管理机构：',
      name: 'CHARGEDEPT'
    },
    {
      label: '数据来源：',
      type: 'Tel',
      name: 'sourcedept'
    }
  ],
  RescueCompany: [
    {
      label: '地址',
      name: 'address'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      label: '值班电话',
      type: 'Tel',
      name: 'dutytel'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'firmproperty',
      label: '企业性质'
    },
    {
      name: 'inusedate',
      label: '投产日期'
    },
    {
      name: 'material',
      label: '生产物资'
    },
    {
      name: 'procap',
      label: '生产能力'
    },
    {
      name: 'matinventory',
      label: '原料库存'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  TransportCompany: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'dutytel',
      type: 'Tel',
      label: '值班电话'
    },
    {
      name: 'superiordept',
      label: '主管单位'
    },
    {
      name: 'superiortel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      name: 'industry',
      label: '所属行业'
    },
    {
      name: 'businscope',
      label: '经营范围'
    },
    {
      label: '工作人员数',
      unit: '人',
      name: 'empnum'
    },
    {
      name: 'passthruput',
      unit: '万人次/年',
      label: '旅客吞吐量'
    },
    {
      name: 'cargothruput',
      unit: '吨',
      label: '货邮吞吐量'
    },
    {
      name: 'roomarea',
      unit: '平方米',
      label: '候车(机、船)厅面积'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  CommunicationCompany: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'dutytel',
      type: 'Tel',
      label: '值班电话'
    },
    {
      name: 'superiordept',
      label: '主管单位'
    },
    {
      name: 'superiortel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      label: '总人数',
      unit: '人',
      name: 'personnum'
    },
    {
      name: 'vehiclenum',
      unit: '辆',
      label: '应急通信车数'
    },
    {
      name: 'specialnum',
      unit: '人',
      label: '专业技能人数'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 烟花爆竹企业
  fireworkent: [
    {
      label: '地址',
      name: 'address'
    },
    // {
    //   name: "county",
    //   label: "行政区划"
    // },
    // {
    //   name: "orgno",
    //   label: "组织机构代码"
    // },
    // {
    //   name: "filingorgan",
    //   label: "备案机关"
    // },
    {
      name: 'depttypecode',
      label: '企业类型'
    },
    {
      name: 'zyfzr',
      label: '负责人'
    },
    {
      name: 'zyfzrlxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'fgaqfzr',
      label: '安全负责人'
    },
    {
      name: 'fgaqfzrlxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'zgrs',
      label: '职工人数',
      unit: '人'
    },
    {
      name: 'tzzyryrs',
      label: '特种作业人员',
      unit: '人'
    },
    {
      name: 'jjlxmc',
      label: '经济类型'
    },
    {
      name: 'aqscbzhdjmc',
      label: '安全生产标准化等级'
    },
    {
      name: 'aqscbzhfsbh',
      label: '安全生产标准化证书编号'
    },
    {
      name: 'xkzbh',
      label: '许可证编号'
    },
    {
      name: 'qyscbh',
      label: '企业场所编号'
    },
    {
      name: 'xkzyxqzi',
      label: '许可证有效期开始日期'
    },
    {
      name: 'xkzyxqzhi',
      label: '许可证有效结束日期'
    },
    {
      name: 'xzfw',
      label: '许可范围'
    },
    {
      name: 'sjcn',
      label: '设计产能'
    },
    // {
    //   name: "artificialper",
    //   label: "法定代表人"
    // },
    // {
    //   name: "tel",
    //   label: "电话号码"
    // },
    // {
    //   name: "busilicnum",
    //   label: "营业执照号码"
    // },
    // {
    //   name: "busilicvaliddate",
    //   label: "营业执照有效期"
    // },
    // {
    //   name: "registfunds",
    //   label: "注册资金"
    // },
    // {
    //   name: "workernum",
    //   unit: "人",
    //   label: "作业人数"
    // },
    // {
    //   name: "qualityinfo",
    //   label: "产品质量检验信息"
    // },
    // {
    //   name: "busscope",
    //   label: "经营范围"
    // },
    // {
    //   name: "waraddress",
    //   label: "仓库地址"
    // },
    // {
    //   name: "waraddress",
    //   label: "仓库地址"
    // },
    // {
    //   name: "warnum",
    //   unit: "个",
    //   label: "仓库个数"
    // },
    // {
    //   name: "wararea",
    //   unit: "平方米",
    //   label: "仓库面积"
    // },
    // {
    //   name: "designstock",
    //   unit: "吨",
    //   label: "设计存量"
    // },
    // {
    //   name: "peakstock",
    //   unit: "吨",
    //   label: "旺季存量"
    // },
    // {
    //   name: "showloc",
    //   label: "展厅位置"
    // },
    // {
    //   name: "trancar",
    //   label: "运输车辆"
    // },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  portwharf: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "districtcode",
    //   label: "行政区划"
    // },
    {
      label: '值班电话',
      type: 'Tel',
      name: 'dutytel'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      label: '联系人',
      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactmtel'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'cdepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      name: 'personnum',
      label: '人数'
    },
    {
      name: 'traffic',
      label: '周边交通状况'
    },
    {
      name: 'inusedate',
      label: '投入使用时间'
    },
    {
      name: 'useyearnum',
      label: '设计使用年限'
    },
    {
      name: 'defencearea',
      label: '防护区域'
    },
    {
      name: 'maxpersonnum',
      unit: '人',
      label: '可容纳人数'
    },
    {
      name: 'monitmode',
      label: '监测方式'
    },
    {
      name: 'defencestep',
      label: '防护措施'
    },
    {
      name: 'commtype',
      label: '应急通信方式'
    },
    {
      name: 'passengervolume',
      unit: '人',
      label: '客运量'
    },
    {
      name: 'cargothroughput',
      unit: '万吨',
      label: '货物吞吐量'
    },
    {
      name: 'grossarea',
      unit: '万平方米',
      label: '总面积'
    },
    {
      name: 'berthnum',
      unit: '个',
      label: '泊位数量'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 地灾隐患点公用
  geologichaz: [
    {
      name: 'id',
      label: '隐患点编号'
    },
    {
      name: 'pac',
      label: '行政区划'
    },
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'gmmc',
      label: '规模'
    },
    {
      name: 'glcjmc',
      label: '管理层级'
    },
    {
      name: 'xqdjmc',
      label: '险情等级'
    },
    {
      name: 'wgzrrxm',
      label: '网格责任人'
    },
    {
      name: 'wgzrrdh',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'wgglyxm',
      label: '网格管理员'
    },
    {
      name: 'wgglydh',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'wgxgyxm',
      label: '网格协管员'
    },
    {
      name: 'wgxgydh',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'wgzgyxm',
      label: '网格专管员'
    },
    {
      name: 'wgzgydh',
      label: '联系方式',
      type: 'Tel'
    },
    // {
    //   name: "jcrxm", label: "监测人"
    // },
    // {
    //   name: "jcrdh", label: "联系方式", type: "Tel"
    // },
    {
      name: 'area',
      label: '总面积',
      unit: '万平方米'
    },
    // {
    //   name: "cd", label: "长度", unit: "米"
    // },
    // {
    //   name: "kd", label: "宽度", unit: "米"
    // },
    // {
    //   name: "gd", label: "高度", unit: "米"
    // },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    // {
    //   name: "throbjects", label: "威胁对象"
    // },
    {
      name: 'thrfamilies',
      label: '威胁户数',
      unit: '户'
    },
    {
      name: 'thrproperty',
      label: '威胁财产',
      unit: '万元'
    },
    {
      name: 'risknum',
      label: '威胁人数',
      unit: '人'
    },
    {
      name: 'longitude',
      label: '经度'
    },
    {
      name: 'latitude',
      label: '纬度'
    },
    // {
    //   name: "protectdept",
    //   label: "防治单位"
    // },
    // {
    //   name: "protectdepttel",
    //   type: "Tel",
    //   label: "防治单位电话"
    // },
    // {
    //   name: "chargedept",
    //   label: "主管单位"
    // },
    // {
    //   name: "chargedepttel",
    //   type: "Tel",
    //   label: "主管单位电话"
    // },
    {
      name: 'bxtzjhdls',
      label: '变形特征及活动历时'
    },
    {
      name: 'wdxfx',
      label: '稳定性分析'
    },
    {
      name: 'wdxxzmc',
      label: '稳定性现状'
    },
    {
      name: 'wdxqsmc',
      label: '稳定性趋势'
    },
    {
      name: 'qzwh',
      label: '潜在危害'
    },
    {
      name: 'lzztyc',
      label: '临灾状态预测'
    },
    {
      name: 'jcff',
      label: '监测方法'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 崩塌地灾隐患点
  mountaincollapse: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'protectdept',
      label: '防治单位'
    },
    {
      name: 'protectdepttel',
      type: 'Tel',
      label: '防治单位电话'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'chargedepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'area',
      unit: '平方米',
      label: '总面积'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'effectlevelcode',
      label: '危害程度'
    },
    {
      name: 'disasterlevelcode',
      label: '灾情分级'
    },
    {
      name: 'disasterscale',
      label: '灾害规模'
    },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    {
      name: 'trend',
      label: '发展趋势'
    },
    {
      name: 'soilchar',
      label: '土质特征'
    },
    {
      name: 'begintime',
      label: '风险初现时间'
    },
    {
      name: 'throbjects',
      unit: '户',
      label: '威胁对象'
    },
    {
      name: 'thrfamilies',
      label: '威胁户数'
    },
    {
      name: 'thrproperty',
      unit: '元',
      label: '威胁财产'
    },
    {
      name: 'risknum',
      unit: '人',
      label: '威胁人数'
    },
    {
      name: 'injuriesdeaths',
      label: '历史伤亡情况'
    },
    {
      name: 'propertyloss',
      label: '历史财产损失'
    },
    {
      name: 'protection',
      label: '治理防护措施'
    },
    {
      name: 'isreplan',
      label: '是否建立防灾预案(0否1是)'
    },
    {
      name: 'isgroupsystem',
      label: '是否建立群测群防体系(0否1是)'
    },
    {
      name: 'criticalvalue',
      unit: '毫米',
      label: '临界降雨量'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 滑坡地灾隐患点
  landslide: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'protectdept',
      label: '防治单位'
    },
    {
      name: 'protectdepttel',
      type: 'Tel',
      label: '防治单位电话'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'chargedepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'area',
      unit: '平方米',
      label: '总面积'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'effectlevelcode',
      label: '危害程度'
    },
    {
      name: 'disasterlevelcode',
      label: '灾情分级'
    },
    {
      name: 'disasterscale',
      label: '灾害规模'
    },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    {
      name: 'trend',
      label: '发展趋势'
    },
    {
      name: 'soilchar',
      label: '土质特征'
    },
    {
      name: 'begintime',
      label: '风险初现时间'
    },
    {
      name: 'throbjects',
      unit: '户',
      label: '威胁对象'
    },
    {
      name: 'thrfamilies',
      label: '威胁户数'
    },
    {
      name: 'thrproperty',
      unit: '元',
      label: '威胁财产'
    },
    {
      name: 'risknum',
      unit: '人',
      label: '威胁人数'
    },
    {
      name: 'injuriesdeaths',
      label: '历史伤亡情况'
    },
    {
      name: 'propertyloss',
      label: '历史财产损失'
    },
    {
      name: 'protection',
      label: '治理防护措施'
    },
    {
      name: 'isreplan',
      label: '是否建立防灾预案(0否1是)'
    },
    {
      name: 'isgroupsystem',
      label: '是否建立群测群防体系(0否1是)'
    },
    {
      name: 'criticalvalue',
      unit: '毫米',
      label: '临界降雨量'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 泥石流地灾隐患点
  debrisflow: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'protectdept',
      label: '防治单位'
    },
    {
      name: 'protectdepttel',
      type: 'Tel',
      label: '防治单位电话'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'chargedepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'area',
      unit: '平方米',
      label: '总面积'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'effectlevelcode',
      label: '危害程度'
    },
    {
      name: 'disasterlevelcode',
      label: '灾情分级'
    },
    {
      name: 'disasterscale',
      label: '灾害规模'
    },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    {
      name: 'trend',
      label: '发展趋势'
    },
    {
      name: 'soilchar',
      label: '土质特征'
    },
    {
      name: 'begintime',
      label: '风险初现时间'
    },
    {
      name: 'throbjects',
      unit: '户',
      label: '威胁对象'
    },
    {
      name: 'thrfamilies',
      label: '威胁户数'
    },
    {
      name: 'thrproperty',
      unit: '元',
      label: '威胁财产'
    },
    {
      name: 'risknum',
      unit: '人',
      label: '威胁人数'
    },
    {
      name: 'injuriesdeaths',
      label: '历史伤亡情况'
    },
    {
      name: 'propertyloss',
      label: '历史财产损失'
    },
    {
      name: 'protection',
      label: '治理防护措施'
    },
    {
      name: 'isreplan',
      label: '是否建立防灾预案(0否1是)'
    },
    {
      name: 'isgroupsystem',
      label: '是否建立群测群防体系(0否1是)'
    },
    {
      name: 'criticalvalue',
      unit: '毫米',
      label: '临界降雨量'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 地面塌陷地灾隐患点
  bottomcollapse: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'protectdept',
      label: '防治单位'
    },
    {
      name: 'protectdepttel',
      type: 'Tel',
      label: '防治单位电话'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'chargedepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'area',
      unit: '平方米',
      label: '总面积'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'effectlevelcode',
      label: '危害程度'
    },
    {
      name: 'disasterlevelcode',
      label: '灾情分级'
    },
    {
      name: 'disasterscale',
      label: '灾害规模'
    },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    {
      name: 'trend',
      label: '发展趋势'
    },
    {
      name: 'soilchar',
      label: '土质特征'
    },
    {
      name: 'begintime',
      label: '风险初现时间'
    },
    {
      name: 'throbjects',
      unit: '户',
      label: '威胁对象'
    },
    {
      name: 'thrfamilies',
      label: '威胁户数'
    },
    {
      name: 'thrproperty',
      unit: '元',
      label: '威胁财产'
    },
    {
      name: 'risknum',
      unit: '人',
      label: '威胁人数'
    },
    {
      name: 'injuriesdeaths',
      label: '历史伤亡情况'
    },
    {
      name: 'propertyloss',
      label: '历史财产损失'
    },
    {
      name: 'protection',
      label: '治理防护措施'
    },
    {
      name: 'isreplan',
      label: '是否建立防灾预案(0否1是)'
    },
    {
      name: 'isgroupsystem',
      label: '是否建立群测群防体系(0否1是)'
    },
    {
      name: 'criticalvalue',
      unit: '毫米',
      label: '临界降雨量'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 采空塌陷地灾隐患点
  emptysubside: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'protectdept',
      label: '防治单位'
    },
    {
      name: 'protectdepttel',
      type: 'Tel',
      label: '防治单位电话'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'chargedepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'area',
      unit: '平方米',
      label: '总面积'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'effectlevelcode',
      label: '危害程度'
    },
    {
      name: 'disasterlevelcode',
      label: '灾情分级'
    },
    {
      name: 'disasterscale',
      label: '灾害规模'
    },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    {
      name: 'trend',
      label: '发展趋势'
    },
    {
      name: 'soilchar',
      label: '土质特征'
    },
    {
      name: 'begintime',
      label: '风险初现时间'
    },
    {
      name: 'throbjects',
      unit: '户',
      label: '威胁对象'
    },
    {
      name: 'thrfamilies',
      label: '威胁户数'
    },
    {
      name: 'thrproperty',
      unit: '元',
      label: '威胁财产'
    },
    {
      name: 'risknum',
      unit: '人',
      label: '威胁人数'
    },
    {
      name: 'injuriesdeaths',
      label: '历史伤亡情况'
    },
    {
      name: 'propertyloss',
      label: '历史财产损失'
    },
    {
      name: 'protection',
      label: '治理防护措施'
    },
    {
      name: 'isreplan',
      label: '是否建立防灾预案(0否1是)'
    },
    {
      name: 'isgroupsystem',
      label: '是否建立群测群防体系(0否1是)'
    },
    {
      name: 'criticalvalue',
      unit: '毫米',
      label: '临界降雨量'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 不稳定斜坡地灾隐患点
  unstableslopes: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'protectdept',
      label: '防治单位'
    },
    {
      name: 'protectdepttel',
      type: 'Tel',
      label: '防治单位电话'
    },
    {
      name: 'chargedept',
      label: '主管单位'
    },
    {
      name: 'chargedepttel',
      type: 'Tel',
      label: '主管单位电话'
    },
    {
      name: 'area',
      unit: '平方米',
      label: '总面积'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'effectlevelcode',
      label: '危害程度'
    },
    {
      name: 'disasterlevelcode',
      label: '灾情分级'
    },
    {
      name: 'disasterscale',
      label: '灾害规模'
    },
    {
      name: 'inducingfactors',
      label: '灾害诱发因素'
    },
    {
      name: 'trend',
      label: '发展趋势'
    },
    {
      name: 'soilchar',
      label: '土质特征'
    },
    {
      name: 'begintime',
      label: '风险初现时间'
    },
    {
      name: 'throbjects',
      unit: '户',
      label: '威胁对象'
    },
    {
      name: 'thrfamilies',
      label: '威胁户数'
    },
    {
      name: 'thrproperty',
      unit: '元',
      label: '威胁财产'
    },
    {
      name: 'risknum',
      unit: '人',
      label: '威胁人数'
    },
    {
      name: 'injuriesdeaths',
      label: '历史伤亡情况'
    },
    {
      name: 'propertyloss',
      label: '历史财产损失'
    },
    {
      name: 'protection',
      label: '治理防护措施'
    },
    {
      name: 'isreplan',
      label: '是否建立防灾预案(0否1是)'
    },
    {
      name: 'isgroupsystem',
      label: '是否建立群测群防体系(0否1是)'
    },
    {
      name: 'criticalvalue',
      unit: '毫米',
      label: '临界降雨量'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 重大危险源
  majorDanger: [
    {
      name: 'firmname',
      label: '企业名称'
    },
    {
      name: 'firmid',
      label: '企业编码'
    },
    {
      name: 'hazardlevelcode',
      label: '危险等级'
    },
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'dangerid',
      label: '危险源编号'
    },
    {
      name: 'dagrvalue',
      label: '危险源R值'
    },
    {
      name: 'chargeper',
      label: '负责人'
    },
    {
      name: 'chargepertel',
      label: '负责人电话',
      type: 'Tel'
    },
    {
      name: 'contactper',
      label: '联系人'
    },
    {
      name: 'contactmtel',
      label: '联系人移动电话',
      type: 'Tel'
    },
    {
      name: 'dutytel',
      label: '值班电话',
      type: 'Tel'
    },
    {
      name: 'dsispark',
      label: '是否位于化工园区'
    },
    {
      name: 'dsparkname',
      label: '工业园区名称'
    },
    {
      name: 'ishavesafesys',
      label: '是否装备安全仪表系统'
    },
    {
      name: 'ishavesafesys',
      label: '是否装备安全仪表系统'
    },
    {
      name: 'isproductqquip',
      label: '是否属于生产装备'
    },
    {
      name: 'isincity',
      label: '是否在城区内'
    },
    {
      name: 'isemergencystopsys',
      label: '是否装备紧急停车系统'
    },
    {
      name: 'istoxicthing',
      label: '是否涉及毒性气体液化气体、剧毒液体'
    },
    {
      name: 'dssafedistance',
      unit: '米',
      label: '与周边重点防护目标最近距离'
    },
    {
      name: 'dspersonnum',
      unit: '人',
      label: '厂区边界外500米范围内人数估算'
    },
    {
      name: 'dssafetyincidents',
      label: '近3年内出现的安全事故情况'
    },
    {
      name: 'iscitynetworking',
      label: '是否已于市级平台联网'
    },
    {
      name: 'ispubonrecord',
      label: '是否已经县区主管部门备案'
    },
    {
      name: 'pubonrecorddate',
      label: '备案时间'
    },
    {
      name: 'stoorprd',
      label: '储存场所或生产装置'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // majorDanger: [
  //   {
  //     name: "address",
  //     label: "地址"
  //   },
  //   // {
  //   //   name: "county",
  //   //   label: "行政区划"
  //   // },
  //   {
  //     name: "firmname",
  //     label: "所属企业"
  //   },
  //   {
  //     name: "hazardlevelcode",
  //     label: "危险等级"
  //   },
  //   {
  //     name: "dutytel",
  //     type: "Tel",
  //     label: "值班电话"
  //   },
  //   {
  //     name: "chargeper",
  //     label: "负责人"
  //   },
  //   {
  //     name: "chargepertel",
  //     type: "Tel",
  //     label: "负责人电话"
  //   },
  //   {
  //     name: "contactper",
  //     label: "联系人"
  //   },
  //   {
  //     name: "contactmtel",
  //     type: "Tel",
  //     label: "联系人电话"
  //   },
  //   {
  //     name: "contactmtel",
  //     type: "Tel",
  //     label: "联系人电话"
  //   },
  //   {
  //     name: "competdep",
  //     label: "主管部门"
  //   },
  //   {
  //     name: "funregioncode",
  //     label: "环境功能"
  //   },
  //   {
  //     name: "evaluatedate",
  //     label: "评估日期"
  //   },
  //   {
  //     name: "evaluatedept",
  //     label: "评估单位"
  //   },
  //   {
  //     name: "danmatname",
  //     label: "危险物质名称"
  //   },
  //   {
  //     name: "danmatnum",
  //     label: "危险物质量"
  //   },
  //   {
  //     name: "dangerfacter",
  //     label: "危险危害因素"
  //   },
  //   {
  //     name: "dagpropertycode",
  //     label: "危险源性质"
  //   },
  //   {
  //     name: "dsispark",
  //     label: "是否位于化工园区"
  //   },
  //   {
  //     name: "dsparkname",
  //     label: "工业园区名称"
  //   },
  //   {
  //     name: "ishavesafesys",
  //     label: "是否装备安全仪表系统"
  //   },
  //   {
  //     name: "ishavesafesys",
  //     label: "是否装备安全仪表系统"
  //   },
  //   {
  //     name: "isproductqquip",
  //     label: "是否属于生产装备"
  //   },
  //   {
  //     name: "isincity",
  //     label: "是否在城区内"
  //   },
  //   {
  //     name: "isemergencystopsys",
  //     label: "是否装备紧急停车系统"
  //   },
  //   {
  //     name: "istoxicthing",
  //     label: "是否涉及毒性气体液化气体、剧毒液体"
  //   },
  //   {
  //     name: "dssafedistance",
  //     unit: "米",
  //     label: "与周边重点防护目标最近距离情况"
  //   },
  //   {
  //     name: "dspersonnum",
  //     unit: "人",
  //     label: "厂区边界外500米范围内人数估算值"
  //   },
  //   {
  //     name: "dssafetyincidents",
  //     label: "近3年内出现的安全事故情况"
  //   },
  //   {
  //     name: "iscitynetworking",
  //     label: "是否已于市级平台联网"
  //   },
  //   {
  //     name: "ispubonrecord",
  //     label: "是否已经县区主管部门备案"
  //   },
  //   {
  //     name: "pubonrecorddate",
  //     label: "备案时间"
  //   },
  //   {
  //     name: "stoorprd",
  //     label: "储存场所或生产装置"
  //   },
  //   {
  //     label: "数据来源",
  //     name: "sourcedept"
  //   },
  //   {
  //     label: "更新时间",
  //     name: "updatetime"
  //   }
  // ],
  // 危化企业
  // hazardous: [
  //   {
  //     name: "address",
  //     label: "工商注册地址"
  //   },
  //   // {
  //   //   name: "county",
  //   //   label: "行政区划"
  //   // },
  //   {
  //     name: "qylx",
  //     label: "企业类型"
  //   },
  //   {
  //     name: "bulidtime",
  //     label: "成立日期"
  //   },
  //   {
  //     name: "economytypecode",
  //     label: "经济类型"
  //   },
  //   {
  //     name: "industrycode",
  //     label: "所属行业大类"
  //   },
  //   {
  //     name: "artificialper",
  //     label: "法定代表人"
  //   },
  //   {
  //     name: "entscalecode",
  //     label: "企业规模"
  //   },
  //   {
  //     name: "salesincome",
  //     label: "销售收入"
  //   },
  //   {
  //     name: "safedutytel",
  //     type: "Tel",
  //     label: "安全值班电话"
  //   },
  //   {
  //     name: "principal",
  //     label: "企业负责人"
  //   },
  //   {
  //     name: "principaltel",
  //     type: "Tel",
  //     label: "企业负责人电话"
  //   },
  //   {
  //     name: "safetymager",
  //     label: "安全负责人"
  //   },
  //   {
  //     name: "safetymagertel",
  //     type: "Tel",
  //     label: "安全负责人电话"
  //   },
  //   {
  //     name: "hazardlevelcode",
  //     label: "危险等级"
  //   },
  //   {
  //     name: "empnum",
  //     unit: "人",
  //     label: "职工人数"
  //   },
  //   {
  //     name: "tankfarmnum",
  //     unit: "个",
  //     label: "罐区总数量"
  //   },
  //   {
  //     name: "tankfarmcubage",
  //     unit: "立方米",
  //     label: "罐区总容积"
  //   },
  //   {
  //     name: "keepgrainnum",
  //     unit: "个",
  //     label: "仓储总数量"
  //   },
  //   {
  //     name: "keepgrainarea",
  //     unit: "平方米",
  //     label: "仓储总面积"
  //   },
  //   {
  //     name: "totalstorcap",
  //     unit: "吨",
  //     label: "总存储量"
  //   },
  //   {
  //     name: "sumoutput",
  //     unit: "吨",
  //     label: "总生产量"
  //   },
  //   {
  //     name: "safetyproductionlevelname",
  //     label: "标准化等级"
  //   },
  //   {
  //     name: "mainprotandscalestr",
  //     label: "重点监管化学品工艺"
  //   },
  //   {
  //     name: "zdwxyxx",
  //     label: "重大危险源信息"
  //   },
  //   {
  //     name: "zdjghxp",
  //     label: "重点监管化学品"
  //   },
  //   {
  //     name: "mainprotandscale",
  //     label: "主要产品及生产规模"
  //   },
  //   {
  //     label: "数据来源",
  //     name: "sourcedept"
  //   },
  //   {
  //     label: "更新时间",
  //     name: "updatetime"
  //   }
  // ],
  hazardous: [
    {
      name: 'address',
      label: '企业注册地址'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      name: 'aqscqysccsdz',
      label: '企业实际地址'
    },
    {
      name: 'xzqhmc',
      label: '行政区划'
    },
    {
      name: 'principal',
      label: '企业负责人'
    },
    {
      name: 'principaltel',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'safetymager',
      label: '安全负责人'
    },
    {
      name: 'safetymagertel',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'xzxkzbh',
      label: '安全生产许可证'
    },
    {
      name: 'xzxkyxqxksrq',
      label: '许可证有效期限开始日期'
    },
    {
      name: 'xzxkyxqxjsrq',
      label: '许可证有效期限结束日期'
    },
    {
      name: 'qylx',
      label: '企业类型'
    },
    {
      name: 'empnum',
      label: '从业人数',
      unit: '人'
    }
  ],
  hazardous_bak: [
    {
      name: 'dagchementid',
      label: '企业编码'
    },
    {
      name: 'xzqhmc',
      label: '行政区划'
    },
    {
      name: 'qylx',
      label: '企业类型'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      name: 'principal',
      label: '企业负责人'
    },
    {
      name: 'principaltel',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'safetymager',
      label: '安全负责人'
    },
    {
      name: 'safetymagertel',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'safedutytel',
      label: '安全值班电话',
      type: 'Tel'
    },
    {
      name: 'xzxkzbh',
      label: '安全生产许可证'
    },
    {
      name: 'xzxkyxqxksrq',
      label: '许可证有效期限开始日期'
    },
    {
      name: 'xzxkyxqxjsrq',
      label: '许可证有效期限结束日期'
    },
    {
      name: 'xzfw',
      label: '许可范围'
    },
    {
      name: 'address',
      label: '工商注册地址'
    },
    {
      name: 'aqscqysccsdz',
      label: '生产场所地址'
    },
    {
      name: 'empnum',
      label: '职工人数',
      unit: '人'
    },
    {
      name: 'zdmj',
      label: '占地面积',
      unit: '平方米'
    },
    {
      name: 'bulidtime',
      label: '成立日期'
    },
    {
      name: 'sfwyhgyq',
      label: '是否位于化工园区'
    },
    {
      name: 'hgyqmc',
      label: '化工园区名称'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 化工园区
  Anjian_chemicalpark: [
    // {
    //   name: "name",
    //   label: "园区名称"
    // },
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'districtcode',
      label: '行政区划'
    },
    // {
    //   name: "introduction",
    //   label: "园区简介"
    // },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '联系方式',
      type: 'Tel',
      name: 'respmtel'
    },
    // {
    //   label: "联系人",
    //   name: "contactper"
    // },
    // {
    //   label: "联系人电话",
    //   type: "Tel",
    //   name: "contactmtel"
    // },
    {
      name: 'fgaqzrr',
      label: '安全负责人'
    },
    {
      name: 'fgaqzrrlxdh',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'qysl',
      label: '企业数量',
      unit: '家'
    },
    {
      name: 'hgqysl',
      label: '化工企业数量',
      unit: '家'
    },

    {
      name: 'zdcymc',
      label: '主导产业'
    },
    {
      name: 'aqfxpgfhfs',
      label: '安全风险评估复核分数'
    },
    {
      name: 'aqfxpgfhdjmc',
      label: '安全风险评估复核等级'
    },
    {
      name: 'ghmj',
      label: '规划面积',
      unit: '平方千米'
    },
    {
      name: 'jcmj',
      label: '建成面积',
      unit: '平方千米'
    },
    {
      name: 'zcz',
      label: '总产值',
      unit: '千万元'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 危化企业
  ANJIAN_hazardous_3dtiles: [
    {
      name: 'dagchementid',
      label: '企业编码'
    },
    {
      name: 'xzqhmc',
      label: '行政区划'
    },
    {
      name: 'qylx',
      label: '企业类型'
    },
    {
      name: 'creditcode',
      label: '统一社会信用代码'
    },
    {
      name: 'principal',
      label: '企业负责人'
    },
    {
      name: 'principaltel',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'safetymager',
      label: '安全负责人'
    },
    {
      name: 'safetymagertel',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'safedutytel',
      label: '安全值班电话',
      type: 'Tel'
    },
    {
      name: 'xzxkzbh',
      label: '安全生产许可证'
    },
    {
      name: 'xzxkyxqxksrq',
      label: '许可证有效期限开始日期'
    },
    {
      name: 'xzxkyxqxjsrq',
      label: '许可证有效期限结束日期'
    },
    {
      name: 'xzfw',
      label: '许可范围'
    },
    {
      name: 'address',
      label: '工商注册地址'
    },
    {
      name: 'aqscqysccsdz',
      label: '生产场所地址'
    },
    {
      name: 'empnum',
      label: '职工人数',
      unit: '人'
    },
    {
      name: 'zdmj',
      label: '占地面积',
      unit: '平方米'
    },
    {
      name: 'bulidtime',
      label: '成立日期'
    },
    {
      name: 'sfwyhgyq',
      label: '是否位于化工园区'
    },
    {
      name: 'hgyqmc',
      label: '化工园区名称'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 化工园区
  Anjian_chemicalpark_3dtiles: [
    // {
    //   name: "name",
    //   label: "园区名称"
    // },
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'districtcode',
      label: '行政区划'
    },
    // {
    //   name: "introduction",
    //   label: "园区简介"
    // },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '联系方式',
      type: 'Tel',
      name: 'respmtel'
    },
    // {
    //   label: "联系人",
    //   name: "contactper"
    // },
    // {
    //   label: "联系人电话",
    //   type: "Tel",
    //   name: "contactmtel"
    // },
    {
      name: 'fgaqzrr',
      label: '安全负责人'
    },
    {
      name: 'fgaqzrrlxdh',
      label: '联系方式',
      type: 'Tel'
    },
    {
      name: 'qysl',
      label: '企业数量',
      unit: '家'
    },
    {
      name: 'hgqysl',
      label: '化工企业数量',
      unit: '家'
    },

    {
      name: 'zdcymc',
      label: '主导产业'
    },
    {
      name: 'aqfxpgfhfs',
      label: '安全风险评估复核分数'
    },
    {
      name: 'aqfxpgfhdjmc',
      label: '安全风险评估复核等级'
    },
    {
      name: 'ghmj',
      label: '规划面积',
      unit: '平方千米'
    },
    {
      name: 'jcmj',
      label: '建成面积',
      unit: '平方千米'
    },
    {
      name: 'zcz',
      label: '总产值',
      unit: '千万元'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 宗教活动场所
  zjhdcs: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'nmzgrs',
      label: '年末职工人数',
      unit: '人'
    },
    // {
    //   name: "zyjsjnrys", label: "专业技术技能人员数", unit: "人"
    // },
    // {
    //   name: "jgglrys", label: "机构管理人员数", unit: "人"
    // },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      name: 'yjtxbzfs',
      label: '应急通信保障方式'
    },
    {
      name: 'dwfzr',
      label: '单位负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'jgbm',
      label: '机构编码'
    },
    {
      name: 'jglx',
      label: '机构类型'
    },
    {
      name: 'rcrs',
      label: '宗教日常人数',
      unit: '人'
    },
    {
      name: 'jrrs',
      label: '宗教节日人数',
      unit: '人'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 社会服务机构
  shfwjg: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    // {
    //   name: "dutytel",
    //   type: "Tel",
    //   label: "值班电话"
    // },
    // {
    //   name: "superiordept",
    //   label: "主管单位"
    // },
    // {
    //   name: "superiortel",
    //   type: "Tel",
    //   label: "主管单位电话"
    // },
    // {
    //   name: "maxhold",
    //   unit: "人",
    //   label: "可容纳人数"
    // },
    // {
    //   name: "area",
    //   unit: "平方千米",
    //   label: "总面积"
    // },
    // {
    //   name: "exitdesc",
    //   label: "安全出入口情况"
    // },
    // {
    //   name: "firefacilities",
    //   label: "消防设施情况"
    // },
    // {
    //   name: "securitynum",
    //   unit: "人",
    //   label: "安保人数"
    // },
    // {
    //   name: "protection",
    //   label: "防护措施"
    // },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'nmzgrs',
      label: '年末职工人数',
      unit: '人'
    },
    {
      name: 'zyjsjnrys',
      label: '专业技术技能人员数',
      unit: '人'
    },
    {
      name: 'jgglrys',
      label: '机构管理人员数',
      unit: '人'
    },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      name: 'yjtxbzfs',
      label: '应急通信保障方式'
    },
    {
      name: 'dwfzr',
      label: '单位负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'jgbm',
      label: '机构编码'
    },
    {
      name: 'jglx',
      label: '机构类型'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 社会服务机构2
  SocialOrg: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    // {
    //   name: "dutytel",
    //   type: "Tel",
    //   label: "值班电话"
    // },
    // {
    //   name: "superiordept",
    //   label: "主管单位"
    // },
    // {
    //   name: "superiortel",
    //   type: "Tel",
    //   label: "主管单位电话"
    // },
    // {
    //   name: "maxhold",
    //   unit: "人",
    //   label: "可容纳人数"
    // },
    // {
    //   name: "area",
    //   unit: "平方千米",
    //   label: "总面积"
    // },
    // {
    //   name: "exitdesc",
    //   label: "安全出入口情况"
    // },
    // {
    //   name: "firefacilities",
    //   label: "消防设施情况"
    // },
    // {
    //   name: "securitynum",
    //   unit: "人",
    //   label: "安保人数"
    // },
    // {
    //   name: "protection",
    //   label: "防护措施"
    // },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'nmzgrs',
      label: '年末职工人数',
      unit: '人'
    },
    {
      name: 'zyjsjnrys',
      label: '专业技术技能人员数',
      unit: '人'
    },
    {
      name: 'jgglrys',
      label: '机构管理人员数',
      unit: '人'
    },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      name: 'yjtxbzfs',
      label: '应急通信保障方式'
    },
    {
      name: 'dwfzr',
      label: '单位负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'jgbm',
      label: '机构编码'
    },
    {
      name: 'jglx',
      label: '机构类型'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 旅游景区
  Tourist: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'nmzgrs',
      label: '年末职工人数',
      unit: '人'
    },
    // {
    //   name: "zyjsjnrys", label: "专业技术技能人员数", unit: "人"
    // },
    // {
    //   name: "jgglrys", label: "机构管理人员数", unit: "人"
    // },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      name: 'yjtxbzfs',
      label: '应急通信保障方式'
    },
    {
      name: 'dwfzr',
      label: '单位负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'jgbm',
      label: '机构编码'
    },
    {
      name: 'jglx',
      label: '机构类型'
    },
    {
      name: 'jqdqdj',
      label: '景区当前等级'
    },
    {
      name: 'kfsj',
      label: '景区开放时间'
    },
    {
      name: 'sszdczl',
      label: '景区瞬时最大承载量',
      unit: '人'
    },
    {
      name: 'rzdczl',
      label: '景区日最大承载量',
      unit: '人'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 公共文化场所
  Tourist2: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "pac",
    //   label: "行政区划"
    // },
    // {
    //   name: "dutytel",
    //   type: "Tel",
    //   label: "值班电话"
    // },
    // {
    //   name: "superiordept",
    //   label: "主管单位"
    // },
    // {
    //   name: "superiortel",
    //   type: "Tel",
    //   label: "主管单位电话"
    // },
    // {
    //   name: "maxhold",
    //   unit: "人",
    //   label: "可容纳人数"
    // },
    // {
    //   name: "area",
    //   unit: "平方米",
    //   label: "总面积"
    // },
    // {
    //   name: "exitdesc",
    //   label: "安全出入口情况"
    // },
    // {
    //   name: "firefacilities",
    //   label: "消防设施情况"
    // },
    // {
    //   name: "securitynum",
    //   unit: "人",
    //   label: "安保人数"
    // },
    // {
    //   name: "protection",
    //   label: "防护措施"
    // },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'nmzgrs',
      label: '年末职工人数',
      unit: '人'
    },
    {
      name: 'zyjsjnrys',
      label: '专业技术技能人员数',
      unit: '人'
    },
    {
      name: 'jgglrys',
      label: '机构管理人员数',
      unit: '人'
    },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      name: 'yjtxbzfs',
      label: '应急通信保障方式'
    },
    {
      name: 'dwfzr',
      label: '单位负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'jgbm',
      label: '机构编码'
    },
    {
      name: 'jglx',
      label: '机构类型'
    },
    {
      label: '数据来源',
      name: 'datasource'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 星级饭店
  Hotel: [
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'jgbm',
      label: '机构编码'
    },
    // {
    //   name: "districtcode",
    //   label: "行政区划"
    // },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      name: 'qyxz',
      label: '企业性质'
    },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'zjyrs',
      label: '总就业人数',
      unit: '人'
    },
    {
      name: 'kfs',
      label: '客房数',
      unit: '间'
    },
    {
      name: 'cws',
      label: '床位数',
      unit: '张'
    },
    {
      name: 'hysrnrs',
      label: '会议室容纳人数',
      unit: '人'
    },
    {
      name: 'qtptss',
      label: '其他配套设施'
    },
    {
      name: 'aqbwrysl',
      label: '安保人员数量',
      unit: '人'
    },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      name: 'yjtxbzfs',
      label: '应急通信保障方式'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 体育场馆
  Gymnasium: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "districtcode",
    //   label: "行政区划"
    // },
    // {
    //   label: "值班电话",
    //   type: "Tel",
    //   name: "dutytel"
    // },
    {
      name: 'gymtypecode',
      label: '场馆类型'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      name: 'yyms',
      label: '运营模式'
    },
    {
      name: 'zdmj',
      label: '占地面积',
      unit: '平方米'
    },
    {
      name: 'cdmj',
      label: '场地面积',
      unit: '平方米'
    },
    {
      name: 'cdcyryzs',
      label: '场地从业人员总数',
      unit: '人'
    },
    {
      name: 'pjmzjdrc_new',
      label: '平均每周接待人次'
    },
    {
      name: 'gzxw',
      label: '观众席位',
      unit: '个'
    },
    {
      name: 'cdhdcs',
      label: '承担活动次数',
      unit: '次'
    },
    {
      name: 'dwkfqk',
      label: '对外开放情况'
    },
    {
      name: 'nkfts',
      label: '年开放天数',
      unit: '天'
    },
    {
      name: 'aqbwrysl',
      label: '安保人员数量',
      unit: '人'
    },
    {
      name: 'yjgdnl',
      label: '应急供电能力'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 大型商超
  Market: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "districtcode",
    //   label: "行政区划"
    // },
    {
      name: 'markettypecode',
      label: '类型'
    },
    {
      label: '负责人',
      name: 'respper'
    },
    {
      label: '负责人电话',
      type: 'Tel',
      name: 'respmtel'
    },
    {
      name: 'zdmj',
      label: '占地面积',
      unit: '平方米'
    },
    {
      name: 'jzmj',
      label: '建筑面积',
      unit: '平方米'
    },
    {
      name: 'nmcyrs',
      label: '年末从业人数',
      unit: '人'
    },
    {
      name: 'rzdrll',
      label: '日最大人流量',
      unit: '人'
    },
    {
      name: 'tws',
      label: '摊位数',
      unit: '个'
    },
    {
      name: 'nzyywsr',
      label: '年主营业务收入',
      unit: '万元'
    },
    {
      name: 'aqbwrysl',
      label: '安保人员数量',
      unit: '人'
    },
    {
      name: 'gsfs',
      label: '供水方式'
    },
    {
      name: 'gnfs',
      label: '供暖方式'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 救援队伍
  RescueTeam: [
    {
      name: 'rescuetypecode',
      label: '队伍类型'
    },
    {
      name: 'address',
      label: '驻地位置'
    },
    // {
    //   name: "rescounty",
    //   label: "行政区划"
    // },
    // {
    //   name: "resfullnum",
    //   unit: "人",
    //   label: "编制人数"
    // },
    // {
    //   name: "equipnum",
    //   label: "装备数量"
    // },
    // {
    //   name: "centertel",
    //   type: "Tel",
    //   label: "指挥中心电话"
    // },
    // {
    //   name: "dutytel",
    //   type: "Tel",
    //   label: "值班电话"
    // },
    {
      name: 'leader',
      label: '队长(负责人)'
    },
    {
      name: 'leadermtel',
      type: 'Tel',
      label: '队长电话'
    },
    {
      name: 'totalpernum',
      unit: '人',
      label: '队伍人数'
    },
    {
      name: 'rypjnl',
      label: '搜救人员平均年龄',
      unit: '岁'
    },
    {
      name: 'sjrysl',
      label: '搜救人员数量',
      unit: '人'
    },
    {
      name: 'zjhclsj',
      label: '队伍组建或成立时间'
    },
    {
      name: 'jdzmj',
      label: '基地占地总面积',
      unit: '平方米'
    },
    {
      name: 'jdjzmj',
      label: '基地建筑面积',
      unit: '平方米'
    },
    {
      name: 'glrysl',
      label: '管理人员数量',
      unit: '人'
    },
    {
      name: 'zyjsrysl',
      label: '专业技术人员数量',
      unit: '人'
    },
    {
      name: 'xfysl',
      label: '消防员数量',
      unit: '人'
    },
    {
      name: 'syndcyqxjycs',
      label: '上一年度参与地震救援次数',
      unit: '次'
    },
    {
      name: 'syndcyqxjyrc',
      label: '上一年度参与地震救援人次',
      unit: '人次'
    },
    {
      name: 'syndcgjyzrs',
      label: '上一年度成功救援总人数',
      unit: '人'
    },
    // {
    //   name: "commissar",
    //   label: "政委"
    // },
    // {
    //   name: "commissarmtel",
    //   type: "Tel",
    //   label: "政委电话"
    // },
    // {
    //   name: "engineer",
    //   label: "总工程师"
    // },
    // {
    //   name: "engineermtel",
    //   type: "Tel",
    //   label: "总工程师电话"
    // },
    // {
    //   name: "instructor",
    //   label: "指导员"
    // },
    // {
    //   name: "instructormtel",
    //   type: "Tel",
    //   label: "指导员电话"
    // },
    // {
    //   name: "timequality",
    //   label: "队伍性质（专、兼职）"
    // },
    // {
    //   name: "chargedept",
    //   label: "主管单位"
    // },
    // {
    //   name: "chargeconper",
    //   label: "主管单位联系人"
    // },
    // {
    //   name: "chargecontel",
    //   type: "Tel",
    //   label: "主管单位联系电话"
    // },
    // {
    //   name: "buildtime",
    //   label: "建队时间"
    // },
    // {
    //   name: "carnum",
    //   unit: "辆",
    //   label: "队站车数"
    // },
    // {
    //   name: "isprepareorg",
    //   label: "是否编制单位"
    // },
    // {
    //   name: "isdutyorg",
    //   label: "是否执勤单位"
    // },
    // {
    //   name: "ishavairport",
    //   label: "是否有机场"
    // },
    // {
    //   name: "equipdesc",
    //   label: "装备情况"
    // },
    // {
    //   name: "preparecadrenum",
    //   unit: "人",
    //   label: "编制干部人数"
    // },
    // {
    //   name: "preparesoldnum",
    //   unit: "人",
    //   label: "编制士兵人数"
    // },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 救援队伍
  RescueTeam11: [
    {
      name: 'rescuetypecode',
      label: '队伍类型'
    },
    {
      name: 'address',
      label: '驻地位置'
    },
    // {
    //   name: "rescounty",
    //   label: "行政区划"
    // },
    // {
    //   name: "resfullnum",
    //   unit: "人",
    //   label: "编制人数"
    // },
    // {
    //   name: "equipnum",
    //   label: "装备数量"
    // },
    // {
    //   name: "centertel",
    //   type: "Tel",
    //   label: "指挥中心电话"
    // },
    // {
    //   name: "dutytel",
    //   type: "Tel",
    //   label: "值班电话"
    // },
    {
      name: 'leader',
      label: '队长(负责人)'
    },
    {
      name: 'leadermtel',
      type: 'Tel',
      label: '队长电话'
    },
    {
      name: 'totalpernum',
      unit: '人',
      label: '队伍人数'
    },
    {
      name: 'rypjnl',
      label: '搜救人员平均年龄',
      unit: '岁'
    },
    {
      name: 'sjrysl',
      label: '搜救人员数量',
      unit: '人'
    },
    {
      name: 'zjhclsj',
      label: '队伍组建或成立时间'
    },
    {
      name: 'jdzmj',
      label: '基地占地总面积',
      unit: '平方米'
    },
    {
      name: 'jdjzmj',
      label: '基地建筑面积',
      unit: '平方米'
    },
    {
      name: 'glrysl',
      label: '管理人员数量',
      unit: '人'
    },
    {
      name: 'zyjsrysl',
      label: '专业技术人员数量',
      unit: '人'
    },
    {
      name: 'xfysl',
      label: '消防员数量',
      unit: '人'
    },
    {
      name: 'syndcyqxjycs',
      label: '上一年度参与地震救援次数',
      unit: '次'
    },
    {
      name: 'syndcyqxjyrc',
      label: '上一年度参与地震救援人次',
      unit: '人次'
    },
    {
      name: 'syndcgjyzrs',
      label: '上一年度成功救援总人数',
      unit: '人'
    },
    // {
    //   name: "commissar",
    //   label: "政委"
    // },
    // {
    //   name: "commissarmtel",
    //   type: "Tel",
    //   label: "政委电话"
    // },
    // {
    //   name: "engineer",
    //   label: "总工程师"
    // },
    // {
    //   name: "engineermtel",
    //   type: "Tel",
    //   label: "总工程师电话"
    // },
    // {
    //   name: "instructor",
    //   label: "指导员"
    // },
    // {
    //   name: "instructormtel",
    //   type: "Tel",
    //   label: "指导员电话"
    // },
    // {
    //   name: "timequality",
    //   label: "队伍性质（专、兼职）"
    // },
    // {
    //   name: "chargedept",
    //   label: "主管单位"
    // },
    // {
    //   name: "chargeconper",
    //   label: "主管单位联系人"
    // },
    // {
    //   name: "chargecontel",
    //   type: "Tel",
    //   label: "主管单位联系电话"
    // },
    // {
    //   name: "buildtime",
    //   label: "建队时间"
    // },
    // {
    //   name: "carnum",
    //   unit: "辆",
    //   label: "队站车数"
    // },
    // {
    //   name: "isprepareorg",
    //   label: "是否编制单位"
    // },
    // {
    //   name: "isdutyorg",
    //   label: "是否执勤单位"
    // },
    // {
    //   name: "ishavairport",
    //   label: "是否有机场"
    // },
    // {
    //   name: "equipdesc",
    //   label: "装备情况"
    // },
    // {
    //   name: "preparecadrenum",
    //   unit: "人",
    //   label: "编制干部人数"
    // },
    // {
    //   name: "preparesoldnum",
    //   unit: "人",
    //   label: "编制士兵人数"
    // },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 工贸企业
  ANJIAN_ENT_WHSMYHBZ: [
    {
      name: 'address',
      label: '地址'
    },
    // {
    //   name: "county",
    //   label: "行政区划"
    // },
    {
      name: 'loginnum',
      label: '工商注册号'
    },
    {
      name: 'artificialper',
      label: '法定代表人'
    },
    {
      name: 'artificialpertel',
      type: 'Tel',
      label: '法人电话'
    },
    {
      name: 'contactper',
      label: '联系人'
    },
    {
      name: 'contactpertel',
      type: 'Tel',
      label: '联系人电话'
    },
    {
      name: 'saferespper',
      label: '安全负责人'
    },
    {
      name: 'saferesppertel',
      type: 'Tel',
      label: '安全负责人联系电话'
    },
    {
      name: 'licenseid',
      label: '安全许可证编号'
    },
    {
      name: 'aqscxkzqsrq',
      label: '许可证启始日期'
    },
    {
      name: 'aqscxkzjzrq',
      label: '许可证截止日期'
    },
    {
      name: 'empnum',
      unit: '人',
      label: '员工总数'
    },
    {
      name: 'specialnum',
      unit: '人',
      label: '特种作业人员数'
    },
    {
      name: 'fullgadminnum',
      unit: '人',
      label: '专职安全管理人员数'
    },
    {
      name: 'mianprd',
      label: '主要产品'
    },
    {
      name: 'capaprd',
      unit: '吨',
      label: '产能'
    },
    {
      name: 'outvalue',
      unit: '万元/年',
      label: '产值'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  // 物资储备库
  ANJIAN_REPERTORY: [
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'districtcode',
      label: '行政区划'
    },
    {
      name: 'levelcode',
      label: '级别'
    },
    {
      name: 'restock',
      label: '储备物资'
    },
    {
      name: 'concateper',
      label: '联系人'
    },
    {
      name: 'concatemobtel',
      label: '联系人电话',
      type: 'Tel'
    },
    {
      name: 'area',
      label: '面积',
      unit: '平方米'
    },
    {
      name: 'capacity',
      label: '库容',
      unit: '立方米'
    },
    {
      name: 'jcsj',
      label: '建成时间'
    },
    {
      name: 'zzwhhglrysl',
      label: '管理人员数量',
      unit: '人'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  jznl01: [
    {
      name: 'address',
      label: '企业地址'
    },
    {
      name: 'jgbm',
      label: '企业代码'
    },
    {
      name: 'dwfzr',
      label: '负责人'
    },
    {
      name: 'lxdh',
      type: 'Tel',
      label: '联系电话'
    },
    {
      name: 'zzjydwrs',
      label: '专职队伍人数',
      unit: '人'
    },
    {
      name: 'dxwjjsl',
      label: '大型挖掘机（指≥30t）数量',
      unit: '台'
    },
    {
      name: 'pbyyyjdwjjsl',
      label: '配备有液压剪的大型挖掘机数量',
      unit: '台'
    },
    {
      name: 'pbymzjdwjjsl',
      label: '配备有拇指夹的大型挖掘机数量',
      unit: '台'
    },
    {
      name: 'dxwjjdjzdgl',
      label: '大型挖掘机单机最大功率',
      unit: 'kw'
    },
    {
      name: 'dxqcsqzjsl',
      label: '大型汽车式起重机（指≥15t）数量',
      unit: '台'
    },
    {
      name: 'zdqzl',
      label: '汽车式起重机单机最大起重量',
      unit: '吨'
    },
    {
      name: 'dxzzjsl',
      label: '大型装载机（功率≥147kw）数量',
      unit: '台'
    },
    {
      name: 'dxzzjgl',
      label: '大型装载机单机最大功率',
      unit: 'kw'
    },
    {
      name: 'dxldsttjsl',
      label: '大型履带式推土机（功率≥250kw）数量',
      unit: '台'
    },
    {
      name: 'dxldsttjdjzdgl',
      label: '大型履带式推土机单机最大功率',
      unit: 'kw'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  jznl02: [
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'jgbm',
      label: '统一社会信用代码/组织机构代码'
    },
    {
      name: 'dwfzr',
      label: '负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'zyywfx',
      label: '主要业务方向'
    },
    {
      name: 'zyjyjstc',
      label: '主要救援技术特长'
    },
    {
      name: 'zyjzzzywtc',
      label: '主要救灾赈灾业务特长'
    },
    {
      name: 'k72xslxcqzdrs',
      label: '可72小时连续出勤最大人数',
      unit: '人'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  jznl03: [
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'dwfzr',
      label: '负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'zhs',
      label: '年末总户数',
      unit: '户'
    },
    {
      name: 'nmczrksl',
      label: '常住人口数量',
      unit: '人'
    },
    {
      name: 'yxxzjddzyzhlx',
      label: '主要灾害类型'
    },
    {
      name: 'bjzhglgzryzs',
      label: '本级灾害管理工作人员总数',
      unit: '人'
    },
    {
      name: 'bjzhxxyrs',
      label: '本级灾害信息员人数',
      unit: '人'
    },
    {
      name: 'zhyjxxjsfs',
      label: '灾害预警信息接收方式'
    },
    {
      name: 'zhyjxxcdfs',
      label: '灾害预警信息传达方式'
    },
    {
      name: 'zqxxsbfs',
      label: '灾情信息上报方式'
    },
    {
      name: 'jzwzcbfs',
      label: '救灾物资储备方式'
    },
    {
      name: 'yjdyhyjfdsbsl',
      label: '应急电源或应急发电设备',
      unit: '台'
    },
    {
      name: 'yjtxsbsl',
      label: '应急通信设备',
      unit: '台'
    },
    {
      name: 'yjgssbsl',
      label: '应急供水设备',
      unit: '台'
    },
    {
      name: 'yjylsbsl',
      label: '应急医疗设备',
      unit: '台'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  jznl04: [
    {
      name: 'address',
      label: '地址'
    },
    {
      name: 'dwfzr',
      label: '负责人'
    },
    {
      name: 'lxdh',
      label: '联系电话',
      type: 'Tel'
    },
    {
      name: 'zhs',
      label: '总户数',
      unit: '户'
    },
    {
      name: 'nmczrksl',
      label: '常住人口数量',
      unit: '人'
    },
    {
      name: 'zero_ss_srs',
      label: '0-14岁人数',
      unit: '人'
    },
    {
      name: 'lw_shysrs',
      label: '65岁以上人数',
      unit: '人'
    },
    {
      name: 'czryrs',
      label: '残障人员数',
      unit: '人'
    },
    {
      name: 'sqylwsfwzhcwsssl',
      label: '社区/村医疗卫生服务站',
      unit: '个'
    },
    {
      name: 'zhxxyrs',
      label: '灾害信息员人数',
      unit: '人'
    },
    {
      name: 'djzczyzrs',
      label: '登记注册志愿者人数',
      unit: '人'
    },
    {
      name: 'mbybyrs',
      label: '民兵预备役人数',
      unit: '人'
    },
    {
      name: 'zhyjbncssl',
      label: '本级灾害应急避难场所数量',
      unit: '个'
    },
    {
      name: 'fzjzyjwzcbfs',
      label: '防灾减灾应急物资储备方式'
    },
    {
      name: 'zhyjxxjsfs',
      label: '灾害预警信息接收方式'
    },
    {
      name: 'zhyjxxcdfs',
      label: '灾害预警信息传达方式'
    },
    {
      name: 'zqxxsbfs',
      label: '灾情信息上报方式'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  resrrvoirWarn: [
    {
      label: '名称',

      unit: '',
      name: 'skmc'
    },
    {
      label: '测站编码',

      unit: '',
      name: 'czbm'
    },
    {
      label: '所属区县',

      name: 'dlwz'
    },
    {
      label: '所属流域',

      unit: '',
      name: 'lymc'
    },
    {
      label: '库水位(m)',

      unit: '',
      name: 'ksw'
    },
    {
      label: '监测时间',

      unit: '',
      name: 'yjsj'
    },
    {
      label: '汛限水位(m)',

      unit: '',
      name: 'xxsw'
    },
    {
      label: '比汛限(m)',

      unit: '',
      name: 'bxx'
    },
    {
      label: '经度',

      name: 'jd'
    },
    {
      label: '纬度',

      unit: '',
      name: 'wd'
    }
  ],
  ALL_CS_DHL_0001: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '类别',

      name: 'type'
    },
    {
      label: '负责人',

      name: 'resp_per'
    },
    {
      label: '联系电话',
      type: 'Tel',

      name: 'resp_per_tel'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '测绘装备',

      name: 'equip'
    },
    {
      label: '出图能力',

      name: 'capacity'
    },
    {
      label: '经验',

      name: 'experience'
    }
  ],
  countyCount: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '人口',

      name: 'pouplationNum',
      unit: '人'
    },
    {
      label: '人口密度',

      name: 'density',
      unit: '人/km²'
    },
    {
      label: '面积',

      name: 'arear',
      unit: 'km²'
    }
  ],
  townCount: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '人口',

      name: 'pouplationNum',
      unit: '人'
    },
    {
      label: '人口密度',

      name: 'density',
      unit: '人/km²'
    },
    {
      label: '面积',

      name: 'arear',
      unit: 'km²'
    }
  ],
  cunCount: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '辖区乡镇',

      name: 'xianName'
    }
  ],
  warning: [
    {
      label: '预警标题',

      name: 'name'
    },
    {
      label: '发布单位',

      name: 'source'
    },
    {
      label: '发布时间',

      name: 'time'
    },
    {
      label: '预警信息',

      name: 'content'
    }
  ],
  weatherWarning: [
    {
      label: 'id',

      name: 'alertId'
    },
    {
      label: 'alertInfoId',

      name: 'alertInfoId'
    },
    {
      label: '发布状态',

      name: 'pubStatus'
    },
    {
      label: '预警标题',

      name: 'headline'
    },
    {
      label: '预警信息',

      name: 'message'
    },
    // {
    //   label: "行政区划",

    //   name: "districtcode"
    // },
    {
      label: '发布时间',

      name: 'sendtime'
    }
  ],
  river: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '实时水位',

      name: 'currentWaterLevel',
      unit: 'm'
    },
    {
      label: '警戒水位',

      name: 'warningStage',
      unit: 'm'
    },
    {
      label: '监测时间',

      name: 'time'
    },
    {
      label: '测站类型',

      name: 'type'
    },
    // {
    //   label: "行政区划",

    //   name: "district"
    // },
    {
      label: '地址',

      name: 'address'
    },

    {
      label: '信息管理单位',

      name: 'dept'
    },
    {
      label: '交换管理单位',

      name: 'cDuty'
    },
    {
      label: '防汛等级',

      name: 'level'
    }
  ],
  riverStation: [
    {
      label: '站点代码',

      name: 'ZDDM'
    },
    {
      label: '站点名称',

      name: 'ZDMC'
    },
    {
      label: '区划编号',

      name: 'QHBH'
    },
    {
      label: '区划名称',

      name: 'QHMC'
    },
    {
      label: '警戒水位',

      name: 'JJSW',
      unit: '米'
    },
    {
      label: '实时水位',

      name: 'SSSW',
      unit: '米'
    },
    {
      label: '更新时间',

      name: 'GXSJ'
    }
  ],
  ANJIAN_DAGCHEMENT01: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '主要负责人',

      name: 'PRINCIPAL'
    },
    {
      label: '安全负责人',

      name: 'SAFETYMAGER'
    },
    {
      label: '风险等级',

      name: 'HAZARDLEVELCODE'
    },
    {
      label: '联系电话',
      type: 'Tel',
      name: 'SAFETYMAGERTEL'
    },
    {
      label: '许可范围',

      name: 'JYFW'
    },

    {
      label: '许可有效期',

      name: 'XZXKYXQXKSRQ'
    },
    {
      label: '登记有效期',

      name: 'REGSTARTDATE'
    },
    {
      label: '发证编号',

      name: 'XZXKZBH'
    },
    {
      label: '变更信息',

      name: 'CHANGEINFO'
    }
  ],
  ANJIAN_DAGCHEMENT02: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '法定代表人',

      name: 'ARTIFICIALPER'
    },
    {
      label: '联系人',

      name: 'CONTACTPER'
    },
    {
      label: '联系电话',
      type: 'Tel',
      name: 'CONTACTPERTEL'
    },
    {
      label: '经营范围',

      name: 'JYFW'
    },
    {
      label: '经营方式',

      name: 'OPERATIONMODE'
    },
    {
      label: '发证日期',

      name: 'CERTIFICATIME'
    },

    {
      label: '许可有效期',

      name: 'XZXKYXQXJSRQ'
    },
    {
      label: '发证编号',

      name: 'XZXKZBH'
    },
    {
      label: '重大危险源等级',

      name: 'DANGERLEVEL'
    },
    {
      label: '风险等级',

      name: 'HAZARDLEVELCODE'
    },
    {
      label: '涉及剧毒品经营品种',

      name: 'HIGHLYTOXICCHEMICALS'
    },
    {
      label: '涉及易制爆化学品经营品种',

      name: 'EXPLOSIVECHEMICALS'
    },
    {
      label: '涉及易制毒化学品经营品种',

      name: 'PRECURSORCHEMICALS'
    }
  ],
  ANJIAN_DAGCHEMENT03: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '法定代表人',

      name: 'ARTIFICIALPER'
    },
    {
      label: '联系人',

      name: 'SAFETYMAGER'
    },
    {
      label: '联系电话',
      type: 'Tel',
      name: 'SAFETYMAGERTEL'
    },
    {
      label: '类型',

      name: 'PROPERTYNAME'
    },
    {
      label: '主要负责人',

      name: 'PRINCIPAL'
    },
    {
      label: '许可范围',

      name: 'JYFW'
    },
    {
      label: '发证日期',

      name: 'CERTIFICATIME'
    },

    {
      label: '许可有效期',

      name: 'XZXKYXQXKSRQ'
    },
    {
      label: '许可结束期',

      name: 'XZXKYXQXJSRQ'
    },
    {
      label: '发证编号',

      name: 'XZXKZBH'
    }
  ],
  ANJIAN_DAGCHEMENT04: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '类型',

      name: 'PROPERTYNAME'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '联系人',

      name: 'CONTACTPER'
    },
    {
      label: '联系电话',
      type: 'Tel',
      name: 'CONTACTPERTEL'
    }
  ],
  trade: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '所属区市',

      name: 'COUNTY'
    },
    {
      label: '主要产品',

      name: 'mianprd'
    },
    {
      label: '产能',

      name: 'capaprd'
    },
    {
      label: '联系人',

      name: 'contactper'
    },
    {
      label: '联系人电话',
      type: 'Tel',
      name: 'contactpertel'
    }
  ],
  riskCommon: [
    {
      label: '名称',

      name: 'name'
    },
    {
      label: '地址',

      name: 'address'
    },
    {
      label: '类型',

      name: 'featurecode'
    },
    {
      label: '规模等级',

      name: 'DISASTERSCALE'
    },
    {
      label: '险情等级',

      name: 'DISASTERLEVELCODE'
    },
    {
      label: '威胁人数',

      name: 'RISKNUM'
    },
    {
      label: '威胁财产',

      name: 'THRPROPERTY'
    },
    {
      label: '形成原因',

      name: 'INDUCINGFACTORS'
    },
    {
      label: '责任主体',

      name: 'CHARGEDEPT'
    },
    {
      label: '群测群防员',

      name: 'INFOPERSONNAME'
    },
    {
      label: '群测群防联系方式',

      name: 'MOBILETEL'
    },
    {
      label: '处置建议',

      name: 'PROTECTION'
    }
  ],
  xzhq: [
    {
      label: '名称',
      name: 'fsdaName'
    },
    // {
    //   label: "行政区划",
    //   name: "xzqhdm"
    // },
    {
      label: '所在位置',
      name: 'fsdaLoc'
    },
    {
      label: '经度',
      name: 'lon'
    },
    {
      label: '纬度',
      name: 'lat'
    },
    {
      label: '类型',
      name: 'fedaType'
    },
    {
      label: '设区时间',
      name: 'buildFlDate'
    },
    {
      label: '总面积',
      name: 'fsdaTotAera',
      unit: 'k㎡'
    },
    {
      label: '圩堤长度',
      name: 'fsdaDikeLen',
      unit: 'km'
    },
    {
      label: '设计行(蓄)洪面积',
      name: 'desFlArea',
      unit: 'k㎡'
    },
    {
      label: '设计行(蓄)洪水位',
      name: 'desFlStag',
      unit: 'm'
    },
    {
      label: '设计蓄洪量',
      name: 'desStorCap',
      unit: 'm³'
    },
    {
      label: '设计行洪流量',
      name: 'desFlFlow',
      unit: 'm³/s'
    },
    {
      label: '耕地面积',
      name: 'arArea',
      unit: '亩'
    },
    {
      label: '区内人口',
      name: 'fsdaPop',
      unit: '万'
    },
    {
      label: '区内生产总值',
      name: 'fsdaGdp',
      unit: '万元'
    },
    {
      label: '所属河流',
      name: 'river'
    },
    {
      label: '备注',
      name: 'note'
    }
  ],
  tbgc: [
    {
      label: '名称',
      name: 'name'
    },
    // {
    //   label: "行政区划",
    //   name: "xzqhdm"
    // },
    {
      label: '所在位置',
      name: 'szwz'
    },
    {
      label: '经度',
      name: 'lon'
    },
    {
      label: '纬度',
      name: 'lat'
    },
    {
      label: '坝址控制流域面积',
      name: 'lymj',
      unit: 'k㎡'
    },
    {
      label: '工程规模',
      name: 'gcgmmc'
    },
    {
      label: '正常蓄水位',
      name: 'xsw',
      unit: 'm'
    },
    {
      label: '总容积',
      name: 'xhst',
      unit: 'm³'
    },
    {
      label: '工程建设情况',
      name: 'gcjsqk'
    },
    {
      label: '归口管理部门',
      name: 'gkglbm'
    },
    {
      label: '开工时间',
      name: 'kgsj'
    },
    {
      label: '建成时间',
      name: 'jcsj'
    },
    {
      label: '备注',
      name: 'note'
    }
  ],
  bz: [
    {
      label: '名称',
      name: 'name'
    },
    // {
    //   label: "代码",
    //   name: "bzdm"
    // },
    {
      label: '所在位置',
      name: 'gcszdz'
    },
    {
      label: '经度',
      name: 'lng'
    },
    {
      label: '纬度',
      name: 'lat'
    },
    {
      label: '泵站类型',
      name: 'bzlxmc'
    },
    {
      label: '装机流量(m³/s)',
      name: 'zjll'
    },
    {
      label: '水泵数量(个)',
      name: 'sbsl'
    },
    {
      label: '机组台数',
      name: 'jzts'
    },
    {
      label: '设计装机总容量',
      name: 'sjzjzrl'
    },
    {
      label: '设计扬程(m)',
      name: 'sjyc'
    },
    {
      label: '工程任务',
      name: 'gcrw'
    },
    {
      label: '供水范围',
      name: 'gsfw'
    },
    {
      label: '工程规模',
      name: 'gcgmmc'
    },
    {
      label: '工程等别',
      name: 'gcdb'
    },

    {
      label: '取水水源类型',
      name: 'qssylxmc'
    },
    {
      label: '主要建筑物级别',
      name: 'zyjzwjb'
    },
    {
      label: '工程建设情况',
      name: 'gcjsqkmc'
    },
    {
      label: '归口部门',
      name: 'gkglbm'
    },
    {
      label: '开工时间',
      name: 'kgsj'
    },
    {
      label: '建成时间',
      name: 'jcsj'
    },
    {
      label: '开始运行时间',
      name: 'ksyxsj'
    },
    {
      label: '备注',
      name: 'note'
    }
  ],
  xgxd: [
    {
      label: '名称',
      name: 'name'
    },
    {
      label: '所在位置',
      name: 'dpdsLoc'
    },
    {
      label: '经度',
      name: 'dpdsLong'
    },
    {
      label: '纬度',
      name: 'dpdsLat'
    },
    {
      label: '类型',
      name: 'zhlx'
    },
    {
      label: '危险等级',
      name: 'wxdj'
    },
    {
      label: '负责人',
      name: 'fzr'
    },
    {
      label: '联系电话',
      name: 'lxdh'
    },
    {
      label: '出险数量',
      name: 'dangNum'
    },
    {
      label: '长度',
      name: 'dpdsLen'
    },
    {
      label: '记录生效时间',
      name: 'effDate'
    },
    {
      label: '记录失效时间',
      name: 'exprDate'
    },
    {
      label: '备注',
      name: 'note'
    }
  ],
  sz: [
    {
      label: '名称',
      name: 'szmc'
    },
    {
      label: '所在位置',
      name: 'szwz'
    },
    {
      label: '起点经度',
      name: 'qdlgtd'
    },
    {
      label: '起点纬度',
      name: 'qdlttd'
    },
    {
      label: '终点经度',
      name: 'zdlgtd'
    },
    {
      label: '终点纬度',
      name: 'zdlttd'
    },
    {
      label: '水闸类型',
      name: 'szlx'
    },
    {
      label: '水闸用途',
      name: 'szyt'
    },
    {
      label: '工程规模',
      name: 'gcgmmc'
    },
    {
      label: '工程等别',
      name: 'gcdb'
    },
    {
      label: '取水水源类型',
      name: 'qssylx'
    },
    {
      label: '最大过闸流量(m³/s)',
      name: 'zdgzll'
    },

    {
      label: '闸孔数量(孔)',
      name: 'zksl'
    },
    {
      label: '装机功率(KW)',
      name: 'zjgl'
    },
    {
      label: '设计装机总容量',
      name: 'sjzjzrl'
    },
    {
      label: '工程建设情况',
      name: 'gcjsqkmc'
    },
    {
      label: '运行状况',
      name: 'yxzk'
    },
    {
      label: '归口管理部门',
      name: 'gkglbm'
    },
    {
      label: '开工时间',
      name: 'kgsj'
    },
    {
      label: '建成时间',
      name: 'jcsj'
    },
    {
      label: '备注',
      name: 'note'
    }
  ],
  ql: [
    {
      label: '名称',
      name: 'qlmc'
    },
    {
      label: '所在位置',
      name: 'qlwz'
    },
    {
      label: '起点经度',
      name: 'qdlon'
    },
    {
      label: '起点纬度',
      name: 'qdlat'
    },
    {
      label: '终点经度',
      name: 'zdlon'
    },
    {
      label: '终点纬度',
      name: 'zdlat'
    },

    {
      label: '主桥类型',
      name: 'zqlx'
    },
    {
      label: '主桥长(m)',
      name: 'zqc'
    },
    {
      label: '主桥面宽(m)',
      name: 'zqmk'
    },
    {
      label: '主桥孔数(孔)',
      name: 'zqks'
    },
    {
      label: '副桥孔数(孔)',
      name: 'fqks'
    },
    {
      label: '主桥桥孔净跨度(m)',
      name: 'zqqkjkd'
    },
    {
      label: '河槽宽度(m)',
      name: 'hckd'
    },
    {
      label: '桥梁宽度(m)',
      name: 'qlkd'
    },
    {
      label: '桥梁地质情况',
      name: 'qldzqk'
    },
    {
      label: '水准基面',
      name: 'szjm'
    },
    {
      label: '假定水准基面位置',
      name: 'jdszjmwz'
    },
    {
      label: '左岸桩号',
      name: 'zazh'
    },
    {
      label: '左岸位置',
      name: 'zawz'
    },
    {
      label: '左岸堤顶高程(m)',
      name: 'zaddgc'
    },
    {
      label: '右岸桩号',
      name: 'yazh'
    },
    {
      label: '右岸位置',
      name: 'yawz'
    },
    {
      label: '右岸堤顶高程(m)',
      name: 'yaddgc'
    },
    {
      label: '两岸堤距(m)',
      name: 'ladj'
    },
    {
      label: '地震基本烈度(度)',
      name: 'dzjbld'
    },
    {
      label: '地震设计烈度(度)',
      name: 'dzsjld'
    },
    {
      label: '桥梁设计荷载(KN)',
      name: 'qlsjhz'
    },
    {
      label: '设计洪水标准',
      name: 'sjhsbz'
    },
    {
      label: '设计洪水位(m)',
      name: 'sjhsw'
    },
    {
      label: '设计洪水流量(m³/s)',
      name: 'sjhsll'
    },
    {
      label: '校核洪水标准',
      name: 'xhhsbz'
    },
    {
      label: '校核洪水位(m)',
      name: 'xhhsw'
    },
    {
      label: '校核洪水流量(m³/s)',
      name: 'xhhsll'
    },

    {
      label: '通航设计最高水位(m)',
      name: 'thsjzgsw'
    },
    {
      label: '河底一般高程(m)',
      name: 'hdybgc'
    },
    {
      label: '历史最高水位(m)',
      name: 'lszgsw'
    },
    {
      label: '历史最大洪峰流量(m³/s)',
      name: 'lszdhfll'
    },
    {
      label: '历史最高水位发生日期',
      name: 'lszgswfssj'
    },
    {
      label: '历史最大洪峰流量发生日期',
      name: 'lszdhfllfssj'
    },
    {
      label: '开工时间',
      name: 'kgsj'
    },
    {
      label: '建成时间',
      name: 'jcsj'
    }
  ],
  ALL_fire_road: [
    {
      label: '调查单位',
      name: 'dcdw'
    },
    {
      label: '调查区域',
      name: 'dcqy'
    },
    {
      label: '调查人姓名',
      name: 'dcrxm'
    },
    {
      label: '电话',
      name: 'dh',
      type: 'Tel'
    },
    {
      label: '长度',
      name: 'length',
      unit: '公里'
    },
    {
      label: '宽度',
      name: 'kd',
      unit: '米'
    },
    {
      label: '起始地址',
      name: 'start_add'
    },
    {
      label: '终点地址',
      name: 'end_add'
    },
    {
      label: '路面类型',
      name: 'leixing'
    },
    {
      label: '等级',
      name: 'level'
    },
    {
      label: '通行能力',
      name: 'txnl'
    },
    {
      label: '设施状况',
      name: 'status'
    },
    {
      label: '建成时间',
      name: 'jssj'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  ALL_forest_barrier: [
    {
      label: '调查单位',
      name: 'dcdw'
    },
    {
      label: '调查区域',
      name: 'dcqy'
    },
    {
      label: '调查人姓名',
      name: 'dcrxm'
    },
    {
      label: '电话',
      name: 'dh',
      type: 'Tel'
    },
    {
      label: '长度',
      name: 'cd',
      unit: '公里'
    },
    {
      label: '宽度',
      name: 'kd',
      unit: '米'
    },
    {
      label: '路面类型',
      name: 'lx'
    },
    {
      label: '起始地址',
      name: 'qsdz'
    },
    {
      label: '终点地址',
      name: 'zzdz'
    },
    {
      label: '设施状况',
      name: 'sszk'
    },

    {
      label: '建成时间',
      name: 'jssj'
    },
    {
      label: '数据来源',
      name: 'sourcedept'
    },
    {
      label: '更新时间',
      name: 'updatetime'
    }
  ],
  rescueTeamALL: [
    {
      label: '队伍名称',
      name: 'rescuename'
    },
    {
      label: '救援队伍类型',
      name: 'rescuetypecode'
    }
  ],
  ALL_JC_EXPERT: [
    {
      label: '名称',
      name: 'expertname'
    },
    {
      label: '工作单位',
      name: 'deptname'
    },
    {
      label: '联系电话',
      name: 'tel'
    },
    {
      label: '专业专长',
      name: 'specialiskill'
    },
    {
      label: '职称',
      name: 'protitle'
    }
  ]
};
