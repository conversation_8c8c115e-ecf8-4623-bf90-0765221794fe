import axios from 'axios';
import * as layerConfig from '@/utils/layer';
import util from '@/utils/gis/util';
// 灾情研判服务
export class EgisDataServer {
  private baseURL: string;
  private clientId: string;
  private clientSecret: string;
  public resourceConfig: any = null;

  constructor(opt: any) {
    this.baseURL = opt.baseURL || '';
    this.clientId = opt.clientId || '';
    this.clientSecret = opt.clientSecret || '';
    this.parseConfig(layerConfig);
  }

  // 解析配置
  private parseConfig(res: any) {
    const resourceConf: any = {};
    for (const key in res) {
      if (Object.prototype.hasOwnProperty.call(res, key)) {
        const conf = res[key];
        if (Object.prototype.toString.call(conf.geomField) === '[object Object]') {
          conf.fieldMap.geom = conf.geomField;
        }
        const children = conf.children || {};
        for (const childKey in children) {
          if (Object.prototype.hasOwnProperty.call(children, childKey)) {
            let childConf = children[childKey];
            const parentConf = JSON.parse(JSON.stringify(conf));
            delete parentConf.children;
            const pWhere = parentConf.filter && parentConf.filter.where;
            childConf = Object.assign(parentConf, childConf);
            childConf.filter = childConf.filter || {};
            childConf.filter.where = childConf.filter.where || '1=1';
            if (childConf.filter.where !== pWhere) {
              childConf.filter.where += ' and ' + pWhere;
            }
            resourceConf[childKey] = childConf;
          }
        }
        resourceConf[key] = conf;
      }
    }
    this.resourceConfig = resourceConf;
  }

  /**
   * 获取筛选条件
   * @param opts 参数
   * @param resourceConf 资源配置
   * @returns 筛选条件
   */
  private getFilterByOpts(opts: any, resourceConf: any) {
    const filter: any = {};
    const initFilter = resourceConf.filter || {};
    filter.where = initFilter.where || '1=1';
    const optFilter: any = opts.filter || {};
    let wheres: any = [];
    if (optFilter.geometry && JSON.stringify(optFilter.geometry) !== '{}') {
      if (optFilter.geometry.type === 'MultiPolygon') {
        const geometries = [];
        optFilter.geometry.coordinates.forEach((item) => {
          const Polygon = this.getPolygonJson(item);
          geometries.push(Polygon);
        });

        filter.geometry = {
          $type: 'MultiPolygon,http://www.Gs.com',
          spatialReference: 4490,
          coordinateDimension: 0,
          geometries: geometries,
          empty: false
        };
      } else {
        filter.geometry = {
          shell: {
            $type: 'LinearRing,http://www.Gs.com',
            spatialReference: 4490,
            points: optFilter.geometry.coordinates[0].map((item: any) => ({
              $type: 'Point,http://www.Gs.com',
              x: item[0],
              y: item[1],
              spatialReference: 4490
            }))
          },
          spatialReference: 4490,
          $type: 'Polygon,http://www.Gs.com',
          holes: []
        };
      }

      filter.spatialRelType = 4;
    }
    // 关键字条件构建
    if (optFilter.keyword) {
      const keywordField = resourceConf.keywordField;
      if (keywordField) {
        if (Array.isArray(keywordField)) {
          const keywordArr: any = keywordField.map((key: any) => `${key} like '%${optFilter.keyword}%'`);
          wheres.push(`(${keywordArr.join(' or ')})`);
        } else {
          const keywordWhere = `(${keywordField} like '%${optFilter.keyword}%')`;
          wheres.push(keywordWhere);
        }
      }
    }
    // 区划编码条件构建
    const districtField = resourceConf.districtField;
    if (Object.prototype.toString.call(districtField) === '[object String]') {
      const districtWhere = this.getDistrictFilter(districtField, optFilter.districtCodes);
      wheres = wheres.concat(districtWhere);
    }
    wheres = Array.from(new Set(wheres)); // 去重
    // 组合资源配置json中自带筛选条件
    wheres.length > 0 && (filter.where += ' and (' + wheres.join(' and ') + ')');
    // 组合传入的where条件
    optFilter.where && (filter.where += ' and (' + optFilter.where + ')');
    const subFields = this.getSubFields(opts, resourceConf);
    subFields && (filter.subFields = subFields);
    return filter;
  }
  private getPolygonJson(coordinates: any) {
    const points = [];
    coordinates.forEach((item) => {
      item.forEach((element) => {
        points.push({
          $type: 'Point,http://www.Gs.com',
          x: element[0],
          y: element[1],
          spatialReference: 4490,
          coordinateDimension: 0,
          empty: false
        });
      });
    });

    return {
      $type: 'Polygon,http://www.Gs.com',
      spatialReference: 4490,
      coordinateDimension: 0,
      shell: {
        $type: 'LinearRing,http://www.Gs.com',
        spatialReference: 4490,
        coordinateDimension: 0,
        points: points
      }
    };
  }
  private getSubFields(opts: any, resourceConf: any) {
    const fieldMap = resourceConf.fieldMap;
    let geomField = null;
    if (Object.prototype.toString.call(resourceConf.geomField) === '[object String]') {
      geomField = resourceConf.geomField || 'shape';
    }
    const fields: any = [];
    if (fieldMap) {
      for (const key in fieldMap) {
        if (Object.prototype.hasOwnProperty.call(fieldMap, key)) {
          const field = fieldMap[key];
          // 取主表直接映射字段
          if (field && Object.prototype.toString.call(field) !== '[object Object]' && !fields.includes(field)) {
            fields.push(field);
          }
        }
      }
      if (geomField && opts.geom !== false && !fields.includes(geomField)) {
        fields.push(geomField);
      }
    }
    const subFields = fields.join(',');
    return subFields;
  }

  private getRelFilter(config: any, opts: any) {
    const field = config.field;
    const table = config.tag;
    const districtCodes = opts.filter.districtCodes;
    let districtWhereStr: any = null;
    if (districtCodes) {
      const districtWheres: any = [];
      const districtCodesArr = districtCodes.split(',');
      if (field) {
        districtCodesArr.forEach((districtCode: any) => {
          districtCode = util.getDistrictMatchCode(districtCode);
          const where = this.getDistrictSql(field, districtCode, table);
          districtWheres.push(where);
        });
        districtWhereStr = '(' + districtWheres.join(' or ') + ')';
      }
    }
    const result = {
      relFilterList: [
        {
          tableName: table,
          relFilter: {
            where: districtWhereStr
          },
          childRelFilterList: []
        }
      ]
    };
    return result;
  }
  /**
   * 获取区划筛选条件
   * @param districtField 区划字段
   * @param districtCodes 区划编码
   * @returns 区划筛选条件
   */
  private getDistrictFilter(districtField: string, optDistrictCodes: string) {
    const wheres: any = [];
    // 参数区划筛选构建
    if (optDistrictCodes && districtField) {
      const districtWheres: any = [];
      const districtCodes = optDistrictCodes.split(',');
      districtCodes.forEach((districtCode: any) => {
        districtCode = util.getDistrictMatchCode(districtCode);
        const where = this.getDistrictSql(districtField, districtCode);
        districtWheres.push(where);
      });
      const districtWhereStr = '(' + districtWheres.join(' or ') + ')';
      wheres.push(districtWhereStr);
    }
    return wheres;
  }
  private getDistrictSql(districtField: string, districtCode: string, table?: string) {
    districtCode = districtCode.replace(/\s*/g, '');
    districtField = districtField.replace(/\s*/g, '');
    const columnArr = (districtField + '').replace(/&&/g, '||').split('||'); // 临时变量，提取字段数组
    let wheres: any = columnArr.map((item) => `${item} like '${districtCode}%'`);
    wheres = districtField.includes('||') ? wheres.join(' or ') : districtField.includes('&&') ? wheres.join(' and ') : wheres.join('');
    return wheres;
  }
  /**
   * 图层列表
   * @param opts
   * @param opts.code {string} 图层id
   * @param [opts.filter] {Object}
   * @param [opts.filter.geometry] {geojson} 空间筛选条件
   * @param [opts.filter.keyword] {string} 关键字
   * @param [opts.filter.districtCodes] {string} 区划编码，逗号隔开
   */
  public getLayerList(opts: any) {
    let withBu = true;
    let withRelation = true;
    const resourceConf = this.resourceConfig[opts.code];
    const layer = resourceConf.layer;
    const self = this;
    const filter: any = {
      spatialFilter: {},
      businessFilter: {},
      relationFilterCollection: {
        relFilterList: []
      }
    };
    switch (resourceConf.districtField && resourceConf.districtField.source) {
      case 'relation':
        withBu = true;
        filter.relationFilterCollection = self.getRelFilter(resourceConf.districtField, opts);
        break;
      case 'business':
        withRelation = true;
        break;
      default:
        break;
    }
    filter.spatialFilter = self.getFilterByOpts(opts, resourceConf);
    const apiName = opts.apiName || 'composeQuery';
    const notPagenation = apiName === 'composeQuery';
    return new Promise((resolve, reject) => {
      if (layer === 'ALL_120301000000' || layer === 'ALL_jc_repertory') {
        withBu = false;
        withRelation = false;
      }
      let url: any = '';
      if (notPagenation) {
        url = `${this.baseURL}/egis/business/v1/wrms/${apiName}?withRelation=${withRelation}&withBu=${withBu}&layer=${layer}`;
      } else {
        const page = opts.page || 0;
        const size = opts.size || 10;
        url = `${this.baseURL}/egis/business/v1/wrms/${apiName}?withRelation=${withRelation}&withBu=${withBu}&layer=${layer}&page=${page}&size=${size}`;
      }

      axios
        .post(url, filter, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${window.btoa(`${this.clientId}:${this.clientSecret}`)}`
          }
        })
        .then((res: any) => {
          const resourceEntityCollection = res.data.result;
          let fields = '';
          let resourceEntities: any = '';
          if (notPagenation) {
            fields = resourceEntityCollection?.featureFields?.fields;
            resourceEntities = resourceEntityCollection.resourceEntities || [];
          } else {
            fields = resourceEntityCollection.entities?.featureFields?.fields;
            resourceEntities = resourceEntityCollection.entities?.resourceEntities || [];
          }

          const result: any = [];
          resourceEntities.forEach((entity: any) => {
            result.push(self.parseResourceEntity(entity, fields, resourceConf.fieldMap));
          });
          if (notPagenation) {
            resolve(result);
          } else {
            resolve({
              list: result,
              total: resourceEntityCollection.count
            });
          }
        })
        .catch((error) => {
          console.error('获取图层数据失败:', error);
          reject(error);
        });
    });
  }

  /**
   * 执行自定义SQL查询
   * @param sqlName SQL名称
   * @param opts 查询参数
   */
  public executeCustomQuery(sqlName: string, opts: any) {
    const url = `${this.baseURL}/egis/business/v1/sqlcall/execute?sqlName=${sqlName}`;

    return new Promise((resolve, reject) => {
      axios
        .post(url, opts, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${window.btoa(`${this.clientId}:${this.clientSecret}`)}`
          }
        })
        .then((res: any) => {
          if (res.data && res.data.result) {
            // 获取列名
            const columns = res.data.result.sortedColumns || [];
            const resultList = res.data.result.result || [];

            // 将二维数组转换为对象数组
            const formattedData = resultList.map((item: any) => {
              const obj: any = {};
              columns.forEach((col: string, index: number) => {
                obj[col] = item[index];
              });

              // 确保经纬度字段统一
              if (obj.jd !== undefined) obj.lon = obj.jd;
              if (obj.wd !== undefined) obj.lat = obj.wd;

              return obj;
            });

            resolve({
              list: formattedData,
              total: formattedData.length
            });
          } else {
            resolve({
              list: [],
              total: 0
            });
          }
        })
        .catch((error) => {
          console.error('执行SQL查询失败:', error);
          reject(error);
        });
    });
  }

  /**
   * 获取详情
   * @param opts
   * @param opts.code {String} 资源code
   * @param opts.id {String}
   */
  public getDetailById(opts: any) {
    const resourceConf = this.resourceConfig[opts.code];
    const layer = resourceConf.layer;
    const url = `${this.baseURL}/egis/business/v1/wrms/findById?layer=${layer}&id=${opts.id}&withRelation=true&withBu=true`;
    const self = this;
    return new Promise((resolve, reject) => {
      axios
        .get(url, {
          headers: {
            Authorization: `Basic ${window.btoa(`${this.clientId}:${this.clientSecret}`)}`
          }
        })
        .then((res: any) => {
          const resourceEntity = res.data.result;
          const fields = resourceEntity.feature.fields.fields;
          const businessFields = resourceEntity?.business?.fields.fields;

          const result = self.parseResourceEntity(resourceEntity, fields, resourceConf.fieldMap);
          if (businessFields) {
            const externalData = self.parseBusResourceEntity(resourceEntity, businessFields, resourceConf.fieldMap);
            Object.assign(result, externalData);
          }
          // console.log("查看详情", result)
          resolve(result);
        })
        .catch((error) => {
          console.error('获取详情失败:', error);
          reject(error);
        });
    });
  }
  private parseBusResourceEntity(entity: any, fields: any, fieldMap: any) {
    const result: any = {};
    const feature = entity.business;
    const properties = feature.properties;
    for (let index = 0; index < fields.length; index++) {
      const field = fields[index].name;
      const obj = properties[index];
      const property = this.getFieldValue(obj);
      if (field === 'shape') {
        result.longitude = property?.x;
        result.latitude = property?.y;
      }
      if (field !== 'shape') {
        result[field] = property;
      }

      for (const fieldM in fieldMap) {
        if (Object.prototype.hasOwnProperty.call(fieldMap, fieldM)) {
          const confField = fieldMap[fieldM];
          if (confField === field) {
            result[fieldM] = property;
          }
        }
      }
    }
    return result;
  }
  /**
   * 解析资源实体
   * @param resourceEntity
   */
  private parseResourceEntity(entity: any, fields: any, fieldMap: any) {
    const result: any = {};
    const feature = entity.feature;
    const properties = feature.properties;
    for (let index = 0; index < fields.length; index++) {
      const field = fields[index].name;
      const obj = properties[index];
      const property = this.getFieldValue(obj);
      if (field === 'shape') {
        switch (property?.$type) {
          case 'Point,http://www.Gs.com':
            result.longitude = property?.x;
            result.latitude = property?.y;
            result.geomType = 'Point';
            break;
          case 'LineString,http://www.Gs.com':
          case 'MultiLineString,http://www.Gs.com':
            result[field] = this.parsePolyline(property);
            result.geomType = 'Polyline';
            break;
        }
      } else {
        result[field] = property;
      }
      for (const fieldM in fieldMap) {
        if (Object.prototype.hasOwnProperty.call(fieldMap, fieldM)) {
          const confField = fieldMap[fieldM];
          if (confField === field) {
            result[fieldM] = property;
          }
        }
      }
    }
    return result;
  }
  private getFieldValue(obj: any) {
    let column = 'value';
    switch (obj.$type) {
      case 'DictProperty':
        column = 'name';
        break;
      case 'GeometryProperty':
        column = 'geometry';
        if (obj[column]) {
          //   obj[column] = this.deserializer.createJsInstance(obj[column]).asGeoJson()
        } else {
          return;
        }
        break;
      default:
        break;
    }
    const value = obj[column] || obj.value;
    return value;
  }
  private parsePolyline(shape: any) {
    const result: any = [];
    const lineLists = []; // 多线数组
    if (shape.$type === 'MultiLineString,http://www.Gs.com') {
      shape.geometries.forEach((ele: any) => {
        lineLists.push(ele);
      });
    } else {
      lineLists.push(shape);
    }
    lineLists.forEach((ele) => {
      const lineList: any = [];
      ele.points.forEach((item: { x: number; y: number; z: number }) => {
        lineList.push([item.x, item.y, item.z === 0 ? 30 : item.z]);
      });
      result.push(lineList);
    });
    return result;
  }
  /**
   * 获取统计数量
   * @param opts 参数
   */
  public getStatistics(opts: any) {
    opts.codes = opts.codes || opts.code; // 增加参数容错
    const self = this;
    opts.codes = opts.codes || opts.code; // 增加参数容错
    const filter: any = {
      $type: 'WrmsLayerComposeFilters,http://www.Gs.com',
      filters: []
    };
    opts.codes.forEach((code: any) => {
      const resourceConf = this.resourceConfig[code];
      const wrmsLayer = resourceConf.layer;
      const filterOpts = {
        code,
        filter: opts.filter
      };
      const singleFilter = {
        $type: 'WrmsLayerComposeFilter,http://www.Gs.com',
        code,
        composeQueryFilterInDto: {
          businessFilter: {},
          relationFilterCollection: {
            relFilterList: []
          },
          spatialFilter: this.getFilterByOpts(filterOpts, resourceConf)
        },
        wrmsLayer
      };
      filter.filters.push(singleFilter);
    });
    const result: any = {};
    return new Promise((resolve, reject) => {
      const url = `${this.baseURL}/egis/business/v1/wrms/batchCount`;
      axios
        .post(url, filter, {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Basic ${window.btoa(`${this.clientId}:${this.clientSecret}`)}`
          }
        })
        .then((res: any) => {
          if (res.data && res.data.result && res.data.result.list) {
            const egisResults = (res.data.result && res.data.result.list) || [];
            for (let index = 0; index < egisResults.length; index++) {
              const egisResult = egisResults[index];
              const layer = egisResult.name;
              const code = filter.filters[index].code;
              const value = egisResult.value;
              result[code] = value;
            }
            resolve(result);
          } else {
            resolve({});
          }
        })
        .catch((error) => {
          console.error('获取统计数据失败:', error);
          reject(error);
        });
    });
  }
}

// 创建实例
const egisDataServer = new EgisDataServer({
  baseURL: '/edss-egis/egis-wrms',
  clientId: '28524d8c65844630a3427270c9a16323',
  clientSecret: '84bc17650bb04491aa8475b9cbe3d1c4'
});

export default egisDataServer;
