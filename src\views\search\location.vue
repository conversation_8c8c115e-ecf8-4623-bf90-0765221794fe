<template>
  <div class="div_big">
    <van-nav-bar :title="detailInfo.eventTitle" left-text="返回" left-arrow @click-left="GoBack" @click-right="switchLayer">
      <template #right>
        <van-icon name="ellipsis" size="25" />
      </template>
    </van-nav-bar>
    <!-- 地图 start -->
    <div class="map" @click="guideLoc('gd')" v-if="locationInfo.getLocSuccess">
      <baseMap
        ref="baseMapRef"
        :mapId="`map_${new Date().getTime()}`"
        :longitude="locationInfo.locInfo.longitude"
        :latitude="locationInfo.locInfo.latitude"
      ></baseMap>
    </div>
    <!-- 地图 end -->
    <div class="layer-icon" @click="switchLayer">
      <van-icon name="points" size="24" />
      <div>图层</div>
    </div>
    <!-- 详情信息 -->
    <div class="footer-content">
      <searchInfo :result-info="detailInfo" type="event" @surround="toSurround" v-if="!surroundVisible"></searchInfo>
      <simplified :result-info="detailInfo" type="event" v-if="surroundVisible" @setLocation="resetLocation"></simplified>
    </div>

    <!-- 周边分析半径选择 -->
    <div class="radius-selector" v-if="showRadiusSelector">
      <div class="radius-title">选择搜索半径</div>
      <div class="radius-options">
        <div
          v-for="radius in radiusOptions"
          :key="radius"
          class="radius-option"
          :class="{ active: selectedRadius === radius }"
          @click="selectRadius(radius)"
        >
          {{ radius }}km
        </div>
      </div>
      <div class="radius-close" @click="closeRadiusSelector">关闭</div>
    </div>

    <!-- 图层面板 -->
    <van-popup v-model="layerShow" position="top" class="layer-popup">
      <div class="slider_div">
        <van-tabs type="card" v-model="current" :ellipsis="false" color="#1f4ae3" @change="changeTab">
          <van-tab v-for="(item, index) in tabArr" :key="index" :title="item.label"></van-tab>
        </van-tabs>
      </div>
    </van-popup>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import baseMap from '@/components/baseMap/baseMap.vue';
import Header from '@/views/common/header.vue';
import { Notify, Toast } from 'vant';
import { customBrowserVersion, guide } from '@/utils/getLocation';
import infoRequest from '@/api/webcross/infoRequest';
import searchInfo from './searchInfo.vue';
import simplified from './simplified.vue';
import layerUtils from '@/utils/gis/layerUtils';
import bufferUtils from '@/utils/gis/bufferUtils';
import egisDataServer from '@/api/service/EgisDataServer';

import danger from './danger.vue';
@Component({
  components: {
    baseMap,
    Header,
    searchInfo,
    simplified,
    danger
  }
})
export default class teamSignIn extends Vue {
  locationInfo: any = {
    getLocSuccess: false, // 获取当前位置是否成功
    locInfo: {
      latitude: 30.6402,
      longitude: 114.3889,
      address: '当前位置'
    } as any // 当前位置
  };
  detailInfo: any = {}; // 详情信息
  type: any = 'event';
  tabArr: any = [];

  // 图层切换
  layerShow: any = false;
  current: any = null; //图层

  // 周边分析相关
  surroundVisible: any = false;
  showRadiusSelector: boolean = false;
  radiusOptions: number[] = [1, 3, 5, 10];
  selectedRadius: number = 5; // 默认5公里
  bufferGeometry: any = null;

  // 图层统计数据
  layerStatistics: any = {};

  switchLayer() {
    this.layerShow = !this.layerShow;
  }

  GoBack() {
    history.back();
  }

  changeTab(index) {
    this.current = index;
    this.layerShow = false;

    // 获取选中的图层类型
    const selectedLayer = this.tabArr[index];
    if (selectedLayer) {
      this.loadLayerData(selectedLayer.key);
    }
  }

  // 操作类型
  active: any = null;
  handleType(type) {
    this.active = type;
    if (type == 'event') {
      this.layerShow = true;
    }
  }

  changeLayer(val, item) {
    this.layerShow = false;
    this.$router.push({
      path: `/layerList/${this.$route.query.eventId}`,
      query: {
        title: val.label,
        layer: item.key,
        type: val.key
      }
    });
  }

  created() {
    const eventId = this.$route.query.eventId;
    this.type = this.$route.query.type;
    this.getInfo(eventId);
    const data = require(`./allLayers.json`);
    this.tabArr = data.allLayers;
  }

  // 跳转周边
  toSurround() {
    this.surroundVisible = true;
    this.showRadiusSelector = true;

    // 默认创建5公里缓冲区
    this.createBuffer(this.selectedRadius);
  }

  // 选择搜索半径
  selectRadius(radius: number) {
    this.selectedRadius = radius;
    this.createBuffer(radius);
    this.showRadiusSelector = false;
  }

  // 关闭半径选择器
  closeRadiusSelector() {
    this.showRadiusSelector = false;
  }

  // 创建缓冲区并显示在地图上
  createBuffer(radius: number) {
    if (!this.locationInfo.getLocSuccess) {
      Notify({ type: 'warning', message: '无法获取位置信息' });
      return;
    }

    // 显示加载提示
    Toast.loading({
      message: '加载中...',
      forbidClick: true,
      duration: 0
    });

    try {
      // 创建缓冲区几何对象
      const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
      this.bufferGeometry = bufferUtils.createCircleBuffer(center, radius);

      // 在地图上显示缓冲区
      this.showBufferOnMap(center, radius);

      // 获取缓冲区内的图层统计数据
      this.getLayerStatistics();

      Toast.clear();
    } catch (error) {
      console.error('创建缓冲区失败:', error);
      Notify({ type: 'danger', message: '创建缓冲区失败' });
      Toast.clear();
    }
  }

  // 在地图上显示缓冲区
  showBufferOnMap(center: [number, number], radius: number) {
    // 获取地图实例
    const map: any = this.$refs.baseMapRef;
    if (!map || !map.egisMap) {
      console.warn('地图尚未初始化');
      return;
    }

    // 使用layerUtils显示缓冲区
    layerUtils.showBufferOnMap(center, radius, {
      fillColor: { r: 255, g: 0, b: 0, a: 30 }, // 半透明红色
      borderColor: { r: 255, g: 0, b: 0, a: 255 }, // 红色边框
      borderThickness: 2
    });

    // 添加中心点标记
    layerUtils.addEventToMap(center);
  }

  // 获取图层统计数据
  async getLayerStatistics() {
    try {
      // 获取所有图层的key
      const layerKeys: string[] = [];

      // 收集所有子标签的key
      this.tabArr.forEach((tab: any) => {
        if (tab.children && tab.children.length > 0) {
          tab.children.forEach((child: any) => {
            if (child.key) {
              layerKeys.push(child.key);
            }
          });
        } else if (tab.key) {
          layerKeys.push(tab.key);
        }
      });

      // 构建查询参数
      const params: any = {
        codes: layerKeys,
        filter: {
          districtCodes: '420100' // 默认使用武汉市的区划编码
        }
      };

      // 如果有缓冲区几何对象，添加到查询参数中
      if (this.bufferGeometry) {
        params.filter.geometry = this.bufferGeometry;
      }

      // 调用接口获取统计数据
      const result = await egisDataServer.getStatistics(params);

      // 更新统计数据
      if (result) {
        this.layerStatistics = result;
        console.log('统计数据结果:', result);
      }
    } catch (error) {
      console.error('获取图层统计数据失败:', error);
    }
  }

  // 加载图层数据
  async loadLayerData(layerKey: string) {
    if (!layerKey) return;

    try {
      Toast.loading({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      });

      // 构建查询参数
      const params: any = {
        code: layerKey,
        filter: {
          districtCodes: '420100' // 默认使用武汉市的区划编码
        }
      };

      // 如果有缓冲区几何对象，添加到查询参数中
      if (this.bufferGeometry) {
        params.filter.geometry = this.bufferGeometry;
      }

      // 调用接口获取数据
      const result = await egisDataServer.getLayerList(params);

      // 处理返回的数据
      if (Array.isArray(result) && result.length > 0) {
        // 在地图上显示点位
        this.showPointsOnMap(result, layerKey);

        Notify({
          type: 'success',
          message: `找到 ${result.length} 个点位`
        });
      } else {
        Notify({
          type: 'warning',
          message: `未找到相关数据`
        });
      }

      Toast.clear();
    } catch (error) {
      console.error('加载图层数据失败:', error);
      Notify({ type: 'danger', message: '加载数据失败，请稍后重试' });
      Toast.clear();
    }
  }

  // 在地图上显示点位
  showPointsOnMap(dataList: any[], layerKey: string) {
    // 清除之前的点位图层
    layerUtils.clearLayer();

    // 获取图标路径
    const iconName = layerKey.toLowerCase();
    const imageUrl = `img/marker/${iconName}.png`;

    // 在地图上显示点位
    layerUtils.showPointList(dataList, {
      useImage: true,
      imageUrl: imageUrl,
      width: 32,
      height: 32
    });

    // 添加点击事件
    layerUtils.addClickEvent(layerUtils.activeLayer, (data: any) => {
      // 点击点位后的处理逻辑
      console.log('点击了点位:', data);
      Notify({ type: 'success', message: `已选择: ${data.name || '未命名'}` });
    });
  }

  closeInfo() {
    return;
  }

  // 获取当前位置
  private guideLoc(signMap) {
    if (this.locationInfo.locInfo.latitude && this.locationInfo.locInfo.longitude) {
      //景点位置partnerAddress 景点经纬度lng lat
      var lng = this.locationInfo.locInfo.longitude;
      var lat = this.locationInfo.locInfo.latitude;
      let address = this.locationInfo.locInfo.address;
      let locInfo = {
        lng,
        lat,
        address
      };
      // guide('bd', locInfo);
    } else {
      Notify({ type: 'danger', message: '请先获取当前位置' });
    }
  }

  resetLocation(val) {
    this.locationInfo.locInfo.latitude = val.latitude;
    this.locationInfo.locInfo.longitude = val.longitude;

    // 如果正在进行周边分析，更新缓冲区
    if (this.surroundVisible) {
      this.createBuffer(this.selectedRadius);
    }
  }

  // 获取详情信息
  getInfo(id) {
    infoRequest.getEventById({ id: id }, (res) => {
      if (res.status === 200) {
        let data = res.data.data;
        this.locationInfo.locInfo.address = data.infoAddress;
        this.locationInfo.locInfo.latitude = data.latitude;
        this.locationInfo.locInfo.longitude = data.longitude;
        this.locationInfo.getLocSuccess = true;
        this.detailInfo = data;

        // 初始化时创建默认5公里缓冲区
        this.$nextTick(() => {
          // 创建默认缓冲区
          const center: [number, number] = [this.locationInfo.locInfo.longitude, this.locationInfo.locInfo.latitude];
          this.bufferGeometry = bufferUtils.createCircleBuffer(center, this.selectedRadius);

          // 获取统计数据
          this.getLayerStatistics();
        });
      }
    });
  }
}
</script>

<style scoped lang="less">
.div_big {
  width: 100%;
  height: 100%;
  position: relative;

  .map {
    width: 100%;
    height: 100%;
  }

  .layer-icon {
    position: fixed;
    right: 20px;
    top: 25%;
    width: 40px;
    padding: 5px 5px;
    text-align: center;
    border-radius: 10px;
    z-index: 999;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .tabs-box {
    position: fixed;
    top: 20px;
    left: 0;
    background: #fff;
    z-index: 999;
    padding: 10px;
  }

  // 周边分析半径选择器
  .radius-selector {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 8px;
    padding: 16px;
    width: 80%;
    max-width: 300px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;

    .radius-title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      text-align: center;
    }

    .radius-options {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-bottom: 16px;

      .radius-option {
        width: 48%;
        padding: 10px 0;
        margin-bottom: 8px;
        text-align: center;
        border: 1px solid #eee;
        border-radius: 4px;
        font-size: 14px;

        &.active {
          background-color: #ff8800;
          color: white;
          border-color: #ff8800;
        }
      }
    }

    .radius-close {
      text-align: center;
      padding: 10px 0;
      border-top: 1px solid #eee;
      color: #1a67f2;
      font-size: 14px;
    }
  }
}

.slider_div {
  padding: 20px 10px;
}

.layer-popup {
  width: 100%;
  height: auto;
  max-height: 80%;
  overflow-y: auto;
}

.planBg {
  background: #e8f1f5;
  color: #3e5dd2 !important;
}
</style>
