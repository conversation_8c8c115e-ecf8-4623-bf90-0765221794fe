<template>
  <div id="app">
    <div>
      <div class="next" @click="clicks('/form')">apphome</div>
      
    </div>
    
  </div>
</template>

<script>
export default {
  name: "appHome",
  data() {
    return {};
  },
  created(){
    console.log("apphome",this.$store)
  },
  methods: {
    clicks(url) {
      console.log("click form")
      this.$router.push({path: '/form' });
    }
  }
};
</script>

<style>
#app{
  overflow: hidden;
}
.next {
  width: 40%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background: blue;
  float: left;
  color: #fff;
  margin: 50px 5%;
  overflow: hidden;
}
</style>
