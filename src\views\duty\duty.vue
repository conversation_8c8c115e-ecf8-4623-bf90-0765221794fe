<template>
  <div class="div_big">
    <van-nav-bar title="值班排班" left-text="首页" right-text="我的值班" @click-right="showMyDuty"  left-arrow @click-left="GoBack">
      <!-- <template #right>
     <span class="right_show_duty" @click="showduty">
        <img src="../../assets/images/icons/calendar.png" alt="" srcset="">
      </span>
    </template> -->
    </van-nav-bar>
    <!-- 主体 -->
    <div class="main_box">
      <!-- <span class="Go_back" @click="GoBack">
        <van-icon size="18" style="vertical-align: middle;margin-top: -3px;padding-right: 3px;" name="arrow-left" />返回
      </span> -->
      <!-- <div v-show="weatherShowDuty" class="time_show_div" @click="monthRange()">
        <p>{{dutyShowDateStr}}</p>
        <span>
          <img src="../../assets/images/icons/calendar.png" alt="" srcset="">
        </span>
      </div> -->
      <!-- 日历 -->
      <div
        class="datePicker"
        @touchstart="moveClick"
        @touchmove="moveEv"
        @touchend="moveEnd"
      >
        <Calendar
          :now="false"
          :responsive="false"
          lunar
          clean
          @select="selected"
          @selectYear="selectYeared"
          @selectMonth="selectMonthed"
          @prev="preved"
          @next="nexted"
          :multi="false"
          :tileContent="tileContent"
          ref="calendar"
        />
      </div>
      <div class="other_org_btn" @click="showDutyOrgSelect = true">
        {{ dutyTitle ? dutyTitle : "其它相关机构单位" }}  <van-icon name="arrow-down" />
      </div>
      <!-- 值班内容 -->
      <div class="duty_box">
        <div>
          <ul class="duty_person_list" v-if="listArr && listArr.length > 0">
            <div class="person_class" v-for="(itemch, index) in listArr" :key="index">
              <span v-if="itemch.userName" class="type_class_title"
                >{{ itemch.groupName
                }}{{
                  itemch.groupName.includes("值班员") ? "(" + itemch.className + ")" : ""
                }}</span
              >
              <li v-if="itemch.userName">
                <span
                  >{{ itemch.userName }}
                  <span v-if="itemch.dutyType">
                    <img
                      v-if="itemch.dutyType == '1'"
                      class="class_type"
                      src="../../assets/images/icons/dayshift_ico.png"
                      alt=""
                      srcset=""
                    />
                    <img
                      v-else-if="itemch.dutyType == '2'"
                      class="class_type"
                      src="../../assets/images/icons/nightshift_ico.png"
                      alt=""
                      srcset=""
                    />
                    <img
                      v-else-if="itemch.dutyType == '3'"
                      class="class_type"
                      src="../../assets/images/icons/main_class_ico.png"
                      alt=""
                      srcset=""
                    />
                    <img
                      v-else-if="itemch.dutyType == '4'"
                      class="class_type"
                      src="../../assets/images/icons/viceclass_ico.png"
                      alt=""
                      srcset=""
                    /> </span
                ></span>
                <!-- <span @click="openPhone(itemch.dutyPhone)"><img v-if="itemch.dutyPhone" src="../../assets/images/icons/tel_btn.png" alt="" srcset=""><img v-else src="../../assets/images/icons/tel_btn_gray.png" alt="" srcset=""></span> -->
                <span v-if="itemch.telNumber">{{ itemch.telNumber }}</span>
                <span @click="openPhone(itemch.telNumber)"
                  ><img src="../../assets/images/icons/phoneBtn.png" alt="" srcset=""
                /></span>
              </li>
            </div>
          </ul>
          <div v-else>
            <div class="list_item_empty2"></div>
          </div>
        </div>
        <!-- 无数据状态 -->
        <!-- <div v-show="listArr.length == 0" class="list_item_empty2"></div> -->
      </div>
    </div>
    <van-popup
      v-model="showDutyOrgSelect"
      v-show="showDutyOrgSelect"
      position="bottom"
      :style="{ height: '80%', width: '100%' }"
    >
      <!-- <dutyOrgSelect  :requestObj="requestObj" @close="closeInfo"/> -->
      <!-- <menuLinkage ref="menuLinkage" @queryChild="queryChild" @queryChildNew="queryChildNew" @setSelectObj="setSelectObj"  type="single" :options="orgList"/> -->
      <orgTree :options="orgList" @handleOrg="handleOrg"></orgTree>
    </van-popup>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import Calendar from "../mpvueCalendar/mpvue-calendar.vue";
import "../mpvueCalendar/browser-style.css";
import apiServer from "../../api/request-service";
import dutyOrgSelect from "./dutyOrgSelect.vue";
import orgTree from "@/views/common/orgTree.vue";
import { Notify } from 'vant';

@Component({
  name: "dynamic",
  components: {
    Calendar,
    dutyOrgSelect,
    orgTree,
  },
})
export default class dynamic extends Vue {
  private value = "";
  //DOM切换
  private dataList = "";
  private openSimple = false;
  private weatherShowDuty = true;
  private dutyShowDateStr = "";
  private org_code = ""; //组织机构
  private listArr = [];
  private dutyTitle = ""; //标题
  private requestObj = {}; //传选择页面参数
  private showDutyOrgSelect = false;
  private showMonth; // 当前的显示月份
  private showYear;  // 当前的年份
  private isCreate = true;
  private allDuty = []; //有值班信息的集合
  private dataStr = "";
  private dutyInfo = {}; //
  private orgObj = {}; //组织机构信息
  private tileContent = [
    // {date: '2023-8-22', className: 'holiday', content: ''},
    // {date: '2023-8-10', className: 'holiday', content: ''},
    // {date: '2023-8-24', className: 'holiday', content: ''}
  ];
  //时间选择器
  private year = "";
  private mounth = "";
  private year_mounth = "";
  private selected_date = "";
  private selected_date_other = "";
  //组织选择期
  private orgList = [];
  private pageXI = 0;
  private pageYI = 0;
  private swcount = 0;
  private defaultProps = {
    children: "children",
    label: "org_name",
  };
  private baseurl = "";
  showduty() {
    console.log("显示");
    this.weatherShowDuty = !this.weatherShowDuty;
  }
  setSelectObj(_obj) {
    console.log("select Obj-------", _obj);
    let orgObj = _obj[0];
    this.closeInfo({ orgcode: orgObj.code, orgname: orgObj.name });
    //this.showDutyOrgSelect=false;
  }
  openPhone(_phone) {
    console.log("phoneNum =>" + _phone);
    window.location.href = `tel://${_phone}`;
    // if(_phone){
    //   _phone = _phone.replace(/-/g,"");//提取字符串中的数据
    //   //console.log("直接输出："+numArr) // => ["123", "456", "789"]
    //   console.log("call phone =>"+_phone)
    //   this.theAndroidH(0,_phone)
    // }else{
    //   this.$toast("暂无电话号码")
    // }
  }
  closeInfo(_value) {
    let _this = this;
    if (_value && _value.orgcode) {
      // 赋值组织机构
      this.org_code = _value.orgcode;
      _this.requestOrg();
      this.requestObj = { org_code: _value.orgcode, org_name: _value.orgname };
      this.mounthnamic_list();
    }
    _this["$gsmdp"].initGoback(function () {
      console.log("调用返回");
      _this.GoBack();
    });
    this.showDutyOrgSelect = false;
  }
  queryChild(obj) {
    let _this = this;
    console.log("调用", obj.id);
    apiServer.findOrgChildrenById({ orgCode: obj.id }, function (res) {
      console.log("res===>-----", res);
      if (res.data) {
        // console.log(JSON.stringify(res.data.data) )
        let orglist = res.data.data;
        _this.$refs.menuLinkage["handleList"](orglist, obj.checked);
        // _this.$refs.menuLinkage['initData'](_this.orgList);
      }
    });
  }

  handleOrg(value) {
    this.org_code = value.orgCode;
    this.dutyTitle = value.orgName;
    this.showDutyOrgSelect = false;
    this.mounthnamic_list();
  }

  openSelectOrg() {
    let _this = this;
    _this["$api"].InfoRequest.getCurrentUserTreeListNew(
      { orgCode: JSON.parse(localStorage.getItem("role")).orgCode },
      (res) => {
        const data = JSON.parse(res.data.data);
        _this.orgList = [data];
        _this.requestObj["org_name"] = data.orgName;
        _this.requestObj["org_code"] = data.orgCode;
      }
    );
    // this.showDutyOrgSelect=true;
  }

  setDateTitle(datastr) {
    let _this = this;
    let dateArr = datastr.split("-");
    if (dateArr.length == 2) {
      _this.dataStr = dateArr[0] + "年" + dateArr[1] + "月";
    } else {
      _this.dataStr = dateArr[0] + "年" + dateArr[1] + "月" + dateArr[2] + "日";
    }
  }

  //点击日期
  private selected(val, val2) {
    let mounth = val[1];
    if (mounth < 9) {
      mounth = "0" + mounth;
    }
    let day = val[2];
    if (day < 9) {
      day = "0" + day;
    }
    // this.value = val2.date;
    let value = val[0] + "-" + mounth + "-" + day;
    //点击页面日历显示详情
    this.showInfo(value);
    this.mounthnamic_list();
  }

  showInfo(value) {
    let _date = new Date(value);
    let weekindex = _date.getDay();
    this.selected_date = value;
    this.weatherShowDuty = false;
    this.year_mounth = value;
    this.listArr = [];
    try {
      this.allDuty.forEach((element) => {
        if (value == element.date) {
          this.listArr = element.dutyArray;
          console.log(this.listArr);
          throw new Error("break");
        }
      });
    } catch (error) {
      console.log(error);
    }
  }

  private monthRange() {
    let _this = this;
    //debugger
    _this.$refs.calendar["changeYear"]();
  }

  //点击年份
  private selectYeared(y) {
    //this.year_mounth = y + '-' + this.mounth;
    this.dutyShowDateStr = y + "-" + this.mounth;
    //this.mounthnamic_list();
  }

  //点击月份
  private selectMonthed(m, y) {
    if (m.toString().length == 1) {
      m = "0" + m;
    }
    this.mounth = m;
    this.dutyShowDateStr = y + "-" + m;
    // this.year_mounth = y + '-' + m;
    // this.mounthnamic_list();
  }

  //上一月
  private preved(y, m, W) {
    console.log("上个月", y, m, W);
    this.showMonth =m;
    this.showYear = y;
    this.tileContent =[];
    // if(m.toString().length==1){
    //   m="0"+m;
    // }
    // this.year_mounth = y + '-' + m;
    // this.mounthnamic_list();
  }

  //下一月
  private nexted(y, m, W) {
    console.log("下个月", y, m, W);
     this.showMonth =m;
      this.showYear = y;
     this.tileContent =[];
    // if(m.toString().length==1){
    //   m="0"+m;
    // }
    // this.year_mounth = y + '-' + m;
    // this.mounthnamic_list();
  }

  private openOrg() {
    this.openSimple = true;
  }

  private handleNodeClick(data) {
    //组织机构
    this.openSimple = false;
    this.org_code = data.emorgid;
    //交互
  }

  private getNowYearAndMounth() {
    let _this = this;
    this.year_mounth = this.$formatTimeStr(new Date(), "1");

    this.showMonth = new Date().getMonth() *1 +1;
     this.showYear = new Date().getFullYear();

    this.dutyShowDateStr = this.year_mounth.substring(
      0,
      this.year_mounth.lastIndexOf("-")
    );
    _this.mounthnamic_list();
  }

  private dataHandle(_dataArr) {
    let _this = this;
    _this.allDuty = []; //清空当月值班信息
    _this.tileContent = [];
    //处理后台接口返回
    if (_dataArr && _dataArr.length > 0) {
      _dataArr.forEach((item, index) => {
        if (item.dataFlag == "1" && item.draftFlag != "1") {
          _this.allDuty.push(item);
          let dateStr = this.$formatTimeToZero(item.date, 1);
          let obj = { date: dateStr, className: "workday", content: " " };
          _this.tileContent.push(obj);
        }
      });
      console.log("节假日--->", _this.tileContent);
      if (_this.isCreate) {
        let myDate = new Date();
        let _days = myDate.getDate().toString();
        if (_days.length == 1) {
          _days = "0" + _days;
        }
        _this.showInfo(_this.year_mounth);
        _this.isCreate = false;
      }
      //debugger
      //todo下面代码为解决切换数据不刷新问题 待优化
      _this.$refs.calendar["changeMonth"](new Date().getMonth() - 1);
      setTimeout(() => {
        _this.$refs.calendar["changeMonth"](new Date().getMonth());
      }, 100);
    }
  }

  theAndroidH(_type, _phoneNum) {
    let _this = this;
    if (_type == 0) {
      _this["$gsmdp"].makePhoneCall({
        phoneNumber: _phoneNum,
      });
    } else if (_type == 1) {
      _this["$gsmdp"].sendMessage({
        data: {
          phoneNumber: _phoneNum,
          message: "", //仅为示例，并非真实的短信内容
          sms: false,
        },
      });
    }
  }

  GoBack() {
    this.$router.go(-1);
  }

  showMyDuty(){
    console.log(this.year_mounth);

    // showMonth
    
    var curDate = new Date();
	// 获取当前月份
	// 将日期设置为32，表示自动计算为下个月的第几天（这取决于当前月份有多少天）
    curDate.setDate(32);
    // 返回当前月份的天数
    const month = this.showMonth  < 10 ? '0' + Number( this.showMonth): this.showMonth; 
    const days = new Date(this.showYear,this.showMonth,0).getDate()

    let startDate =  this.showYear +'-'+ month+'-' +'01';
    let endDate = this.showYear +'-'+month+'-'+ days;
    this.getMyDuty(startDate,endDate)
  }


  getMyDuty(startDate,endDate){
    let role = JSON.parse(localStorage.getItem("role"))
     let tenantId = role.tenantId;
     let mobile = role.cellphone;
     let name = role.name

     let param = {
      // date:arr[0]+"-"+arr[1],
      startDate: startDate,
      endDate: endDate,
      orgCode: this.org_code,
      dutyType: tenantId.split(".").length > 2 ? "GP" : "EP",
    };
    this["$api"].InfoRequest.findDutyInfoByDateNew(param, (res) => {
      const data = JSON.parse(res.data.data);
      let total = [];
      data.forEach(el=>{
        total = total.concat(el)
      });
      total.forEach((ele)=>{
        ele.data.forEach((child,index)=>{
          if(child.userName == name||
            child.telNumber == mobile
          ){
              this.tileContent.push({
                date:this.$formatTimeToZero(child.date, 1)  ,
                className: 'myduty',
                 content: ''
              })
          }
        })
      });
      if(!this.tileContent.length){
        Notify('本月暂无排班')
      }
    });
  }

  private filterArray(data, id) {
    //组织机构的递归
    var fa = function (parentid) {
      var _array = [];
      for (var i = 0; i < data.length; i++) {
        var n = data[i];
        if (n.parentcode === parentid) {
          n.children = fa(n.emorgid);
          _array.push(n);
        }
      }
      return _array;
    };
    return fa(id);
  }
  private clearTree(obj) {
    this.openSimple = false;
    this.org_code = "";
    //交互
  }
  private mounthnamic_list() {
    let _this = this;
    _this.isCreate = true;
    let tenantId = JSON.parse(localStorage.getItem("role")).tenantId;
    let currtime = _this.$formatTimeStr();
    let arr = _this.year_mounth.split("-");
    let param = {
      // date:arr[0]+"-"+arr[1],
      startDate: _this.year_mounth,
      endDate: _this.year_mounth,
      orgCode: _this.org_code,
      dutyType: tenantId.split(".").length > 2 ? "GP" : "EP",
    };
    this["$api"].InfoRequest.findDutyInfoByDateNew(param, (res) => {
      const data = JSON.parse(res.data.data);
      data[0].forEach((item) => {
        if (item.date === _this.year_mounth) {
          _this.listArr = item.data;
        }
      });
    });
  }
  //取月份数据遍历方法
  private sort(data) {}
  moveEv(e) {
    this.swcount++;
    if (this.swcount > 5) {
      let pageY = e.changedTouches[0].pageY;
      //console.log("yvalue=>"+(pageY-this.pageYI)*4)
      let pageX = e.changedTouches[0].pageX;
    }
  }

  moveEnd(e) {
    let _this = this;
    let pageX = e.changedTouches[0].pageX;
    console.log(pageX + "===>" + this.pageXI);
    /*左右滑*/
    if (pageX + 100 < this.pageXI) {
      console.log("进入右滑");
      //this.NextMonth();
      _this.$refs.calendar["next"]();
      this.swcount = 0;
    } else if (pageX > this.pageXI + 100) {
      console.log("进入左滑");
      _this.$refs.calendar["prev"]();
      //this.PreMonth();
      this.swcount = 0;
    }
  }

  moveClick(e) {
    this.swcount = 0;
    console.log("moveClick==>" + e.changedTouches[0].pageX);
    this.pageXI = e.changedTouches[0].pageX;
    this.pageYI = e.changedTouches[0].pageY;
  }

  requestOrg() {
    let _this = this;
    let params = {
      orgCode: _this.org_code,
    };
    apiServer.findOrgObByCode(params, function (res) {
      console.log("org info==>", res);
      if (res.data.data) {
        _this.orgObj = res.data.data;
        _this.dutyTitle = res.data.data.fullOrgName;
        console.log(_this.orgObj);
      }
    });
  }

  scrolldiv() {
    if (this.weatherShowDuty) {
      this.weatherShowDuty = false;
    }
  }

  private created() {
    let _this = this;
    const role = localStorage.getItem("role");
    _this.org_code = JSON.parse(role).orgCode;
    _this.dutyTitle = JSON.parse(role).orgName;
    _this.$enJsBack();
    _this.requestOrg();
    _this.getNowYearAndMounth();
    setTimeout(function () {
      _this.weatherShowDuty = false;
    }, 100);
  }

  mounted() {
    let _this = this;
    _this.openSelectOrg();
    _this["$gsmdp"].initGoback(function () {
      console.log("调用返回");
      _this.GoBack();
    });
  }

  destroyed() {
    // 离开该页面需要移除这个监听的事件，不然会报错
    window.removeEventListener("scroll", this.scrolldiv);
  }
}
</script>
<style scoped lang="scss">
.div_big {
  [class*="van-hairline"]::after {
    border: none;
  }
  .datePicker,
  .time_show_div {
    width: 100%;
    // position: absolute;
    z-index: 100;
  }
  .datePicker {
    // height: 50vh;
    .mpvue-calendar {
      background: #058bf6;
    }
  }
  .right_show_duty {
    width: 100%;
    text-align: right;
    padding-top: 5px;
    img {
      width: 50%;
    }
  }
  // height: 667px;
  .other_org_btn {
    width: 100%;
    height: 8vh;
    line-height: 8vh;
    text-align: center;
    font-size: 16px;
    // position: absolute;
    // bottom: 0px;
    background: #1f4ae3;
    color: #ffffff;
  }
  .main_box {
    // height: calc( 100% - 8vh );
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    margin: 0;
    background: #f4f7f8;
    .list_item_empty2 {
      margin-top: 20px;
    }
    .div_duty_info {
      width: 90%;
      height: 23vh;
      margin: 2.6vw 5% 0px 5%;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-image: url("../../assets/images/icons/card_duty.png");
      .div_duty_info_header {
        width: 100%;
        line-height: 30px;
        padding-top: 10px;
        span {
          display: inline-block;
        }
        span:nth-of-type(1) {
          width: 50%;
          color: #ffffff;
          font-size: 16px;
          font-weight: bold;
          padding-left: 10px;
        }
        span:nth-of-type(2) {
          width: 50%;
          text-align: right;
          color: #70bfff;
          font-size: 14px;
          padding-right: 10px;
        }
      }
      .div_duty_info_body {
        width: 100%;
        height: calc(23vh - 45px);
        padding-top: 10px;
        overflow-x: auto;
        .info_item_big {
          height: 100%;
          white-space: nowrap;
          height: 100%;
          box-sizing: border-box;
          padding-left: calc(30vw / 2);
          padding-top: 0.2rem;
          .info_item_big_item {
            display: inline-block;
            width: 30vw;
            height: calc(23vh - 65px);
            span:nth-of-type(1) {
              width: 100%;
              display: inline-block;
              height: 50%;
              padding: 0px 25%;
              img {
                width: 76%;
                margin-left: 12%;
              }
            }
            span:nth-of-type(2) {
              width: 100%;
              height: 23%;
              text-align: center;
              color: white;
              display: block;
              font-size: 15px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            span:nth-of-type(3) {
              width: 100%;
              height: 23%;
              text-align: center;
              color: white;
              display: block;
              font-size: 15px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    .duty_person_list {
      width: 100%;
      padding-left: 5%;
      .person_class {
        > span {
          display: block;
          line-height: 45px;
          width: 90%;
          font-size: 16px;
        }
        .div_line {
          width: 90%;
          margin-left: 5%;
          height: 1px;
          border-top: #c9c9c9 solid 1px;
        }
        > li {
          width: 95%;
          height: 6vh;
          line-height: 6vh;
          background: white;
          border-radius: 5px;
          position: relative;
          font-size: 15px;
          > span {
            display: inline-block;
            height: 6vh;
            img {
              height: 4vh;
              vertical-align: middle;
            }
          }
          .class_type {
            height: 2vh;
            vertical-align: middle;
          }

          > span:nth-of-type(1) {
            width: 52%;
            padding-left: 5%;
            float: left;
          }
          // >span:nth-of-type(2){
          //   width: 15%;
          //   padding-left: 5%;
          //   position: relative;
          // }
          // >span:nth-of-type(2):after{
          //   position: absolute;
          //   top: 20%;
          //   right: -29%;
          //   content: '';
          //   width: 1px;
          //   height: 60%;
          //   border-right: #c9c9c9 solid 1px;
          // }
          > span:nth-of-type(3) {
            padding-left: 10%;
            width: 15%;
          }
        }
      }

      // >li:before{
      //   position: absolute;
      //   top: 1px;
      //   left: 0;
      //   content: '';

      // }
    }
    .div_date_title {
      width: 100%;
      padding-left: 10px;
      color: #1f49e4;
      font-size: 20px;
      line-height: 50px;
    }
    .div_cell_title {
      width: calc(100% - 5px);
      margin-left: 10px;
      line-height: 50px;
      border-top: 0.1px solid #c9c9c9;
      span {
        display: inline-block;
      }
      .div_cell_title_left {
        width: 20%;
      }
      .div_cell_title_right {
        width: 20%;
      }
    }
    .time_show_div {
      width: 100%;
      height: 8vh;
      background: #1541e2;
      color: white;
      position: relative;
      p {
        margin: 0;
        font-size: 20px;
        height: 100%;
        line-height: 8vh;
        text-align: center;
      }
      span {
        width: 8vw;
        height: 8vw;
        position: absolute;
        top: 2.66667vw;
        right: 15px;
        img {
          width: 30px;
          height: 30px;
        }
      }
    }
  }
  .duty_box {
    width: 100%;
    padding-bottom: 0.5rem;
    margin-bottom: 50px;
    p {
      padding: 5px 10px;
      span:nth-of-type(1) {
        font-size: 18px;
      }
    }
    .duty_div {
      width: 100%;
      background: #fff;
      padding: 8px 20px;
      margin: 8px 0;
      font-size: 15px;
      .time {
        height: 40px;
        line-height: 40px;
        font-weight: bold;
      }
      .duty_ul {
        padding: 5px 0;
        border-top: 1px solid #ddd;
        li {
          height: 25px;
          line-height: 25px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    .duty_singgle {
      width: 100%;
      background: #fff;
      padding: 2px 20px;
      margin: 0;
      li {
        height: 30px;
        line-height: 30px;
        font-size: 17px;
        margin-bottom: 5px;
        font-family: "微软雅黑";

        .duty_person_info {
          width: 82%;
          float: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .duty_person_info_empty {
          width: 92%;
          float: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .duty_person_info_empty_title {
          width: 26%;
          float: left;
        }
        .duty_person_info_empty_content {
          width: 70%;
          float: left;
        }
        span {
          color: #4277bf;
          display: inline-block;
        }
        > span:nth-of-type(1) {
          width: 25px;
          float: left;
        }
        > span:nth-of-type(2) {
          width: 25px;
          margin-left: 5px;
        }
        img {
          width: 25px;
          vertical-align: middle;
          margin-top: -4px;
        }
      }
    }
  }
}

/deep/ .mpvue-calendar td.myduty{
  color: pink;
  background: #1a66f2;
  position: relative;
  &:after{
    position: absolute;
    left: 0;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    // background: red;
    content: '班';
    align-items: center;
    font-size: 10px;
    text-align: center;
    line-height: 20px;
  }
}
</style>
