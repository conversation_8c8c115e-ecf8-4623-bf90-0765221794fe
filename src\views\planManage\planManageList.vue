<template>
  <div class="div_big">
    <van-nav-bar title="预案" left-text="返回" left-arrow @click-left="GoBack" @click-right="showPopups">
      <template #right>
        <van-icon name="ellipsis" size="25" />
      </template>
    </van-nav-bar>
    <!-- invoke common List -->

    <!-- <commonList :loadObj="{name:'planItem'}" :tabArr="tabData" :searchObj="{whetherHave:true,placeholder:'请输入搜索关键词'}"></commonList> -->

    <!-- 内容 {{ index }} -->
    <commonList
      ref="slider"
      :loadObj="{ name: 'planManageItem' }"
      :tabArr="tabData"
      :searchObj="{ whetherHave: true, placeholder: '请输入搜索关键词' }"
      :sliderType="sliderType"
      @changeType="changeType"
    ></commonList>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import commonList from '../common/commonList.vue';
import apiServer from '../../api/request-service';
/**
 * {"code":"10000","name":"总体预案","currentIndex":1,"currentSearch":""},
    {"code":"20000","name":"专项预案","currentIndex":1,"currentSearch":""},
    {"code":"30000","name":"部门预案","currentIndex":1,"currentSearch":""},
    {"code":"40000","name":"企业预案","currentIndex":1,"currentSearch":""}
 */
@Component({
  components: {
    commonList
  }
})
export default class planManageList extends Vue {
  private sliderType = {
    planType: [],
    eventType: []
  };

  changeType(idx, name) {
    let _this = this;
    _this.tabData.forEach((item, index) => {
      if (name == item.label) {
        item.children.forEach(element => {
          item.checked=false;
        });
        _this.sliderType.planType=item.children
      }
    });
      _this.getEventType()
  }
  showPopups() {
    console.log(this.$refs.slider);
    this.$refs.slider['showPopup']();
  }
  tabData = [
    // { code: '10000', name: '总体预案', currentIndex: 1, currentSearch: '' },
    // { code: '20000', name: '专项预案', currentIndex: 1, currentSearch: '' },
    // { code: '30000', name: '部门预案', currentIndex: 1, currentSearch: '' },
    // { code: '40000', name: '企业预案', currentIndex: 1, currentSearch: '' }
  ];
  selected: any = 0;
  refreshing: any = false;
  loading: any = false;
  showDetail: any = false;
  pageIndex: any = 1;
  pageSize: any = 10;
  keyword: any = '';
  open: any = false;
  position: any = 'left';
  queryMap: any = {};

  onClickLeft() {
    console.log('返回点击');
  }
  clickMore() {
    console.log('打开菜单');
    this.open = true;
  }
  handleObj(item) {
    let _this = this;
    console.log(item);
    if (!item.uploadMore) {
      _this.refreshing = false;
      _this.loading = false;
    }
  }
  refresh() {
    let _this = this;
    _this.refreshing = true;
    _this.pageIndex = 1;
    let pageObj = { pageIndex: 1, pageSize: _this.pageSize };
    _this.$refs.itemRef['queryRequest'](pageObj);
  }
  load() {
    const _this = this;
    _this.pageIndex++;
    _this.loading = true;
    let pageObj = { pageIndex: _this.pageIndex, pageSize: _this.pageSize };
    _this.$refs.itemRef['queryRequest'](pageObj);
  }
  GoBack() {
    this.$router.push('/')
  //  let _this=this;
  //   try {
  //       _this['$gsmdp'].goBack(function(){
  //         console.log("Gback")
  //       })
  //   } catch (error) {
  //       console.log("close error")
  //   }
  }
  closeDetail() {
    this.showDetail = false;
  }
  private planTypes = [];
  initTab() {
    let _this = this;
    let param = {};
    apiServer.findPlanManageMenu(
      param,
      function(res) {
        let arr = res.data.data;
        arr.forEach((item, index) => {
          item.name = item.label;
          item.code = item.id;
        });
        _this.tabData = arr;
        _this.tabData[0].children.forEach(element => {
          element.checked=false;
        });
        _this.sliderType.planType = _this.tabData[0].children;
      },
      true
    );
  }
    getEventType() {
    let _this = this;
    let param = {};
    apiServer.findEventType(
      param,
      function(res) {
        let arr = res.data.data.treeData;
         arr.forEach(element => {
          element.checked=false;
        });
       _this.sliderType.eventType  = arr;
     
      },
      true
    );
  }
  created() {
    let _this = this;
    _this.initTab();
    _this.getEventType()
    _this.$enJsBack();
  }
 
  mounted() {
    let _this = this;
    _this['$gsmdp'].initGoback(function() {
      console.log('调用返回');
      _this.GoBack();
    });
    //  this.$gsmdp.initGoback(function(){
    //   _this.GoBack()
    // })
  }
}
</script>
<style scoped lang="scss">
.div_big {
  width: 100%;
  height: 100%;
  .search_div {
    width: 100%;
  }
}
.content_list {
  height: calc(100% - 46px);
  overflow: auto;
  background: #f1efef;
}
.showMenu {
  height: 30px;
  position: absolute;
  right: 0px;
  z-index: 20;
  top: -0.2vw;
  color: white;
}
</style>
