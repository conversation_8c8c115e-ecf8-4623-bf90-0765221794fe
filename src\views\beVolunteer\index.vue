<template>
  <div class="beVolunteer">
    <Header title="报名志愿者"></Header>
    <div class="container">
      <div class="beVolunteer-content">
        <span class="beVolunteer-content-title">湖北省武汉市汛期现场救援志愿者报名</span>
        <div class="beVolunteer-content-reminder">
          <i class="ico ico-reminder"></i>
          完成登记后经审批派发应急速达码方可进入现场区域
        </div>
        <van-form @submit="onSubmit">
          <div class="beVolunteer-content-item">
            <div class="beVolunteer-content-item-title">
              <i class="ico ico-realname"></i>
              实名认证
            </div>
            <van-cell-group inset>
              <van-field input-align="right" 
                v-model="applyInfo.name" 
                label="姓名" 
                name="name"
                placeholder="请输入姓名" 
                :rules="rules.name" />
              <van-field input-align="right" 
                v-model="applyInfo.idcard" 
                label="身份证号" 
                name="idcard"
                placeholder="请输入身份证号码" 
                :rules="rules.idcard" />
              <van-field input-align="right" 
                v-model="applyInfo.tel" 
                name="tel" 
                label="手机号" 
                placeholder="请输入手机号码" 
                :rules="rules.tel" />
              <van-field input-align="right"
                v-model="applyInfo.verifyCode"
                center
                clearable
                label="验证码"
                name="verifyCode" 
                placeholder="请输入验证码" :rules="[{ required: true, message: '请输入验证码' }]"
              >
                <template #button>
                  <van-button size="small" type="primary">获取验证码</van-button>
                </template>
              </van-field>
            </van-cell-group>
          </div>
          <div class="beVolunteer-content-item">
            <div class="beVolunteer-content-item-title">
              <i class="ico ico-register"></i>
              参与救援登记
            </div>
            <van-cell-group inset>
              <van-field input-align="right" 
                v-model="applyInfo.unitName" 
                label="姓名" 
                name="unitName" 
                placeholder="请输入单位名称"
                :rules="[{ required: true, message: '请输入单位名称' }]" />
              <van-field input-align="right" 
                v-model="applyInfo.arriveNum" 
                label="抵达人数" 
                name="arriveNum" 
                placeholder="请输入抵达人数"
                :rules="rules.arriveNum" />
              <van-field input-align="right" 
                v-model="applyInfo.contact" 
                name="contact" 
                label="联系人" 
                placeholder="请输入联系人姓名"
                :rules="[{ required: true, message: '请输入联系人姓名' }]" />
              <van-field input-align="right" 
                v-model="applyInfo.contactTel" 
                type="tel" 
                name="contactTel" 
                label="联系方式" 
                placeholder="请输入联系人电话"
                :rules="rules.tel" />
            </van-cell-group>
          </div>
          <div class="beVolunteer-content-item">
            <van-cell-group inset>
              <van-field class="column-form"
                v-model="applyInfo.experience"
                rows="4"
                name="experience"
                label="救援经历"
                type="textarea"
                placeholder="请输入参加过社会性救援类型及次数等相关内容"
                :rules="[{ required: true, message: '请输入参加过社会性救援类型及次数等相关内容' }]"
              />
            </van-cell-group>
          </div>
          <div class="beVolunteer-content-item">
            <van-cell-group inset>
              <van-field class="column-form"
                v-model="applyInfo.teamDesc"
                rows="4"
                name="teamDesc"
                label="队伍描述"
                type="textarea"
                placeholder="请输入队伍人员组成，男女人数，成员平均年龄等相关内容"
                :rules="[{ required: true, message: '请输入队伍人员组成，男女人数，成员平均年龄等相关内容' }]"
              />
            </van-cell-group>
          </div>
          <div class="beVolunteer-content-item">
            <van-cell-group inset>
              <van-field class="column-form"
                v-model="applyInfo.goods"
                rows="4"
                name="goods"
                label="携带物资装备"
                type="textarea"
                placeholder="请输入携带的物资装备"
                :rules="[{ required: true, message: '请输入携带的物资装备' }]"
              />
            </van-cell-group>
          </div>
          <div class="beVolunteer-content-item">
            <van-cell-group inset>
              <van-field class="column-form"
                v-model="applyInfo.remark"
                rows="4"
                name="remark"
                autosize
                label="备注"
                type="textarea"
                placeholder="请输入备注信息"
                :rules="[{ required: true, message: '请输入备注信息' }]"
              />
            </van-cell-group>
          </div>
          <div class="submit-btn">
            <van-button block type="primary" native-type="submit">
              确认提交
            </van-button>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
@Component({
  components: {
    Header,
  }
})
export default class beVolunteer extends Vue {
  private patternPhone:any = /1[3456789]\d{9}/; // 电话号码正则
  private applyInfo:any = {
    name: '',
    idcard: '',
    tel: null,
    verifyCode: null,
    unitName: '',
    arriveNum: null,
    contact: '',
    contactTel: null,
    experience: '',
    teamDesc: '',
    goods: '',
    remark: ''
  }
  private rules:any = {
    name: [
      { required: true, message: '请输入姓名'},
      { pattern: /^[a-zA-Z\u4e00-\u9fa5]{2,10}$/, message: '请输入正确的姓名' }
    ],
    idcard: [
      { required: true, message: '请输入身份证号码'},
      { pattern: /(^\d{15}$)|(^\d{17}([0-9]|X)$)/, message: '请输入正确的身份证号码' }
    ],
    tel: [
      { required: true, message: '请输入手机号'},
      { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号' }
    ],
    arriveNum: [
      { required: true, message: '请输入抵达人数'},
      { pattern: /^\d{0,10}$/, message: '请输入数字' }
    ],
  }
  private onSubmit(values) {
    console.log('submit', values);
    this.$router.push('/beVolunteer/applySuccess')
  }
  private onFailed(errorInfo:any) {
  };
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images';
.beVolunteer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f5f6f6;
  span {
    display: inline-block;
  }
  .container {
    flex: 1;
    width: 100%;
    padding: 0;
    overflow: auto;
    .beVolunteer-content {
      color: #333;
      &-title {
        width: 100%;
        height: 64px;
        line-height: 64px;
        padding: 0 12px;
        font-size: 18px;
        background-color: #fff;
      }
      &-reminder {
        display: flex;
        align-items: center;
        height: 40px;
        padding: 0 10px;
        font-size: 14px;
        color: #f89142;
        background-color: #fff7f0;
        .ico-reminder {
          width: 14px;
          height: 14px;
          background: url("@{url}/volunteer/ico-reminder.png") center no-repeat;
          background-size: cover;
        }
      }
      &-item {
        margin-bottom: 10px;
        padding: 0 10px;
        &-title {
          display: flex;
          align-items: center;
          padding: 15px 12px 14px 12px;
          font-size: 18px;
          .ico {
            width: 20px;
            height: 20px;
            &.ico-realname {
              background: url("@{url}/volunteer/ico-realnam.png") center no-repeat;
            }
            &.ico-register {
              background: url("@{url}/volunteer/ico-register.png") center no-repeat;
            }
          }
        }
        /deep/.van-field__button {
          .van-button--primary {
            background-color: #f5f6f6;
            border-color: #f5f6f6;
            color: #326eff;
            border-radius: 4px;
          }
        }
        /deep/textarea.van-field__control {
          height: 92px;
          min-height: 92px;
        }
        .column-form {
          flex-direction: column; 
          border: none;
          /deep/.van-cell__title {
            width: 100%;
            padding-bottom: 12px;
            border-bottom: 1px solid #fafafa;
          }
          /deep/.van-cell__value {
            padding-top: 12px;
          }
        }
      }
    }
    .submit-btn {
      width: 100%;
      background: #fff;
      // margin-top: 10px;
      padding: 16px;
      /deep/.van-button {
        background: #326eff;
      }
    }
  }
  .ico {
    display: inline-block;
    margin-right: 7px;
    // width: 20px;
    // height: 20px;
    background-size: contain!important;
  }
  /deep/.van-field__error-message {
    text-align: right;;
  }
}
</style>