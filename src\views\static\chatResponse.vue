<template>
  <div class="chatResponse">
    <!-- <Header title="快速响应"></Header> -->
    <div class="chatResponse-content">
      <div class="chat-div">
        <div class="chat-div-item">
          <img class="org-avatar" :src="require('@/assets/images/static/avatar.png')" alt="">
          <div class="team">
            <span class="team-title">现场指挥部</span>
            <img :src="require(`@/assets/images/static/${id}.png`)" alt="">
          </div>
        </div>
        <span class="time">{{time}}</span>
        <div class="chat-div-item">
          <img class="org-avatar" :src="require('@/assets/images/static/avatar.png')" alt="">
          <div class="team">
            <span class="team-title">现场指挥部</span>
            <img :src="id.indexOf('ss_') === 0 ? require('@/assets/images/static/ss_task.png') : require('@/assets/images/static/task.png')" alt="">
          </div>
        </div>
      </div>
      <div class="footer">
        <img :src="require('@/assets/images/static/bottom.png')" alt="">
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
@Component({
  components: {
    Header,
  }
})
export default class chatResponse extends Vue {
  private id: any = 'wb';
  private time: any = '';
  mounted() {
    let date = new Date();
    let Month = date.getMonth()+1 > 9 ? date.getMonth()+1 : `0${date.getMonth()+1}`;
    let Day = date.getDate() > 9 ? date.getDate() : '0'+date.getDate();
    let Hour = date.getHours() > 9 ? date.getHours() : '0'+date.getHours();
    let MM = date.getMinutes() > 9 ? date.getMinutes() : '0'+date.getMinutes();
    let curTime = `${date.getFullYear()}-${Month}-${Day} ${Hour}:${MM}`;
    this.time = curTime;
    this.id = this.$route.params.id; // 不同队伍id
  }
}
</script>

<style lang="less" scoped>
.chatResponse {
  background: #eee;
  &-content {
    // height: calc(100% - 51px);
    height: 100%;
    overflow: auto;
    .chat-div {
      height: calc(100% - 89px);
      overflow: auto;
      padding: 20px 15px;
      &-item {
        display: flex;
        .org-avatar {
          width: 40px;
          height: 40px;
          margin-right: 10px;
          margin-top: 8px;
        }
        .team {
          flex: 1;
          &-title {
            display: inline-block;
            margin-bottom: 10px;
            color: #666;
            font-size: 14px;
          }

        }
      }
      .time {
        display: inline-block;
        width: 100%;
        margin: 20px 0;
        text-align: center;
        color: #9a9a9a;
        font-size: 14px;
      }
    }
  }
  img {
    width: 100%;
  }
}
</style>

