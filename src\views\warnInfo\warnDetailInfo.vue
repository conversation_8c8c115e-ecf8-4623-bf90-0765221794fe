<template>
  <div class="list">
    <Header title="预警信息详情"></Header>
    <div class="list-card">
      <van-cell-group inset>
        <van-cell v-for="ele in listData" :key="ele.prop" :value="ele.type !== 'label' ? ele.value : null" :label="ele.type === 'label'? ele.value : null">
          <template #title>
            <van-icon :name="ele.icon" :color="ele.color" style="margin-right: 10px" />
            <span>{{ele.name}}</span>
          </template>
          <template #label v-if="ele.prop === 'file'">
            <div class="list-casualties">
              <div v-for="obj in ele.label" :key="obj.name" class="custom-title">{{obj.name}}</div>
            </div>
          </template>
          <template #right-icon v-if="ele.prop === 'warnType'">
            <img :src="getImg(ele.imgUrl)" alt="" style="width: 70px; height: 70px"/>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import { Notify } from 'vant';
@Component({
  components: {
    Header
  }
})
export default class warnDetailInfo extends Vue {
  private listData:any = [
    {
      name: '告警类型',
      prop: 'warnType',
      value: '',
      color: '#c8e3ee',
      icon: 'coupon-o',
      imgUrl: '',
    },
    {
      name: '信息标题',
      prop: 'headline',
      value: '',
      color: '#eed49b',
      icon: 'peer-pay',
      type: 'label'
    },
    {
      name: '发布单位名称',
      prop: 'sender',
      value: '',
      color: '#eed49b',
      icon: 'coupon-o',
      obj: 'alert'
    },
    {
      name: '预警级别',
      prop: 'severityName',
      value: '',
      color: '#b5dbe8',
      icon: 'bar-chart-o',
    },
    {
      name: '影响范围',
      prop: 'areaDesc',
      value: '',
      color: '#e4bfa0',
      icon: 'flag-o',
    },
    {
      name: '预警时间',
      prop: 'sendTimeStr',
      value: '',
      color: '#b5dbe8',
      icon: 'clock-o',
    },
    {
      name: '预警生效时间',
      prop: 'effectiveStr',
      value: '',
      color: '#b5dbe8',
      icon: 'underway-o',
    },
    {
      name: '发布内容',
      prop: 'message',
      value: '',
      color: '#c8e3ee',
      icon: 'notes-o',
      type: 'label',
      obj: 'alert'
    },
    {
      name: '应对措施',
      prop: 'instruction',
      value: '',
      color: '#eed49b',
      icon: 'shield-o',
      type: 'label'
    },
  ]

  mounted() {
    this.getDetail();
  }

  private getDetail() {
    const that = this;
    let alertId = this.$route.query.alertInfoId;
    this['$api'].InfoRequest.getWarningInfo({alertId}, res => {
      if (res.status === 200) {
        let data = JSON.parse(res.data.data);
        that.listData.forEach((item: any) => {
          if (item.prop === 'warnType') {
            item.value = null;
            item.imgUrl = that.getWarnType(data.eventType, data.severity);
          } else if (item.prop === 'severityName') {
            item.value = that.getSeverityName(data.severity);
          } else if (item.obj) {
            item.value = data[item.obj][item.prop];
          } else  {
            item.value = data[item.prop];
          }
        })
      } else {
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    })
  }

  private getWarnType(eventType, severity) {
    let res: any;
    if (severity === 'Red') {
      res = 1;
    } else if (severity === 'Orange') {
      res = 2;
    } else if (severity === 'Yellow') {
      res = 3;
    } else {
      res = 4;
    }
    res = `${eventType ? eventType : 'default'}_${res}.png`;
    return res;
  }

  private getSeverityName(severity) {
    let res: any;
    if (severity === 'Red') {
      res = '红色预警';
    } else if (severity === 'Orange') {
      res = '橙色预警';
    } else if (severity === 'Yellow') {
      res = '黄色预警';
    } else if (severity === 'Blue') {
      res = '蓝色预警';
    }
    return res;
  }

  private getImg(url) {
    if (!url) return false;
    return require(`@/assets/images/warnInfo/${url}`);
  }
} 
</script>

<style lang="less" scoped>
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-card {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    .van-cell-group {
      display: flex;
      flex-direction: column;
      margin: 12px 10px;
      padding: 0 10px;
      .van-cell {
        padding: 12px 0;
      }
    }
  }
}
</style>