<template>
  <div class="list">
    <Header title="知识案例"></Header>
    <div class="list-main">
      <div class="list-main-search">
        <van-search v-model="searchObj.caseName" placeholder="案例名称关键字搜索" @search="onSearch"/>
      </div>
      <div class="list-main-cont" style="height: calc(100% - 100px)">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            offset="100"
            @load="onLoad"
          >
            <van-cell v-for="(item, index) in list" :key="index" :title="item.caseName" class="van-ellipsis" @click="handleToDetail(item)" />
          </van-list>
        </van-pull-refresh>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Notify } from 'vant';
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
@Component({
  components: {
    Header
  }
})
export default class knowledgeCaseList extends Vue {
  searchObj: any = {
    caseName: '',
    nowPage: 1,
    pageSize: 10,
    typeCodes: []
  }
  list: any = [];
  total:any =  0;
  loading: boolean = false;
  finished: boolean = false;
  refreshing: boolean = false;
  typeCodes:any ='';
  mounted() {
    // this.getList()
    let typeCodes = this.$route.query.eventTypeCode;
    this.typeCodes = typeCodes;
  }
  getList(flag = false) {
    const that = this;
    if (this.list.length && this.list.length === this.total && !flag) {
      return
    }
    if( this.typeCodes){
         this.searchObj.typeCodes = [this.typeCodes];
    }else {
       this.searchObj.typeCodes = [this.$store.state.app.teamTypeCode];
    }
    taskRequest.getCaseList(this.searchObj, function(res){
      if(res.status === 200) {
        const data = res.data.data
        if(that.searchObj.nowPage === 1) {
          that.list = data.list;
        } else {
          that.list = [...that.list, ...data.list]
        }
        that.total = data.total;
        that.loading = false;
        that.refreshing = false;
        if(that.list.length >= data.total) {
          that.finished = true;
        } else {
          that.finished = false;
        }
        that.searchObj.nowPage++;
      } else {
        that.finished = true;
        Notify({ type: 'danger', message: res.data.msg || '请求失败' });
      }
    })
  }
  onSearch() {
    this.searchObj.nowPage = 1;
    this.getList(true)
  }
  // 跳转详情页面
  handleToDetail(item) {
    this.$router.push({
      path: `/knowledgeCase`,
      query: {
        caseId: item.caseId
      }
    })
  }
  // 下拉刷新
  public onRefresh() {
    this.searchObj.nowPage = 1;
    this.list = [];
    // if (this.finished) return;
    this.getList(true);
  }
  // 上拉加载刷新
  public onLoad() {
    if (this.finished) return;
    this.getList();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &-main {
    // position: absolute;
    // z-index: 1;
    // left: 0;
    // top: 46px;
    // height: calc(100vh - 46px);
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    // overflow: auto;
    &-search {
      height: 56px;
    }
    &-cont {
      // flex: 1;
      // height: calc(100% - 100px);
      overflow: auto;
    }
    &-add {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      background-color: #fff;
      color: #03A9F4;
      letter-spacing: 3px;
      font-weight: bold;
    }
  }
}
</style>