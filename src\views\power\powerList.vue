<template>
  <div class="list">
    <Header title="力量投入"></Header>
    <div class="list-search">
      <van-search v-model="listParams.keywords" placeholder="关键字搜索" />
      <div class="search-btn" @click="handleSearch">查询</div>
    </div>
    <van-tabs v-model="active" color="#1f4ae3" :ellipsis="false" title-active-color="#1f4ae3" @change="changeTab">
      <van-tab v-for="(item, index) in tabArr" :title="item.name" :key="index">
        <div class="content_list" :style="{ height: autoHeight }" @touchstart="moveClick" @touchmove="moveEv" @touchend="moveEnd">
          <mu-load-more @refresh="refresh" :refreshing="refreshing" :loading="loading" @load="load">
            <ul class="content_ul" ref="container" @click="changeShow">
              <deliveryItem ref="itemsRef" :requestList="item.list"></deliveryItem>
            </ul>
          </mu-load-more>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
import deliveryItem from '@/views/power/deliveryItem.vue';
@Component({
  components: {
    Header,
    deliveryItem
  }
})
export default class list extends Vue {
  private showPicker: boolean = false;
  autoHeight: any = ''; //动态列表高度
  private swcount = 0;
  private pageXI = 0;
  private pageYI = 0;
  private show: any = false;
  active: any = 0;
  pageIndex: any = 1;
  lists: any = [];
  listParams: any = {
    nowPage: 1,
    pageSize: 20,
    teamId: '',
    keywords: ''
  };
  tabArr = [
    { code: '20', name: '救援力量', currentIndex: 1, unit: '只', list: [] },
    { code: '10', name: '工作组', currentIndex: 1, unit: '个', list: [] },
    { code: '30', name: '成员单位', currentIndex: 1, unit: '家', list: [] },
    { code: '50', name: '专家', currentIndex: 1, unit: '个', list: [] }
  ];
  loading: boolean = false;
  refreshing: boolean = false;
  memberList = [{ name: '第一批次调度方案' }];
  refresh() {}
  load() {}
  handleSearch() {
    this.getData();
  }
  eventId: any = '';
  private getData() {
    let _this = this;
    const params = { eventId: _this.eventId, isQueryAll: true, isShowChildrenDetail: false, qrpBaseId: '' };
    apiServer.getMemberUnitList(params, function (res) {
      let memberList = res.data.data.memberList;
      _this.tabArr.forEach((item) => {
        memberList.forEach((val) => {
          if (item.code == val.groupCode) {
            item.name = item.name +' '+ val.groupCount + item.unit;
            item.list = val.memberDetailList;
          }
        });
      });
    });
  }
  private getQueryString() {
    var url = window.location.href.split('=')[1];
    return url;
  }
  changeTab(_index, itemName) {
    let _this = this;
  }
  changeShow() {
    if (this.show) {
      this.show = false;
      return;
    }
  }
  hideNavBar() {
    if (this['$dd'].env.platform != 'notInDingTalk') {
      this['$dd'].biz.navigation.hideBar({
        hidden: true // true：隐藏，false：显示
      });
    }
  }
  moveEv(e) {
    this.swcount++;
    if (this.swcount > 5) {
      let pageY = e.changedTouches[0].pageY;
      //console.log("yvalue=>"+(pageY-this.pageYI)*4)
      let pageX = e.changedTouches[0].pageX;
    }
  }
  moveEnd(e) {
    let _this = this;
    let pageX = e.changedTouches[0].pageX;
    // console.log(pageX + '===>' + this.pageXI);
    /*左右滑*/
    if (pageX + 100 < this.pageXI) {
      console.log('进入右滑');
      //this.NextMonth();

      this.show = true;
      this.swcount = 0;
    } else if (pageX > this.pageXI + 100) {
      console.log('进入左滑');
      this.show = false;
      //this.PreMonth();
      this.swcount = 0;
    }
  }

  moveClick(e) {
    this.swcount = 0;
    console.log('moveClick==>' + e.changedTouches[0].pageX);
    this.pageXI = e.changedTouches[0].pageX;
    this.pageYI = e.changedTouches[0].pageY;
  }
  calcAutoHeight() {
    let _this = this;
    let aheight = '100% - 46px ';
    if (_this.tabArr.length > 0) {
      aheight += '- 58px ';
    }
    if (_this.listParams.keywords) {
      aheight += ' - 53px ';
    }
    _this.autoHeight = 'calc(' + aheight + ')';
  }
  created() {
    this.listParams.teamId = ''; //localStorage.getItem('teamId')
    this.eventId = this.$route.query.eventId;
    this.getData();
    this.hideNavBar();
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.list {
  width: 100%;
  height: 100%;
  span {
    display: inline-block;
  }
  &-search {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 13px;
    background: #fff;
    /deep/.van-search {
      flex: 1;
      padding: 0;
      margin-right: 11px;
      height: 30px;
      .van-search__content {
        height: 100%;
      }
      .van-cell {
        color: #b5b5b5;
        background: #f4f7f8;
      }
    }
    /deep/.el-select {
      flex: 1;
      background: #f4f7f8;
      .el-input__inner {
        height: 30px;
        border: none;
        color: #b5b5b5;
        outline: none;
      }
    }
    .search-btn {
      border: 1px solid #c9ccd7;
      border-radius: 2px;
      padding: 0 5px;
      margin-left: 10px;
      height: 30px;
      line-height: 30px;
      color: #333;
    }
  }
  &-content {
    height: calc(100% - 90px);
    overflow: auto;
    padding: 8px;
    &-item {
      margin-bottom: 5px;
      background: url('@{url}/quickResponse/ico-light.png') 90% top no-repeat;
      background-size: 17%;
      background-color: #fff;
      padding: 17px 12px;
      border-radius: 4px;
      &-name,
      &-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        span:first-child {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .latestReplyText {
          color: #666;
        }
      }
      &-name {
        span:last-child {
          color: #666;
        }
        .name {
          font-size: 16px;
          color: #1c1d1d;
        }
      }
      &-bottom {
        &-time {
          margin-right: 11px;
          color: #666;
          font-size: 15px;
        }
        &-status {
          display: flex;
          align-items: center;
          padding: 2px 6px;
          color: #1a67f2;
          font-size: 12px;
          border: 1px solid #1a67f2;
          border-radius: 2px;
          /deep/.van-image {
            margin-right: 5px;
          }
        }
      }
    }
  }
}
</style>
