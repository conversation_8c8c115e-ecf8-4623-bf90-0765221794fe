<template>
  <div class="searchDetail">
    <div class="search_div">
      <van-search :maxlength="20"  placeholder="请输入搜索关键字" v-model="keywords" show-action @search="onSearch" @cancel="onCancel" />
    </div>
    <div class="teamSignIn-content" id="assPCon" ref="myScrollbar">
      <template v-if='replyList.length && loadOver'>
           <div v-for="(item, index) in replyList" :key="index">
        <div class="teamSignIn-itemes left" v-if="item.show">
          <div class="teamSignIn-user">{{ item.teamName.slice(0, 4) || '省应急厅' }}</div>
          <div class="teamSignIn-info">
            <div>{{ item.teamName || '省应急厅' }}</div>
            <div class="teamSignIn-boxes">
              <span
                >{{ item.previewText }} <b>{{ item.keywords }}</b> {{ item.afterText }}
              </span>
            </div>
          </div>
        </div>
      </div>
      </template>
      <template v-if='!replyList.length && loadOver'>
       <van-empty description="暂无数据" />
      </template>

     
      
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import taskRequest from '@/api/webcross/task';

@Component({})
export default class SearchDetail extends Vue {
  private keywords = '';
  private replyList = [];
  private eventId: any = '';
  private loadOver = false;
  private teamId: any = '';
  private onSearch() {
    let self = this;
    taskRequest.getAppReply(
      {
        nowPage: 1,
        pageSize: 100,
        keywords: this.keywords,
        eventId: this.eventId,
        teamId: this.teamId
      },
      (res) => {
        if (res.data.status === 200) {
          this.loadOver = true
          this.replyList = (res.data.data.list || []).map((el) => {
            return {
              ...el,
              previewText: el.replyText.slice(0, el.replyText.indexOf(self.keywords)),
              afterText: el.replyText.slice(el.replyText.indexOf(self.keywords) + self.keywords.length),
              show: el.replyText.indexOf(self.keywords) > -1,
              keywords:self.keywords
            };
          });
        }
      }
    );
  }

  private onCancel() {
    this.$router.go(-1);
  }
  private mounted() {
    this.eventId = this.$route.query.eventId;
    this.teamId = this.$route.query.teamId;
    console.log(this.$route.query);
  }
}
</script>

<style lang="less" scoped>
@url: '~@/assets/images/';
.searchDetail {
  height: 100%;
  background: white;
  .van-search__action {
    color: #1a67f2;
  }
  .teamSignIn-user {
    width: 40px;
    height: 40px;
    background: #1a67f2;
    font-size: 14px;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    line-height: 20px;
    margin-right: 7px;
  }
  .teamSignIn-info {
    margin-left: 20px;
    flex: 1;
    border-bottom: 1px solid #ebedf0;
    p {
      font-size: 14px;
      color: #666;
    }
    .teamSignIn-box {
      background: linear-gradient(to bottom, #1967f2 0%, #fff 30%);
      border-radius: 5px;
      padding: 0 6px 10px 6px;
      h2 {
        height: 45px;
        line-height: 45px;
        text-align: center;
        font-size: 19px;
        color: #fff;
        margin: 0;
      }
    }
    .teamSignIn-boxes {
      padding: 0 6px 3px 6px;
      b {
        color: #1a67f2;
        font-weight: normal;
      }

      //       h2 {
      //         height: 45px;
      //         line-height: 45px;
      //         font-size: 19px;
      //         color: #724511;
      //         margin: 0;
      //         i {
      //           display: inline-block;
      //           width: 26px;
      //           height: 26px;
      //           background: url('@{url}/quickResponse/teamSignIn05.png') center no-repeat;
      //           background-size: contain;
      //           position: relative;
      //           top: 5px;
      //           margin-right: 5px;
      //         }
      //         span {
      //           display: inline-block;
      //           flex: 1;
      //         }
      //         &.task-title {
      //           display: flex;
      //           padding: 10px 0;
      //           align-items: baseline;
      //           height: unset;
      //           line-height: unset;
      //         }
      //       }
    }
  }
}
</style>
