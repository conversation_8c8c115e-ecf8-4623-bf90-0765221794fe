<template>
  <div class="eventInformation">
    <Header title="事件信息"></Header>
    <div class="eventInformation-container">
       <div class="eventitle">{{eventInformationData.eventTitle}}</div>
       <div class="eventCon">
         <div class="eventItem">
           <div class="eventItemL">事发时间：</div>
           <div class="eventItemR">{{eventInformationData.eventTime}}</div>
         </div>
         <div class="eventItem">
           <div class="eventItemL">事发地点：</div>
           <div class="eventItemR">{{eventInformationData.eventAdress}}
              <div class="eventMap">
                <!-- <BaseMap></BaseMap> -->
                <!-- <div @click="clickMap">11111</div> -->
              </div>
           </div>          
         </div>         
          <div class="eventItem">
           <div class="eventItemL">事件类型：</div>
           <div class="eventItemR">{{eventInformationData.eventType}}</div>
         </div>
          <div class="eventItem">
           <div class="eventItemL">事件等级：</div>
           <div class="eventItemR">{{eventInformationData.eventGrade}}</div>
         </div>
          <div class="eventItem">
           <div class="eventItemL">详情描述：</div>
           <div class="eventItemR">{{eventInformationData.eventDetail}}</div>
         </div>         
       </div>     
    </div>
    <!-- <van-empty description="暂无事件信息数据"> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import apiServer from '../../api/request-service';
import BaseMap from '@/components/baseMap/baseMap.vue';

@Component({
  components: {
    Header,
    // BaseMap
  }
})
export default class eventInformation extends Vue { 
  private eventInformationData:any={
    eventTitle:'湖北省荆州市石首市防汛事件',
    eventTime:"2022-05-09  16:31",
    eventAdress:"湖北省荆州市石首市笔架湾",  
    eventType:"自然灾害",
    eventGrade:"重大",
    eventDetail:"水文部门通报长江流域沙市站已达到44.5m,石首北门口水位已达到39.85m，长江上游一号洪水已经形成，一号洪水将于12小时后通过荆州石首境内",

}
  mounted() {    
  }  
}
</script>

<style lang="less" scoped>
.eventInformation-container{
  background: #fff;
  margin-top:10px;
  border-radius: 5px;
  padding:0 15px 15px 15px;
  .eventitle{
    font-size: 18px;
    color: #000;
    text-align: center;
    border-bottom:1px solid rgb(215, 215, 215);
    line-height: 40px;
    height: 40px;
    margin-bottom:5px;
  }
  .eventItem{
    display: flex;
    padding: 2px 0;
    .eventItemL{
      width:70px;         
    }
    .eventItemR{
       width:calc(100% - 70px);
    }
  }
  .eventItem:last-child{
    display: inherit;
    .eventItemL{
      float: left;
    }
    .eventItemR{
      width:auto
    }
  }
  .eventMap{
    width:100%;   
    height: 250px;
    border:1px solid #ccc
  }
}

</style>