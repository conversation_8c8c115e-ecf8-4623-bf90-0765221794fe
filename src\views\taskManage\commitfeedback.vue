<template>
  <div style="width:100%;height: 100%;">
    <div class="big_div">
     <van-nav-bar title="任务管理" left-text="     "   left-arrow  @click-left="GoBack" >
        <template #right>
        <!-- <van-icon name="ellipsis" size="25" /> -->
      </template>
      </van-nav-bar>
      <div class="big_content">
        <div class="feedback_top">
          <span class="span_content" >
            <!-- <span>
              <div class="select_text">应急管理指挥中心</div>
              
            </span> -->
            <input type="text" v-model="tasktitle" :maxlength="50" placeholder="请输入反馈标题">
            <span></span>
          </span>
        </div>
        <div class="feedback_top">
          <span class="span_content" @click="selectReceived">
            <span>
              <div class="select_text" :style="{color:stateColorStr}">{{stateStr}}</div>
              <div class="select_icon">
                <img src="../../assets/images/as_03.png" alt="" srcset="">
              </div>
            </span>
            <span></span>
          </span>
        </div>
        <div class="feed_input">
          <div class="pit" @click="openVoice"><img class="inh" src="../../assets/images/yx.png" alt="" width="15"></div>
          <x-textarea :max="200" :height="150" placeholder="请输入反馈信息" v-model="backinfo"></x-textarea>
        </div>
        <div class="feedback_location clearfix">
         
          <textarea class="address_span" :readonly="addressReadonly" @click="addressReadonly?$showBottomToast('请先点击右侧选择定位点！'):''" :style="{marginTop:marginTop,height:addressheight}" ref="address" v-model="address">
          </textarea>
          <span class="locationSet_span " @click="manualLocation">
            <span><img src="../../assets/images/icon/locationWhite.png" alt="" srcset=""> </span>
          </span> 
          <!-- <span class="address_span_all">
            <textarea  v-model="feedbackadd" maxlength="100">
            </textarea>
            <span class="relocation"  @click="manualLocation">
              <img src="../../assets/images/icon/locationWhite.png" alt="" srcset="">
            </span>
          </span> -->
        </div>
        
        <div class="file_div">
          <fileObj :typeflag="1" ref="fileObj" @fliws="getFiles" @uplist="muplist" />
          <div class="file_select">
            <span @click="openSelect('menu1')">
              <img src="../../assets/images/icon/back_picture.png" alt="" srcset="">
              <font>照片</font>
            </span>
            <span @click="openSelect('menu2')">
              <img src="../../assets/images/icon/back_vedio.png" alt="" srcset="">
              <font>视频</font>
            </span>
            <span @click="openSelect('menu3')">
              <img src="../../assets/images/icon/audioselect.png" alt="" srcset="">
              <font>音频</font>
            </span>
          </div>
        </div>
        <span class="send_btn" @click="submitBtn">提交反馈</span>
       
      </div>
      <popup v-if="commonMapModel" v-model="commonMapModel" height="100%" width="100%" position="right">
        <commonMap ref="CMap" bigHeight="90vh" :Enable="true" initName="relocation" @handle="mapHandle" />
      </popup>
    </div>
    <div @click="showPopupPicker=false;" v-if="showPopupPicker"  style="z-index: 500;width:100%;height: 100%;background: rgba(0,0,0,.5);position: fixed;left: 0;top: 0;">
		</div>
    <!-- <popup v-if="replenishModel" v-model="replenishModel" height="100%" width="100%" position="right">
        <replenish  @close="closeReplenish"/>
    </popup> -->
    
    <popup-picker :showCell="false"  :show.sync="showPopupPicker" ref="taskState" :data="taskStateList" @on-change="onChange" :columns="3" v-model="taskStateModel" ></popup-picker>
  </div>
</template>



<script>
import fileObj from "../commons/testcorpper";
import { taskfeedbackSave , uploadFiles } from "../../assets/js/service";
import commonMap from "../commons/commonMap";
export default {
  components: {
    fileObj,
    commonMap
  },
  props:['taskId',"eventId"],
   data () {
    return {
      queryMap:{},
      date: '',
      marker:"",
      map:"",
      address:"",
      stateStr:"请选择任务状态",
      marginTop:"3%",
      addressheight:"45px",
      stateColorStr:"#898989",
      addressReadonly:true,
      replenishModel:false,
      showPopupPicker:false,
      taskStateModel:[],
      fileList:[],
      userid:"",
      orgcode:"",
      longitude:"",
      latitude:"",
      feedbackadd:"",
      execstate:"",
      backinfo:"",
      tasktitle:"",
      paramObj:{},
      taskStateList: [{
        name: '未接收',
        value: '1',
        parent: 0
      },{
        name: '已出发',
        value: '2',
        parent: 0
      }, {
        name: '已抵达',
        value: '3',
        parent: 0
      }
      , {
        name: '执行中',
        value: '4',
        parent: 0
      }
      , {
        name: '已结束',
        value: '5',
        parent: 0
      }],
      commonMapModel:false,
      lontatStr:"",//保留android赋值的坐标
    }
  },
  created(){
    let _this=this;
    _this.refreshLocation();
    _this.userid=_this.$store.state.user.userid;
    _this.orgcode=_this.$store.state.user.orgcode;
    console.log(_this.taskId)
    this.$enJsBack()//调用安卓方法（手机back键返回问题）
  },
  methods: {
    GoBack(){
      this.$closeStates();
      if(this.$refs.fileObj.GetAriaHidden()){
        this.$emit("close")
      }else{
        this.$refs.fileObj.closePreview();
      }
    },
    selectReceived(){
      console.log("open select received")
      this.showPopupPicker=true;
    },
    initAddressHeight(){
      let scrollHeight=this.$refs.address.scrollHeight;
      if(scrollHeight>35){
        this.marginTop="1.7vh";
        this.addressheight="45px";
      }else{
         this.marginTop="3vh";
         this.addressheight="35px";
      }
      console.log(scrollHeight)
    },
    openSelect(_type){
      this.$refs.fileObj.clickMenu(_type);
    },
    openVoice(){
      if(this.$isAndroid()){
        window.android.openVoice();
      }else{
        console.log("打开语音识别功能")
      }
    },
    setContent(_text){
      let _this=this;
      if(_text){
        _this.backinfo+=_text;
      }
    },
    locationSet(lontatStr){
      let _this=this;
      _this.lontatStr=lontatStr;
      console.log("locationSet===>"+lontatStr)
      _this.longitude=lontatStr.split("_")[0];
      _this.latitude=lontatStr.split("_")[1];
      _this.address=lontatStr.split("_")[2];
      _this.addressReadonly=false;
      // setTimeout(()=>{
      //   _this.initAddressHeight();
      // },100)
    },
    manualLocation(){
      let _this=this;
      _this.commonMapModel=true;
      setTimeout(()=>{
        _this.$refs.CMap.locationSet(_this.lontatStr);
      },1000)
    },
    mapHandle(_obj){
      console.log(_obj)
      if(_obj.opentype&&_obj.opentype=='locationSure'){
        this.locationSet(_obj.locationStr);
      }
      this.commonMapModel=false;
    },
    refreshLocation(){
      let _this=this;
       if(this.$isAndroid()){
          console.log("invoking android!")
          // window.android.getCurrentLocation();
          this.$gsmdp.getLocation({
          data: {
          type: 'gcj02',
          altitude: 'true'
          },
          success(result) {
            //alert('getLocation' + JSON.stringify(result));
            //alert(result.longitude+"==>"+result.latitude)
            _this.$gsmdp.getLocationInfo({
                  data:{
                    "longitude":result.longitude,
                    "latitude":result.latitude,
                    "address":"",
                  },
                  success(res){
                    if(res.longitude!=""&&res.latitude!=""&&res.address!=""){
                      let addressStr=res.longitude+"_"+res.latitude+"_"+res.address;
                      _this.locationSet(addressStr)
                    }
                  }
                  
                })
              },
              fail(err) {
              console.log(err);
              }
            });
        }else{
          this.locationSet("93.793409_31.896125_四川省雅安市拉亚镇桑木卡拉亚镇桑木卡亚镇桑木卡")
        }
    },
    getFiles(data){
      this.fileList=data;
    },
    onChange(){
      this.stateStr=this.$refs.taskState.getNameValues();

    },
    muplist(data){
			this.paramObj=data;
			console.log(JSON.stringify(this.data))
    },
    sendFeedback(){
      console.log("to commit;")
      let _this=this;
      if(_this.taskStateModel.length>0){
        this.execstate =_this.taskStateModel[0];
      }
      let attalist=this.attatchids;
      try {
        if(attalist.length>=0){
        }else{
          if(typeof(attalist)=='object'){
            let arrlist=[];
            arrlist.push(attalist);
            attalist=arrlist;
          }else{
             attalist="";
          }
        }
      } catch (error) {
        console.log("not arrlist")
        if(typeof(attalist)=='object'){
          let arrlist=[];
          arrlist.push(attalist);
          attalist=arrlist;
        }else{
          console.log("not arrlist and not obj")
        }
      }
      // let param={"tasktitle":this.tasktitle,"taskid":this.taskId,"userid":this.userid,"orgcode":this.orgcode,"longitude":this.longitude,"latitude":this.latitude,
      // "address":this.address,"execstate":this.execstate,"backinfo":this.backinfo,"attatchids":this.attatchids};
      let param={
        "tasktitle":this.tasktitle,
        "address": this.address,
        "attachmentList": attalist,
        "backinfo": this.backinfo,
        "createorgcode":this.orgcode,
        "eventid": this.eventId,
        "teamid":this.$store.state.teamid,
        "execstate": this.execstate,
        "backstate":"0",
        "execuserid": this.userid,
        "latitude": this.latitude,
        "longitude":this.longitude,
        "taskid": this.taskId,
        "userid": this.userid
      }
      let paramObj={"obj":JSON.stringify(param)};
      console.log(paramObj)
      if(this.execstate){
        taskfeedbackSave(param,function(res){
          console.log("save taskfeedbackSave==>",res)
          if(res.status=='200'){
            _this.$showBottomToast("上传成功")
            // _this.$emit("close");
            _this.GoBack()
            _this.$emit("success");
          }else{
            _this.$showBottomToast(res.message);
          }
        })
      }else{
            _this.$showBottomToast("请选择任务状态")
				_this.$vux.loading.hide();
      }
    },
    submitBtn(){
      let _this=this;
      if(_this.$verification(_this.stateStr,9,"状态") === undefined||!_this.$verification(_this.address,0,"任务反馈地址")){
        return;
      }
      _this.$vux.loading.show({
        text: '上传中'
      })
      try{
				if(!_this.paramObj.uplist&&!_this.paramObj.upvideolist){
					_this.sendFeedback();
				}else{
					console.log(_this.paramObj.uplist.join(','))
					console.log(_this.paramObj.upvideolist.join(','))
          // window.android.uploadFiles(_this.paramObj.uplist.join(',')+"",_this.paramObj.upvideolist.join(',')+"","mobile/saveattach.mvc");
           uploadFiles(_this.paramObj,function(_resultObj) {
              console.log("页面上传成功回调==>"+JSON.stringify(_resultObj))
              if(_resultObj){
                 _this.$vux.loading.hide()
                console.log("resultObj==>"+_resultObj)
                _this.attatchids=_resultObj;
                _this.sendFeedback();
              }
             
            })
					console.log("调用uploadFiles 成功")
				}
			}catch(e){
        console.log(e)
				console.log("调用uploadFiles 失败")
				_this.$vux.loading.hide();
				//TODO handle the exception
			}

    },
    uploadFeild(){
			let _this=this;
			_this.$vux.toast.show({
				text: "上传附件失败",
				type: "cancel"
			});
			_this.$vux.loading.hide();
		},
    fileSuccessCommit(_resultObj){
      console.log("fileSuccessCommit==>"+JSON.stringify(_resultObj))
      this.$vux.loading.hide()
      console.log("resultObj==>"+_resultObj)
      this.attatchids=_resultObj;
      this.sendFeedback();

    },
  },
  watch:{
    tasktitle(){
      this.tasktitle=this.$changeVal(this.tasktitle)
    },
    address(){
      this.address=this.$changeVal(this.address)
      //this.initAddressHeight();
    },
    backinfo(){
      setTimeout(() => {
        this.backinfo=this.$changeVal(this.backinfo)
      }, 10);
    },
    stateStr(){
      //console.log(this.stateStr)
      this.stateColorStr="#000";
    }
  },
  mounted(){
    let _this=this;
    console.log("come in commitfeedback")
    window.fileSuccessCommit=this.fileSuccessCommit;
    window.locationSet=this.locationSet;
    window.GoBack=this.GoBack;//安卓调用GoBack（手机back键返回问题）
    window.setContent=this.setContent;
    this.$gsmdp.initGoback(function(){
      _this.GoBack()
    })
    console.log($(".vux-popup-mask"));
    // $(".vux-popup-mask").remove();
    // console.log($(".vux-popup-mask"));

  }
  ,beforeCreate(){
    console.log($(".vux-popup-mask"));
    
  }
}
</script>

<style lang="less" scoped>
.big_div{
  width: 100%;
  height: 100%;
  // background:url("../../assets/images/bj.png");
  // background-size: 100% 100%;
  // background-repeat: no-repeat;
  .big_title{
    width: 100%;
    height: 50px;
    line-height: 50px;
    span{
      display: inline-block;
      float: left;
      color: #ffffff;
    }
    .span_front{
      width: 75%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      padding-left: 5%;
      font-size: 16px;
    }
    .span_handle{
       width: 25%;
       img{
         width: 75%;
         vertical-align: middle;
       }
    }
  }
  .big_content{
    width: 100%;
    height:calc( 100% - 46px - 8.5vh) ;
    overflow-x: hidden;
    overflow-y: auto;
    border-top: solid 1px rgba(255, 255, 255, 0.15);
    background: #ffffff;
    .feedback_top{
      width: 100%;
      height: 8vh;
      line-height: 8vh;
      // padding-left: 5%;
      font-size: 16px;
      span{
        display: inline-block;
      }
      .span_title{
        width: 16%;
        height: 100%;
        float: left;
      }
      .span_content{
        width: 100%;
        height: 100%;
        color: #000;
        position: relative;
        input{
          width: 94%;
          height: 70%;
          margin: 2.8%;
          border: #afafaf 1px solid;
          border-radius: 6px;
          padding-left: 10px;
        }
        span:nth-child(1){
          display: inline-block;
          width: 94%;
          height: 70%;
          margin: 2.8%;
          border: #afafaf 1px solid;
          border-radius: 6px;
          .select_text{
            display: inline-block;
            width: 80%;
            height: 100%;
            line-height: 5vh;
            padding-left: 10px;
            float: left;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .select_icon{
            position: absolute;
            right: 8px;
            padding-bottom: 6%;
            width: 8%;
            height: 12%;
            img{
               width: 63%;
               margin-bottom: 20%;
            }
          }
        }
      }
    }
    .feedback_location{
      width: 100%;
      height: 12%;
      border-top: solid 1px #c7c7c7;
      //border-bottom: solid 1px #c7c7c7;
      span{
        display: inline-block;
      }
      .address_span{
        width: 88%;
        height: 35px;
        float: left;
        padding-left: 5%;
        font-size: 15px;
        // padding-bottom: 10px;
        // padding-top: 3%;
      }
      
      .locationSet_span{
        width: 10%;
        height:100%;
        position: relative;
        span{
            background: #409eff;
            width: 9vw;
            height: 9vw;
            // padding: 1.5vw 2.2vw;
            margin-right: 5px;
            border-radius: 6px;
            position: absolute;
            top: calc( ( 10vh - 9vw ) / 2);
            img{
              width: 4.5vw;
              position: absolute;
              top: 1.5vw;
              left: 2vw;
            }
        }
      }
      .address_span_all{
        width: 94%;
        margin-left: 3%;
        height: 10vh;
        border: 1px solid #afafaf;
        border-radius: 6px;
        margin-bottom: .2rem;
        padding: .1rem;
        position: relative;
        textarea{
          width: 90%;
          height: 8vh;
          font-size: 16px;
          color: #898989;
        }
        .relocation{
          width: 9vw;
          height: 9vw;
          position: absolute;
          right: 0px;
          bottom: 0px;
          display: inline-block;
          background: #409eff;
          border-radius: 6px 0px 6px 0px;
          img{
            width: 44%;
            margin: 25% 28%;
          }
        }
      }
     
    }
    .feed_input{
      position: relative;
      .pit{
        position: absolute;
        right: 5px;
        bottom: 5px;
        z-index: 99;
        background: #409eff;
        width: 9vw;
        height: 9vw;
        padding: 1.5vw 2.2vw;
        margin-right: 5px;
        border-radius: 6px;
        img{
          width: 15px;
        }
      };
    }
    .file_div{
      width: 100%;
      height: 35%;
      border-top: solid 1px #c7c7c7;
      .file_select{
        height: 50%;
        padding-left: 10%;
        padding-top: 6%;
        margin-top: 4%;
        border-top: solid 1px #c5c5c5;
        span{
          width: 25%;
          height: 80%;
          margin-left: 3%;
          display: inline-block;
          font{
            width: 100%;
            display: block;
            text-align: center;
            margin-top: -4px;
          }
          img{
            width: 60%;
            margin-left: 20%;
          }
        }
      }
    }
    .send_btn{
      width: 100%;
      height: 8vh;
      display: block;
      line-height: 8vh;
      color: #ffffff;
      font-size: 17px;
      text-align: center;
      background: #139be5;
      position: absolute;
      bottom: 1px;
    }
  }
}

.picker-buttons {
  margin: 0 15px;
}
</style>
