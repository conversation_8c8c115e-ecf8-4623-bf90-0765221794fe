[
    {
        "type":"input","title":"发明创造名称","field":"","value":""
    },
    {
        "type":"input","title":"申请号/专利号","field":"patent_number","value":""
    },
    {
        "type":"select","title":"委托人","field":"applicants","value":[],
        "props": {
            "multiple": true,
            "filterable": true
        },
        "request": true,
        "url": ""

    },
    {
        "type":"checkbox","title":"委托类型","field":"poa_application","value":[],
        "options":[
            {"value":0,"label":"测试1"},
            {"value":1,"label":"测试2"}
        ],
        "event":{
            
        }
    },
    {
        "type":"select","title":"代理机构","field":"agency","value":[],
        "props": {
            "multiple": true,
            "filterable": true,
        },
        "request": true,
        "url": ""

    },
    {
        "type":"select","title":"代理人","field":"agents","value":[],
        "props": {
            "multiple": true,
            "filterable": true,
            "multiple-limit":2
        },
        "request": true,
        "url": ""

    }
  ]