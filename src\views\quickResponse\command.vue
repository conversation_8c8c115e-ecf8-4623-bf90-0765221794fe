<template>
  <div class="knowledgeCase" ref="mainHeight">
    <Header title="指挥体系"></Header>
    <van-tabs v-model="active" @click="onClick">
      <van-tab :title="item.text" v-for="(item, index) in systemList" :key="index">
        <van-loading color="#0094ff" v-if="!loadover" vertical>加载中...</van-loading>
        <template v-if="selectItem != '工作组'">
          <div class="commnandIndex" v-if="item.jobDuty">
            <div class="commandList">
              <p class="duty">职责</p>
              <div>
                {{ item.jobDuty }}
              </div>
            </div>
          </div>
          <div class="commnandIndex">
            <div class="commandList" v-if="item.commandDetail && item.commandDetail.length">
              <p class="duty">人员</p>
              <div class="dutyFlex" v-for="(ele, ind) in item.commandDetail" :key="ind">
                <div class="label" v-if="ele.groupType">{{ ele.groupType }}</div>
                <div class="dutyList">
                  <p>
                    <span>{{ ele.contactPer }}</span>
                    <span>{{ ele.orgName }}</span>
                    <b v-if="ele.personJob">({{ ele.personJob }})</b>
                      <a :href="'tel:'+ ele.contactTel"><van-icon name="phone" color="#1a66f2" /></a> 
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="commandIndex" v-if="item.taskDuty">
            <van-steps direction="vertical" active-color="#969799" active-icon="underway-o" inactive-icon="underway-o">
              <van-step v-for="(ele, ind) in dutyDetail.taskDuty" :key="ind">
                <van-collapse accordion v-model="activeName">
                  <van-collapse-item :title="ele.taskPhaseName" :name="ind">
                    <p v-for="(element, i) in ele.taskDuty" :key="i">{{ i + 1 }}:{{ element }}</p>
                  </van-collapse-item>
                </van-collapse>
              </van-step>
            </van-steps>
          </div>

          <van-empty description="暂无数据" v-if="isEmpty" />
        </template>
        <template v-else>
          <!-- 工作组详情 -->
          <van-tree-select :height="height" ref="treeSelect" :items="items" :main-active-index.sync="activeSlide">
            <template #content v-if="item.child">
              <van-loading color="#0094ff" v-if="!loadover" vertical>加载中...</van-loading>
              <div class="commnandIndex" v-if="item.child[activeSlide].jobDuty">
                <div class="commandList">
                  <p class="duty">职责</p>
                  <div>
                    {{ item.child[activeSlide].jobDuty }}
                  </div>
                </div>
              </div>
              <div class="commnandIndex" v-if="item.child[activeSlide].commandDetail.length > 0">
                <div class="commandList">
                  <p class="duty">人员</p>
                  <div class="dutyFlex" v-for="(ele, ind) in item.child[activeSlide].commandDetail" :key="ind">
                    <div class="label" v-if="ele.groupType">{{ ele.groupType }}</div>
                    <div class="dutyList">
                      <p>
                        <span>{{ ele.contactPer }}</span>
                        <span>{{ ele.orgName }}</span>
                        <b v-if="ele.personJob">({{ ele.personJob }})</b>
                       <a :href="'tel:'+ ele.contactTel"><van-icon name="phone" color="#1a66f2" /></a> 
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="commandIndex">
                <van-steps direction="vertical" active-color="#969799" active-icon="underway-o" inactive-icon="underway-o">
                  <van-step v-for="(ele, ind) in items[activeSlide].phaseList" :key="ind">
                    <van-collapse accordion v-model="activeName">
                      <van-collapse-item :title="ele.name" :name="ind">
                        <!-- <p v-for="(element, i) in ele.taskList" :key="i">{{ i + 1 }}:{{ element.taskText }}</p> -->
                        <div v-for="(element, i) in ele.taskList" :key="i">
                          <p>
                            <b>{{ i + 1 }}:{{ element.taskTitle }}</b>
                          </p>
                          <p>{{ element.taskText }}</p>
                        </div>
                      </van-collapse-item>
                    </van-collapse>
                  </van-step>
                </van-steps>
              </div>
              <van-empty description="暂无数据" v-if="isEmpty" />
            </template>
          </van-tree-select>
        </template>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import Header from '@/views/common/header.vue';
import taskRequest from '@/api/webcross/task';
import { retry } from 'rxjs/operator/retry';
import { Notify } from 'vant';

@Component({
  components: {
    Header
  }
})
export default class knowledgeCase extends Vue {
  private selectItem = '';
  private activeName = '';
  private height = 550;
  private active = 0;
  private isEmpty = false;
  private loadover = false;
  private dutyDetail: any = {};
  private systemList: any = [
    {
      text: '指挥部',
      commandType: '1'
    },
    {
      text: '指挥部办公室',
      commandType: '2'
    },
    {
      text: '成员单位',
      commandType: '3'
    },
    {
      text: '工作组',
      commandType: '4'
    }
  ];

  private groupId;
  private taskAll = [];
  private taskList = [];

  private onClick(name, title) {
    let i = -1;
    this.systemList.forEach((el, index) => {
      if (el.text == title) {
        i = index;
      }
    });
    console.log(name, title);
    if (this.selectItem == title) {
      return;
    } else {
      // this.loadover = false;
      this.selectItem = title;
      if (title == '工作组') {
        this.handleData();
      }
    }
  }

  private handleData() {
    this.taskAll = this.items[this.activeSlide].taskPhase || [];
    let taskAllList: any = [];
    if (this.taskAll.length) {
      for (let i = 0; i < this.taskAll.length; i++) {
        if (this.taskAll[i].taskDetail.length) {
          for (let j = 0; j < this.taskAll[i].taskDetail.length; j++) {
            taskAllList.push(this.taskAll[i].taskDetail[j]);
          }
        }
      }
    }
    for (let i = 0; i < this.phaseList.length; i++) {
      this.phaseList[i].taskList = [];
      for (let j = 0; j < taskAllList.length; j++) {
        if (this.phaseList[i].name == taskAllList[j].taskPhaseName) {
          // console.log(this.taskAll[j].taskDetail, 'aaad');
          this.phaseList[i].taskList.push(taskAllList[j]);
          continue;
        }
      }
    }
  }

  private activeSlide = 0;
  private items = [];
  private phaseList = [];
  @Watch('activeSlide')
  private handleSlide(val) {
    console.log(val,'val')
    // (this.$refs.treeSelect as any).scrollTop = 0;
    if(this.items.length){
         this.handleData();
    }
   
  }


  private sortByOrder(list){
    let listA = list.filter((el)=>el.groupType=='组长');
    let listB = list.filter((el)=>el.groupType=='副组长');
    let listC = list.filter((el)=>el.groupType=='成员');
    let lists = [...listC,...listB,...listA];
    return [...listA,...listB,...listC]
  }

  private list = [];
  private mounted() {
    let height = (this.$refs['mainHeight'] as any).clientHeight;
    // 带参数的跳转
    let dirsrtrcode = this.$route.query.districtCode;
    // if (dirsrtrcode && dirsrtrcode.indexOf('429') > -1) {
    //   dirsrtrcode = dirsrtrcode.slice(0, 6);
    // } else if (dirsrtrcode && dirsrtrcode.indexOf('429') == -1) {
    //   dirsrtrcode = dirsrtrcode.slice(0, 4) + '00';
    // }
    this.height = height - 100;
    let self = this;
    taskRequest.getPlanList(
      {
        eventId:  this.$route.query.eventId
      },
      (res) => {
        console.log(res, 'res');
        let curPlan =
          res.data.data.filter((el) => {
            return el.isStart == 1;
          })[0] ||
          res.data.data[0] ||
          {};
        if (Object.keys(curPlan).length > 0) {
          // 通过预案找方案
          taskRequest.getSearchByPlanId(
            {
              planId: curPlan.planId
            },
            (result) => {
              let qrpBaseId = result.data.data.id;
              taskRequest.getSystemInfo(
                {
                  eventId:  curPlan.eventId,
                  isLook: true,
                  planId: curPlan.planId,
                  qrpBaseId:  qrpBaseId,
                  districtCode: dirsrtrcode&&dirsrtrcode.slice(0,6)
                },
                (resList) => {
                  this.loadover = true;
                  if (resList.data.status == '200') {
                    let list = resList.data.data.commandMain;
                    if (!list.length) {
                      this.isEmpty = true;
                      return;
                    }
                    let regionList = self.systemList;
                    for (let i = 0; i < list.length; i++) {
                      for (let j = 0; j < self.systemList.length; j++) {
                        if (list[i].commandType == self.systemList[j].commandType) {
                          self.$set(self.systemList[j], 'jobDuty', list[i].jobDuty);
                          if (self.systemList[j].commandType != 4) {
                            self.$set(self.systemList[j], 'commandDetail', list[i].commandDetail);
                            self.$set(self.systemList[j], 'taskDetail', list[i].taskDetail);
                            self.$set(self.systemList[j], 'commandId', list[i].commandId);
                          } else {
                            if (!self.systemList[j].child) {
                              self.systemList[j].child = [];
                              self.systemList[j].child.push({
                                name: list[i].name,
                                commandDetail:this.sortByOrder( list[i].commandDetail),
                                jobDuty: list[i].jobDuty,
                                taskPhase: list[i].taskPhase,
                                commandId: list[i].commandId
                              });
                            } else {
                              self.systemList[j].child.push({
                                name: list[i].name,
                                commandDetail:this.sortByOrder( list[i].commandDetail),
                                jobDuty: list[i].jobDuty,
                                taskPhase: list[i].taskPhase,
                                commandId: list[i].commandId
                              });
                            }
                          }
                          continue;
                        }
                      }
                    }
                    this.items = self.systemList[3].child.map((el) => {
                      return {
                        ...el,
                        text: el.name,
                        commandId: el.commandId
                      };
                    });
                    let taskList = [];
                    //  获取对应的任务阶段
                    taskRequest.getPhaseGroup(
                      {
                        dicCode: 'PHASE_CODE'
                      },
                      (res) => {
                        console.log(res.data.data);
                        let phaseList = res.data.data.map((ele) => {
                          return {
                            ...ele,
                            phaseList: []
                          };
                        });
                        this.phaseList = phaseList;
                        for (let j = 0; j < this.items.length; j++) {
                          this.$set(this.items[j], 'phaseList', phaseList);
                        }
                        let groupId = this.$route.query.groupId;
                        let index = -1;
                        if (groupId) {
                          index = this.items.findIndex((el) => {
                            return el.commandId == groupId;
                          });
                          if (index >= 0) {
                            this.selectItem = '工作组';
                            this.active = 3;
                            this.activeSlide = index;
                            console.log(this.items,'ittt')
                            this.handleSlide( this.activeSlide)
                          }
                        } else {
                          // 当前工作组未找到
                          // Notify({type:'danger',message:'该工作组不存在'})
                        }
                      }
                    );
                  } else {
                    this.isEmpty = true;
                  }
                }
              );
            }
          );
        }
      }
    );
  }
}
</script>

<style lang="less" scoped>
/deep/ .van-tabs__line {
  background-color: #1967f2;
}
.knowledgeCase {
  width: 100%;
  height: 100%;
  background: white;
}

.autoHeight {
  height: calc(100vh - 60px);
  background: red;
}

.commnandIndex {
  .commandList {
    padding: 0 20px;
    .duty {
      position: relative;
      padding-left: 8px;
      &:after {
        position: absolute;
        left: 0;
        top: 50%;
        width: 2px;
        height: 16px;
        background-color: #1967f2;
        content: '';
        transform: translateY(-50%);
      }
    }
    .dutyFlex {
      display: flex;
      align-items: flex-start;
      padding: 4px 0;
      .label {
        width: 90px;
        text-align: left;
      }
      .dutyList {
        flex: 1;

        span {
          margin-right: 4px;
        }
        p {
          margin: 0;
        }
      }
    }
  }
}
/deep/ .van-cell,
/deep/ .van-collapse-item__content {
  padding: 6px 4px !important;
  p {
    margin: 7px 0;
  }
}
/deep/ .van-tabs__content {
  height: calc(100vh - 70px);
  overflow-y: scroll;
}
/deep/ .van-loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate3d(-50%, -50%, 0);
}
</style>
