import Vue from 'vue';
import { v1 as uuidv1 } from 'uuid';
import { Notify, Toast } from 'vant';
import axios from 'axios';
import CryptoJS from 'crypto-js';

// 政务微信配置参数
interface ZwwxConfig {
  agentId: string;
  corpId: string;
  secret: string;
  debug?: boolean; // 是否开启debug模式
}

interface LocationResult {
  latitude: number; // 纬度，浮点数，范围为90 ~ -90
  longitude: number; // 经度，浮点数，范围为180 ~ -180
  speed: number; // 速度，以米/每秒计
  accuracy: number; // 位置精度
  gps_status: number; // GPS状态
  altitude?: number; // 高度，单位米
}

interface LocationOptions {
  type?: string; // 坐标类型，默认为wgs84
  showLoading?: boolean; // 是否显示加载提示，默认为true
}

// 缓存对象
const CACHE_KEYS = {
  ACCESS_TOKEN: 'zwwx_access_token',
  TOKEN_EXPIRE_TIME: 'zwwx_token_expire_time',
  JSAPI_TICKET: 'zwwx_jsapi_ticket',
  TICKET_EXPIRE_TIME: 'zwwx_ticket_expire_time'
};

class ZwwxSDK {
  private config: ZwwxConfig = {
    agentId: '',
    corpId: '',
    secret: '',
    debug: false
  };

  private isInitialized = false;
  private isConfiguring = false;

  /**
   * 设置政务微信SDK配置
   * @param config 配置对象
   */
  setConfig(config: ZwwxConfig): ZwwxSDK {
    this.config = { ...this.config, ...config };
    return this;
  }

  /**
   * 开启调试模式
   */
  enableDebug(): ZwwxSDK {
    this.config.debug = true;
    if (window['wx']) {
      window['wx'].config({
        debug: true
      });
    }
    return this;
  }

  /**
   * 检查当前环境是否为政务微信环境
   * @returns 是否为政务微信环境
   */
  isWeiXin(): boolean {
    // if (process.env.NODE_ENV === 'development') {
    //   return true;
    // }

    const ua = navigator.userAgent.toLowerCase();
    return !!ua.match(/micromessenger/i) && !!ua.match(/zwwx/i);
  }

  /**
   * 获取access_token（有效期7200秒）
   * @returns Promise<string> 返回access_token
   */
  async getAccessToken(): Promise<string> {
    const now = Date.now();
    const cachedToken = localStorage.getItem(CACHE_KEYS.ACCESS_TOKEN);
    const tokenExpireTime = localStorage.getItem(CACHE_KEYS.TOKEN_EXPIRE_TIME);

    // 如果缓存中的token仍然有效，直接返回
    if (cachedToken && tokenExpireTime && now < parseInt(tokenExpireTime)) {
      return cachedToken;
    }

    if (!this.config.corpId || !this.config.secret) {
      throw new Error('缺少必要的corpId或secret参数');
    }

    try {
      console.log('正在获取新的access_token...');
      const response = await axios.get('/cgi-bin/gettoken', {
        params: {
          corpid: this.config.corpId,
          corpsecret: this.config.secret
        }
      });

      if (response.data && response.data.access_token) {
        const accessToken = response.data.access_token;
        // 设置过期时间，提前200秒过期，确保安全
        const expireTime = now + (response.data.expires_in - 200) * 1000;

        // 保存到localStorage
        localStorage.setItem(CACHE_KEYS.ACCESS_TOKEN, accessToken);
        localStorage.setItem(CACHE_KEYS.TOKEN_EXPIRE_TIME, expireTime.toString());

        console.log('access_token已更新并缓存');
        return accessToken;
      } else {
        throw new Error(`获取access_token失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取access_token请求失败:', error);
      throw error;
    }
  }

  /**
   * 获取jsapi_ticket（有效期7200秒）
   * @returns Promise<string> 返回jsapi_ticket
   */
  async getJsapiTicket(): Promise<string> {
    const now = Date.now();
    const cachedTicket = localStorage.getItem(CACHE_KEYS.JSAPI_TICKET);
    const ticketExpireTime = localStorage.getItem(CACHE_KEYS.TICKET_EXPIRE_TIME);

    // 如果缓存中的ticket仍然有效，直接返回
    if (cachedTicket && ticketExpireTime && now < parseInt(ticketExpireTime)) {
      return cachedTicket;
    }

    try {
      console.log('正在获取新的jsapi_ticket...');
      const accessToken = await this.getAccessToken();
      const response = await axios.get('/cgi-bin/get_jsapi_ticket', {
        params: {
          access_token: accessToken
        }
      });

      if (response.data && response.data.ticket) {
        const jsapiTicket = response.data.ticket;
        // 设置过期时间，提前200秒过期，确保安全
        const expireTime = now + (response.data.expires_in - 200) * 1000;

        // 保存到localStorage
        localStorage.setItem(CACHE_KEYS.JSAPI_TICKET, jsapiTicket);
        localStorage.setItem(CACHE_KEYS.TICKET_EXPIRE_TIME, expireTime.toString());

        console.log('jsapi_ticket已更新并缓存');
        return jsapiTicket;
      } else {
        throw new Error(`获取jsapi_ticket失败: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      console.error('获取jsapi_ticket请求失败:', error);
      throw error;
    }
  }

  /**
   * 检查缓存状态
   * @returns {boolean} 缓存是否有效
   */
  checkCache(): boolean {
    try {
      const token = localStorage.getItem(CACHE_KEYS.ACCESS_TOKEN);
      const tokenExpire = localStorage.getItem(CACHE_KEYS.TOKEN_EXPIRE_TIME);
      const ticket = localStorage.getItem(CACHE_KEYS.JSAPI_TICKET);
      const ticketExpire = localStorage.getItem(CACHE_KEYS.TICKET_EXPIRE_TIME);

      const now = Date.now();
      const tokenValid = token && tokenExpire && now < parseInt(tokenExpire);
      const ticketValid = ticket && ticketExpire && now < parseInt(ticketExpire);

      return tokenValid && ticketValid;
    } catch (e) {
      console.error('检查缓存状态出错:', e);
      return false;
    }
  }

  /**
   * 刷新凭证
   * @returns {Promise<void>}
   */
  async refreshCredentials(): Promise<void> {
    this.clearCredentials();
    try {
      const token = await this.getAccessToken();
      const ticket = await this.getJsapiTicket();
      console.log('凭证刷新成功:', { token: token.substring(0, 8) + '...', ticket: ticket.substring(0, 8) + '...' });
    } catch (error) {
      console.error('刷新凭证失败:', error);
      throw error;
    }
  }

  /**
   * 清除缓存的凭证
   */
  clearCredentials(): void {
    localStorage.removeItem(CACHE_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(CACHE_KEYS.TOKEN_EXPIRE_TIME);
    localStorage.removeItem(CACHE_KEYS.JSAPI_TICKET);
    localStorage.removeItem(CACHE_KEYS.TICKET_EXPIRE_TIME);
    console.log('已清除政务微信SDK凭证缓存');
  }

  /**
   * 生成权限验证签名
   * @param url 当前页面URL，不包含#及其后面部分
   * @returns Promise<{signature, nonceStr, timestamp}> 返回签名结果
   */
  async generateSignature(url: string): Promise<{ signature: string; nonceStr: string; timestamp: string }> {
    // 获取jsapi_ticket
    const jsapiTicket = await this.getJsapiTicket();

    // 生成随机字符串和时间戳
    const nonceStr = uuidv1().replace(/-/g, '');
    const timestamp = Math.floor(Date.now() / 1000).toString();

    // 按照字典序拼接签名串
    const str = [`jsapi_ticket=${jsapiTicket}`, `noncestr=${nonceStr}`, `timestamp=${timestamp}`, `url=${url}`].sort().join('&');

    // 使用sha1进行签名
    const signature = CryptoJS.SHA1(str).toString(CryptoJS.enc.Hex);

    return {
      signature,
      nonceStr,
      timestamp
    };
  }

  /**
   * 动态加载微信JS-SDK
   * @returns Promise<void>
   */
  private loadWxScript(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果已经加载过JS-SDK
      if (window['wx']) {
        resolve();
        return;
      }
      console.log('正在动态加载政务微信JS-SDK...');

      // 创建script标签
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = 'https://res.wx.qq.com/open/js/jweixin-1.2.0.js';
      script.async = true;

      // 加载成功回调
      script.onload = () => {
        console.log('政务微信JS-SDK加载成功');
        resolve();
      };

      // 加载失败回调
      script.onerror = (error) => {
        console.error('政务微信JS-SDK加载失败:', error);
        reject(new Error('政务微信JS-SDK加载失败'));
      };

      // 添加到文档中
      document.body.appendChild(script);
    });
  }

  /**
   * 配置政务微信JS-SDK
   * @param jsApiList 需要使用的JS接口列表
   * @returns Promise<void>
   */
  async configWx(jsApiList: string[] = []): Promise<void> {
    // 如果已经在配置中，等待配置完成
    if (this.isConfiguring) {
      return new Promise((resolve, reject) => {
        const checkInterval = setInterval(() => {
          if (!this.isConfiguring) {
            clearInterval(checkInterval);
            if (this.isInitialized) {
              resolve();
            } else {
              reject(new Error('SDK配置失败'));
            }
          }
        }, 100);
      });
    }

    this.isConfiguring = true;

    try {
      await this.loadWxScript();

      // 获取当前页面URL，不包含hash部分
      const url = process.env.NODE_ENV === 'development' ? 'http://**************:8990/gemp-app' : window.location.href.split('#')[0];

      // 默认的API列表
      const defaultApiList = ['getLocation', 'openLocation', 'chooseImage', 'uploadImage', 'previewImage', 'getLocalImgData'];

      // 合并API列表
      const apiList = Array.from(new Set([...defaultApiList, ...jsApiList]));

      console.log('正在配置政务微信JS-SDK...', { url, apiList });

      // 获取签名
      const signature = await this.generateSignature(url);

      // 配置微信JS接口
      window['wx'].config({
        beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
        debug: this.config.debug, // 开启调试模式
        appId: this.config.corpId, // 必填，企业微信的corpID
        timestamp: signature.timestamp, // 必填，生成签名的时间戳
        nonceStr: signature.nonceStr, // 必填，生成签名的随机串
        signature: signature.signature, // 必填，签名
        jsApiList: apiList // 必填，需要使用的JS接口列表
      });

      // 注册成功回调
      window['wx'].ready(() => {
        console.log('政务微信JS-SDK配置成功');
        this.isInitialized = true;
        this.isConfiguring = false;
      });

      // 注册错误回调
      window['wx'].error((res: any) => {
        console.error('政务微信JS-SDK配置失败:', res);
        this.isInitialized = false;
        this.isConfiguring = false;
        throw new Error(`政务微信JS-SDK配置失败: ${JSON.stringify(res)}`);
      });
    } catch (error) {
      console.error('政务微信JS-SDK配置过程发生错误:', error);
      this.isInitialized = false;
      this.isConfiguring = false;
      throw error;
    }
  }

  /**
   * 获取当前位置信息
   * @param options 配置选项
   * @returns Promise 返回位置信息
   */
  async getLocation(options: LocationOptions = {}): Promise<LocationResult> {
    const settings = {
      type: 'gcj02',
      showLoading: true,
      ...options
    };

    if (settings.showLoading) {
      Toast.loading({
        message: '定位中...',
        forbidClick: true,
        duration: 0
      });
    }
    try {
      // 检查是否在政务微信环境中
      if (!this.isWeiXin()) {
        if (settings.showLoading) Toast.clear();
        throw new Error('非政务微信环境，无法获取位置');
      }

      // 检查SDK是否已加载，如果未加载，尝试加载
      if (!window['wx']) {
        try {
          await this.loadWxScript();
        } catch (loadError) {
          if (settings.showLoading) Toast.clear();

          // 开发环境下使用模拟位置
          if (process.env.NODE_ENV === 'development') {
            console.warn('开发环境下使用模拟位置数据');
            Toast.clear();
            // 武汉市中心坐标
            return {
              latitude: 30.592849,
              longitude: 114.305539,
              speed: 0,
              accuracy: 65,
              gps_status: 3,
              altitude: 0
            };
          }

          throw new Error('无法加载政务微信JS-SDK: ' + loadError.message);
        }
      }

      // 如果SDK未初始化，先初始化
      if (!this.isInitialized) {
        try {
          await this.configWx(['getLocation']);
        } catch (configError) {
          if (settings.showLoading) Toast.clear();

          // 开发环境下使用模拟位置
          if (process.env.NODE_ENV === 'development') {
            console.warn('开发环境下使用模拟位置数据');
            // 武汉市中心坐标
            return {
              latitude: 30.592849,
              longitude: 114.305539,
              speed: 0,
              accuracy: 65,
              gps_status: 3,
              altitude: 0
            };
          }

          throw configError;
        }
      }

      // 调用位置API
      return new Promise<LocationResult>((resolve, reject) => {
        try {
          window['wx'].getLocation({
            type: settings.type,
            success: (res: LocationResult) => {
              if (settings.showLoading) Toast.clear();
              resolve({
                latitude: res.latitude,
                longitude: res.longitude,
                speed: res.speed || 0,
                accuracy: res.accuracy || 0,
                gps_status: res.gps_status || 3,
                altitude: res.altitude || 0
              });
            },
            cancel: () => {
              if (settings.showLoading) Toast.clear();
              const error = new Error('用户拒绝授权获取位置');
              Notify({ type: 'warning', message: '需要授权位置信息才能使用此功能' });
              reject(error);
            },
            fail: (res: any) => {
              if (settings.showLoading) Toast.clear();
              console.error('获取位置信息失败:', res);

              // 开发环境下使用模拟位置
              if (process.env.NODE_ENV === 'development') {
                console.warn('开发环境下使用模拟位置数据');
                // 武汉市中心坐标
                resolve({
                  latitude: 30.592849,
                  longitude: 114.305539,
                  speed: 0,
                  accuracy: 65,
                  gps_status: 3,
                  altitude: 0
                });
                return;
              }

              const error = new Error('获取位置信息失败: ' + JSON.stringify(res));
              Notify({ type: 'danger', message: '获取位置信息失败' });
              reject(error);
            }
          });
        } catch (callError) {
          if (settings.showLoading) Toast.clear();
          console.error('调用getLocation异常:', callError);

          // 开发环境下使用模拟位置
          if (process.env.NODE_ENV === 'development') {
            console.warn('开发环境下使用模拟位置数据');
            // 武汉市中心坐标
            resolve({
              latitude: 30.592849,
              longitude: 114.305539,
              speed: 0,
              accuracy: 65,
              gps_status: 3,
              altitude: 0
            });
            return;
          }

          reject(callError);
        }
      });
    } catch (error) {
      if (settings.showLoading) Toast.clear();
      Notify({ type: 'danger', message: error.message || '获取位置信息失败' });
      throw error;
    }
  }

  /**
   * 打开地图并显示位置
   * @param options 位置信息
   */
  async openLocation(options: { latitude: number; longitude: number; name?: string; address?: string; scale?: number }): Promise<void> {
    // 检查参数
    if (!options || !options.latitude || !options.longitude) {
      throw new Error('缺少必要的位置信息');
    }

    // 检查是否在政务微信环境中
    if (!this.isWeiXin()) {
      throw new Error('非政务微信环境，无法打开地图');
    }

    // 检查SDK是否已加载，如果未加载，尝试加载
    if (!window['wx']) {
      try {
        await this.loadWxScript();
      } catch (loadError) {
        throw new Error('无法加载政务微信JS-SDK: ' + loadError.message);
      }
    }

    // 如果SDK未初始化，先初始化
    if (!this.isInitialized) {
      await this.configWx(['openLocation']);
    }

    const params = {
      latitude: parseFloat(options.latitude.toString()),
      longitude: parseFloat(options.longitude.toString()),
      name: options.name || '当前位置',
      address: options.address || '',
      scale: options.scale || 14
    };

    return new Promise<void>((resolve, reject) => {
      window['wx'].openLocation({
        ...params,
        success: () => resolve(),
        fail: (res: any) => {
          console.error('打开地图失败:', res);
          reject(new Error('打开地图失败'));
        }
      });
    });
  }
}

export default new ZwwxSDK();
