import { gsmdp } from '../gsmdp/gsmdp'
import store from '@/store';
/**
 * 初始化用户信息
 */
var isAndroid_ios = () => {
    //android is true else is false
    var u = navigator.userAgent,
        app = navigator.appVersion;
    var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Linux') > -1; //android终端或者uc浏览器
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    return isAndroid == true ? true : false;
}
let resultStr = {
    "accessTokenValidity": null,
    "baseToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjlmYzNhYWFhLTkzOGYtNDY5NC1hODQxLWFjODlmYjE1M2VmNyIsImp0aSI6IjlmYzNhYWFhLTkzOGYtNDY5NC1hODQxLWFjODlmYjE1M2VmNyIsInVzZXJuYW1lIjoiaGJzemJ5In0.3GLIa0KxLlEfJ2Q0BGJopicUCp9ttsVHWd9MDtbPp4U",
    "bearerToken": null,
    "euipOrgDto": {},
    "euipPersonDTO": {},
    "privilege": [],
    "refreshToken": "a77c02a7-ea7a-4334-9dd3-66e08d4ebfe9",
    "refreshTokenValidity": null,
    "role": null,
    "roleInfoDTO": [
    ],
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpZCI6IjlmYzNhYWFhLTkzOGYtNDY5NC1hODQxLWFjODlmYjE1M2VmNyIsImp0aSI6IjlmYzNhYWFhLTkzOGYtNDY5NC1hODQxLWFjODlmYjE1M2VmNyIsInVzZXJuYW1lIjoiaGJzemJ5In0.3GLIa0KxLlEfJ2Q0BGJopicUCp9ttsVHWd9MDtbPp4U",
    "userDto": {
        "additionalInfo": null,
        "additionalInformations": null,
        "address": null,
        "authority": [
            "1",
            "0"
        ],
        "birthday": "",
        "cardNumber": null,
        "clientUserStatus": null,
        "createTime": "2021-05-19 17:33:09",
        "creatorId": "f7b0dc8c-2bd1-4d6f-8818-4d2463740b8e",
        "creatorUserName": "13720327253",
        "description": null,
        "district": "420000",
        "districtName": "湖北省",
        "dutyNumber": null,
        "email": null,
        "faxed": null,
        "firstName": null,
        "gender": "1",
        "id": "f7b0dc8c-2bd1-4d6f-8818-4d2463740b8e",
        "image": null,
        "inInactiveClientGroup": null,
        "inInactivePlatformGroup": null,
        "inactivePlatformGroup": null,
        "lastLoginTime": "2021-07-28 18:39:06",
        "lastName": null,
        "lastPwdUpdateTime": "2021-05-24 14:16:01",
        "lockStatus": 0,
        "manager": null,
        "middleName": null,
        "name": "湖北省值班员",
        "onlineStatus": 0,
        "orgId": "d3dcd0c1f9214fc28240d15e8d21c6a9",
        "orgName": "湖北省湖北省应急管理厅",
        "personId": "ff8080817d02ad6d017d0dc4c4430012",
        "personJob": null,
        "phone": "13518987654",
        "roles": null,
        "status": 0,
        "tenantId": "GJ.HUBS",
        "updateTime": "2021-05-24 14:16:01",
        "updatorId": "2400aac1-c5e9-4f1a-bae6-6beddda85f9c",
        "updatorUserName": "15515260355",
        "userDomain": "local",
        "userName": "15515260355"
    }
}
export const initData = (callback) => {
    console.log("isandroid==>" + isAndroid_ios())
    let isAndroid = isAndroid_ios();

    store.commit('SET_CONFIG_INFO', {needVoice:"true"});
    callback(resultStr);
};

